package tiering

import (
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
)

func GetTrialProvenanceForTier(tier tieringExtPb.Tier) tieringEnumPb.Provenance {
	switch tier {
	case tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		return tieringEnumPb.Provenance_PROVENANCE_OPT_IN_FOR_PRIME_TRIAL
	case tieringExtPb.Tier_TIER_FI_INFINITE:
		return tieringEnumPb.Provenance_PROVENANCE_OPT_IN_FOR_INFINITE_TRIAL
	case tieringExtPb.Tier_TIER_FI_PLUS:
		return tieringEnumPb.Provenance_PROVENANCE_OPT_IN_FOR_PLUS_TRIAL
	default:
		return tieringEnumPb.Provenance_PROVENANCE_UNSPECIFIED
	}
}
