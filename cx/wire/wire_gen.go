// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"context"
	config4 "github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials/stscreds"
	"github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/aws/aws-sdk-go-v2/service/sts"
	"github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/api/pkg/monorail/payload"
	config2 "github.com/epifi/be-common/pkg/aws/v2/config"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	sqs2 "github.com/epifi/be-common/pkg/aws/v2/sqs"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/monorail/api_wrapper"
	"github.com/epifi/be-common/pkg/ratelimiter"
	"github.com/epifi/be-common/pkg/ratelimiter/store"
	"github.com/epifi/be-common/pkg/storage/v2"
	dao20 "github.com/epifi/gamma/actor_activity/dao"
	"github.com/epifi/gamma/api/accounts/balance"
	developer58 "github.com/epifi/gamma/api/accounts/developer"
	"github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/accounts/statement"
	"github.com/epifi/gamma/api/accrual"
	"github.com/epifi/gamma/api/actor"
	developer2 "github.com/epifi/gamma/api/actor/developer"
	"github.com/epifi/gamma/api/actor_activity"
	"github.com/epifi/gamma/api/alfred"
	developer46 "github.com/epifi/gamma/api/alfred/developer"
	"github.com/epifi/gamma/api/aml"
	developer45 "github.com/epifi/gamma/api/aml/developer"
	developer35 "github.com/epifi/gamma/api/analyser/developer"
	"github.com/epifi/gamma/api/analyser/investment"
	"github.com/epifi/gamma/api/analyser/txnaggregates"
	"github.com/epifi/gamma/api/auth"
	developer18 "github.com/epifi/gamma/api/auth/developer"
	"github.com/epifi/gamma/api/auth/liveness"
	developer9 "github.com/epifi/gamma/api/auth/liveness/developer"
	"github.com/epifi/gamma/api/auth/location"
	developer41 "github.com/epifi/gamma/api/auth/orchestrator/developer"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/bankcust/compliance"
	developer52 "github.com/epifi/gamma/api/bankcust/developer"
	"github.com/epifi/gamma/api/card/control"
	cx7 "github.com/epifi/gamma/api/card/cx"
	developer8 "github.com/epifi/gamma/api/card/developer"
	"github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/casbin"
	developer14 "github.com/epifi/gamma/api/casbin/developer"
	"github.com/epifi/gamma/api/casper"
	developer21 "github.com/epifi/gamma/api/casper/developer"
	"github.com/epifi/gamma/api/casper/discounts"
	"github.com/epifi/gamma/api/casper/exchanger"
	"github.com/epifi/gamma/api/casper/external_vendor_redemption"
	"github.com/epifi/gamma/api/casper/redemption"
	"github.com/epifi/gamma/api/categorizer"
	developer16 "github.com/epifi/gamma/api/categorizer/developer"
	developer38 "github.com/epifi/gamma/api/celestial/developer"
	"github.com/epifi/gamma/api/cms"
	developer51 "github.com/epifi/gamma/api/cms/developer"
	developer54 "github.com/epifi/gamma/api/collection/developer"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/comms/developer"
	"github.com/epifi/gamma/api/comms/developer/actions"
	"github.com/epifi/gamma/api/comms/inapptargetedcomms"
	"github.com/epifi/gamma/api/comms/user_preference"
	"github.com/epifi/gamma/api/connected_account"
	developer25 "github.com/epifi/gamma/api/connected_account/developer"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/credit_limit_estimator"
	developer57 "github.com/epifi/gamma/api/creditreportv2/developer"
	"github.com/epifi/gamma/api/cx/chat"
	crm_issue_tracker_integration2 "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration"
	profile2 "github.com/epifi/gamma/api/cx/data_collector/profile"
	"github.com/epifi/gamma/api/cx/data_collector/salaryprogram/salaryb2b"
	developer13 "github.com/epifi/gamma/api/cx/developer"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/cx/error_activity/watson_info_provider"
	issue_resolution_feedback2 "github.com/epifi/gamma/api/cx/issue_resolution_feedback"
	"github.com/epifi/gamma/api/cx/manual_ticket_stage_wise_comms"
	"github.com/epifi/gamma/api/cx/ticket"
	"github.com/epifi/gamma/api/cx/user_issue_info"
	"github.com/epifi/gamma/api/cx/watson"
	mock_client2 "github.com/epifi/gamma/api/cx/watson/mock_client"
	"github.com/epifi/gamma/api/deposit"
	developer3 "github.com/epifi/gamma/api/deposit/developer"
	watson5 "github.com/epifi/gamma/api/deposit/watson"
	"github.com/epifi/gamma/api/employment"
	developer53 "github.com/epifi/gamma/api/employment/developer"
	"github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/firefly/billing"
	cx5 "github.com/epifi/gamma/api/firefly/cx"
	developer37 "github.com/epifi/gamma/api/firefly/developer"
	lms2 "github.com/epifi/gamma/api/firefly/lms"
	"github.com/epifi/gamma/api/firefly/pinot"
	"github.com/epifi/gamma/api/fittt"
	"github.com/epifi/gamma/api/fittt/devconsole"
	developer24 "github.com/epifi/gamma/api/fittt/developer"
	"github.com/epifi/gamma/api/fittt/scheduler"
	"github.com/epifi/gamma/api/fittt/sports"
	"github.com/epifi/gamma/api/frontend/account/sa_closure"
	developer50 "github.com/epifi/gamma/api/health_engine/developer"
	developer12 "github.com/epifi/gamma/api/inapphelp/developer"
	"github.com/epifi/gamma/api/inapphelp/faq/serving"
	"github.com/epifi/gamma/api/inapphelp/issue_reporting"
	"github.com/epifi/gamma/api/inapphelp/media"
	"github.com/epifi/gamma/api/inappreferral"
	developer26 "github.com/epifi/gamma/api/inappreferral/developer"
	"github.com/epifi/gamma/api/inappreferral/season"
	"github.com/epifi/gamma/api/insights"
	"github.com/epifi/gamma/api/insights/accessinfo"
	developer15 "github.com/epifi/gamma/api/insights/developer"
	"github.com/epifi/gamma/api/insights/emailparser"
	"github.com/epifi/gamma/api/insights/epf"
	"github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/api/investment/aggregator"
	"github.com/epifi/gamma/api/investment/dynamic_ui_element"
	"github.com/epifi/gamma/api/investment/mutualfund/catalog"
	developer28 "github.com/epifi/gamma/api/investment/mutualfund/developer"
	"github.com/epifi/gamma/api/investment/mutualfund/external"
	"github.com/epifi/gamma/api/investment/mutualfund/foliodetails"
	order3 "github.com/epifi/gamma/api/investment/mutualfund/order"
	"github.com/epifi/gamma/api/investment/mutualfund/order/filegenerator"
	"github.com/epifi/gamma/api/investment/mutualfund/order/operations"
	"github.com/epifi/gamma/api/investment/mutualfund/order/prerequisite_handler"
	"github.com/epifi/gamma/api/investment/mutualfund/order/reverse_feed"
	"github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	"github.com/epifi/gamma/api/investment/mutualfund/reconciliation"
	watson4 "github.com/epifi/gamma/api/investment/watson"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/kyc/agent"
	developer4 "github.com/epifi/gamma/api/kyc/developer"
	"github.com/epifi/gamma/api/kyc/vkyc"
	developer56 "github.com/epifi/gamma/api/leads/developer"
	developer23 "github.com/epifi/gamma/api/merchant/developer"
	developer59 "github.com/epifi/gamma/api/nps/developer"
	"github.com/epifi/gamma/api/nudge"
	developer33 "github.com/epifi/gamma/api/nudge/developer"
	"github.com/epifi/gamma/api/nudge/journey"
	"github.com/epifi/gamma/api/order"
	aa2 "github.com/epifi/gamma/api/order/aa"
	cx2 "github.com/epifi/gamma/api/order/cx"
	developer6 "github.com/epifi/gamma/api/order/developer"
	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/order/recon"
	"github.com/epifi/gamma/api/p2pinvestment"
	cx8 "github.com/epifi/gamma/api/p2pinvestment/cx"
	developer31 "github.com/epifi/gamma/api/p2pinvestment/developer"
	"github.com/epifi/gamma/api/p2pinvestment/incidentmanager"
	"github.com/epifi/gamma/api/pan"
	developer47 "github.com/epifi/gamma/api/pan/developer"
	"github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/pay/cx"
	developer42 "github.com/epifi/gamma/api/pay/developer"
	"github.com/epifi/gamma/api/pay/internationalfundtransfer"
	"github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator"
	"github.com/epifi/gamma/api/pay/payincidentmanager"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	developer7 "github.com/epifi/gamma/api/paymentinstrument/developer"
	payload2 "github.com/epifi/gamma/api/pkg/airflow/payload"
	preapprovedloan2 "github.com/epifi/gamma/api/preapprovedloan"
	cx6 "github.com/epifi/gamma/api/preapprovedloan/cx"
	developer36 "github.com/epifi/gamma/api/preapprovedloan/developer"
	"github.com/epifi/gamma/api/preapprovedloan/sherlock_banners"
	"github.com/epifi/gamma/api/product"
	developer48 "github.com/epifi/gamma/api/quest/developer"
	manager3 "github.com/epifi/gamma/api/quest/manager"
	"github.com/epifi/gamma/api/recurringpayment"
	developer29 "github.com/epifi/gamma/api/recurringpayment/developer"
	"github.com/epifi/gamma/api/recurringpayment/enach"
	developer30 "github.com/epifi/gamma/api/recurringpayment/enach/developer"
	"github.com/epifi/gamma/api/rewards"
	"github.com/epifi/gamma/api/rewards/campaigncomm"
	developer17 "github.com/epifi/gamma/api/rewards/developer"
	"github.com/epifi/gamma/api/rewards/generator"
	"github.com/epifi/gamma/api/rewards/luckydraw"
	"github.com/epifi/gamma/api/rewards/projector"
	"github.com/epifi/gamma/api/rewards/rewardoffers"
	"github.com/epifi/gamma/api/risk"
	"github.com/epifi/gamma/api/risk/case_management"
	developer39 "github.com/epifi/gamma/api/risk/developer"
	"github.com/epifi/gamma/api/risk/lea"
	profile3 "github.com/epifi/gamma/api/risk/profile"
	"github.com/epifi/gamma/api/risk/redlist"
	"github.com/epifi/gamma/api/risk/whitelist"
	developer22 "github.com/epifi/gamma/api/rms/developer"
	manager2 "github.com/epifi/gamma/api/rms/manager"
	developer61 "github.com/epifi/gamma/api/salaryestimation/developer"
	"github.com/epifi/gamma/api/salaryprogram"
	cx9 "github.com/epifi/gamma/api/salaryprogram/cx"
	developer34 "github.com/epifi/gamma/api/salaryprogram/developer"
	dynamic_ui_element2 "github.com/epifi/gamma/api/salaryprogram/dynamic_ui_element"
	"github.com/epifi/gamma/api/salaryprogram/healthinsurance"
	"github.com/epifi/gamma/api/salaryprogram/referrals"
	"github.com/epifi/gamma/api/savings"
	developer10 "github.com/epifi/gamma/api/savings/developer"
	"github.com/epifi/gamma/api/savings/extacct"
	watson2 "github.com/epifi/gamma/api/savings/watson"
	"github.com/epifi/gamma/api/screener"
	developer40 "github.com/epifi/gamma/api/screener/developer"
	"github.com/epifi/gamma/api/search"
	developer62 "github.com/epifi/gamma/api/search/developer"
	"github.com/epifi/gamma/api/search/indexer"
	developer60 "github.com/epifi/gamma/api/securities/developer"
	"github.com/epifi/gamma/api/segment"
	consumer4 "github.com/epifi/gamma/api/segment/consumer"
	developer32 "github.com/epifi/gamma/api/segment/developer"
	"github.com/epifi/gamma/api/simulator/cx/watson_client"
	"github.com/epifi/gamma/api/simulator/lending/preapprovedloan"
	developer63 "github.com/epifi/gamma/api/simulator/openbanking/accounts/developer"
	"github.com/epifi/gamma/api/simulator/profileevaluator"
	"github.com/epifi/gamma/api/stockguardian/sgapigateway/application"
	"github.com/epifi/gamma/api/stockguardian/sgapigateway/dbstate"
	kyc3 "github.com/epifi/gamma/api/stockguardian/sgapigateway/kyc"
	"github.com/epifi/gamma/api/stockguardian/sgapigateway/lms"
	"github.com/epifi/gamma/api/tiering"
	developer44 "github.com/epifi/gamma/api/tiering/developer"
	pinot2 "github.com/epifi/gamma/api/tiering/pinot"
	"github.com/epifi/gamma/api/timeline"
	developer20 "github.com/epifi/gamma/api/timeline/developer"
	"github.com/epifi/gamma/api/tspuser"
	developer55 "github.com/epifi/gamma/api/tspuser/developer"
	"github.com/epifi/gamma/api/typesv2"
	developer49 "github.com/epifi/gamma/api/upcomingtransactions/developer"
	"github.com/epifi/gamma/api/upi"
	cx4 "github.com/epifi/gamma/api/upi/cx"
	developer11 "github.com/epifi/gamma/api/upi/developer"
	"github.com/epifi/gamma/api/upi/mandate"
	onboarding2 "github.com/epifi/gamma/api/upi/onboarding"
	"github.com/epifi/gamma/api/user"
	developer5 "github.com/epifi/gamma/api/user/developer"
	"github.com/epifi/gamma/api/user/group"
	location2 "github.com/epifi/gamma/api/user/location"
	"github.com/epifi/gamma/api/user/onboarding"
	watson3 "github.com/epifi/gamma/api/user/onboarding/watson"
	"github.com/epifi/gamma/api/useractions"
	account2 "github.com/epifi/gamma/api/usstocks/account"
	catalog2 "github.com/epifi/gamma/api/usstocks/catalog"
	developer43 "github.com/epifi/gamma/api/usstocks/developer"
	operations2 "github.com/epifi/gamma/api/usstocks/operations"
	order2 "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/api/usstocks/portfolio"
	"github.com/epifi/gamma/api/vendorgateway/aa"
	"github.com/epifi/gamma/api/vendorgateway/crm/leadsquared"
	"github.com/epifi/gamma/api/vendorgateway/cx/chatbot/livechatfallback/senseforth"
	"github.com/epifi/gamma/api/vendorgateway/cx/chatbot/nugget"
	"github.com/epifi/gamma/api/vendorgateway/cx/federal"
	"github.com/epifi/gamma/api/vendorgateway/cx/freshchat"
	"github.com/epifi/gamma/api/vendorgateway/cx/freshdesk"
	"github.com/epifi/gamma/api/vendorgateway/cx/ozonetel"
	"github.com/epifi/gamma/api/vendorgateway/ekyc"
	"github.com/epifi/gamma/api/vendorgateway/extvalidate"
	"github.com/epifi/gamma/api/vendorgateway/fennel"
	"github.com/epifi/gamma/api/vendorgateway/investments/p2p"
	"github.com/epifi/gamma/api/vendorgateway/namecheck"
	"github.com/epifi/gamma/api/vendorgateway/namecheck/employernamecategoriser"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	deposit2 "github.com/epifi/gamma/api/vendorgateway/openbanking/deposit"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/dispute"
	payment2 "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	"github.com/epifi/gamma/api/vendorgateway/scienaptic"
	"github.com/epifi/gamma/api/vendorgateway/stocks"
	mutualfund2 "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund"
	"github.com/epifi/gamma/api/vendormapping"
	developer19 "github.com/epifi/gamma/api/vendormapping/developer"
	federal2 "github.com/epifi/gamma/api/vendornotification/openbanking/recurringpayment/federal"
	"github.com/epifi/gamma/api/wealthonboarding"
	cx3 "github.com/epifi/gamma/api/wealthonboarding/cx"
	developer27 "github.com/epifi/gamma/api/wealthonboarding/developer"
	"github.com/epifi/gamma/cx/activity"
	"github.com/epifi/gamma/cx/admin_actions"
	helper10 "github.com/epifi/gamma/cx/admin_actions/helper"
	processor10 "github.com/epifi/gamma/cx/admin_actions/processor"
	"github.com/epifi/gamma/cx/app_log"
	dao14 "github.com/epifi/gamma/cx/app_log/dao"
	"github.com/epifi/gamma/cx/audit_log"
	dao10 "github.com/epifi/gamma/cx/audit_log/dao"
	"github.com/epifi/gamma/cx/call"
	"github.com/epifi/gamma/cx/call/blocker"
	consumer8 "github.com/epifi/gamma/cx/call/consumer"
	processor11 "github.com/epifi/gamma/cx/call/consumer/processor"
	dao6 "github.com/epifi/gamma/cx/call/dao"
	helper13 "github.com/epifi/gamma/cx/call/helper"
	"github.com/epifi/gamma/cx/call_ivr"
	dao23 "github.com/epifi/gamma/cx/call_ivr/dao"
	"github.com/epifi/gamma/cx/call_routing"
	dao16 "github.com/epifi/gamma/cx/call_routing/dao"
	helper11 "github.com/epifi/gamma/cx/call_routing/helper"
	"github.com/epifi/gamma/cx/call_routing/helper/priority_helper"
	chat2 "github.com/epifi/gamma/cx/chat"
	"github.com/epifi/gamma/cx/chat/bot/livechatfallback"
	"github.com/epifi/gamma/cx/chat/bot/workflow"
	"github.com/epifi/gamma/cx/chat/bot/workflow/execute_action_processor"
	"github.com/epifi/gamma/cx/chat/bot/workflow/fetch_data_processor"
	consumer9 "github.com/epifi/gamma/cx/chat/consumer"
	processor12 "github.com/epifi/gamma/cx/chat/consumer/processor"
	dao4 "github.com/epifi/gamma/cx/chat/dao"
	helper4 "github.com/epifi/gamma/cx/chat/helper"
	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/config/worker"
	connected_account2 "github.com/epifi/gamma/cx/connected_account"
	"github.com/epifi/gamma/cx/consumer"
	"github.com/epifi/gamma/cx/consumer/contact_event"
	"github.com/epifi/gamma/cx/consumer/create_ticket_event"
	"github.com/epifi/gamma/cx/consumer/escalation"
	"github.com/epifi/gamma/cx/consumer/s3_event"
	"github.com/epifi/gamma/cx/consumer/ticket_event"
	helper5 "github.com/epifi/gamma/cx/consumer/ticket_event/helper"
	"github.com/epifi/gamma/cx/consumer/update_ticket_event"
	"github.com/epifi/gamma/cx/consumer/update_ticket_event/issue_resolution_feedback"
	processor2 "github.com/epifi/gamma/cx/consumer/update_ticket_event/issue_resolution_feedback/processor"
	"github.com/epifi/gamma/cx/crm_issue_tracker_integration"
	consumer10 "github.com/epifi/gamma/cx/crm_issue_tracker_integration/consumer"
	processor13 "github.com/epifi/gamma/cx/crm_issue_tracker_integration/consumer/processor"
	dao22 "github.com/epifi/gamma/cx/crm_issue_tracker_integration/dao"
	helper15 "github.com/epifi/gamma/cx/crm_issue_tracker_integration/helper"
	"github.com/epifi/gamma/cx/crm_issue_tracker_integration/ticket_translator"
	"github.com/epifi/gamma/cx/crm_issue_tracker_integration/ticket_translator/monorail_description"
	"github.com/epifi/gamma/cx/customer_auth"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
	"github.com/epifi/gamma/cx/customer_auth/dao"
	"github.com/epifi/gamma/cx/customer_auth/verifier"
	"github.com/epifi/gamma/cx/data_collector/account"
	alfred2 "github.com/epifi/gamma/cx/data_collector/alfred"
	"github.com/epifi/gamma/cx/data_collector/card"
	comms2 "github.com/epifi/gamma/cx/data_collector/comms"
	firefly2 "github.com/epifi/gamma/cx/data_collector/firefly"
	fittt3 "github.com/epifi/gamma/cx/data_collector/fittt"
	"github.com/epifi/gamma/cx/data_collector/helper"
	"github.com/epifi/gamma/cx/data_collector/investment/mutualfund"
	"github.com/epifi/gamma/cx/data_collector/investment/usstocks"
	kyc2 "github.com/epifi/gamma/cx/data_collector/kyc"
	onboarding3 "github.com/epifi/gamma/cx/data_collector/onboarding"
	p2pinvestment3 "github.com/epifi/gamma/cx/data_collector/p2pinvestment"
	internationalfundtransfer3 "github.com/epifi/gamma/cx/data_collector/pay/internationalfundtransfer"
	"github.com/epifi/gamma/cx/data_collector/payment_instruments"
	preapprovedloan3 "github.com/epifi/gamma/cx/data_collector/preapprovedloan"
	"github.com/epifi/gamma/cx/data_collector/preapprovedloan/factory"
	generator2 "github.com/epifi/gamma/cx/data_collector/preapprovedloan/generator"
	"github.com/epifi/gamma/cx/data_collector/preapprovedloan/generator/impl"
	"github.com/epifi/gamma/cx/data_collector/preapprovedloan/providers"
	"github.com/epifi/gamma/cx/data_collector/profile"
	referrals3 "github.com/epifi/gamma/cx/data_collector/referrals"
	rewards2 "github.com/epifi/gamma/cx/data_collector/rewards"
	"github.com/epifi/gamma/cx/data_collector/risk_ops_wealth"
	"github.com/epifi/gamma/cx/data_collector/salarydataops"
	salaryb2b2 "github.com/epifi/gamma/cx/data_collector/salaryprogram/salaryb2b"
	savings2 "github.com/epifi/gamma/cx/data_collector/savings"
	"github.com/epifi/gamma/cx/data_collector/sherlock_actor_activity/handler"
	"github.com/epifi/gamma/cx/data_collector/sherlock_actor_activity/repository"
	"github.com/epifi/gamma/cx/data_collector/sherlock_actor_activity/usecase"
	tiering2 "github.com/epifi/gamma/cx/data_collector/tiering"
	"github.com/epifi/gamma/cx/data_collector/transaction"
	"github.com/epifi/gamma/cx/data_collector/user_requests"
	"github.com/epifi/gamma/cx/data_collector/user_requests/account_details_collector"
	vkyccall3 "github.com/epifi/gamma/cx/data_collector/vkyccall"
	"github.com/epifi/gamma/cx/data_collector/vkyccall/serviceprovider"
	"github.com/epifi/gamma/cx/data_collector/wealth_onboarding"
	actions2 "github.com/epifi/gamma/cx/developer/actions"
	consumer5 "github.com/epifi/gamma/cx/developer/actions/consumer"
	"github.com/epifi/gamma/cx/developer/actions/consumer/event_processors"
	processor6 "github.com/epifi/gamma/cx/developer/actions/processor"
	casper2 "github.com/epifi/gamma/cx/developer/actions/processor/casper"
	"github.com/epifi/gamma/cx/developer/actions/processor/deposits"
	processor7 "github.com/epifi/gamma/cx/developer/actions/processor/firefly"
	fittt2 "github.com/epifi/gamma/cx/developer/actions/processor/fittt"
	insights2 "github.com/epifi/gamma/cx/developer/actions/processor/insights"
	internationalfundtransfer2 "github.com/epifi/gamma/cx/developer/actions/processor/internationalfundtransfer"
	"github.com/epifi/gamma/cx/developer/actions/processor/lending"
	mutualfund3 "github.com/epifi/gamma/cx/developer/actions/processor/mutualfund"
	p2pinvestment2 "github.com/epifi/gamma/cx/developer/actions/processor/p2pinvestment"
	referrals2 "github.com/epifi/gamma/cx/developer/actions/processor/referrals"
	"github.com/epifi/gamma/cx/developer/actions/processor/stockguardian"
	"github.com/epifi/gamma/cx/developer/actions/processor/userrisk"
	usstocks2 "github.com/epifi/gamma/cx/developer/actions/processor/usstocks"
	"github.com/epifi/gamma/cx/developer/actions/processor/usstocks/validator"
	vkyccall2 "github.com/epifi/gamma/cx/developer/actions/processor/vkyccall"
	insights3 "github.com/epifi/gamma/cx/developer/actions/processor/wealth/networth/insights"
	wealthonboarding2 "github.com/epifi/gamma/cx/developer/actions/processor/wealthonboarding"
	developer64 "github.com/epifi/gamma/cx/developer/cx_db_states"
	processor9 "github.com/epifi/gamma/cx/developer/cx_db_states/processor"
	"github.com/epifi/gamma/cx/developer/db_states"
	collector3 "github.com/epifi/gamma/cx/developer/db_states/collector"
	helper8 "github.com/epifi/gamma/cx/developer/helper"
	"github.com/epifi/gamma/cx/developer/ticket_summary"
	dispute2 "github.com/epifi/gamma/cx/dispute"
	consumer2 "github.com/epifi/gamma/cx/dispute/consumer"
	dao12 "github.com/epifi/gamma/cx/dispute/dao"
	cache2 "github.com/epifi/gamma/cx/dispute/dao/cache"
	helper6 "github.com/epifi/gamma/cx/dispute/helper"
	"github.com/epifi/gamma/cx/dispute/job"
	processor4 "github.com/epifi/gamma/cx/dispute/job/processor"
	"github.com/epifi/gamma/cx/dispute/job/reverse_update_processor"
	processor3 "github.com/epifi/gamma/cx/dispute/processor"
	helper7 "github.com/epifi/gamma/cx/dispute/processor/helper"
	"github.com/epifi/gamma/cx/dispute/questionnaire_helper"
	"github.com/epifi/gamma/cx/error_activity"
	dao19 "github.com/epifi/gamma/cx/error_activity/dao"
	"github.com/epifi/gamma/cx/error_activity/trigger_processor"
	watson_info_provider2 "github.com/epifi/gamma/cx/error_activity/watson_info_provider"
	"github.com/epifi/gamma/cx/escalations"
	dao13 "github.com/epifi/gamma/cx/escalations/dao"
	federal3 "github.com/epifi/gamma/cx/federal"
	"github.com/epifi/gamma/cx/fittt/devconsole"
	helper2 "github.com/epifi/gamma/cx/helper"
	"github.com/epifi/gamma/cx/inapphelp_feedback_engine_clients/feedback_subscription"
	"github.com/epifi/gamma/cx/interceptor"
	celestial2 "github.com/epifi/gamma/cx/internal/celestial"
	issue_resolution_feedback4 "github.com/epifi/gamma/cx/internal/issue_resolution_feedback"
	watson7 "github.com/epifi/gamma/cx/internal/watson"
	"github.com/epifi/gamma/cx/internal/watson/activity_helper"
	comms4 "github.com/epifi/gamma/cx/internal/watson/comms"
	"github.com/epifi/gamma/cx/issue_category"
	dao3 "github.com/epifi/gamma/cx/issue_category/dao"
	"github.com/epifi/gamma/cx/issue_category/issue_category_id_fetcher"
	"github.com/epifi/gamma/cx/issue_category/manager"
	"github.com/epifi/gamma/cx/issue_config"
	dao18 "github.com/epifi/gamma/cx/issue_config/dao"
	issue_resolution_feedback3 "github.com/epifi/gamma/cx/issue_resolution_feedback"
	dao21 "github.com/epifi/gamma/cx/issue_resolution_feedback/dao"
	"github.com/epifi/gamma/cx/landing_page"
	"github.com/epifi/gamma/cx/landing_page/sherlock_activity_generator"
	"github.com/epifi/gamma/cx/liveness_video"
	manual_ticket_stage_wise_comms2 "github.com/epifi/gamma/cx/manual_ticket_stage_wise_comms"
	"github.com/epifi/gamma/cx/nudge_parser"
	"github.com/epifi/gamma/cx/payout"
	consumer3 "github.com/epifi/gamma/cx/payout/consumer"
	dao15 "github.com/epifi/gamma/cx/payout/dao"
	processor5 "github.com/epifi/gamma/cx/payout/processor"
	"github.com/epifi/gamma/cx/payout/processor/cash"
	"github.com/epifi/gamma/cx/payout/processor/fi_coins"
	dao9 "github.com/epifi/gamma/cx/priority_routing/dao"
	"github.com/epifi/gamma/cx/priority_routing/priority_routing_helper"
	"github.com/epifi/gamma/cx/priority_routing/routing_engine"
	"github.com/epifi/gamma/cx/priority_routing/routing_engine/processor"
	"github.com/epifi/gamma/cx/rate_limit"
	"github.com/epifi/gamma/cx/risk_ops"
	"github.com/epifi/gamma/cx/risk_ops/chart"
	"github.com/epifi/gamma/cx/risk_ops/products"
	"github.com/epifi/gamma/cx/risk_ops/products/fetchers"
	"github.com/epifi/gamma/cx/risk_ops/review"
	actions4 "github.com/epifi/gamma/cx/risk_ops/review/actions"
	"github.com/epifi/gamma/cx/risk_ops/review/annotations"
	"github.com/epifi/gamma/cx/risk_ops/review/comments"
	"github.com/epifi/gamma/cx/risk_ops/review/watchlist"
	actor2 "github.com/epifi/gamma/cx/risk_ops/transaction_review/actor"
	"github.com/epifi/gamma/cx/risk_ops/transaction_review/builder"
	categorizer2 "github.com/epifi/gamma/cx/risk_ops/transaction_review/categorizer"
	"github.com/epifi/gamma/cx/risk_ops/transaction_review/fetcher"
	transaction2 "github.com/epifi/gamma/cx/risk_ops/transaction_review/transaction"
	"github.com/epifi/gamma/cx/sherlock_auth"
	sherlock_banners2 "github.com/epifi/gamma/cx/sherlock_banners"
	collector2 "github.com/epifi/gamma/cx/sherlock_banners/collector"
	dao24 "github.com/epifi/gamma/cx/sherlock_banners/dao"
	helper14 "github.com/epifi/gamma/cx/sherlock_banners/helper"
	"github.com/epifi/gamma/cx/sherlock_feedback"
	dao25 "github.com/epifi/gamma/cx/sherlock_feedback/dao"
	"github.com/epifi/gamma/cx/sherlock_scripts"
	"github.com/epifi/gamma/cx/sherlock_scripts/script_helper"
	"github.com/epifi/gamma/cx/sherlock_sop"
	dao26 "github.com/epifi/gamma/cx/sherlock_sop/dao"
	"github.com/epifi/gamma/cx/sherlock_sop/sop_helper"
	"github.com/epifi/gamma/cx/sherlock_user"
	dao5 "github.com/epifi/gamma/cx/sherlock_user/dao"
	"github.com/epifi/gamma/cx/sherlock_user/provisioner"
	"github.com/epifi/gamma/cx/sherlock_user/sherlock_user_wrapper"
	"github.com/epifi/gamma/cx/sprinklr"
	dao11 "github.com/epifi/gamma/cx/sprinklr/dao"
	kyc4 "github.com/epifi/gamma/cx/stockguardian/kyc"
	ticket2 "github.com/epifi/gamma/cx/ticket"
	consumer6 "github.com/epifi/gamma/cx/ticket/consumer"
	"github.com/epifi/gamma/cx/ticket/csat"
	comms3 "github.com/epifi/gamma/cx/ticket/csat/comms"
	"github.com/epifi/gamma/cx/ticket/csat/token_manager"
	dao7 "github.com/epifi/gamma/cx/ticket/dao"
	helper9 "github.com/epifi/gamma/cx/ticket/helper"
	processor8 "github.com/epifi/gamma/cx/ticket/processor"
	user_issue_info2 "github.com/epifi/gamma/cx/user_issue_info"
	"github.com/epifi/gamma/cx/validation"
	watson6 "github.com/epifi/gamma/cx/watson"
	"github.com/epifi/gamma/cx/watson/collector"
	consumer7 "github.com/epifi/gamma/cx/watson/consumer"
	helper12 "github.com/epifi/gamma/cx/watson/consumer/helper"
	ticket_event2 "github.com/epifi/gamma/cx/watson/consumer/ticket_event"
	dao2 "github.com/epifi/gamma/cx/watson/dao"
	helper3 "github.com/epifi/gamma/cx/watson/helper"
	"github.com/epifi/gamma/cx/watson/ingest_event"
	"github.com/epifi/gamma/cx/watson/mock_client"
	types2 "github.com/epifi/gamma/cx/wire/types"
	"github.com/epifi/gamma/featurestore"
	"github.com/epifi/gamma/featurestore/wire"
	dao8 "github.com/epifi/gamma/inapphelp/issue_reporting/dao"
	dao17 "github.com/epifi/gamma/investment/mutualfund/payment_handler/dao"
	"github.com/epifi/gamma/pkg/airflow/wrapper"
	"github.com/epifi/gamma/pkg/feature/release"
	config3 "github.com/epifi/gamma/pkg/feature/release/config"
	genconf2 "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	fittt4 "github.com/epifi/gamma/pkg/fittt"
	opensearch2 "github.com/epifi/gamma/pkg/opensearch"
	"github.com/epifi/gamma/pkg/strapi"
	"github.com/epifi/gamma/risk/accountstatus"
	actions3 "github.com/epifi/gamma/scripts/dev_actions/actions"
	dbstate2 "github.com/epifi/gamma/sherlock/dev/dbstate"
	"github.com/epifi/gamma/sherlock/dev/dbstate/omegle"
	"github.com/epifi/gamma/sherlock/dev/dbstate/vkyccall"
	"github.com/epifi/gamma/varys/logsource"
	"github.com/epifi/gamma/verifi/pkg"
	"github.com/opensearch-project/opensearch-go"
	"github.com/redis/go-redis/v9"
	"github.com/slack-go/slack"
	"go.temporal.io/api/workflowservice/v1"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"gorm.io/gorm"
	"net/http"
	"time"
)

// Injectors from wire.go:

func InitializeAuthenticationService(ctx context.Context, conf *config.Config) *sherlock_auth.CognitoAuthService {
	client := getCognitoIDPClient(ctx, conf)
	string2 := getCognitoUserPoolId(conf)
	authValidation := getAuthValidationConfig(conf)
	cognitoAuthService := sherlock_auth.NewCognitoAuthService(client, string2, authValidation)
	return cognitoAuthService
}

func InitializeAuthorizationService(casbinClient casbin.CasbinClient) *sherlock_auth.CasbinAuthorizationService {
	casbinAuthorizationService := sherlock_auth.NewCasbinAuthorizationService(casbinClient)
	return casbinAuthorizationService
}

func InitSherlockActorActivityService(config2 *config.Config, genConfig *genconf.Config, dbConn types.SherlockPGDB, authClient auth.AuthClient, actorActivityClient actor_activity.ActorActivityClient, actorClient actor.ActorClient, piClient paymentinstrument.PiClient, usersClient user.UsersClient, pClient payment.PaymentClient, orderClient order.OrderServiceClient, savingsClient savings.SavingsClient, depositClient deposit.DepositClient, investmentCatalogManagerClient catalog.CatalogManagerClient, p2pInvestmentClient p2pinvestment.P2PInvestmentClient, portfolioManagerClient portfolio.PortfolioManagerClient, usstocksOrderManagerClient order2.OrderManagerClient, accountBalanceClinet balance.BalanceClient, ffClient firefly.FireflyClient) *handler.SherlockActorActivityHandler {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(dbConn)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(dbConn)
	authFactorRetryLimit := getAuthFactoryRetryLimit(config2)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(dbConn)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConfig, authFactorRetryLimit, usersClient, customerAuthenticationV2DAO)
	sherlockActorActivityRepoImpl := repository.NewSherlockActorActivityRepoImpl(actorActivityClient)
	dataCollectorHelper := helper.NewDataCollectorHelper(actorClient, piClient, usersClient, pClient, orderClient, savingsClient, depositClient, investmentCatalogManagerClient, p2pInvestmentClient, portfolioManagerClient, usstocksOrderManagerClient, accountBalanceClinet, ffClient)
	sherlockActorActivityServiceImpl := usecase.NewSherlockActorActivityServiceImpl(sherlockActorActivityRepoImpl, dataCollectorHelper, pClient)
	sherlockActorActivityHandler := handler.NewSherlockActorActivityHandler(authEngine, sherlockActorActivityServiceImpl)
	return sherlockActorActivityHandler
}

// config: {"cxS3Client": "CxS3Config().BucketName"}
func InitializeProfileService(conf *config.Config, db types.SherlockPGDB, savingsClient savings.SavingsClient, userClient user.UsersClient, actorClient actor.ActorClient, cxS3Client types2.CxS3Client, ticketServiceClient ticket.TicketClient, vendorMappingClient vendormapping.VendorMappingServiceClient, cxConf *genconf.Config, externalAccountsClient extacct.ExternalAccountsClient, tieringClient tiering.TieringClient, bcClient bankcust.BankCustomerServiceClient, onbClient onboarding.OnboardingClient, commsClient comms.CommsClient, operationalStatusClient operstatus.OperationalStatusServiceClient, chatClient chat.ChatsClient, piClient paymentinstrument.PiClient, pClient payment.PaymentClient, orderClient order.OrderServiceClient, depositClient deposit.DepositClient, investmentCatalogManagerClient catalog.CatalogManagerClient, p2pInvestmentClient p2pinvestment.P2PInvestmentClient, portfolioManagerClient portfolio.PortfolioManagerClient, usstocksOrderManagerClient order2.OrderManagerClient, accountBalanceClinet balance.BalanceClient, upiOnboardingClient onboarding2.UpiOnboardingClient, accountPiRelationClient account_pi.AccountPIRelationClient, payClient pay.PayClient, payCxClient cx.CXClient, orderCxClient cx2.CXClient, ffClient firefly.FireflyClient, segmentClient segment.SegmentationServiceClient) *profile.Service {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(db)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(db)
	authFactorRetryLimit := getAuthFactoryRetryLimit(conf)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(db)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, cxConf, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	s3Client := types2.CxS3ClientProvider(cxS3Client)
	customerIdentifier := helper2.NewCustomerIdentifier(userClient, actorClient, chatClient, vendorMappingClient)
	dataCollectorHelper := helper.NewDataCollectorHelper(actorClient, piClient, userClient, pClient, orderClient, savingsClient, depositClient, investmentCatalogManagerClient, p2pInvestmentClient, portfolioManagerClient, usstocksOrderManagerClient, accountBalanceClinet, ffClient)
	transactionsDataCollectorHelper := helper.NewTransactionsDataCollectorHelper(orderCxClient, orderClient, actorClient, savingsClient, dataCollectorHelper, payCxClient, payClient, pClient, accountPiRelationClient, upiOnboardingClient)
	service := profile.NewService(savingsClient, authEngine, userClient, actorClient, s3Client, ticketServiceClient, vendorMappingClient, customerIdentifier, cxConf, dataCollectorHelper, commsClient, externalAccountsClient, tieringClient, bcClient, onbClient, operationalStatusClient, transactionsDataCollectorHelper, segmentClient)
	return service
}

func InitializeCustomerIdentifier(userClient user.UsersClient, actorClient actor.ActorClient, chatClient chat.ChatsClient, vmClient vendormapping.VendorMappingServiceClient) *helper2.CustomerIdentifier {
	customerIdentifier := helper2.NewCustomerIdentifier(userClient, actorClient, chatClient, vmClient)
	return customerIdentifier
}

func IntializeCustomerCardService(config2 *config.Config, genConfig *genconf.Config, dbConn types.SherlockPGDB, userClient user.UsersClient, cpClient provisioning.CardProvisioningClient, ccClient control.CardControlClient, tieringClient tiering.TieringClient, payClient pay.PayClient) *card.Service {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(dbConn)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(dbConn)
	authFactorRetryLimit := getAuthFactoryRetryLimit(config2)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(dbConn)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConfig, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	service := card.NewService(cpClient, ccClient, authEngine, tieringClient, payClient)
	return service
}

func InitializeAccountService(db types.SherlockPGDB, extAcctClient extacct.ExternalAccountsClient, commsClient comms.CommsClient, cxConfig *config.Config, genConf *genconf.Config, vgDepositClient deposit2.DepositClient, bcClient bankcust.BankCustomerServiceClient, watsonClient watson.WatsonClient, ticketClient ticket.TicketClient, actorClient actor.ActorClient, piClient paymentinstrument.PiClient, usersClient user.UsersClient, pClient payment.PaymentClient, orderClient order.OrderServiceClient, savingsClient savings.SavingsClient, depositClient deposit.DepositClient, investmentCatalogManagerClient catalog.CatalogManagerClient, p2pInvestmentClient p2pinvestment.P2PInvestmentClient, portfolioManagerClient portfolio.PortfolioManagerClient, usstocksOrderManagerClient order2.OrderManagerClient, accountBalanceClinet balance.BalanceClient, authClient auth.AuthClient, ffClient firefly.FireflyClient, saClosureClient sa_closure.SavingsAccountClosureClient) *account.Service {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(db)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(db)
	authFactorRetryLimit := getAuthFactoryRetryLimit(cxConfig)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(db)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConf, authFactorRetryLimit, usersClient, customerAuthenticationV2DAO)
	dataCollectorHelper := helper.NewDataCollectorHelper(actorClient, piClient, usersClient, pClient, orderClient, savingsClient, depositClient, investmentCatalogManagerClient, p2pInvestmentClient, portfolioManagerClient, usstocksOrderManagerClient, accountBalanceClinet, ffClient)
	service := account.NewService(depositClient, authEngine, pClient, piClient, dataCollectorHelper, extAcctClient, actorClient, usersClient, savingsClient, cxConfig, commsClient, authClient, vgDepositClient, bcClient, watsonClient, ticketClient, saClosureClient)
	return service
}

func InitializeWatsonClientService() *mock_client.MockWatsonClientService {
	mockWatsonClientService := mock_client.NewMockWatsonClientService()
	return mockWatsonClientService
}

func InitializeWatsonService(db types.SherlockPGDB, incidentReportPub types2.WatsonIncidentReportingPublisher, incidentResolvePub types2.WatsonIncidentResolutionPublisher, simulatorWatsonClientClient watson_client.WatsonClientClient, mockCxWatsonClient mock_client2.MockWatsonClientServiceClient, watsonSavingsClient watson2.WatsonClient, watsonOnboardingClient watson3.WatsonClient, payIncidentManagerClient payincidentmanager.PayIncidentManagerClient, investmentWatsonClient watson4.WatsonClient, watsonDepositsClient watson5.WatsonClient, p2pWatsonClient incidentmanager.IncidentManagerClient, watsonClient watson.WatsonClient, conf *config.Config, ticketEventForWatsonPub types2.WatsonTicketEventPublisher, errorActivityWatsonClient watson_info_provider.WatsonInfoProviderClient, manualTicketStageWiseCommsClient manual_ticket_stage_wise_comms.ManualTicketStageWiseCommsClient) *watson6.Service {
	reportEvent := ingest_event.NewReportEvent(incidentReportPub)
	resolveEvent := ingest_event.NewResolveEvent(incidentResolvePub)
	ingestEventFactory := ingest_event.NewIngestEventFactory(reportEvent, resolveEvent)
	incidentDao := dao2.NewIncidentDao(db)
	requestValidatorV1 := helper3.NewValidator()
	issueCategoryDao := dao3.NewIssueCategoryDao(db)
	issueCategoryManagerImpl := manager.NewIssueCategoryManagerImpl(issueCategoryDao)
	requestValidatorV2 := helper3.NewValidatorV2(requestValidatorV1, issueCategoryManagerImpl)
	requestValidatorFactoryImpl := helper3.NewHelperFactoryImpl(requestValidatorV1, requestValidatorV2)
	iWatsonIncidentInfoCollectorFactory := WatsonIncidentInfoCollectorFactoryProvider(simulatorWatsonClientClient, mockCxWatsonClient, watsonSavingsClient, watsonOnboardingClient, payIncidentManagerClient, investmentWatsonClient, watsonDepositsClient, p2pWatsonClient, errorActivityWatsonClient, manualTicketStageWiseCommsClient)
	watsonHelper := watson7.NewWatsonHelper(iWatsonIncidentInfoCollectorFactory, incidentDao, watsonClient)
	watsonHelperV2 := watson7.NewWatsonHelperV2(watsonHelper)
	helperFactoryImpl := watson7.NewHelperFactoryImpl(watsonHelper, watsonHelperV2)
	watsonConfig := getWatsonConfig(conf)
	incidentTicketDetailDao := dao2.NewIncidentTicketDetailDao(db)
	service := watson6.NewService(ingestEventFactory, incidentDao, requestValidatorFactoryImpl, helperFactoryImpl, watsonConfig, ticketEventForWatsonPub, incidentTicketDetailDao)
	return service
}

func InitializeKycService(kycClient kyc.KycClient, vkycClient vkyc.VKYCClient, config2 *config.Config, genConfig *genconf.Config, dbConn types.SherlockPGDB, livenessClient liveness.LivenessClient, actorClient actor.ActorClient, usersClient user.UsersClient, obClient onboarding.OnboardingClient, bcClient bankcust.BankCustomerServiceClient, panClient pan.PanClient, compClient compliance.ComplianceClient) *kyc2.Service {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(dbConn)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(dbConn)
	authFactorRetryLimit := getAuthFactoryRetryLimit(config2)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(dbConn)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConfig, authFactorRetryLimit, usersClient, customerAuthenticationV2DAO)
	kycConfig := getKYCConfig(config2)
	service := kyc2.NewService(kycClient, authEngine, vkycClient, kycConfig, livenessClient, actorClient, usersClient, obClient, bcClient, panClient, compClient)
	return service
}

func InitializeTransactionService(conf *config.Config, orderTxnClient cx2.CXClient, genConfig *genconf.Config, dbConn types.SherlockPGDB, txnCategorizerClient categorizer.TxnCategorizerClient, payCxClient cx.CXClient, payClient pay.PayClient, accountPiClient account_pi.AccountPIRelationClient, upiOnboardingClient onboarding2.UpiOnboardingClient, actorClient actor.ActorClient, piClient paymentinstrument.PiClient, usersClient user.UsersClient, pClient payment.PaymentClient, orderClient order.OrderServiceClient, savingsClient savings.SavingsClient, depositClient deposit.DepositClient, investmentCatalogManagerClient catalog.CatalogManagerClient, p2pInvestmentClient p2pinvestment.P2PInvestmentClient, portfolioManagerClient portfolio.PortfolioManagerClient, usstocksOrderManagerClient order2.OrderManagerClient, accountBalanceClinet balance.BalanceClient, client recurringpayment.RecurringPaymentServiceClient, ffClient firefly.FireflyClient, upiMandateClient mandate.MandateServiceClient, enachMandateClient enach.EnachServiceClient) *transaction.Service {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(dbConn)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(dbConn)
	authFactorRetryLimit := getAuthFactoryRetryLimit(conf)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(dbConn)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConfig, authFactorRetryLimit, usersClient, customerAuthenticationV2DAO)
	configTransaction := getTransactionConf(conf)
	dataCollectorHelper := helper.NewDataCollectorHelper(actorClient, piClient, usersClient, pClient, orderClient, savingsClient, depositClient, investmentCatalogManagerClient, p2pInvestmentClient, portfolioManagerClient, usstocksOrderManagerClient, accountBalanceClinet, ffClient)
	orderConfig := getOrderConfig(conf)
	transactionsDataCollectorHelper := helper.NewTransactionsDataCollectorHelper(orderTxnClient, orderClient, actorClient, savingsClient, dataCollectorHelper, payCxClient, payClient, pClient, accountPiClient, upiOnboardingClient)
	txnCategoryDataCollectorHelperImpl := helper.NewTxnCategoriesHelperImpl(txnCategorizerClient)
	service := transaction.NewService(orderTxnClient, authEngine, configTransaction, actorClient, dataCollectorHelper, orderConfig, transactionsDataCollectorHelper, txnCategoryDataCollectorHelperImpl, client, upiMandateClient, enachMandateClient)
	return service
}

func InitializeUserRequestsService(conf *config.Config, genConfig *genconf.Config, dbConn types.SherlockPGDB, userClient user.UsersClient, savingsClient savings.SavingsClient, statementClient statement.AccountStatementClient, eventBroker events.Broker, commsClient comms.CommsClient) *userReqPb.Service {
	sherlockUserRequestsConfig := getSherlockUserRequestsConfig(conf)
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(dbConn)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(dbConn)
	authFactorRetryLimit := getAuthFactoryRetryLimit(conf)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(dbConn)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConfig, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	savingsAccountCollector := account_details_collector.NewSavingsAccountCollector(savingsClient)
	accountDetailsFactoryImpl := account_details_collector.NewAccountDetailsFactory(savingsAccountCollector)
	service := userReqPb.NewUserRequestsService(sherlockUserRequestsConfig, authEngine, statementClient, eventBroker, accountDetailsFactoryImpl, genConfig, commsClient)
	return service
}

func InitializeTicketValidation(freshdeskClient freshdesk.FreshdeskClient, redisClient types2.CxRedisStore, conf *config.Config) *validation.TicketValidation {
	client := NewRedisClient(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	ticketValidation := validation.NewTicketValidation(freshdeskClient, redisCacheStorage, conf)
	return ticketValidation
}

func InitializeCustomerAuth(commsClient comms.CommsClient, db types.SherlockPGDB, confg *genconf.Config, conf *config.Config, userClient user.UsersClient, chatsClient chat.ChatsClient, ticketPub types2.FreshdeskTicketPublisher, orderClient order.OrderServiceClient, bcClient bankcust.BankCustomerServiceClient, onboardingClient onboarding.OnboardingClient, redisClient types2.CxRedisStore) *customer_auth.CustomerAuth {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(db)
	callbackResponseDAO := dao.NewCallbackResponseDAO(db)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(db)
	authFactorRetryLimit := getAuthFactoryRetryLimit(conf)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(db)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, confg, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	authFactorRetryLogsDAO := dao.NewAuthFactorRetryLogsDAO(db)
	dobVerifier := verifier.NewDOBVerifier(userClient, authFactorStatesDAO, authFactorRetryLogsDAO, authEngine, authFactorRetryLimit)
	pinCodeVerifier := verifier.NewPinCodeVerifier(userClient, authFactorStatesDAO, authFactorRetryLogsDAO, authEngine, authFactorRetryLimit)
	panVerifier := verifier.NewPANVerifier(userClient, authFactorStatesDAO, authFactorRetryLogsDAO, authEngine, authFactorRetryLimit)
	customerAuth := getCustomerAuthConf(conf)
	emailVerifier := verifier.NewEmailVerifier(userClient, authFactorStatesDAO, authEngine, authFactorRetryLogsDAO, callbackResponseDAO, commsClient, authFactorRetryLimit, customerAuth, confg)
	mobilePromptVerifier := verifier.NewMobilePromptVerifier(userClient, authFactorStatesDAO, confg, authFactorRetryLogsDAO, authEngine, callbackResponseDAO, commsClient, onboardingClient, authFactorRetryLimit)
	configTransaction := getTransactionConf(conf)
	txnVerifier := verifier.NewTxnVerifier(userClient, authFactorStatesDAO, authFactorRetryLogsDAO, authEngine, authFactorRetryLimit, configTransaction, orderClient)
	fathersNameVerifier := verifier.NewFathersNameVerifier(userClient, authFactorStatesDAO, authFactorRetryLogsDAO, authEngine, authFactorRetryLimit)
	mothersNameVerifier := verifier.NewMothersNameVerifier(userClient, authFactorStatesDAO, authFactorRetryLogsDAO, authEngine, authFactorRetryLimit)
	verificationFactory := customer_auth.NewVerificationFactory(dobVerifier, pinCodeVerifier, panVerifier, emailVerifier, mobilePromptVerifier, txnVerifier, fathersNameVerifier, mothersNameVerifier)
	freshdeskUpdateEventDAO := dao4.NewFreshdeskUpdateEventDAO(db)
	client := NewRedisClient(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	customer_authCustomerAuth := customer_auth.NewCustomerAuth(customerAuthenticationDAO, callbackResponseDAO, authFactorStatesDAO, authEngine, verificationFactory, chatsClient, freshdeskUpdateEventDAO, ticketPub, authFactorRetryLogsDAO, authFactorRetryLimit, customerAuth, customerAuthenticationV2DAO, bcClient, userClient, redisCacheStorage, confg)
	return customer_authCustomerAuth
}

func InitializeAuthEngine(db types.SherlockPGDB, genConf *genconf.Config, authFactorRetryLimit *config.AuthFactorRetryLimit, client user.UsersClient) *auth_engine.AuthEngine {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(db)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(db)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(db)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConf, authFactorRetryLimit, client, customerAuthenticationV2DAO)
	return authEngine
}

func InitializeLandingPageService(userClient user.UsersClient, actorClient actor.ActorClient, ozonetelClient ozonetel.OzonetelClient, chatClient chat.ChatsClient, db types.SherlockPGDB, authClient auth.AuthClient, vmClient vendormapping.VendorMappingServiceClient, conf *config.Config, genConf *genconf.Config, customerProfileClient profile2.CustomerProfileClient) *landing_page.Service {
	customerIdentifier := helper2.NewCustomerIdentifier(userClient, actorClient, chatClient, vmClient)
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(db)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(db)
	authFactorRetryLimit := getAuthFactoryRetryLimit(conf)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(db)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConf, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	sherlockUserInfoDao := dao5.NewSherlockUserInfoDao(db)
	sherlockUserRoleDao := dao5.NewSherlockUserRoleDao(db)
	gormDB := GormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	sherlockUser := sherlock_user_wrapper.NewSherlockUserDao(sherlockUserInfoDao, sherlockUserRoleDao, gormTxnExecutor)
	callDetailsDao := dao6.NewCallDetailsDao(db)
	supportTicketDao := dao7.NewSupportTicketDao(db)
	userQueryLogDaoImpl := dao8.NewUserQueryLogDao(db)
	issueCategoryDao := dao3.NewIssueCategoryDao(db)
	userProfileGenerator := sherlock_activity_generator.NewUserProfileGenerator(customerProfileClient)
	userDetailsGridGeneratorFactoryImpl := sherlock_activity_generator.NewUserDetailsGridGeneratorFactoryImpl(userProfileGenerator, conf)
	service := landing_page.NewService(customerIdentifier, ozonetelClient, authEngine, authClient, sherlockUser, callDetailsDao, supportTicketDao, conf, userQueryLogDaoImpl, genConf, issueCategoryDao, userDetailsGridGeneratorFactoryImpl)
	return service
}

func InitializeChatInitInformationService(db types.SherlockPGDB, ticketPub types2.FreshdeskTicketPublisher, gconf *genconf.Config, conf *config.Config, vmClient vendormapping.VendorMappingServiceClient, redisClient types2.CxRedisStore, onbClient onboarding.OnboardingClient, actorClient actor.ActorClient, userClient user.UsersClient, salaryProgramClient salaryprogram.SalaryProgramClient, authClient auth.AuthClient, userGroupClient group.GroupClient, freshChatClient freshchat.FreshchatClient, fdClient freshdesk.FreshdeskClient, eventBroker events.Broker, savingsClient savings.SavingsClient, operationalStatusClient operstatus.OperationalStatusServiceClient, balanceClient balance.BalanceClient, chatClient chat.ChatsClient, vgNuggetClient nugget.NuggetChatbotServiceClient) *chat2.Service {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	freshdeskUpdateEventDAO := dao4.NewFreshdeskUpdateEventDAO(db)
	featureReleaseConfig := getDynFeatureReleaseConfig(gconf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	userPriorityPropertiesDao := dao9.NewUserPriorityPropertiesDao(redisClient)
	priority_routing_helperHelper := priority_routing_helper.NewPriorityDataHelper(userPriorityPropertiesDao, userClient, actorClient)
	priorityRoutingConfig := getPriorityRoutingConfig(conf)
	highPriorityRuleProcessor := processor.NewHighPriorityRuleProcessor(priority_routing_helperHelper, priorityRoutingConfig)
	lowPriorityRuleProcessor := processor.NewLowPriorityRuleProcessor(priority_routing_helperHelper, priorityRoutingConfig)
	currentlyOnbRuleProcessor := processor.NewCurrentlyOnbRuleProcessor(onbClient, priority_routing_helperHelper)
	salaryProgramUsersRuleProcessor := processor.NewSalaryProgramUsersRuleProcessor(salaryProgramClient)
	factory := processor.NewRuleFactory(highPriorityRuleProcessor, lowPriorityRuleProcessor, currentlyOnbRuleProcessor, salaryProgramUsersRuleProcessor)
	routingEngine := routing_engine.NewRoutingEngine(factory)
	freshchatUserMappingDao := dao4.NewFreshchatUserMappingDao(db)
	freshchatUserMappingHelper := helper4.NewFreshchatUserMappingHelper(gconf, routingEngine, freshchatUserMappingDao, userClient, freshChatClient, vmClient)
	useNewOperationalStatusAPIFlag := IsNewOperationalStatusAPIEnabled(conf)
	fetcher := accountstatus.ProvideFetcherImplementation(savingsClient, operationalStatusClient, balanceClient, useNewOperationalStatusAPIFlag)
	customerIdentifier := helper2.NewCustomerIdentifier(userClient, actorClient, chatClient, vmClient)
	service := chat2.NewService(domainIdGenerator, ticketPub, freshdeskUpdateEventDAO, vmClient, gconf, authClient, actorClient, userClient, evaluator, freshchatUserMappingHelper, onbClient, conf, fetcher, eventBroker, fdClient, customerIdentifier, vgNuggetClient)
	return service
}

func InitializeAuditLogService(db types.SherlockPGDB, conf *config.Config) *audit_log.Service {
	auditLog := getAuditLog(conf)
	auditLogDao := dao10.NewAuditLogDao(db, auditLog)
	service := audit_log.NewAuditLogService(auditLogDao, auditLog)
	return service
}

// config: {"s3Client": "S3EventConsumerConfig().BucketName()"}
func InitializeConsumerService(db types.SherlockPGDB, fdClient freshdesk.FreshdeskClient, contactPub types2.FreshdeskContactPublisher, userClient user.UsersClient, actorClient actor.ActorClient, chatClient chat.ChatsClient, vmClient vendormapping.VendorMappingServiceClient, ticketClient ticket.TicketClient, conf *config.Config, genConf *genconf.Config, addNotePub types2.DisputeAddNoteTicketPublisher, bcClient bankcust.BankCustomerServiceClient, onboardingClient onboarding.OnboardingClient, updateTicketPub types2.UpdateTicketPublisher, disputeExtPub types2.DisputeExternalPublisher, pClient payment.PaymentClient, piClient paymentinstrument.PiClient, orderClient order.OrderServiceClient, userGroupClient group.GroupClient, createDisputeTicketPub types2.DisputeCreateTicketPublisher, savingsClient savings.SavingsClient, commsClient comms.CommsClient, vgDisputeClient dispute.DisputeClient, s3Client s3.S3Client, createTicketEventPublisher types2.CreateTicketEventPublisher, federalVgClient federal.FederalEscalationServiceClient) *consumer.Service {
	freshdeskUpdateEventDAO := dao4.NewFreshdeskUpdateEventDAO(db)
	deleteContactService := contact_event.NewDeleteContactService(freshdeskUpdateEventDAO, fdClient)
	updateCfService := ticket_event.NewUpdateCfService(fdClient, freshdeskUpdateEventDAO)
	customerIdentifier := helper2.NewCustomerIdentifier(userClient, actorClient, chatClient, vmClient)
	freshdeskAutomationHelper := helper5.NewFreshdeskAutomationHelper(genConf, fdClient)
	updateRequesterService := ticket_event.NewUpdateRequesterService(freshdeskUpdateEventDAO, fdClient, customerIdentifier, contactPub, vmClient, bcClient, freshdeskAutomationHelper, onboardingClient, genConf, userClient)
	createTicketService := ticket_event.NewCreateTicketService(fdClient, freshdeskUpdateEventDAO)
	updateTicketService := ticket_event.NewUpdateTicketService(fdClient, freshdeskUpdateEventDAO)
	freshChatConfig := getFreshChatConfig(conf)
	updateRequesterForSourceService := ticket_event.NewUpdateRequesterForSourceService(freshdeskUpdateEventDAO, fdClient, customerIdentifier, freshChatConfig, bcClient, freshdeskAutomationHelper, onboardingClient, genConf, userClient)
	bulkTicketJobDao := dao7.NewBulkTicketJobDao(db)
	ticketFailureLogDao := dao7.NewTicketFailureLogDao(db)
	ticketRawBulkUpdateProcessor := update_ticket_event.NewTicketRawBulkUpdateProcessor(fdClient, bulkTicketJobDao, ticketFailureLogDao)
	callConfig := getCallConfig(conf)
	sherlockUserInfoDao := dao5.NewSherlockUserInfoDao(db)
	sherlockUserRoleDao := dao5.NewSherlockUserRoleDao(db)
	gormDB := GormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	sherlockUser := sherlock_user_wrapper.NewSherlockUserDao(sherlockUserInfoDao, sherlockUserRoleDao, gormTxnExecutor)
	callDetailsTicketUpdateProcessor := update_ticket_event.NewCallDetailsTicketUpdateProcessor(fdClient, callConfig, sherlockUser)
	disputeFeedbackTicketUpdateProcessor := processor2.NewDisputeFeedbackTicketUpdateProcessor(ticketClient, genConf)
	issueResolutionFeedbackTicketUpdateFactory := issue_resolution_feedback.NewIssueResolutionFeedbackTicketUpdateFactory(disputeFeedbackTicketUpdateProcessor)
	issueResolutionFeedbackTicketUpdateProcessor := update_ticket_event.NewIssueResolutionFeedbackTicketUpdateProcessor(issueResolutionFeedbackTicketUpdateFactory, fdClient)
	expectedResolutionDateUpdateProcessor := update_ticket_event.NewExpectedResolutionDateUpdateProcessor(fdClient)
	supportTicketDao := dao7.NewSupportTicketDao(db)
	sherlockTicketInfoUpdateProcessor := update_ticket_event.NewSherlockTicketInfoUpdateProcessor(fdClient, supportTicketDao, conf)
	sprinklrCaseDetailsDao := dao11.NewSprinklrCaseDetailsDao(db)
	createTicketFromSprinklrEventProcessor := create_ticket_event.NewCreateTicketFromSprinklrEventProcessor(sprinklrCaseDetailsDao, fdClient, customerIdentifier, conf, gormTxnExecutor)
	updateTicketFromSprinklrEventProcessor := update_ticket_event.NewUpdateTicketFromSprinklrEventProcessor(sprinklrCaseDetailsDao, fdClient, conf)
	disputeDetailsUpdateProcessor := update_ticket_event.NewDisputeDetailsUpdateProcessor(fdClient)
	disputeIdToTicketIdMappingDao := dao12.NewDisputeIdToTicketIdMappingDao(db)
	disputeTicketLogDao := dao12.NewDisputeTicketLogDao(db)
	disputeDao := dao12.NewDisputeDao(db)
	genconfDispute := getDynDispute(genConf)
	disputeConfigDao := dao12.NewDisputeConfigDao(db)
	federalDmpDisputeDetailDao := dao12.NewFederalDmpDisputeDetailDao(db)
	disputeHelper := helper6.NewDisputeHelper(disputeExtPub, disputeDao, pClient, piClient, orderClient, actorClient, userClient, userGroupClient, genconfDispute, disputeConfigDao, createDisputeTicketPub, customerIdentifier, savingsClient, federalDmpDisputeDetailDao, gormTxnExecutor, commsClient, genConf, vgDisputeClient)
	dmpCorrespondenceCreateTicketEventProcessor := create_ticket_event.NewDMPCorrespondenceCreateTicketEventProcessor(disputeIdToTicketIdMappingDao, disputeTicketLogDao, addNotePub, updateTicketPub, disputeHelper, ticketClient)
	cxTicketUpdateEventProcessor := update_ticket_event.NewCxTicketUpdateEventProcessor(fdClient)
	addPrivateNoteEventProcessor := create_ticket_event.NewAddPrivateNoteEventProcessor(fdClient)
	ticketRawUpdateEventProcessor := update_ticket_event.NewTicketRawUpdateEventProcessor(fdClient)
	cxTicketEventProcessor := create_ticket_event.NewCxTicketEventProcessor(ticketClient, createTicketEventPublisher)
	escalationDao := dao13.NewEscalationDao(db)
	escalationUpdateDao := dao13.NewEscalationUpdateDao(db)
	escalationEventProcessor := escalation_event.NewEscalationEventProcessor(escalationDao, escalationUpdateDao, fdClient)
	escalationAttachmentDao := dao13.NewEscalationAttachmentDao(db)
	escalationCreationEventProcessor := escalation_event.NewEscalationCreationEventProcessor(escalationDao, escalationUpdateDao, escalationAttachmentDao, fdClient, federalVgClient, genConf)
	eventFactory := consumer.NewEventFactory(deleteContactService, updateCfService, updateRequesterService, createTicketService, updateTicketService, updateRequesterForSourceService, ticketRawBulkUpdateProcessor, callDetailsTicketUpdateProcessor, issueResolutionFeedbackTicketUpdateProcessor, expectedResolutionDateUpdateProcessor, sherlockTicketInfoUpdateProcessor, createTicketFromSprinklrEventProcessor, updateTicketFromSprinklrEventProcessor, disputeDetailsUpdateProcessor, dmpCorrespondenceCreateTicketEventProcessor, cxTicketUpdateEventProcessor, addPrivateNoteEventProcessor, ticketRawUpdateEventProcessor, cxTicketEventProcessor, escalationEventProcessor, escalationCreationEventProcessor)
	callSummarizationOutputFileProcessor := s3_event.NewCallSummarizationOutputFileProcessor(s3Client, ticketClient, genConf)
	s3EventProcessorFactoryImpl := s3_event.NewS3EventProcessorFactory(callSummarizationOutputFileProcessor, genConf)
	service := consumer.NewService(eventFactory, eventFactory, eventFactory, s3EventProcessorFactoryImpl, genConf)
	return service
}

func InitializeOnboardingService(kycClient kyc.KycClient, dbconn types.SherlockPGDB, livenessClient liveness.LivenessClient, obClient onboarding.OnboardingClient, conf *config.Config, userGroupClient group.GroupClient, authClient auth.AuthClient, actorClient actor.ActorClient, usersClient user.UsersClient, gconf *genconf.Config, productClient product.ProductClient, salaryClient salaryprogram.SalaryProgramClient, employmentClient employment.EmploymentClient) *onboarding3.Service {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(dbconn)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(dbconn)
	authFactorRetryLimit := getAuthFactoryRetryLimit(conf)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(dbconn)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, gconf, authFactorRetryLimit, usersClient, customerAuthenticationV2DAO)
	v := getOnboardingStageDetailsMapping(conf)
	service := onboarding3.NewService(kycClient, authEngine, livenessClient, obClient, v, authClient, userGroupClient, usersClient, actorClient, gconf, productClient, salaryClient, employmentClient)
	return service
}

func InitializeWealthOnboardingService(config2 *config.Config, genConfig *genconf.Config, dbConn types.SherlockPGDB, userClient user.UsersClient, wonbClient wealthonboarding.WealthOnboardingClient, woCxClient cx3.WealthCxServiceClient) *wealth_onboarding.Service {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(dbConn)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(dbConn)
	authFactorRetryLimit := getAuthFactoryRetryLimit(config2)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(dbConn)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConfig, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	service := wealth_onboarding.NewService(wonbClient, woCxClient, authEngine)
	return service
}

func InitializeInvestmentService(config2 *config.Config, genConfig *genconf.Config, dbConn types.SherlockPGDB, userClient user.UsersClient, catalogClient catalog.CatalogManagerClient, rmsClient manager2.RuleManagerClient, orderManagerClient order3.OrderManagerClient, paymentHandlerClient payment_handler.PaymentHandlerClient, wobClient wealthonboarding.WealthOnboardingClient) *mutualfund.Service {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(dbConn)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(dbConn)
	authFactorRetryLimit := getAuthFactoryRetryLimit(config2)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(dbConn)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConfig, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	service := mutualfund.NewInvestmentService(catalogClient, rmsClient, orderManagerClient, authEngine, paymentHandlerClient, wobClient)
	return service
}

func InitializeUsStockService(config2 *config.Config, genConfig *genconf.Config, dbConn types.SherlockPGDB, userClient user.UsersClient, celestialClient celestial.CelestialClient, accountManagerClient account2.AccountManagerClient, OrderManagerClient order2.OrderManagerClient, PortfolioManagerClient portfolio.PortfolioManagerClient) *usstocks.Service {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(dbConn)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(dbConn)
	authFactorRetryLimit := getAuthFactoryRetryLimit(config2)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(dbConn)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConfig, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	usStocksOpsConfig := getUsStocksOpsConfig(config2)
	service := usstocks.NewUsStocksInvestmentService(authEngine, celestialClient, accountManagerClient, OrderManagerClient, PortfolioManagerClient, usStocksOpsConfig)
	return service
}

func InitializeAuthCallbackService(db types.SherlockPGDB, conf *config.Config, genConf *genconf.Config, freshdeskClient freshdesk.FreshdeskClient, redisClient types2.CxRedisStore, userClient user.UsersClient) *customer_auth.AuthCallback {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(db)
	callbackResponseDAO := dao.NewCallbackResponseDAO(db)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(db)
	authFactorRetryLimit := getAuthFactoryRetryLimit(conf)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(db)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConf, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	authFactorRetryLogsDAO := dao.NewAuthFactorRetryLogsDAO(db)
	client := NewRedisClient(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	ticketValidation := validation.NewTicketValidation(freshdeskClient, redisCacheStorage, conf)
	httpClient := getHttpClientForSherlock(conf)
	sherlock := getSherlockConf(conf)
	secrets := getSecrets(conf)
	customerAuth := getCustomerAuthConf(conf)
	authCallback := customer_auth.NewAuthCallback(customerAuthenticationDAO, callbackResponseDAO, authFactorStatesDAO, authEngine, authFactorRetryLogsDAO, authFactorRetryLimit, ticketValidation, httpClient, sherlock, secrets, customerAuth, userClient, redisCacheStorage)
	return authCallback
}

func InitializePiService(piClient paymentinstrument.PiClient, acPiClient account_pi.AccountPIRelationClient, upiClient upi.UPIClient, cxUpiClient cx4.UpiCXClient, db types.SherlockPGDB, conf *config.Config, genConf *genconf.Config, userClient user.UsersClient) *payment_instruments.Service {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(db)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(db)
	authFactorRetryLimit := getAuthFactoryRetryLimit(conf)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(db)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConf, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	service := payment_instruments.NewService(piClient, acPiClient, authEngine, upiClient, cxUpiClient)
	return service
}

func InitializeRewardsService(db types.SherlockPGDB, conf *config.Config, genConf *genconf.Config, rewardOffersClient rewardoffers.RewardOffersClient, offersClient casper.OfferListingServiceClient, rewardsClient rewards.RewardsGeneratorClient, offerRedemptionClient redemption.OfferRedemptionServiceClient, exchangerOffersClient exchanger.ExchangerOfferServiceClient, actorServiceClient actor.ActorClient, usersClient user.UsersClient, rewardsProjectionClient projector.ProjectorServiceClient, externalVendorRedemptionClient external_vendor_redemption.ExternalVendorRedemptionServiceClient, accrualClient accrual.AccrualClient, fireflyClient firefly.FireflyClient) *rewards2.Service {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(db)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(db)
	authFactorRetryLimit := getAuthFactoryRetryLimit(conf)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(db)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConf, authFactorRetryLimit, usersClient, customerAuthenticationV2DAO)
	service := rewards2.NewService(authEngine, rewardOffersClient, offersClient, offerRedemptionClient, rewardsClient, exchangerOffersClient, usersClient, rewardsProjectionClient, externalVendorRedemptionClient, accrualClient, fireflyClient)
	return service
}

func InitializeDisputeHelper(db types.SherlockPGDB, disputeExtPub types2.DisputeExternalPublisher, pClient payment.PaymentClient, piClient paymentinstrument.PiClient, orderClient order.OrderServiceClient, actorClient actor.ActorClient, userClient user.UsersClient, userGroupClient group.GroupClient, gconf *genconf.Config, createDisputeTicketPub types2.DisputeCreateTicketPublisher, chatClient chat.ChatsClient, vmClient vendormapping.VendorMappingServiceClient, savingsClient savings.SavingsClient, commsClient comms.CommsClient, vgDisputeClient dispute.DisputeClient) *helper6.DisputeHelper {
	disputeDao := dao12.NewDisputeDao(db)
	genconfDispute := getDynDispute(gconf)
	disputeConfigDao := dao12.NewDisputeConfigDao(db)
	customerIdentifier := helper2.NewCustomerIdentifier(userClient, actorClient, chatClient, vmClient)
	federalDmpDisputeDetailDao := dao12.NewFederalDmpDisputeDetailDao(db)
	gormDB := GormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	disputeHelper := helper6.NewDisputeHelper(disputeExtPub, disputeDao, pClient, piClient, orderClient, actorClient, userClient, userGroupClient, genconfDispute, disputeConfigDao, createDisputeTicketPub, customerIdentifier, savingsClient, federalDmpDisputeDetailDao, gormTxnExecutor, commsClient, gconf, vgDisputeClient)
	return disputeHelper
}

// config: {"cxS3Client": "CxS3Config().BucketName"}
func InitializeDisputeService(db types.SherlockPGDB, disPub types2.DisputePublisher, aClient actor.ActorClient, conf *config.Config, genConfig *genconf.Config, broker events.Broker, upiClient upi.UPIClient, usersClient user.UsersClient, userGroupClient group.GroupClient, updateTicketPub types2.DisputeUpdateTicketPublisher, disputeCreateTicketPub types2.DisputeCreateTicketPublisher, createTicketPub types2.CreateTicketPublisher, chatClient chat.ChatsClient, vmClient vendormapping.VendorMappingServiceClient, vgDisputeClient dispute.DisputeClient, cxS3Client types2.CxS3Client, freshdeskUpdateTicketPub types2.UpdateTicketPublisher, bcClient bankcust.BankCustomerServiceClient, disputeExtPub types2.DisputeExternalPublisher, pClient payment.PaymentClient, piClient paymentinstrument.PiClient, orderClient order.OrderServiceClient, savingsClient savings.SavingsClient, commsClient comms.CommsClient, redisClient types2.CxRedisStore) *dispute2.Service {
	configDispute := getDispute(conf)
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(db)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(db)
	authFactorRetryLimit := getAuthFactoryRetryLimit(conf)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(db)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConfig, authFactorRetryLimit, usersClient, customerAuthenticationV2DAO)
	disputeDao := dao12.NewDisputeDao(db)
	disputeConfigDao := dao12.NewDisputeConfigDao(db)
	genconfDispute := getDynDispute(genConfig)
	customerIdentifier := helper2.NewCustomerIdentifier(usersClient, aClient, chatClient, vmClient)
	federalDmpDisputeDetailDao := dao12.NewFederalDmpDisputeDetailDao(db)
	gormDB := GormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	disputeHelper := helper6.NewDisputeHelper(disputeExtPub, disputeDao, pClient, piClient, orderClient, aClient, usersClient, userGroupClient, genconfDispute, disputeConfigDao, disputeCreateTicketPub, customerIdentifier, savingsClient, federalDmpDisputeDetailDao, gormTxnExecutor, commsClient, genConfig, vgDisputeClient)
	udirHelper := helper7.NewUDIRHelper(upiClient, disputeDao, disputeHelper, genconfDispute)
	featureReleaseConfig := getFeatureReleaseConfig(conf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(aClient, usersClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewStaticConfEvaluator(featureReleaseConfig, constraintFactoryImpl)
	escalateDisputeToNPCI := processor3.NewEscalateDisputeToNPCI(udirHelper)
	escalateDisputeToFreshDesk := processor3.NewEscalateDisputeToFreshDesk(disputeCreateTicketPub, customerIdentifier, udirHelper, disputeDao, bcClient, updateTicketPub, disputeHelper, genConfig)
	client := NewRedisClient(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	disputeIdempotencyCacheImpl := cache2.NewDisputeIdempotencyCacheImpl(redisCacheStorage, genconfDispute)
	federalBankDisputeProcessor := processor3.NewFederalBankDisputeProcessor(disputeDao, vgDisputeClient, federalDmpDisputeDetailDao, gormTxnExecutor, disputeHelper, disputeIdempotencyCacheImpl)
	disputeEscalationFactory := dispute2.NewDisputeEscalationFactory(escalateDisputeToNPCI, escalateDisputeToFreshDesk, federalBankDisputeProcessor)
	disputeCorrespondenceDetailDao := dao12.NewDisputeCorrespondenceDetailDao(db)
	s3Client := types2.CxS3ClientProvider(cxS3Client)
	disputeDocumentDetailDao := dao12.NewDisputeDocumentDetailDao(db)
	questionnaireHelper := questionnaire_helper.NewQuestionnaireHelper(disputeHelper)
	disputeIdToTicketIdMappingDao := dao12.NewDisputeIdToTicketIdMappingDao(db)
	service := dispute2.NewService(configDispute, authEngine, disputeDao, disPub, disputeConfigDao, disputeHelper, conf, broker, udirHelper, evaluator, disputeEscalationFactory, disputeCorrespondenceDetailDao, s3Client, gormTxnExecutor, disputeDocumentDetailDao, vgDisputeClient, federalDmpDisputeDetailDao, genconfDispute, questionnaireHelper, createTicketPub, disputeIdToTicketIdMappingDao, freshdeskUpdateTicketPub)
	return service
}

func InitializeDisputeConsumerService(db types.SherlockPGDB, fClient freshdesk.FreshdeskClient, updateTicketPub types2.DisputeUpdateTicketPublisher, createTicketPub types2.DisputeCreateTicketPublisher, userClient user.UsersClient, addNotePub types2.DisputeAddNoteTicketPublisher, actorClient actor.ActorClient, chatClient chat.ChatsClient, upiClient upi.UPIClient, vmClient vendormapping.VendorMappingServiceClient, genConfig *genconf.Config, vgDisputeClient dispute.DisputeClient, bcClient bankcust.BankCustomerServiceClient, ticketClient ticket.TicketClient, disputeExtPub types2.DisputeExternalPublisher, pClient payment.PaymentClient, piClient paymentinstrument.PiClient, orderClient order.OrderServiceClient, userGroupClient group.GroupClient, savingsClient savings.SavingsClient, commsClient comms.CommsClient, redisClient types2.CxRedisStore) *consumer2.DisputeConsumerSvc {
	disputeDao := dao12.NewDisputeDao(db)
	genconfDispute := getDynDispute(genConfig)
	disputeConfigDao := dao12.NewDisputeConfigDao(db)
	customerIdentifier := helper2.NewCustomerIdentifier(userClient, actorClient, chatClient, vmClient)
	federalDmpDisputeDetailDao := dao12.NewFederalDmpDisputeDetailDao(db)
	gormDB := GormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	disputeHelper := helper6.NewDisputeHelper(disputeExtPub, disputeDao, pClient, piClient, orderClient, actorClient, userClient, userGroupClient, genconfDispute, disputeConfigDao, createTicketPub, customerIdentifier, savingsClient, federalDmpDisputeDetailDao, gormTxnExecutor, commsClient, genConfig, vgDisputeClient)
	udirHelper := helper7.NewUDIRHelper(upiClient, disputeDao, disputeHelper, genconfDispute)
	escalateDisputeToNPCI := processor3.NewEscalateDisputeToNPCI(udirHelper)
	escalateDisputeToFreshDesk := processor3.NewEscalateDisputeToFreshDesk(createTicketPub, customerIdentifier, udirHelper, disputeDao, bcClient, updateTicketPub, disputeHelper, genConfig)
	client := NewRedisClient(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	disputeIdempotencyCacheImpl := cache2.NewDisputeIdempotencyCacheImpl(redisCacheStorage, genconfDispute)
	federalBankDisputeProcessor := processor3.NewFederalBankDisputeProcessor(disputeDao, vgDisputeClient, federalDmpDisputeDetailDao, gormTxnExecutor, disputeHelper, disputeIdempotencyCacheImpl)
	disputeEscalationFactory := dispute2.NewDisputeEscalationFactory(escalateDisputeToNPCI, escalateDisputeToFreshDesk, federalBankDisputeProcessor)
	disputeTicketLogDao := dao12.NewDisputeTicketLogDao(db)
	disputeConsumerSvc := consumer2.NewDisputeConsumerSvc(disputeDao, fClient, addNotePub, disputeEscalationFactory, disputeConfigDao, disputeTicketLogDao, disputeHelper, genconfDispute, udirHelper, ticketClient)
	return disputeConsumerSvc
}

// config: {"cxS3Client": "CxS3Config().BucketName"}
func InitializeAppLogService(actorClient actor.ActorClient, chatClient chat.ChatsClient, vmClient vendormapping.VendorMappingServiceClient, redisClient types2.CxRedisStore, commsClient comms.CommsClient, conf *config.Config, cxS3Client types2.CxS3Client, genConfig *genconf.Config, dbConn types.SherlockPGDB, userClient user.UsersClient) *app_log.Service {
	cxCacheStore := CxCacheStorageProvider(redisClient)
	s3Client := types2.CxS3ClientProvider(cxS3Client)
	appLogDao := dao14.NewAppLogDao(cxCacheStore, genConfig, s3Client)
	actorLogMappingDao := dao14.NewActorLogMappingDao(cxCacheStore, genConfig)
	logIdDao := dao14.NewLogIdDao(cxCacheStore, genConfig)
	appLog := getDynAppLog(genConfig)
	customerIdentifier := helper2.NewCustomerIdentifier(userClient, actorClient, chatClient, vmClient)
	appLogsNotificationContent := getAppLogsNotificationContent(conf)
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(dbConn)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(dbConn)
	authFactorRetryLimit := getAuthFactoryRetryLimit(conf)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(dbConn)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConfig, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	service := app_log.NewAppLogService(appLogDao, actorLogMappingDao, logIdDao, appLog, genConfig, customerIdentifier, commsClient, appLogsNotificationContent, authEngine)
	return service
}

func InitializeCommsService(commsClient comms.CommsClient, conf *config.Config, genConfig *genconf.Config, dbConn types.SherlockPGDB, userClient user.UsersClient) *comms2.Service {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(dbConn)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(dbConn)
	authFactorRetryLimit := getAuthFactoryRetryLimit(conf)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(dbConn)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConfig, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	configComms := getCommsConf(conf)
	service := comms2.NewService(commsClient, authEngine, configComms)
	return service
}

// config: {"s3Client": "Dispute().S3BucketName()"}
func InitializeDisputeJobService(userClient user.UsersClient, actorClient actor.ActorClient, chatClient chat.ChatsClient, vmClient vendormapping.VendorMappingServiceClient, db types.SherlockPGDB, fClient freshdesk.FreshdeskClient, updateTicketPub types2.DisputeUpdateTicketPublisher, vgDisputeClient dispute.DisputeClient, s3Client types2.Disputes3Client, genConfig *genconf.Config, sClient savings.SavingsClient, commsClient comms.CommsClient, ticketClient ticket.TicketClient, issueClient issue_resolution_feedback2.IssueResolutionFeedbackServiceClient, updateTicketPublisher types2.UpdateTicketPublisher, disputeExtPub types2.DisputeExternalPublisher, pClient payment.PaymentClient, piClient paymentinstrument.PiClient, orderClient order.OrderServiceClient, userGroupClient group.GroupClient, createDisputeTicketPub types2.DisputeCreateTicketPublisher) *job.Service {
	disputeDao := dao12.NewDisputeDao(db)
	genconfDispute := getDynDispute(genConfig)
	disputeConfigDao := dao12.NewDisputeConfigDao(db)
	customerIdentifier := helper2.NewCustomerIdentifier(userClient, actorClient, chatClient, vmClient)
	federalDmpDisputeDetailDao := dao12.NewFederalDmpDisputeDetailDao(db)
	gormDB := GormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	disputeHelper := helper6.NewDisputeHelper(disputeExtPub, disputeDao, pClient, piClient, orderClient, actorClient, userClient, userGroupClient, genconfDispute, disputeConfigDao, createDisputeTicketPub, customerIdentifier, sClient, federalDmpDisputeDetailDao, gormTxnExecutor, commsClient, genConfig, vgDisputeClient)
	freshdeskProcessor := processor4.NewFreshdeskProcessor(disputeDao, disputeHelper, fClient, genconfDispute, ticketClient, issueClient)
	client := types2.Disputes3ClientProvider(s3Client)
	sftpProcessor := processor4.NewSftpProcessor(disputeDao, client, genconfDispute, vgDisputeClient, updateTicketPub, disputeHelper, customerIdentifier, sClient)
	processorFactory := job.NewProcessorFactory(freshdeskProcessor, sftpProcessor)
	disputeNotificationLogDao := dao12.NewDisputeNotificationLogDao(db)
	freshdeskReverseUpdateProcessor := reverse_update_processor.NewFreshdeskReverseUpdateProcessor(disputeDao, disputeHelper, fClient, commsClient, customerIdentifier, genConfig, disputeNotificationLogDao)
	disputeIdToTicketIdMappingDao := dao12.NewDisputeIdToTicketIdMappingDao(db)
	federalBankReverseUpdateProcessor := reverse_update_processor.NewFederalBankReverseUpdateProcessor(disputeDao, federalDmpDisputeDetailDao, genConfig, vgDisputeClient, updateTicketPublisher, disputeHelper, disputeIdToTicketIdMappingDao)
	reverseUpdateProcessorFactory := job.NewReverseUpdateProcessorFactory(freshdeskReverseUpdateProcessor, federalBankReverseUpdateProcessor)
	service := job.NewService(disputeDao, processorFactory, reverseUpdateProcessorFactory, genConfig)
	return service
}

func InitializeTicketSummaryService(fClient freshdesk.FreshdeskClient, chatClient chat.ChatsClient, actorClient actor.ActorClient, userClient user.UsersClient, vmClient vendormapping.VendorMappingServiceClient) *ticket_summary.TicketSummaryService {
	customerIdentifier := helper2.NewCustomerIdentifier(userClient, actorClient, chatClient, vmClient)
	ticketSummaryService := ticket_summary.NewTicketSummaryService(fClient, customerIdentifier)
	return ticketSummaryService
}

func InitializeDbStatesService(commsDbStateClient developer.CommsDbStatesClient, devActorEntityClient developer2.DevActorClient, devDepositClient developer3.DevDepositClient, devKycClient developer4.DevKYCClient, devUserClient developer5.DevUserClient, orderDbStateClient developer6.DevClient, piDbStateClient developer7.DevPaymentIntrumentClient, devCardClient developer8.CardDbStatesClient, devLivenessClient developer9.DevLivenessClient, devSavingsClient developer10.SavingsDbStatesClient, upiDbStateClient developer11.DevClient, devInapphelpClient developer12.DevInapphelpClient, devCxClient developer13.DevCXClient, casbinDevClient developer14.DevCasbinClient, insightsDevClient developer15.DevInsightsClient, categorizerDevClient developer16.DevCategorizerClient, rewardsDevClient developer17.RewardsDevClient, devAuthClient developer18.DevAuthClient, vmDevClient developer19.DevVendorMappingClient, timelineDevClient developer20.DevTimelineClient, casperDevClient developer21.CasperDevClient, rmsDevClient developer22.RMSDbStatesClient, devMerchantClient developer23.DevMerchantClient, fitttDevClient developer24.FITTTDbStatesClient, caDevClient developer25.DevConnectedAccClient, devInappreferralClient developer26.DevInAppReferralClient, woOnbDevClient developer27.DevWealthOnboardingClient, investmentDBStateClient developer28.MutualFundDbStatesClient, devrecurringPaymentClient developer29.RecurringPaymentDevClient, enachDevClient developer30.EnachDevClient, devP2PInvestmentClient developer31.DevP2PInvestmentClient, devSegmentClient developer32.SegmentDbStatesClient, devNudgeClient developer33.NudgeDbStatesClient, salaryProgramDevClient developer34.SalaryProgramDevClient, analyserDevClient developer35.DevAnalyserClient, preApprovedDevClient developer36.DevPreApprovedLoanClient, ccDevClient developer37.DevFireflyClient, celestialDevClient developer38.DeveloperClient, riskDevClient developer39.DeveloperClient, scrnrDevClient developer40.DeveloperClient, devAuthOrchClient developer41.DevOrchestratorClient, payDevClient developer42.DevClient, usStocksDBStatesClient developer43.DBStateClient, tieringDevClient developer44.TieringDevServiceClient, amlDevClient developer45.AmlDevServiceClient, alfredDevClient developer46.DeveloperClient, panDevClient developer47.DeveloperClient, questDevClient developer48.QuestDbStatesClient, upcomingTxnsDevClient developer49.DevUpcomingTransactionsClient, healthEngineDevClient developer50.HealthEngineDevClient, cmsDevClient developer51.CmsDevClient, devBcClient developer52.DevBankCustClient, devOmegleClientToSGApiGatewayServer pkg.DevOmegleClientToSGApiGatewayServer, devOmegleClientToOnboardingServer pkg.DevOmegleClientToOnboardingServer, devVkycCallClientToSGApiGatewayServer pkg.DevVkycCallClientToSGApiGatewayServer, devVkycCallClientToOnboardingServer pkg.DevVkycCallClientToOnboardingServer, devEmpClient developer53.DevEmploymentClient, collectionDeveloperClient developer54.DeveloperClient, sgApiGwDbsClient dbstate.DBStateServiceClient, tspUserServiceClient tspuser.TspUserServiceClient, devClient developer55.DeveloperClient, cxConf *genconf.Config, casbinClient casbin.CasbinClient, leadClient developer56.DevLeadClient, crDevClient developer57.DevCreditReportClient, accountsDevClient developer58.AccountsDbStatesClient, npsDevClient developer59.NpsDbStatesClient, securitiesDevClient developer60.SecuritiesDevClient, salaryEstDevClient developer61.DevSalaryEstimationClient) *db_states.Service {
	epifiTechVkycCallCollector := vkyccall.NewEpifiTechVkycCallCollector(devVkycCallClientToOnboardingServer)
	stockguardianVkycCallCollector := vkyccall.NewStockguardianVkycCallCollector(devVkycCallClientToSGApiGatewayServer)
	epifiTechOmegleCollector := omegle.NewEpifiTechOmegleCollector(devOmegleClientToOnboardingServer)
	stockguardianOmegleCollector := omegle.NewStockguardianOmegleCollector(devOmegleClientToSGApiGatewayServer)
	stockguardianApiGatewayDbStateCollector := dbstate2.NewStockguardianApiGatewayDbStateCollector(sgApiGwDbsClient)
	iCollectorFactory := getDBStateCollectorFactory(commsDbStateClient, devActorEntityClient, devDepositClient, devKycClient, devUserClient, orderDbStateClient, piDbStateClient, devCardClient, devLivenessClient, devSavingsClient, upiDbStateClient, devInapphelpClient, devCxClient, casbinDevClient, insightsDevClient, categorizerDevClient, rewardsDevClient, devAuthClient, vmDevClient, timelineDevClient, casperDevClient, rmsDevClient, devMerchantClient, fitttDevClient, caDevClient, devInappreferralClient, woOnbDevClient, investmentDBStateClient, devrecurringPaymentClient, enachDevClient, devP2PInvestmentClient, devSegmentClient, devNudgeClient, salaryProgramDevClient, analyserDevClient, preApprovedDevClient, ccDevClient, celestialDevClient, riskDevClient, scrnrDevClient, devAuthOrchClient, payDevClient, usStocksDBStatesClient, tieringDevClient, amlDevClient, alfredDevClient, panDevClient, questDevClient, upcomingTxnsDevClient, healthEngineDevClient, cmsDevClient, devBcClient, epifiTechVkycCallCollector, stockguardianVkycCallCollector, epifiTechOmegleCollector, stockguardianOmegleCollector, devEmpClient, collectionDeveloperClient, stockguardianApiGatewayDbStateCollector, devClient, leadClient, crDevClient, accountsDevClient, npsDevClient, securitiesDevClient, salaryEstDevClient)
	service := db_states.NewDbStatesServer(iCollectorFactory, cxConf, casbinClient)
	return service
}

func InitializePayoutService(db types.SherlockPGDB, accountPiRelationClient account_pi.AccountPIRelationClient, orderServiceClient order.OrderServiceClient, gconf *genconf.Config, actorClient actor.ActorClient, timelineClient timeline.TimelineServiceClient, piClient paymentinstrument.PiClient, payoutStatusCheckPublisher types2.PayoutStatusCheckPublisher, payClient pay.PayClient, rewardsClient rewards.RewardsGeneratorClient) *payout.Service {
	genconfPayout := getDynPayout(gconf)
	payoutRequestDAO := dao15.NewPayoutRequestDAO(db)
	delayPublisher := types2.PayoutStatusCheckPublisherProvider(payoutStatusCheckPublisher)
	cashPayoutProcessor := cash.NewCashPayoutProcessor(payoutRequestDAO, accountPiRelationClient, orderServiceClient, genconfPayout, timelineClient, actorClient, piClient, delayPublisher, payClient)
	fiCoinsPayoutProcessor := fi_coins.NewFiCoinsPayoutProcessor(payoutRequestDAO, genconfPayout, rewardsClient, delayPublisher)
	factory := processor5.NewPayoutFactory(cashPayoutProcessor, fiCoinsPayoutProcessor)
	service := payout.NewPayoutService(genconfPayout, payoutRequestDAO, factory)
	return service
}

func InitializePayoutConsumerService(db types.SherlockPGDB, orderServiceClient order.OrderServiceClient, gconf *genconf.Config, payoutStatusCheckPublisher types2.PayoutStatusCheckPublisher, payClient pay.PayClient, rewardsClient rewards.RewardsGeneratorClient) *consumer3.Service {
	payoutRequestDAO := dao15.NewPayoutRequestDAO(db)
	genconfPayout := getDynPayout(gconf)
	delayPublisher := types2.PayoutStatusCheckPublisherProvider(payoutStatusCheckPublisher)
	service := consumer3.NewPayoutConsumerService(payoutRequestDAO, orderServiceClient, genconfPayout, delayPublisher, payClient, rewardsClient)
	return service
}

// config: {"cxS3Client": "CxS3Config().BucketName", "epifiIconsS3Client": "EpifiIconS3Config().BucketName", "dataS3Client": "DataS3Config().BucketName", "federalEscalationAttachmentsS3Client": "FederalEscalationConfig().FederalEscalationAttachmentBucketName()"}
func InitializeDevActionsService(ctx context.Context, sqsClient *sqs.Client, chatClient chat.ChatsClient, kycClient kyc.KycClient, livenessClient liveness.LivenessClient, userClient user.UsersClient, savingsClient savings.SavingsClient, vmClient vendormapping.VendorMappingServiceClient, cardClient provisioning.CardProvisioningClient, rewardOfferClient rewardoffers.RewardOffersClient, luckyDrawSvcClient luckydraw.LuckyDrawServiceClient, userGroupClient group.GroupClient, onbClient onboarding.OnboardingClient, authClient auth.AuthClient, indexerClient indexer.IndexerClient, searchClient search.ActionBarClient, emailParserClient emailparser.ConsumerServiceClient, accountPiClient account_pi.AccountPIRelationClient, offerCatalogClient casper.OfferCatalogServiceClient, offerInventoryClient casper.OfferInventoryServiceClient, offerListingClient casper.OfferListingServiceClient, actorClient actor.ActorClient, reconClient recon.LedgerReconciliationClient, epClient emailparser.EmailParserClient, accessinfoClient accessinfo.AccessInfoClient, rmsClient manager2.RuleManagerClient, sportsClient sports.SportsManagerClient, schedulerClient schedulerpb.SchedulerServiceClient, vkycClient vkyc.VKYCClient, upClient user_preference.UserPreferenceClient, orderClient order.OrderServiceClient, rmsEventPublisher types2.RMSEventPublisher, commsClient comms.CommsClient, rewardsCampaignCommClient campaigncomm.RewardsCampaignCommClient, rewardsClient rewards.RewardsGeneratorClient, statementClient statement.AccountStatementClient, offerRedemptionClient redemption.OfferRedemptionServiceClient, devActionDelayPublisher types2.DevActionDelayPublisher, casbinClient casbin.CasbinClient, connectedAccClient connected_account.ConnectedAccountClient, paymentClient payment.PaymentClient, woClient wealthonboarding.WealthOnboardingClient, inAppReferralClient inappreferral.InAppReferralClient, inAppHelpServingClient serving.ServeFAQClient, vgAaClient aa.AccountAggregatorClient, inAppHelpMediaClient media.InAppHelpMediaClient, investmentsCatalogClient catalog.CatalogManagerClient, investmentOrderManagerClient order3.OrderManagerClient, exchangerOfferSvcClient exchanger.ExchangerOfferServiceClient, searchDevClient developer62.DevSearchClient, depositClient deposit.DepositClient, wOnbCxClient cx3.WealthCxServiceClient, fileGeneratorClient filegenerator.FileGeneratorClient, recurringPaymentClient recurringpayment.RecurringPaymentServiceClient, cxS3Client types2.CxS3Client, cxConf *config.Config, inappTargetedCommsClient inapptargetedcomms.InAppTargetedCommsClient, mfprerequisiteHandlerClient prerequisite_handler.PrerequisiteHandlerClient, mfOpsClient operations.OrderOperationsClient, insightsClient insights.InsightsClient, devP2PInvestmentClient developer31.DevP2PInvestmentClient, reverseFeedManagerClient reverse_feed.ReverseFeedManagerClient, seasonsClient season.SeasonServiceClient, segmentClient segment.SegmentationServiceClient, mfVGClient mutualfund2.MutualFundClient, upiClient upi.UPIClient, dbconn types.SherlockPGDB, segmentConsumerClient consumer4.ConsumerClient, ticketClient ticket.TicketClient, mfReconciliationServiceClient reconciliation.ReconciliationServiceClient, ccCxClient cx5.CxClient, preapprovedloanCxClient cx6.CxClient, preApprovedLoanSimClient preapprovedloan.PreApprovedLoanClient, preApprovedLoanClient preapprovedloan2.PreApprovedLoanClient, preapprovedloanDevClient developer36.DevPreApprovedLoanClient, profileEvaluatorClient profileevaluator.ProfileEvaluatorClient, empClient employment.EmploymentClient, salaryReferralsClient referrals.ReferralsClient, uaClient useractions.UserActionsClient, riskClient risk.RiskClient, leaRiskClient lea.LeaClient, upiOnboardingClient onboarding2.UpiOnboardingClient, client nudge.NudgeServiceClient, accountsDevSvcClient developer63.AccountsDevServiceClient, payClient pay.PayClient, discountClient discounts.DiscountServiceClient, userIssueInfoClient user_issue_info.UserIssueInfoServiceClient, payFileGeneratorClient file_generator.FileGeneratorClient, iftFileProcessorEventPublisher types2.IFTFileProcessorEventPublisher, rewardsManualGiveawayEventPublisher types2.RewardsManualGiveawayEventPublisher, riskCaseIngestionPublisher types2.RiskCasePublisher, genConf *genconf.Config, redlistClient redlist.RedListClient, usstocksCatalogClient catalog2.CatalogManagerClient, amlClient aml.AmlClient, ussOrderMgClient order2.OrderManagerClient, celestialClient celestial.CelestialClient, fitttClient fittt.FitttClient, iftClient internationalfundtransfer.InternationalFundTransferClient, caseManagementClient case_management.CaseManagementClient, temporalWorkflowServiceClient workflowservice.WorkflowServiceClient, empFeClient employment.EmploymentFeClient, bcClient bankcust.BankCustomerServiceClient, vgAccountsClient accounts.AccountsClient, ussOperationClient operations2.OperationsClient, extValidateClient extvalidate.ExternalValidateClient, nameCheckClient namecheck.UNNameCheckClient, cmsServiceClient cms.CmsServiceClient, dynamicUIElementServiceClient dynamic_ui_element.DynamicUIElementServiceClient, cardCxClient cx7.CxClient, actionClient actions.CommsDevActionClient, kycAgentPb agent.KycAgentServiceClient, p2pSimulationServiceClient p2pinvestment.SimulationClient, p2p2 p2pinvestment.P2PInvestmentClient, vgStocksClient stocks.StocksClient, questManagerClient manager3.ManagerClient, oprStatusServiceClient operstatus.OperationalStatusServiceClient, redisClient types2.CxRedisStore, vgEkycClient ekyc.EKYCClient, vgP2pClient p2p.P2PClient, mfFolioMgClient foliodetails.MfFolioServiceClient, salaryClient salaryprogram.SalaryProgramClient, rewardSimulatorClient generator.SimulatorClient, riskDisputeUploadPublisher types2.RiskDisputePublisher, casperItcDownloadFileQueuePublisher types2.CasperItcDownloadFileQueuePublisher, accountManagerClient account2.AccountManagerClient, whiteListClient whitelist.WhiteListClient, orderUpdatePublisher types2.OrderUpdateEventForTxnCategorizationPublisher, dataS3Client types2.DataS3Client, epifiIconsS3Client types2.EpifiIconsS3Client, ffClient firefly.FireflyClient, issueReportingClient issue_reporting.ServiceClient, journeyClient journey.JourneyServiceClient, vkyccallTroubleshootCl pkg.VkycCallTroubleshootClientToOnboardingServer, vkyccallTroubleshootCl2 pkg.VkycCallTroubleshootClientToSGApiGatewayServer, alfredClient alfred.AlfredClient, aggregatorClient aggregator.InvestmentAggregatorClient, ffAccountingClient accounting.AccountingClient, aaOrderClient aa2.AccountAggregatorClient, aaTxnPublisher types2.AATxnCategorizationPublisher, ccTxnPublisher types2.CCTxnCategorizationPublisher, sgApiGwKycClient kyc3.KYCClient, salaryb2bClient salaryb2b.SalaryB2BClient, sgLmsClient lms.LmsClient, saClosureClient sa_closure.SavingsAccountClosureClient, spDynamicUIElementClient dynamic_ui_element2.DynamicUIElementServiceClient, sgApplicationClient application.ApplicationClient, rpFederalClient federal2.RecurringPaymentClient, publisher types2.FederalEscalationCreateEventPublisher, federalEscalationAttachmentsS3Client types2.FederalEscalationAttachmentsS3Client, epfClient epf.EpfClient, mfExternalClient external.MFExternalOrdersClient, networthClient networth.NetWorthClient, consentClient consent.ConsentClient, investmentAnalyticsClient investment.InvestmentAnalyticsClient, riskProfileClient profile3.ProfileClient) (*actions2.DevActions, error) {
	executeDisbursalRetry := stockguardian.NewExecuteDisbursalRetry(sgApplicationClient)
	retryLiveness := processor6.NewRetryLiveness(kycClient, livenessClient)
	retryCreateAccount := processor6.NewRetryCreateAccount(actorClient, savingsClient, onbClient)
	retryCreateBankCustomer := processor6.NewRetryCreateBankCustomer(bcClient)
	retryEKYC := processor6.NewRetryEKYC(kycClient)
	customerIdentifier := helper2.NewCustomerIdentifier(userClient, actorClient, chatClient, vmClient)
	retryCkyc := processor6.NewRetryCkyc(kycClient, customerIdentifier)
	createCard := processor6.NewCreateCard(cardClient)
	createRewardOffer := processor6.NewCreateRewardOffer(rewardOfferClient, segmentClient, questManagerClient)
	createRewardOfferInBulk := processor6.NewCreateRewardOfferInBulk(rewardOfferClient, segmentClient)
	updateRewardOfferStatus := processor6.NewUpdateRewardOfferStatus(rewardOfferClient)
	createLuckyDrawCampaign := processor6.NewCreateLuckyDrawCampaign(luckyDrawSvcClient)
	createLuckyDraw := processor6.NewCreateLuckyDraw(luckyDrawSvcClient)
	cxCacheStore := CxCacheStorageProvider(redisClient)
	s3Client := types2.CxS3ClientProvider(cxS3Client)
	appLogDao := dao14.NewAppLogDao(cxCacheStore, genConf, s3Client)
	actorLogMappingDao := dao14.NewActorLogMappingDao(cxCacheStore, genConf)
	logIdDao := dao14.NewLogIdDao(cxCacheStore, genConf)
	appLog := getDynAppLog(genConf)
	appLogsNotificationContent := getAppLogsNotificationContent(cxConf)
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(dbconn)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(dbconn)
	authFactorRetryLimit := getAuthFactoryRetryLimit(cxConf)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(dbconn)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConf, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	service := app_log.NewAppLogService(appLogDao, actorLogMappingDao, logIdDao, appLog, genConf, customerIdentifier, commsClient, appLogsNotificationContent, authEngine)
	pullAppLogs := processor6.NewPullAppLogs(service, genConf)
	addUserGroupMapping := processor6.NewAddUserGroupMapping(userGroupClient)
	deleteUser := processor6.NewDeleteUser(onbClient)
	recoverAuthFactorUpdate := processor6.NewRecoverAuthFactorUpdate(authClient)
	updateRewardOfferDisplay := processor6.NewUpdateRewardOfferDisplay(rewardOfferClient)
	indexActorPis := processor6.NewIndexActorPis(indexerClient, accountPiClient)
	indexActorGmailData := processor6.NewIndexActorGmailData(indexerClient, emailParserClient)
	createOffer := processor6.NewCreateOffer(offerCatalogClient, questManagerClient)
	createOffersInBulk := processor6.NewCreateOffersInBulk(offerCatalogClient)
	createOfferInventory := processor6.NewCreateOfferInventory(offerInventoryClient)
	addOfferToInventory := processor6.NewAddOfferToInventory(offerInventoryClient)
	deleteOfferInventory := processor6.NewDeleteOfferInventory(offerInventoryClient)
	createOfferListing := processor6.NewCreateOfferListing(offerListingClient)
	createOrUpdateMediaPlaylistSegmentMapping := processor6.NewCreateOrUpdateMediaPlaylistSegmentMapping(inAppHelpMediaClient, inAppHelpServingClient)
	createExchangerOfferInventory := processor6.NewCreateExchangerOfferInventory(exchangerOfferSvcClient)
	incrementExchangerOfferInventory := processor6.NewIncrementExchangerOfferInventory(exchangerOfferSvcClient)
	updateOfferListing := processor6.NewUpdateOfferListing(offerListingClient)
	deleteOfferListing := processor6.NewDeleteOfferListing(offerListingClient)
	reconProcessor := processor6.NewReconProcessor(savingsClient, reconClient, actorClient)
	updateOfferDisplayRank := processor6.NewUpdateOfferDisplayRank(offerCatalogClient, exchangerOfferSvcClient)
	unlinkGmailAccount := processor6.NewUnlinkGmailAccount(epClient, accessinfoClient)
	syncOnb := processor6.NewSyncOnb(onbClient, userClient)
	createRule := fittt2.NewCreateRule(rmsClient)
	publishSharkTankEvent := fittt2.NewPublishSharkTankEvent(fitttClient)
	updateRule := fittt2.NewUpdateRule(rmsClient)
	bulkUpdateSubscriptionsState := fittt2.NewBulkUpdateSubscriptionsState(rmsClient)
	archiveRulesAndSubscriptions := fittt2.NewArchiveRulesAndSubscriptions(rmsClient)
	fitCreateSchedule := fittt2.NewFITCreateSchedule(schedulerClient)
	fitStopSchedules := fittt2.NewFITStopSchedules(schedulerClient)
	updateRulesWeightage := fittt2.NewUpdateRulesWeightage(rmsClient)
	getRulesForClient := fittt2.NewGetRulesForClient(rmsClient)
	updateOnboardingStage := processor6.NewUpdateOnboardingStage(onbClient)
	processNonResidentCrossValidationManualReview := processor6.NewProcessNonResidentCrossValidationManualReview(onbClient)
	passOnboardingStage := processor6.NewPassOnboardingStage(onbClient)
	passportManualReview := processor6.NewPassportManualReview(onbClient)
	updateOfferDisplay := processor6.NewUpdateOfferDisplay(offerCatalogClient)
	initiateMatchUpdate := processor6.NewInitiateMatchUpdate(sportsClient)
	initiateCardNotifications := processor6.NewInitiateCardNotifications(cardClient)
	markLiveNessPassed := processor6.NewMarkLiveNessPassed(kycClient, livenessClient)
	markFaceMatchPassed := processor6.NewMarkFaceMatchPassed(kycClient, livenessClient)
	updateShippingAddressAtVendor := processor6.NewUpdateShippingAddressAtVendor(userClient)
	createShippingPreference := processor6.NewCreateShippingPreference(userClient)
	retryPayOrderRMSEvent := fittt2.NewRetryPayOrderRMSEvent(rmsEventPublisher, orderClient)
	sendRewardsCampaignComm := processor6.NewSendRewardsCampaignComm(rewardsCampaignCommClient)
	unredactedUser := processor6.NewUnredactedUser(userClient)
	orderProcessor := processor6.NewOrderProcessor(orderClient, actorClient, userClient, paymentClient, bcClient)
	retryRewardProcessing := processor6.NewRetryRewardProcessing(rewardsClient)
	getTxnAggrFit := processor6.NewGetTxnAggrFit(searchClient)
	updateUserProfileName := processor6.NewUpdateUserProfileName(userClient, actorClient)
	accountStatement := processor6.NewAccountStatement(statementClient)
	onboardingSnapshot := processor6.NewOnboardingSnapshot(onbClient)
	backFillFreshdeskTicketContacts := processor6.NewBackFillFreshdeskTicketContacts(devActionDelayPublisher)
	createRewardOfferGroup := processor6.NewCreateRewardOfferGroup(rewardOfferClient)
	resetKYCNameDobRetry := processor6.NewResetKYCNameDobRetry(kycClient)
	retryOfferRedemption := processor6.NewRetryOfferRedemption(offerRedemptionClient)
	manualScreeningUpdate := processor6.NewManualScreeningUpdate(onbClient)
	resetDebitCardNameRetry := processor6.NewResetDebitCardNameRetry(onbClient)
	unblockUnNameCheckUser := processor6.NewUnblockUnNameCheckUser(onbClient, userClient, actorClient)
	refreshVKYCStatus := processor6.NewRefreshVKYCStatus(userClient)
	updateCardPinSet := processor6.NewUpdateCardPinSet(cardClient)
	raiseAaConsent := processor6.NewRaiseAaConsent(connectedAccClient)
	forceCardCreationEnquiry := processor6.NewForceCardCreationEnquiry(cardClient)
	triggerRewardsManualGiveawayEvent := processor6.NewTriggerRewardsManualGiveawayEvent(rewardsManualGiveawayEventPublisher, actorClient)
	deleteUserGroupMapping := processor6.NewDeleteUserGroupMapping(userGroupClient)
	syncWealthOnboarding := wealthonboarding2.NewSyncWealthOnboarding(woClient)
	unlockInAppReferral := processor6.NewUnlockInAppReferral(inAppReferralClient)
	triggerVkycCallback := processor6.NewTriggerVkycCallback(vkycClient)
	handleSavingsAccountClosure := processor6.NewHandleSavingsAccountClosure(savingsClient)
	updateGmailInsightsMerchants := processor6.NewUpdateGmailInsightsMerchants(epClient)
	updateGmailMerchantQueries := processor6.NewUpdateGmailMerchantQueries(epClient)
	updateInAppHelpFAQ := processor6.NewUpdateInAppHelpFAQ(inAppHelpServingClient)
	aaConsentStatusUpdate := processor6.NewAaConsentStatusUpdate(connectedAccClient, vgAaClient)
	aaAccountDeLink := processor6.NewAaAccountDeLink(connectedAccClient, vgAaClient)
	reactivateDevice := processor6.NewReactivateDevice(authClient)
	reopenClosedSavingsAccount := processor6.NewReopenClosedSavingsAccount(savingsClient)
	updateWOnbStatus := wealthonboarding2.NewUpdateWOnbStatus(woClient)
	fitttCreateHomeCard := fittt2.NewFitttCreateHomeCard(rmsClient)
	fitttUpdateHomeCard := fittt2.NewFitttUpdateHomeCard(rmsClient)
	updatePanReview := processor6.NewUpdatePanReview(onbClient)
	addMediaPlaylist := processor6.NewAddMediaPlaylist(inAppHelpMediaClient)
	updateMediaPlaylist := processor6.NewUpdateMediaPlaylist(inAppHelpMediaClient)
	addMediaContentStory := processor6.NewAddMediaContentStory(inAppHelpMediaClient)
	updateMediaContentStory := processor6.NewUpdateMediaContentStory(inAppHelpMediaClient)
	addUIContextToMediaPlaylistMapping := processor6.NewAddUIContextToMediaPlaylistMapping(inAppHelpMediaClient, inAppHelpServingClient)
	deleteUIContextToMediaPlaylistMapping := processor6.NewDeleteUIContextToMediaPlaylistMapping(inAppHelpMediaClient)
	addMediaPlaylistToMediaContentMapping := processor6.NewAddMediaPlaylistToMediaContentMapping(inAppHelpMediaClient)
	deleteMediaPlaylistToMediaContentMapping := processor6.NewDeleteMediaPlaylistToMediaContentMapping(inAppHelpMediaClient)
	createMutualFund := mutualfund3.NewCreateMutualFund(investmentsCatalogClient)
	updateMutualFund := mutualfund3.NewUpdateMutualFund(investmentsCatalogClient)
	addCreditMISFileMetaData := mutualfund3.NewAddCreditMISFileMetaData(investmentOrderManagerClient)
	mutualFundReverseFeedFileUpload := mutualfund3.NewMutualFundReverseFeedFileUpload(investmentOrderManagerClient)
	aaReplayAccountEvent := processor6.NewAaReplayAccountEvent(connectedAccClient)
	createExchangerOffer := processor6.NewCreateExchangerOffer(exchangerOfferSvcClient, questManagerClient)
	createExchangerOfferGroup := processor6.NewCreateExchangerOfferGroup(exchangerOfferSvcClient)
	createExchangerOfferListing := processor6.NewCreateExchangerOfferListing(exchangerOfferSvcClient)
	updateExchangerOfferDisplay := processor6.NewUpdateExchangerOfferDisplay(exchangerOfferSvcClient)
	updateExchangerOfferStatus := processor6.NewUpdateExchangerOfferStatus(exchangerOfferSvcClient)
	updateExchangerOfferListing := processor6.NewUpdateExchangerOfferListing(exchangerOfferSvcClient)
	deleteExchangerOfferListing := processor6.NewDeleteExchangerOfferListing(exchangerOfferSvcClient)
	aaReplayTxnEvent := processor6.NewAaReplayTxnEvent(connectedAccClient)
	parseQueryBase := processor6.NewParseQueryBase(searchDevClient)
	forceProcessDepositRequest := deposits.NewForceProcessDepositRequest(depositClient)
	markWealthLivenessPassed := wealthonboarding2.NewMarkWealthLivenessPassed(wOnbCxClient)
	mutualFundDeactivateEntityFromFile := mutualfund3.NewMutualFundDeactivateEntityFromFile(fileGeneratorClient)
	fitttCreateCollection := fittt2.NewFitttCreateCollection(rmsClient)
	fitttUpdateCollection := fittt2.NewFitttUpdateCollection(rmsClient)
	markWealthRedactionPassed := wealthonboarding2.NewMarkWealthRedactionPassed(wOnbCxClient)
	markWealthExpiryPassed := wealthonboarding2.NewMarkWealthExpiryPassed(wOnbCxClient)
	updateMutualFundOrderStatus := mutualfund3.NewUpdateMutualFundOrderStatus(investmentOrderManagerClient)
	triggerRecurringPaymentExecution := processor6.NewTriggerRecurringPaymentExecution(recurringPaymentClient)
	uploadMarketingCampaignUsersList := processor6.NewUploadMarketingCampaignUsersList(s3Client, cxConf)
	mutualFundDownloadCreditMISReport := mutualfund3.NewMutualFundDownloadCreditMISReport(fileGeneratorClient, investmentOrderManagerClient)
	addInAppTargetedCommsElement := processor6.NewAddInAppTargetedCommsElement(inappTargetedCommsClient, inAppHelpServingClient)
	updateInAppTargetedCommsElement := processor6.NewUpdateInAppTargetedCommsElement(inappTargetedCommsClient, inAppHelpServingClient, segmentClient)
	mfRetriggerPrerequisites := mutualfund3.NewMFRetriggerPrerequisites(fileGeneratorClient, mfprerequisiteHandlerClient, mfVGClient)
	mfDownloadOpsFile := mutualfund3.NewMFDownloadOpsFile(mfOpsClient)
	updateInsightFramework := processor6.NewUpdateInsightFramework(insightsClient)
	updateInsightSegment := processor6.NewUpdateInsightSegment(insightsClient)
	updateInsightContentTemplate := processor6.NewUpdateInsightContentTemplate(insightsClient)
	mfUploadCatalogUpdate := mutualfund3.NewMfUploadCatalogUpdate(investmentsCatalogClient)
	uploadCreditMISToVendorPublisher := uploadCreditMISToVendorPublisherProvider(ctx, cxConf, sqsClient)
	mfUploadCreditMISNonProd := mutualfund3.NewMfUploadCreditMISNonProd(uploadCreditMISToVendorPublisher, investmentOrderManagerClient)
	updateRewardOfferDisplayRank := processor6.NewUpdateRewardOfferDisplayRank(rewardOfferClient)
	addUserToVKYCPriority := processor6.NewAddUserToVKYCPriority(vkycClient)
	updateInvTranStatus := p2pinvestment2.NewUpdateInvTranStatus(devP2PInvestmentClient)
	manualCardUnsuspend := processor6.NewManualCardUnsuspend(cardClient)
	updateTotalInvestmentCount := p2pinvestment2.NewUpdateTotalInvestmentCount(devP2PInvestmentClient)
	updateUserPhoto := processor6.NewUpdateUserPhoto(userClient, s3Client, customerIdentifier)
	mfProcessReverseFeedFile := mutualfund3.NewMFProcessReverseFeedFile(reverseFeedManagerClient, investmentOrderManagerClient)
	mfCreateCollection := mutualfund3.NewMfCreateCollection(investmentsCatalogClient)
	mfAddFundToCollection := mutualfund3.NewMfAddFundToCollection(investmentsCatalogClient)
	mfRemoveFundsFromCollection := mutualfund3.NewMfRemoveFundsFromCollection(investmentsCatalogClient)
	mfUpdateFundInCollectionFundMapping := mutualfund3.NewUpdateFundInCollectionFundMapping(investmentsCatalogClient)
	createCollection := usstocks2.NewUSStocksCreateCollection(usstocksCatalogClient)
	addStockToCollection := usstocks2.NewUSStocksAddStockToCollection(usstocksCatalogClient)
	removeStockFromCollection := usstocks2.NewUSStocksRemoveStockFromCollection(usstocksCatalogClient)
	updateStockInCollection := usstocks2.NewUSStocksUpdateStockInCollection(usstocksCatalogClient)
	updateCollection := usstocks2.NewUSStocksUpdateCollection(usstocksCatalogClient)
	resetOnboardingData := usstocks2.NewResetOnboardingData(accountManagerClient)
	updateP2PVendorResponsesApprovalStatus := p2pinvestment2.NewUpdateP2PVendorResponsesApprovalStatus(p2pSimulationServiceClient, p2p2)
	mfUpdateCollection := mutualfund3.NewUpdateCollection(investmentsCatalogClient)
	physicalCardRequest := processor6.NewPhysicalCardRequest(cardClient)
	createReferralsSeason := processor6.NewCreateReferralsSeason(seasonsClient)
	updateReferralsSeason := processor6.NewUpdateReferralsSeason(seasonsClient)
	deleteSegment := processor6.NewDeleteSegment(segmentClient)
	markStepStaleWealthOnboarding := wealthonboarding2.NewMarkStepStaleWealthOnboarding(wOnbCxClient)
	requestNewCard := processor6.NewRequestNewCard(cardClient)
	triggerVPACreation := processor6.NewTriggerVPACreation(actorClient, upiClient, authClient, savingsClient, upiOnboardingClient)
	db := GormDBProvider(dbconn)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(db)
	callRoutingManualMappingsDao := dao16.NewCallRoutingManualMappingsDao(dbconn)
	addManualCallRoutingMappings := processor6.NewAddManualCallRoutingMappings(gormTxnExecutor, cxConf, callRoutingManualMappingsDao)
	triggerSegmentExport := processor6.NewTriggerSegmentExport(segmentConsumerClient)
	createSegment := processor6.NewCreateSegment(segmentClient, segmentConsumerClient)
	updateSegment := processor6.NewUpdateSegment(segmentClient, segmentConsumerClient)
	deepLinkBase64Encoder := processor6.NewDeepLinkBase64Encoder()
	creditCardUpdateCardRequestStatus := processor6.NewCreditCardUpdateCardRequestStatus(ccCxClient)
	preApprovedLoanManualReview := processor6.NewPreApprovedLoanManualReview(preapprovedloanCxClient)
	preApprovedLoanCreateOffer := processor6.NewPreApprovedLoanCreateOffer(preApprovedLoanClient, preapprovedloanDevClient)
	preApprovedLoanUpdateStatus := processor6.NewPreApprovedLoanUpdateStatus(preApprovedLoanSimClient)
	preApprovedLoanLlEntityStatus := processor6.NewPreApprovedLoanLlEntityStatus(preApprovedLoanSimClient)
	categoriseScreenerDomains := processor6.NewCategoriseScreenerDomains(empClient)
	createSalaryProgramReferralsSeason := processor6.NewCreateSalaryProgramReferralsSeason(salaryReferralsClient)
	startUserAction := processor6.NewStartUserAction(uaClient, authClient, actorClient)
	savingsRiskBankAction := processor6.NewSavingsRiskBankAction(riskClient)
	ussGetDocumentFromBucket := usstocks2.NewUSSGetDocumentFromBucket(ussOperationClient)
	createNudge := processor6.NewCreateNudge(client, inappTargetedCommsClient, segmentClient)
	createNudgesInBulk := processor6.NewCreateNudgesInBulk(client, inappTargetedCommsClient, segmentClient)
	editNudge := processor6.NewEditNudge(client)
	updateNudgeStatus := processor6.NewUpdateNudgeStatus(client)
	updateUserFatherName := processor6.NewUpdateUserFatherName(userClient, actorClient)
	updateAccountFreezeStatusInSimulator := processor6.NewUpdateAccountFreezeStatusInSimulator(actorClient, savingsClient, accountsDevSvcClient)
	internationalFundsTransferConfig := iftConfigProvider(cxConf)
	iftSlackAlertClient := iftSlackAlertClientProvider(cxConf)
	internationalFundTransferUploadLrsCheckFile := internationalfundtransfer2.NewInternationalFundTransferUploadLrsCheckFile(internationalFundsTransferConfig, iftSlackAlertClient, payFileGeneratorClient, iftFileProcessorEventPublisher)
	internationalFundTransferAcknowledgeSwiftTransfer := internationalfundtransfer2.NewInternationalFundTransferAcknowledgeSwiftTransfer(internationalFundsTransferConfig, iftSlackAlertClient, payFileGeneratorClient, iftFileProcessorEventPublisher)
	httpClient := getHttpClientForSherlock(cxConf)
	acknowledgeInwardSwiftTransfer := internationalfundtransfer2.NewAcknowledgeInwardSwiftTransfer(payFileGeneratorClient, ussOrderMgClient, celestialClient, httpClient, iftClient, genConf)
	payDownloadLrsCheckFile := internationalfundtransfer2.NewPayDownloadLrsCheckFile(payFileGeneratorClient, payClient)
	foreignRemittanceProcessInwardRemittance := internationalfundtransfer2.NewForeignRemittanceProcessInwardRemittance()
	updateAccountFreezeStatus := processor6.NewUpdateAccountFreezeStatus(actorClient, authClient, userClient, savingsClient, inappTargetedCommsClient, cxConf)
	createDiscount := processor6.NewCreateDiscount(discountClient)
	deleteDiscount := processor6.NewDeleteDiscount(discountClient)
	uploadUserIssueInforForAgents := processor6.NewUploadUserIssueInforForAgents(userIssueInfoClient)
	depositUpdateInterestRate := deposits.NewDepositUpdateInterestRate(depositClient)
	updateUserEmployment := processor6.NewUpdateUserEmployment(empClient, empFeClient)
	depositAddInterestRate := deposits.NewDepositAddInterestRate(depositClient)
	depositDeleteInterestRate := deposits.NewDepositDeleteInterestRate(depositClient)
	onbDetailsByPan := wealthonboarding2.NewOnbDetailsByPan(wOnbCxClient)
	profileEvaluatorUpdateRules := processor6.NewProfileEvaluatorUpdateRules(profileEvaluatorClient)
	verifyIncomeOccupationDiscrepancy := processor6.NewVerifyIncomeOccupationDiscrepancy(empClient)
	createReferralsSegmentedComponent := processor6.NewCreateReferralsSegmentedComponent(inAppReferralClient)
	updateReferralsSegmentedComponent := processor6.NewUpdateReferralsSegmentedComponent(inAppReferralClient)
	deleteReferralsSegmentedComponent := processor6.NewDeleteReferralsSegmentedComponent(inAppReferralClient)
	uploadRiskCases := processor6.NewUploadRiskCases(s3Client, genConf, riskCaseIngestionPublisher, caseManagementClient, vmClient)
	removeRedListEntry := processor6.NewRemoveRedListEntry(redlistClient, gormTxnExecutor)
	addRedListEntry := processor6.NewAddRedListEntry(redlistClient)
	p2PGetInvestorDashboard := p2pinvestment2.NewP2PGetInvestorDashboard(devP2PInvestmentClient)
	p2PGetInvestmentSummary := p2pinvestment2.NewP2PGetInvestmentSummary(devP2PInvestmentClient)
	p2PDownloadReconFile := p2pinvestment2.NewP2PDownloadReconFile(devP2PInvestmentClient)
	getSignedUrlWealth := wealthonboarding2.NewGetSignedUrlWealth(wOnbCxClient)
	usStocksRefreshStockDetails := usstocks2.NewUSStocksRefreshStockDetails(usstocksCatalogClient)
	amlReportUpload := processor6.NewAmlReportUpload(amlClient)
	deactivateDevice := processor6.NewDeactivateDevice(authClient, actorClient)
	createForexRate := internationalfundtransfer2.NewCreateForexRate(iftClient)
	updateForexRate := internationalfundtransfer2.NewUpdateForexRate(iftClient)
	reprieveVkyc := processor6.NewReprieveVkyc(vkycClient, commsClient, actorClient)
	usstocksGrossSummary := usstocks2.NewUsstocksGrossSummary(ussOrderMgClient)
	amlUpdateFileGenStatus := processor6.NewAmlUpdateFileGenStatus(amlClient)
	performActionForTxnReview := processor6.NewPerformActionForTxnReview(caseManagementClient)
	getDocumentFromBucket := mutualfund3.NewGetDocumentFromBucket(mfOpsClient, cxConf)
	forexRateReport := internationalfundtransfer2.NewForexRateReport(iftClient)
	currentForexRate := internationalfundtransfer2.NewCurrentForexRate(iftClient)
	wealthUploadDocument := wealthonboarding2.NewWealthUploadDocument(wOnbCxClient)
	updateP2PVendorResponseMaturityTransactionDaysToExpire := p2pinvestment2.NewUpdateP2PVendorResponseMaturityTransactionDaysToExpire(p2pSimulationServiceClient)
	createTemporalSchedule := processor6.NewCreateTemporalSchedule(temporalWorkflowServiceClient)
	getForexRates := internationalfundtransfer2.NewGetForexRates(iftClient)
	addHomePromoBannerElement := processor6.NewAddHomePromoBannerElement(inappTargetedCommsClient)
	usstocksAccountStatement := usstocks2.NewUsstocksAccountStatement(vgAccountsClient)
	paymentCrdb := dao17.NewPaymentCrdb(db)
	mutualFundRetryOrderFromStart := mutualfund3.NewMutualFundRetryOrderFromStart(fileGeneratorClient, investmentOrderManagerClient, paymentCrdb)
	riskBankActionManualOverride := processor6.NewRiskBankActionManualOverride(riskClient, genConf)
	createAllowedAnnotation := processor6.NewCreateAllowedAnnotation(caseManagementClient)
	vendorAccountPennyDrop := processor6.NewVendorAccountPennyDrop(userClient, extValidateClient, commsClient, cxConf, nameCheckClient)
	triggerKycNameDobValidation := processor6.NewTriggerKycNameDobValidation(userClient, vgEkycClient)
	onboardingTroubleshootingDetails := processor6.NewOnboardingTroubleshootingDetails(userClient, onbClient, actorClient, authClient, userGroupClient, vkycClient, kycClient)
	failBankCustomer := processor6.NewFailBankCustomer(bcClient)
	updatePendingUserDataWealth := wealthonboarding2.NewUpdatePendingUserDataWealth(wOnbCxClient)
	createTicketDetailsTransformation := processor6.NewCreateTicketDetailsTransformation(ticketClient, cxConf)
	updateTicketDetailsTransformation := processor6.NewUpdateTicketDetailsTransformation(ticketClient)
	deleteTicketDetailsTransformation := processor6.NewDeleteTicketDetailsTransformation(ticketClient)
	mfReconciliation := mutualfund3.NewMFReconciliation(mfReconciliationServiceClient)
	celestialProcessor := processor6.NewCelestialProcessor(celestialClient)
	uploadLeaComplaints := processor6.NewUploadLeaComplaints(riskClient, savingsClient, depositClient)
	createReferralNotificationConfig := referrals2.NewCreateReferralNotificationConfig(inAppReferralClient)
	updateReferralNotificationConfigStatus := referrals2.NewUpdateReferralNotificationConfigStatus(inAppReferralClient)
	updateReferralNotificationConfigContent := referrals2.NewUpdateReferralNotificationConfigContent(inAppReferralClient)
	markUsersByAcquisitionInfo := processor6.NewMarkUsersByAcquisitionInfo(userGroupClient)
	deleteReferralNotificationConfig := referrals2.NewDeleteReferralNotificationConfig(inAppReferralClient)
	addHomePopupBannerElement := processor6.NewAddHomePopupBannerElement(inappTargetedCommsClient)
	depositListAccountsVendor := deposits.NewDepositListAccountVendor(depositClient)
	palMarkLoanRequestCancel := processor6.NewPalMarkLoanRequestCancel(preapprovedloanCxClient)
	createProduct := processor6.NewCreateProduct(cmsServiceClient)
	createSku := processor6.NewCreateSku(cmsServiceClient)
	createCouponsInBulk := processor6.NewCreateCouponsInBulk(cmsServiceClient)
	aggrTaxReport := internationalfundtransfer2.NewAggrTaxReport(payFileGeneratorClient)
	createOrUpdateDynamicUIElementVariant := processor6.NewCreateOrUpdateDynamicUIElementVariant(dynamicUIElementServiceClient)
	updateDynamicUIElementEvaluatorConfig := processor6.NewUpdateDynamicUIElementEvaluatorConfig(dynamicUIElementServiceClient)
	markCardDeliveryTrackingStateReceived := processor6.NewMarkCardDeliveryTrackingStateReceived(cardCxClient)
	loanUpdateLoanStepStatus := processor6.NewLoanUpdateLoanStepStatus(preapprovedloanDevClient)
	addEmployers := processor6.NewAddEmployers(genConf, empClient)
	rejectOutwardSwiftFileTransactions := internationalfundtransfer2.NewRejectOutwardSwiftFileTransactions(iftClient)
	whiteListEmailId := processor6.NewWhiteListEmailId(actionClient)
	createKycAgent := processor6.NewCreateKycAgent(genConf, kycAgentPb, authClient, commsClient)
	deleteKycAgent := processor6.NewDeleteKycAgent(kycAgentPb)
	monorailApiWrapper := initialiseMonorailHttpClient(ctx, cxConf)
	airflowApiWrapper, err := initialiseAirflowHttpClient(cxConf)
	if err != nil {
		return nil, err
	}
	dataExtraction := processor6.NewDataExtraction(monorailApiWrapper, airflowApiWrapper, cxConf, s3Client)
	issueConfigDaoPGDB := dao18.NewIssueConfigDaoPGDB(dbconn, gormTxnExecutor)
	createIssueConfig := processor6.NewCreateIssueConfig(issueConfigDaoPGDB)
	updateRewardOffer := processor6.NewUpdateRewardOffer(rewardOfferClient)
	generateChatbotAccessToken := processor6.NewChatbotAccessTokenGenerator(userClient, actorClient, authClient)
	p2PRegisterBankingDetails := p2pinvestment2.NewP2PRegisterBankingDetails(devP2PInvestmentClient)
	annotationsHelper := annotations.NewAnnotationsHelper(caseManagementClient)
	uploadLEAComplaintNarrations := processor6.NewUploadLEAComplaintNarrations(riskClient, savingsClient, annotationsHelper)
	updateEmployerDetails := processor6.NewUpdateEmployerDetails(empClient)
	bulkSetupReferralSegmentedComponent := referrals2.NewBulkSetupReferralSegmentedComponent(inAppReferralClient, commsClient)
	setUserCommsPreference := processor6.NewSetUserCommsPreference(userClient)
	creditCardUpdateCardRequestStageStatus := processor7.NewCreditCardUpdateCardRequestStageStatus(ccCxClient)
	uploadLEAComplaintSources := processor6.NewUploadLEAComplaintSources(riskClient, savingsClient, depositClient, commsClient)
	simulateOutwardFundTransferNonProd := usstocks2.NewSimulateOutwardFundTransferNonProd(vgStocksClient)
	createFaqContextMapping := processor6.NewCreateFaqContextMapping(inAppHelpServingClient)
	p2PUpdateInvestmentTransaction := p2pinvestment2.NewP2PUpdateInvestmentTransaction(devP2PInvestmentClient)
	fetchAccountStatus := processor6.NewFetchAccountStatus(oprStatusServiceClient, savingsClient, commsClient)
	client2 := types2.CxRedisStoreProvider(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client2)
	createHomeSimulatorLayout := processor6.NewCreateHomeSimulatorLayout(userClient, redisCacheStorage)
	createBankRelationshipWithBroker := usstocks2.NewCreateBankRelationshipWithBroker(vgStocksClient)
	createWatsonTicketDetails := processor6.NewCreateWatsonTicketDetails(issueConfigDaoPGDB)
	updateWatsonTicketDetails := processor6.NewUpdateWatsonTicketDetails(issueConfigDaoPGDB)
	preApprovedLoanAbflDevAction := lending.NewPreApprovedLoanAbflDevAction(preApprovedLoanSimClient)
	eventConfigDaoImpl := dao19.NewEventConfigsDaoImpl(dbconn)
	issueCategoryDao := dao3.NewIssueCategoryDao(dbconn)
	issueCategoryManagerImpl := manager.NewIssueCategoryManagerImpl(issueCategoryDao)
	createEventConfig := processor6.NewCreateEventConfig(eventConfigDaoImpl, issueCategoryManagerImpl)
	updateEventConfig := processor6.NewUpdateEventConfig(eventConfigDaoImpl)
	mfFolioService := mutualfund3.NewMfFolioService(mfFolioMgClient)
	p2PGetInvestorCashLedger := p2pinvestment2.NewP2PGetInvestorCashLedger(vgP2pClient)
	initiateRefund := internationalfundtransfer2.NewInitiateRefund(celestialClient)
	raiseManualSalaryVerificationRequestsInBulk := processor6.NewRaiseManualSalaryVerificationRequestsInBulk(salaryClient, orderClient)
	simulateRewardGeneration := processor6.NewSimulateRewardGenerationService(rewardSimulatorClient)
	updateSalaryProgramReferralsSeason := processor6.NewUpdateSalaryProgramReferralsSeason(salaryReferralsClient)
	processItcPointsHandbackFile := casper2.NewProcessItcPointsHandbackFile(casperItcDownloadFileQueuePublisher)
	uploadDisputes := processor6.NewUploadDisputes(riskClient, savingsClient, commsClient, riskDisputeUploadPublisher)
	segmentMetadataAction := processor6.NewSegmentMetadataAction(segmentClient, dataS3Client, cxConf)
	setSegmentMetadataApprovalStatus := processor6.NewSetSegmentMetadataApprovalStatus(segmentClient, dataS3Client, cxConf, airflowApiWrapper)
	sofLimitManualOverride := usstocks2.NewSofLimitManualOverride(iftClient)
	addPinCodeEntry := processor6.NewCreatePinCodeDetails(onbClient)
	passRiskScreenerAttempt := processor6.NewPassRiskScreenerAttempt(riskClient)
	extractPanFromDocket := wealthonboarding2.NewExtractPanFromDocket(wOnbCxClient)
	activityMetadataDaoImpl := dao20.NewActivityMetadataDao(dbconn)
	createActivityMetadata := processor6.NewCreateActivityMetadata(activityMetadataDaoImpl, issueCategoryManagerImpl)
	riskManageWhiteList := userrisk.NewRiskManageWhiteList(whiteListClient)
	usstocksAddInvestorAddress := usstocks2.NewAddInvestorAddress(accountManagerClient)
	triggerTxnCategorization := processor6.NewTriggerTxnCategorization(orderClient, orderUpdatePublisher, ffAccountingClient, aaOrderClient, aaTxnPublisher, ccTxnPublisher)
	getUsStocksAccountActivitiesCsv := usstocks2.NewGetUsStocksAccountActivitiesCsv(ussOrderMgClient)
	triggerUnsecuredCCRenewalFeeReversal := processor7.NewTriggerUnsecuredCCRenewalFeeReversal(ffClient)
	generateAdHocInwardRemittanceFiles := internationalfundtransfer2.NewGenerateAdHocInwardRemittanceFiles(savingsClient, ussOrderMgClient)
	userQueryLogDaoImpl := dao8.NewUserQueryLogDao(dbconn)
	generateModelOutput := processor6.NewGenerateModelOutput(userClient, issueReportingClient, userQueryLogDaoImpl, commsClient)
	regenerateInwardFile := internationalfundtransfer2.NewRegenerateInwardFile(iftClient, savingsClient, ussOrderMgClient)
	mapDebitCardForexTxn := processor6.NewMapDebitCardForexTxn(cardCxClient)
	createJourney := processor6.NewCreateJourney(journeyClient)
	updateJourney := processor6.NewUpdateJourney(journeyClient)
	updateActivityMetadata := processor6.NewUpdateActivityMetadata(activityMetadataDaoImpl, issueCategoryManagerImpl)
	vkycCallTroubleshootClientWrapper := pkg.NewVkycCallTroubleshootClientWrapper(vkyccallTroubleshootCl, vkyccallTroubleshootCl2)
	vkycCallStateHandler := vkyccall2.NewVKYCCallStateHandler(vkycCallTroubleshootClientWrapper)
	uploadUnifiedLeaComplaints := processor6.NewUploadUnifiedLeaComplaints(leaRiskClient, commsClient)
	deleteServiceRequest := processor6.NewDeleteServiceRequest(alfredClient)
	failDebitCardRequest := processor6.NewFailDebitCardRequest(cardCxClient)
	usStocksTradingAccountSummary := usstocks2.NewUsStocksTradingAccountSummary(aggregatorClient)
	cacheContactUsModelResponse := processor6.NewCacheContactUsModelResponse(genConf, redisCacheStorage)
	b2BOnboardingStatusTracking := processor6.NewB2BOnboardingStatusTracking(userClient, onbClient, actorClient, userGroupClient, bcClient, savingsClient, vkycClient, oprStatusServiceClient, salaryClient, commsClient)
	updateDetails := usstocks2.NewUSStocksUpdateDetails(usstocksCatalogClient)
	downloadFilesByVendorOrderIds := mutualfund3.NewDownloadFilesByVendorOrderIds(mfOpsClient, cxConf)
	stockguardianCKYCImageRedaction := processor6.NewStockguardianCKYCImageRedaction(sgApiGwKycClient)
	leadMgmtDownloadFile := processor6.NewLeadMgmtDownloadFile(salaryb2bClient, cxConf)
	depositUpdateState := deposits.NewDepositUpdateState(depositClient)
	executeEmiMandate := stockguardian.NewExecuteEmiMandate(sgLmsClient)
	getSaClosureEligibility := processor6.NewGetSaClosureEligibility(saClosureClient, commsClient, genConf)
	createOrUpdateSPDynamicUIVariant := processor6.NewCreateOrUpdateSPDynamicUIVariant(spDynamicUIElementClient)
	creatingOrUpdatingNewSPDynamicUIEvaluatorConfig := processor6.NewCreatingOrUpdatingNewSPDynamicUIEvaluatorConfig(spDynamicUIElementClient)
	redListProcessor := processor6.NewRedListProcessor(redlistClient)
	sendEnachMandateNotificationCallback := processor6.NewSendEnachMandateNotificationCallback(rpFederalClient)
	b2BUserOnboardingStatusTrackingLimitedDetails := processor6.NewB2BUserOnboardingStatusTrackingLimitedDetails(userClient, onbClient, actorClient, userGroupClient, bcClient, savingsClient, vkycClient, oprStatusServiceClient, salaryClient, commsClient)
	updateLoansOutcallTicket := lending.NewUpdateLoansOutcallTicket(ticketClient)
	createSuggestedActionsForRule := processor6.NewCreateSuggestedActionsForRule(caseManagementClient)
	escalationDao := dao13.NewEscalationDao(dbconn)
	federalEscalationCreation := processor6.NewFederalEscalationCreation(publisher, federalEscalationAttachmentsS3Client, escalationDao, ticketClient, bcClient, savingsClient)
	createRuleReviewTypeMapping := processor6.NewCreateRuleReviewTypeMapping(caseManagementClient)
	b2BUnNameCheckFailureEmail := processor6.NewB2BUnNameCheckFailureEmail(userClient, onbClient, commsClient)
	refreshUserInfoFromPartnerBank := processor6.NewRefreshUserInfoFromPartnerBank(userClient, actorClient)
	deleteUserAssets := insights2.NewDeleteUserAssets(epfClient, mfExternalClient, networthClient, consentClient, investmentAnalyticsClient)
	uploadImageToEpifiIconsS3Bucket := processor6.NewUploadImageToEpifiIconsS3Bucket(epifiIconsS3Client)
	expireLOEC := processor6.NewExpireLOEC(preapprovedloanCxClient)
	opensearchClient := openSearchClientProvider(genConf)
	openSearchLogService := NewOpenSearchLogService(opensearchClient)
	fetchUtrsFromLogProcessor := mutualfund3.NewFetchUtrsFromLogProcessor(openSearchLogService)
	executeEmploymentVerification := stockguardian.NewExecuteEmploymentVerification(sgApplicationClient)
	resetLoanRequest := processor6.NewResetLoanRequest(preapprovedloanCxClient)
	getFilesFromBucket := insights3.NewGetFilesFromBucket(genConf, networthClient)
	ussValidator := validator.NewUSSValidator(savingsClient, riskProfileClient, userClient, actorClient)
	sellAllOpenPositions := usstocks2.NewSellAllOpenPositions(accountManagerClient, ussOrderMgClient, vgStocksClient, usstocksCatalogClient, ussValidator)
	withdrawAllUssWalletFunds := usstocks2.NewWithdrawAllUssWalletFunds(accountManagerClient, ussOrderMgClient, vgStocksClient, iftClient, ussValidator)
	devActionFactory := actions2.NewDevActionFactory(executeDisbursalRetry, retryLiveness, retryCreateAccount, retryCreateBankCustomer, retryEKYC, retryCkyc, createCard, createRewardOffer, createRewardOfferInBulk, updateRewardOfferStatus, createLuckyDrawCampaign, createLuckyDraw, pullAppLogs, addUserGroupMapping, deleteUser, recoverAuthFactorUpdate, updateRewardOfferDisplay, indexActorPis, indexActorGmailData, createOffer, createOffersInBulk, createOfferInventory, addOfferToInventory, deleteOfferInventory, createOfferListing, createOrUpdateMediaPlaylistSegmentMapping, createExchangerOfferInventory, incrementExchangerOfferInventory, updateOfferListing, deleteOfferListing, reconProcessor, updateOfferDisplayRank, unlinkGmailAccount, syncOnb, createRule, publishSharkTankEvent, updateRule, bulkUpdateSubscriptionsState, archiveRulesAndSubscriptions, fitCreateSchedule, fitStopSchedules, updateRulesWeightage, getRulesForClient, updateOnboardingStage, processNonResidentCrossValidationManualReview, passOnboardingStage, passportManualReview, updateOfferDisplay, initiateMatchUpdate, initiateCardNotifications, markLiveNessPassed, markFaceMatchPassed, updateShippingAddressAtVendor, createShippingPreference, retryPayOrderRMSEvent, sendRewardsCampaignComm, unredactedUser, orderProcessor, retryRewardProcessing, getTxnAggrFit, updateUserProfileName, accountStatement, onboardingSnapshot, backFillFreshdeskTicketContacts, createRewardOfferGroup, resetKYCNameDobRetry, retryOfferRedemption, manualScreeningUpdate, resetDebitCardNameRetry, unblockUnNameCheckUser, refreshVKYCStatus, updateCardPinSet, raiseAaConsent, forceCardCreationEnquiry, triggerRewardsManualGiveawayEvent, deleteUserGroupMapping, syncWealthOnboarding, unlockInAppReferral, triggerVkycCallback, handleSavingsAccountClosure, updateGmailInsightsMerchants, updateGmailMerchantQueries, updateInAppHelpFAQ, aaConsentStatusUpdate, aaAccountDeLink, reactivateDevice, reopenClosedSavingsAccount, updateWOnbStatus, fitttCreateHomeCard, fitttUpdateHomeCard, updatePanReview, addMediaPlaylist, updateMediaPlaylist, addMediaContentStory, updateMediaContentStory, addUIContextToMediaPlaylistMapping, deleteUIContextToMediaPlaylistMapping, addMediaPlaylistToMediaContentMapping, deleteMediaPlaylistToMediaContentMapping, createMutualFund, updateMutualFund, addCreditMISFileMetaData, mutualFundReverseFeedFileUpload, aaReplayAccountEvent, createExchangerOffer, createExchangerOfferGroup, createExchangerOfferListing, updateExchangerOfferDisplay, updateExchangerOfferStatus, updateExchangerOfferListing, deleteExchangerOfferListing, aaReplayTxnEvent, parseQueryBase, forceProcessDepositRequest, markWealthLivenessPassed, mutualFundDeactivateEntityFromFile, fitttCreateCollection, fitttUpdateCollection, markWealthRedactionPassed, markWealthExpiryPassed, updateMutualFundOrderStatus, triggerRecurringPaymentExecution, uploadMarketingCampaignUsersList, mutualFundDownloadCreditMISReport, addInAppTargetedCommsElement, updateInAppTargetedCommsElement, mfRetriggerPrerequisites, mfDownloadOpsFile, updateInsightFramework, updateInsightSegment, updateInsightContentTemplate, mfUploadCatalogUpdate, mfUploadCreditMISNonProd, updateRewardOfferDisplayRank, addUserToVKYCPriority, updateInvTranStatus, manualCardUnsuspend, updateTotalInvestmentCount, updateUserPhoto, mfProcessReverseFeedFile, mfCreateCollection, mfAddFundToCollection, mfRemoveFundsFromCollection, mfUpdateFundInCollectionFundMapping, createCollection, addStockToCollection, removeStockFromCollection, updateStockInCollection, updateCollection, resetOnboardingData, updateP2PVendorResponsesApprovalStatus, mfUpdateCollection, physicalCardRequest, createReferralsSeason, updateReferralsSeason, deleteSegment, markStepStaleWealthOnboarding, requestNewCard, triggerVPACreation, addManualCallRoutingMappings, triggerSegmentExport, createSegment, updateSegment, deepLinkBase64Encoder, creditCardUpdateCardRequestStatus, preApprovedLoanManualReview, preApprovedLoanCreateOffer, preApprovedLoanUpdateStatus, preApprovedLoanLlEntityStatus, categoriseScreenerDomains, createSalaryProgramReferralsSeason, startUserAction, savingsRiskBankAction, ussGetDocumentFromBucket, createNudge, createNudgesInBulk, editNudge, updateNudgeStatus, updateUserFatherName, updateAccountFreezeStatusInSimulator, internationalFundTransferUploadLrsCheckFile, internationalFundTransferAcknowledgeSwiftTransfer, acknowledgeInwardSwiftTransfer, payDownloadLrsCheckFile, foreignRemittanceProcessInwardRemittance, updateAccountFreezeStatus, createDiscount, deleteDiscount, uploadUserIssueInforForAgents, depositUpdateInterestRate, updateUserEmployment, depositAddInterestRate, depositDeleteInterestRate, onbDetailsByPan, profileEvaluatorUpdateRules, verifyIncomeOccupationDiscrepancy, createReferralsSegmentedComponent, updateReferralsSegmentedComponent, deleteReferralsSegmentedComponent, uploadRiskCases, removeRedListEntry, addRedListEntry, p2PGetInvestorDashboard, p2PGetInvestmentSummary, p2PDownloadReconFile, getSignedUrlWealth, usStocksRefreshStockDetails, amlReportUpload, deactivateDevice, createForexRate, updateForexRate, reprieveVkyc, usstocksGrossSummary, amlUpdateFileGenStatus, performActionForTxnReview, getDocumentFromBucket, forexRateReport, currentForexRate, wealthUploadDocument, updateP2PVendorResponseMaturityTransactionDaysToExpire, createTemporalSchedule, getForexRates, addHomePromoBannerElement, usstocksAccountStatement, mutualFundRetryOrderFromStart, riskBankActionManualOverride, createAllowedAnnotation, vendorAccountPennyDrop, triggerKycNameDobValidation, onboardingTroubleshootingDetails, failBankCustomer, updatePendingUserDataWealth, createTicketDetailsTransformation, updateTicketDetailsTransformation, deleteTicketDetailsTransformation, mfReconciliation, celestialProcessor, uploadLeaComplaints, createReferralNotificationConfig, updateReferralNotificationConfigStatus, updateReferralNotificationConfigContent, markUsersByAcquisitionInfo, deleteReferralNotificationConfig, addHomePopupBannerElement, depositListAccountsVendor, palMarkLoanRequestCancel, createProduct, createSku, createCouponsInBulk, aggrTaxReport, createOrUpdateDynamicUIElementVariant, updateDynamicUIElementEvaluatorConfig, markCardDeliveryTrackingStateReceived, loanUpdateLoanStepStatus, addEmployers, rejectOutwardSwiftFileTransactions, whiteListEmailId, createKycAgent, deleteKycAgent, dataExtraction, createIssueConfig, updateRewardOffer, generateChatbotAccessToken, p2PRegisterBankingDetails, uploadLEAComplaintNarrations, updateEmployerDetails, bulkSetupReferralSegmentedComponent, setUserCommsPreference, creditCardUpdateCardRequestStageStatus, uploadLEAComplaintSources, simulateOutwardFundTransferNonProd, createFaqContextMapping, p2PUpdateInvestmentTransaction, fetchAccountStatus, createHomeSimulatorLayout, createBankRelationshipWithBroker, createWatsonTicketDetails, updateWatsonTicketDetails, preApprovedLoanAbflDevAction, createEventConfig, updateEventConfig, mfFolioService, p2PGetInvestorCashLedger, initiateRefund, raiseManualSalaryVerificationRequestsInBulk, simulateRewardGeneration, updateSalaryProgramReferralsSeason, processItcPointsHandbackFile, uploadDisputes, segmentMetadataAction, setSegmentMetadataApprovalStatus, sofLimitManualOverride, addPinCodeEntry, passRiskScreenerAttempt, extractPanFromDocket, createActivityMetadata, riskManageWhiteList, usstocksAddInvestorAddress, triggerTxnCategorization, getUsStocksAccountActivitiesCsv, triggerUnsecuredCCRenewalFeeReversal, generateAdHocInwardRemittanceFiles, generateModelOutput, regenerateInwardFile, mapDebitCardForexTxn, createJourney, updateJourney, updateActivityMetadata, vkycCallStateHandler, uploadUnifiedLeaComplaints, deleteServiceRequest, failDebitCardRequest, usStocksTradingAccountSummary, cacheContactUsModelResponse, b2BOnboardingStatusTracking, updateDetails, downloadFilesByVendorOrderIds, stockguardianCKYCImageRedaction, leadMgmtDownloadFile, depositUpdateState, executeEmiMandate, getSaClosureEligibility, createOrUpdateSPDynamicUIVariant, creatingOrUpdatingNewSPDynamicUIEvaluatorConfig, redListProcessor, sendEnachMandateNotificationCallback, b2BUserOnboardingStatusTrackingLimitedDetails, updateLoansOutcallTicket, createSuggestedActionsForRule, federalEscalationCreation, createRuleReviewTypeMapping, b2BUnNameCheckFailureEmail, refreshUserInfoFromPartnerBank, deleteUserAssets, uploadImageToEpifiIconsS3Bucket, expireLOEC, fetchUtrsFromLogProcessor, executeEmploymentVerification, resetLoanRequest, getFilesFromBucket, sellAllOpenPositions, withdrawAllUssWalletFunds)
	devHelper := helper8.NewDevHelper(casbinClient, genConf)
	devActions := actions2.NewDevActions(devActionFactory, devHelper, genConf)
	return devActions, nil
}

func InitializeDevActionsCLIRegistryService(userClient user.UsersClient, userGroupClient group.GroupClient, preApprovedLoanClient preapprovedloan2.PreApprovedLoanClient, palDevClient developer36.DevPreApprovedLoanClient) (*actions3.DevActionCLIRegistry, error) {
	addUserGroupMapping := processor6.NewAddUserGroupMapping(userGroupClient)
	addUserGroupMappingDevAction := actions3.NewAddUserGroupMappingDevAction(addUserGroupMapping)
	preApprovedLoanCreateOffer := processor6.NewPreApprovedLoanCreateOffer(preApprovedLoanClient, palDevClient)
	actionsPreApprovedLoanCreateOffer := actions3.NewPreApprovedLoanCreateOffer(preApprovedLoanCreateOffer)
	devActionCLIRegistry := actions3.NewDevActionRegistry(addUserGroupMappingDevAction, actionsPreApprovedLoanCreateOffer)
	return devActionCLIRegistry, nil
}

func InitializeDevActionConsumerService(db types.SherlockPGDB, ticketPublisher types2.FreshdeskTicketPublisher, fdClient freshdesk.FreshdeskClient) *consumer5.Service {
	freshdeskUpdateEventDAO := dao4.NewFreshdeskUpdateEventDAO(db)
	fdTicketContactUpdate := event_processors.NewFDTicketContactUpdate(fdClient, freshdeskUpdateEventDAO, ticketPublisher)
	eventFactory := consumer5.NewEventFactory(fdTicketContactUpdate)
	service := consumer5.NewDevActionConsumerService(eventFactory)
	return service
}

// config: {"sherlockS3Client": "LivenessVideoConfig().S3BucketName"}
func InitializeLivenessVideoService(ctx context.Context, lClient liveness.LivenessClient, casbinClient casbin.CasbinClient, sherlockS3Client types2.LivenessVideoConfigS3Client, conf *config.Config, dbConn types.SherlockPGDB) *liveness_video.Service {
	authFunction := getAuthFunc(conf)
	accessControlFunction := accessControlFuncProvider()
	getCxDescriptor := getCxDescriptorProvider()
	casbinAuthorizationService := sherlock_auth.NewCasbinAuthorizationService(casbinClient)
	client := getCognitoIDPClient(ctx, conf)
	string2 := getCognitoUserPoolId(conf)
	authValidation := getAuthValidationConfig(conf)
	cognitoAuthService := sherlock_auth.NewCognitoAuthService(client, string2, authValidation)
	auditLog := getAuditLog(conf)
	auditLogDao := dao10.NewAuditLogDao(dbConn, auditLog)
	service := audit_log.NewAuditLogService(auditLogDao, auditLog)
	s3Client := types2.LivenessVideoConfigS3ClientProvider(sherlockS3Client)
	liveness_videoService := liveness_video.NewLivenessVideoService(lClient, authFunction, accessControlFunction, getCxDescriptor, casbinAuthorizationService, cognitoAuthService, service, s3Client, conf)
	return liveness_videoService
}

func InitializeTicketService(ctx context.Context, fdClient freshdesk.FreshdeskClient, casbinClient casbin.CasbinClient, db types.SherlockPGDB, paymentClient payment.PaymentClient, orderClient order.OrderServiceClient, updateTicketPublisher types2.UpdateTicketPublisher, createTicketPublisher types2.CreateTicketPublisher, gconf *genconf.Config, userClient user.UsersClient, actorClient actor.ActorClient, chatClient chat.ChatsClient, vmClient vendormapping.VendorMappingServiceClient, cxConf *config.Config, redisClient types2.CxRedisStore, disputeExtPub types2.DisputeExternalPublisher, piClient paymentinstrument.PiClient, userGroupClient group.GroupClient, createDisputeTicketPub types2.DisputeCreateTicketPublisher, savingsClient savings.SavingsClient, commsClient comms.CommsClient, vgDisputeClient dispute.DisputeClient, eventBroker events.Broker, ticketClient ticket.TicketClient) (*ticket2.Service, error) {
	s3RecordingClient, err := getS3CallRecordingClient(ctx, cxConf)
	if err != nil {
		return nil, err
	}
	s3TranscriptClient, err := getS3TranscriptionClient(ctx, cxConf)
	if err != nil {
		return nil, err
	}
	authFunction := getAuthFunc(cxConf)
	accessControlFunction := accessControlFuncProvider()
	getCxDescriptor := getCxDescriptorProvider()
	casbinAuthorizationService := sherlock_auth.NewCasbinAuthorizationService(casbinClient)
	client := getCognitoIDPClient(ctx, cxConf)
	string2 := getCognitoUserPoolId(cxConf)
	authValidation := getAuthValidationConfig(cxConf)
	cognitoAuthService := sherlock_auth.NewCognitoAuthService(client, string2, authValidation)
	auditLog := getAuditLog(cxConf)
	auditLogDao := dao10.NewAuditLogDao(db, auditLog)
	service := audit_log.NewAuditLogService(auditLogDao, auditLog)
	supportTicketDao := dao7.NewSupportTicketDao(db)
	sherlockUserInfoDao := dao5.NewSherlockUserInfoDao(db)
	sherlockUserRoleDao := dao5.NewSherlockUserRoleDao(db)
	gormDB := GormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	sherlockUser := sherlock_user_wrapper.NewSherlockUserDao(sherlockUserInfoDao, sherlockUserRoleDao, gormTxnExecutor)
	bulkTicketJobDao := dao7.NewBulkTicketJobDao(db)
	ticketFailureLogDao := dao7.NewTicketFailureLogDao(db)
	bulkTicketJobConfig := getBulkTicketJobConfig(cxConf)
	ticketConfig := getDynTicketConfig(gconf)
	issueConfigDaoPGDB := dao18.NewIssueConfigDaoPGDB(db, gormTxnExecutor)
	ticketHelper := helper9.NewTicketHelper(paymentClient, orderClient, supportTicketDao, cxConf, issueConfigDaoPGDB)
	disputeDao := dao12.NewDisputeDao(db)
	genconfDispute := getDynDispute(gconf)
	disputeConfigDao := dao12.NewDisputeConfigDao(db)
	customerIdentifier := helper2.NewCustomerIdentifier(userClient, actorClient, chatClient, vmClient)
	federalDmpDisputeDetailDao := dao12.NewFederalDmpDisputeDetailDao(db)
	disputeHelper := helper6.NewDisputeHelper(disputeExtPub, disputeDao, paymentClient, piClient, orderClient, actorClient, userClient, userGroupClient, genconfDispute, disputeConfigDao, createDisputeTicketPub, customerIdentifier, savingsClient, federalDmpDisputeDetailDao, gormTxnExecutor, commsClient, gconf, vgDisputeClient)
	transactionsProcessor := processor8.NewTransactionsProcessor(ticketHelper, disputeHelper)
	savingsProcessor := processor8.NewSavingsProcessor()
	rewardsProcessor := processor8.NewRewardsProcessor()
	actorActivityProcessor := processor8.NewActorActivityProcessor()
	attachEntityFactory := processor8.NewAttachEntityFactory(transactionsProcessor, savingsProcessor, rewardsProcessor, actorActivityProcessor)
	ticketDetailsTransformationDao := dao7.NewTicketDetailsTransformationDao(db)
	client2 := NewRedisClient(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client2)
	issueCategoryDao := dao3.NewIssueCategoryDao(db)
	issueCategoryManagerImpl := manager.NewIssueCategoryManagerImpl(issueCategoryDao)
	userQueryLogDaoImpl := dao8.NewUserQueryLogDao(db)
	featureReleaseConfig := getDynFeatureReleaseConfig(gconf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	inAppCsatResponsesDao := dao7.NewInAppCsatResponsesDao(db)
	csatConfig := csatConfigProvider(gconf)
	resolutionCsatEvaluator := csat.NewResolutionCsatEvaluator(supportTicketDao, issueConfigDaoPGDB, inAppCsatResponsesDao, csatConfig)
	jwtTokenManager := token_manager.NewJwtTokenManager()
	ticketService := ticket2.NewTicketService(fdClient, s3RecordingClient, s3TranscriptClient, authFunction, accessControlFunction, getCxDescriptor, casbinAuthorizationService, cognitoAuthService, service, supportTicketDao, sherlockUser, bulkTicketJobDao, ticketFailureLogDao, updateTicketPublisher, createTicketPublisher, bulkTicketJobConfig, ticketConfig, attachEntityFactory, ticketHelper, customerIdentifier, ticketDetailsTransformationDao, cxConf, redisCacheStorage, issueCategoryManagerImpl, disputeHelper, eventBroker, userQueryLogDaoImpl, issueConfigDaoPGDB, commsClient, userClient, ticketClient, evaluator, resolutionCsatEvaluator, jwtTokenManager, inAppCsatResponsesDao)
	return ticketService, nil
}

func InitializeTicketConsumerHelper(db types.SherlockPGDB, paymentClient payment.PaymentClient, orderClient order.OrderServiceClient, conf *config.Config) *helper9.TicketHelper {
	supportTicketDao := dao7.NewSupportTicketDao(db)
	gormDB := GormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	issueConfigDaoPGDB := dao18.NewIssueConfigDaoPGDB(db, gormTxnExecutor)
	ticketHelper := helper9.NewTicketHelper(paymentClient, orderClient, supportTicketDao, conf, issueConfigDaoPGDB)
	return ticketHelper
}

func InitializeCxDevService(db types.SherlockPGDB, redisClient types2.CxRedisStore, cxGenConf *genconf.Config, riskProfileClient profile3.ProfileClient) *developer64.CXDev {
	disputeConfigDao := dao12.NewDisputeConfigDao(db)
	devDisputeConfig := processor9.NewDevDisputeConfig(disputeConfigDao)
	disputeDao := dao12.NewDisputeDao(db)
	disputeTicketLogDao := dao12.NewDisputeTicketLogDao(db)
	devDispute := processor9.NewDevDispute(disputeDao, disputeTicketLogDao)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(db)
	devCustomerAuth := processor9.NewDevCustomerAuth(customerAuthenticationV2DAO)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(db)
	authFactorRetryLogsDAO := dao.NewAuthFactorRetryLogsDAO(db)
	callbackResponseDAO := dao.NewCallbackResponseDAO(db)
	devAuthFactor := processor9.NewDevAuthFactor(authFactorStatesDAO, authFactorRetryLogsDAO, callbackResponseDAO)
	freshdeskUpdateEventDAO := dao4.NewFreshdeskUpdateEventDAO(db)
	devFreshdeskConsumer := processor9.NewDevFreshdeskConsumer(freshdeskUpdateEventDAO)
	devDisputeManualIntervention := processor9.NewDevDisputeManualIntervention(disputeDao, disputeTicketLogDao)
	supportTicketDao := dao7.NewSupportTicketDao(db)
	devSupportTicketDetails := processor9.NewDevSupportTicketDetails(supportTicketDao)
	callDetailsDao := dao6.NewCallDetailsDao(db)
	devCallDetails := processor9.NewDevCallDetails(callDetailsDao)
	ticketDetailsTransformationDao := dao7.NewTicketDetailsTransformationDao(db)
	devTicketDetailsTransformations := processor9.NewDevTicketDetailsTransformations(ticketDetailsTransformationDao)
	issueResolutionFeedbackDao := dao21.NewIssueResolutionFeedbackDao(db)
	devIssueResolutionFeedbacks := processor9.NewDevIssueResolutionFeedbacks(issueResolutionFeedbackDao)
	issueResolutionUserResponseLogDao := dao21.NewIssueResolutionUserResponseLogDao(db)
	devIssueResolutionUserResponses := processor9.NewDevIssueResolutionUserResponses(issueResolutionUserResponseLogDao)
	payoutRequestDAO := dao15.NewPayoutRequestDAO(db)
	devPayoutDetails := processor9.NewDevPayoutDetails(payoutRequestDAO)
	freshchatUserMappingDao := dao4.NewFreshchatUserMappingDao(db)
	devFreshchatUserMappings := processor9.NewDevFreshchatUserMappings(freshchatUserMappingDao)
	sprinklrCaseDetailsDao := dao11.NewSprinklrCaseDetailsDao(db)
	devSprinklrCaseDetails := processor9.NewDevSprinklrCaseDetails(sprinklrCaseDetailsDao)
	incidentDao := dao2.NewIncidentDao(db)
	devWatsonIncident := processor9.NewDevWatsonIncident(incidentDao)
	incidentTicketDetailDao := dao2.NewIncidentTicketDetailDao(db)
	devWatsonIncidentTicketDetail := processor9.NewDevWatsonIncidentTicketDetail(incidentTicketDetailDao)
	incidentCommsDetailDao := dao2.NewIncidentCommsDetailDao(db)
	devWatsonIncidentCommsDetail := processor9.NewDevWatsonIncidentCommsDetail(incidentCommsDetailDao)
	cxCacheStore := CxCacheStorageProvider(redisClient)
	actorLogMappingDao := dao14.NewActorLogMappingDao(cxCacheStore, cxGenConf)
	devActorAppLogMapping := processor9.NewDevActorAppLogMapping(actorLogMappingDao)
	crmToIssueTrackerMappingDao := dao22.NewCrmToIssueTrackerMappingDao(db)
	devCrmToIssueTrackerMappings := processor9.NewDevCrmToIssueTrackerMappings(crmToIssueTrackerMappingDao)
	devDisputeTicketLog := processor9.NewDevDisputeTicketLog(disputeTicketLogDao)
	disputeNotificationLogDao := dao12.NewDisputeNotificationLogDao(db)
	devDisputeNotificationLog := processor9.NewDevDisputeNotificationLog(disputeNotificationLogDao)
	disputeCorrespondenceDetailDao := dao12.NewDisputeCorrespondenceDetailDao(db)
	devDisputeCorrespondenceDetail := processor9.NewDevDisputeCorrespondenceDetail(disputeCorrespondenceDetailDao)
	disputeDocumentDetailDao := dao12.NewDisputeDocumentDetailDao(db)
	devDisputeDocumentDetail := processor9.NewDevDisputeDocumentDetail(disputeDocumentDetailDao)
	disputeIdToTicketIdMappingDao := dao12.NewDisputeIdToTicketIdMappingDao(db)
	devDisputeIdToTicketIdMapping := processor9.NewDevDisputeIdToTicketIdMapping(disputeIdToTicketIdMappingDao)
	federalDmpDisputeDetailDao := dao12.NewFederalDmpDisputeDetailDao(db)
	devFederalDmpDisputeDetail := processor9.NewDevFederalDmpDisputeDetail(federalDmpDisputeDetailDao)
	issueCategoryDao := dao3.NewIssueCategoryDao(db)
	devIssueCategory := processor9.NewDevIssueCategory(issueCategoryDao)
	gormDB := GormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	issueConfigDaoPGDB := dao18.NewIssueConfigDaoPGDB(db, gormTxnExecutor)
	devIssueConfig := processor9.NewDevIssueConfig(issueConfigDaoPGDB)
	callIvrDetailsDaoImpl := dao23.NewCallIvrDetailsDaoImpl(db)
	callIVRDetails := processor9.NewCallIVRDetails(callIvrDetailsDaoImpl)
	activityMetadataDaoImpl := dao20.NewActivityMetadataDao(db)
	activityMetaData := processor9.NewActivityMetaData(activityMetadataDaoImpl)
	escalationDao := dao13.NewEscalationDao(db)
	devGetFederalEscalation := processor9.NewDevGetFederalEscalation(escalationDao)
	escalationUpdateDao := dao13.NewEscalationUpdateDao(db)
	devGetFederalEscalationUpdates := processor9.NewDevGetFederalEscalationUpdates(escalationDao, escalationUpdateDao)
	escalationAttachmentDao := dao13.NewEscalationAttachmentDao(db)
	devGetFederalEscalationAttachments := processor9.NewDevGetFederalEscalationAttachments(escalationAttachmentDao)
	devNuggetResourceAPI := processor9.NewDevNuggetResourceAPI(riskProfileClient)
	devFactory := developer64.NewDevFactory(devDisputeConfig, devDispute, devCustomerAuth, devAuthFactor, devFreshdeskConsumer, devDisputeManualIntervention, devSupportTicketDetails, devCallDetails, devTicketDetailsTransformations, devIssueResolutionFeedbacks, devIssueResolutionUserResponses, devPayoutDetails, devFreshchatUserMappings, devSprinklrCaseDetails, devWatsonIncident, devWatsonIncidentTicketDetail, devWatsonIncidentCommsDetail, devActorAppLogMapping, devCrmToIssueTrackerMappings, devDisputeTicketLog, devDisputeNotificationLog, devDisputeCorrespondenceDetail, devDisputeDocumentDetail, devDisputeIdToTicketIdMapping, devFederalDmpDisputeDetail, devIssueCategory, devIssueConfig, callIVRDetails, activityMetaData, devGetFederalEscalation, devGetFederalEscalationUpdates, devGetFederalEscalationAttachments, devNuggetResourceAPI)
	cxDev := developer64.NewCXDev(devFactory)
	return cxDev
}

func InitializeRateLimiterService(redisClient types2.CxRedisStore, rlClient ratelimiter.RateLimiter, genConf *genconf.Config) *rate_limit.RedisRateLimiter {
	client := NewRedisClient(redisClient)
	redisRateLimiter := rate_limit.NewRedisRateLimiterService(client, rlClient, genConf)
	return redisRateLimiter
}

func InitializeFederalService(fdClient freshdesk.FreshdeskClient) *federal3.Service {
	service := federal3.NewFederalService(fdClient)
	return service
}

func InitializeFitttService(config2 *config.Config, genConfig *genconf.Config, dbConn types.SherlockPGDB, userClient user.UsersClient, fitttClient fittt.FitttClient, rmsClient manager2.RuleManagerClient, usStocksCatalogManagerClient catalog2.CatalogManagerClient) *fittt3.Service {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(dbConn)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(dbConn)
	authFactorRetryLimit := getAuthFactoryRetryLimit(config2)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(dbConn)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConfig, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	usStocks := fittt4.NewUSStocks(usStocksCatalogManagerClient)
	service := fittt3.NewService(fitttClient, authEngine, rmsClient, usStocks)
	return service
}

// config: {"cxS3Client": "CxS3Config().BucketName"}
func InitializeAdminActionService(chatClient chat.ChatsClient, vmClient vendormapping.VendorMappingServiceClient, db types.SherlockPGDB, userClient user.UsersClient, authClient auth.AuthClient, actorClient actor.ActorClient, ticketPublisher types2.FreshdeskTicketPublisher, obClient onboarding.OnboardingClient, kycClient kyc.KycClient, casbinClient casbin.CasbinClient, cardProvisioningClient provisioning.CardProvisioningClient, freshdeskClient freshdesk.FreshdeskClient, redisClient types2.CxRedisStore, genConfig *genconf.Config, commsClient comms.CommsClient, cxS3Client types2.CxS3Client, conf *config.Config) *admin_actions.AdminActionsService {
	revokeUserAccess := processor10.NewRevokeUserAccess(actorClient, authClient, userClient)
	resetKYCNameDobRetry := processor10.NewResetKYCNameDobRetry(kycClient)
	resetUser := processor10.NewResetUser(obClient)
	resetLivenessFmRetry := processor10.NewResetLivenessFmRetry(kycClient)
	resetDebitCardNameRetry := processor10.NewResetDebitCardNameRetry(obClient)
	refreshUserInfoFromPartnerBank := processor10.NewRefreshUserInfoFromPartnerBank(userClient, actorClient)
	manualScreeningUpdate := processor10.NewManualScreeningUpdate(obClient)
	createCard := processor10.NewCreateCard(cardProvisioningClient)
	updateUserParentsName := processor10.NewUpdateUserParentsName(userClient)
	setUserCommsPreference := processor10.NewSetUserCommsPreference(userClient)
	cxCacheStore := CxCacheStorageProvider(redisClient)
	s3Client := types2.CxS3ClientProvider(cxS3Client)
	appLogDao := dao14.NewAppLogDao(cxCacheStore, genConfig, s3Client)
	actorLogMappingDao := dao14.NewActorLogMappingDao(cxCacheStore, genConfig)
	logIdDao := dao14.NewLogIdDao(cxCacheStore, genConfig)
	appLog := getDynAppLog(genConfig)
	customerIdentifier := helper2.NewCustomerIdentifier(userClient, actorClient, chatClient, vmClient)
	appLogsNotificationContent := getAppLogsNotificationContent(conf)
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(db)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(db)
	authFactorRetryLimit := getAuthFactoryRetryLimit(conf)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(db)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConfig, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	service := app_log.NewAppLogService(appLogDao, actorLogMappingDao, logIdDao, appLog, genConfig, customerIdentifier, commsClient, appLogsNotificationContent, authEngine)
	pullAppLogs := processor10.NewPullAppLogs(service)
	adminActionFactory := admin_actions.NewAdminActionFactory(revokeUserAccess, resetKYCNameDobRetry, resetUser, resetLivenessFmRetry, resetDebitCardNameRetry, refreshUserInfoFromPartnerBank, manualScreeningUpdate, createCard, updateUserParentsName, setUserCommsPreference, pullAppLogs)
	client := NewRedisClient(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	ticketValidation := validation.NewTicketValidation(freshdeskClient, redisCacheStorage, conf)
	freshdeskUpdateEventDAO := dao4.NewFreshdeskUpdateEventDAO(db)
	adminActionHelper := helper10.NewAdminActionHelper(casbinClient)
	callDetailsDao := dao6.NewCallDetailsDao(db)
	adminActionsService := admin_actions.NewAdminActionsService(adminActionFactory, customerIdentifier, ticketValidation, freshdeskUpdateEventDAO, ticketPublisher, adminActionHelper, callDetailsDao)
	return adminActionsService
}

func InitializeReferralService(actorClient actor.ActorClient, chatClient chat.ChatsClient, vmClient vendormapping.VendorMappingServiceClient, refClient inappreferral.InAppReferralClient, rewardsClient rewards.RewardsGeneratorClient, onboardingClient onboarding.OnboardingClient, savingsClient savings.SavingsClient, orderTxnClient cx2.CXClient, config2 *config.Config, genConfig *genconf.Config, dbConn types.SherlockPGDB, userClient user.UsersClient) *referrals3.Service {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(dbConn)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(dbConn)
	authFactorRetryLimit := getAuthFactoryRetryLimit(config2)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(dbConn)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConfig, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	customerIdentifier := helper2.NewCustomerIdentifier(userClient, actorClient, chatClient, vmClient)
	referralConfig := getReferralConfig(config2)
	service := referrals3.NewReferralService(authEngine, refClient, rewardsClient, customerIdentifier, onboardingClient, savingsClient, orderTxnClient, referralConfig)
	return service
}

func InitializeFITDevConsoleService(fitDevClient devconsolepb.DeveloperConsoleServiceClient) *devconsole.Service {
	service := devconsole.NewService(fitDevClient)
	return service
}

func InitializeCallRoutingService(chatClient chat.ChatsClient, vmClient vendormapping.VendorMappingServiceClient, onboardingClient onboarding.OnboardingClient, userGroupClient group.GroupClient, salaryProgramClient salaryprogram.SalaryProgramClient, externalAccountsClient extacct.ExternalAccountsClient, dbConn types.SherlockPGDB, conf *genconf.Config, segmentClient segment.SegmentationServiceClient, eventsBroker events.Broker, ffClient firefly.FireflyClient, actorClient actor.ActorClient, piClient paymentinstrument.PiClient, usersClient user.UsersClient, pClient payment.PaymentClient, orderClient order.OrderServiceClient, savingsClient savings.SavingsClient, depositClient deposit.DepositClient, investmentCatalogManagerClient catalog.CatalogManagerClient, p2pInvestmentClient p2pinvestment.P2PInvestmentClient, portfolioManagerClient portfolio.PortfolioManagerClient, usstocksOrderManagerClient order2.OrderManagerClient, accountBalanceClinet balance.BalanceClient, riskProfileClient profile3.ProfileClient, commsClient comms.CommsClient, productClient product.ProductClient, callRoutingEventPublisher types2.CallRoutingEventPublisher, redisClient types2.CxRedisStore, tieringClient tiering.TieringClient, ticketClient ticket.TicketClient, preApprovedLoanClient preapprovedloan2.PreApprovedLoanClient, compClient compliance.ComplianceClient) *call_routing.Service {
	callRoutingManualMappingsDao := dao16.NewCallRoutingManualMappingsDao(dbConn)
	dataCollectorHelper := helper.NewDataCollectorHelper(actorClient, piClient, usersClient, pClient, orderClient, savingsClient, depositClient, investmentCatalogManagerClient, p2pInvestmentClient, portfolioManagerClient, usstocksOrderManagerClient, accountBalanceClinet, ffClient)
	customerIdentifier := helper2.NewCustomerIdentifier(usersClient, actorClient, chatClient, vmClient)
	callRoutingConfig := getCallRoutingConfig(conf)
	callDetailsDao := dao6.NewCallDetailsDao(dbConn)
	client := NewRedisClient(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	priorityHelper := priority_helper.NewPriorityHelper(callRoutingConfig, segmentClient, callDetailsDao, eventsBroker, redisCacheStorage)
	featureReleaseConfig := getDynFeatureReleaseConfig(conf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, usersClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	blockerImpl := blocker.NewBlocker(usersClient, redisCacheStorage, conf, riskProfileClient, commsClient, tieringClient, ticketClient, eventsBroker, evaluator, client, preApprovedLoanClient, compClient, ffClient)
	callRoutingHelper := helper11.NewCallRoutingHelper(onboardingClient, conf, callRoutingManualMappingsDao, salaryProgramClient, dataCollectorHelper, customerIdentifier, usersClient, priorityHelper, ffClient, riskProfileClient, commsClient, productClient, redisCacheStorage, evaluator, blockerImpl, preApprovedLoanClient)
	service := call_routing.NewCallRoutingService(usersClient, actorClient, userGroupClient, conf, callRoutingHelper, callRoutingEventPublisher)
	return service
}

// config: {"s3Client": "LivenessVideoConfig().S3BucketName", "riskS3Client": "RiskS3Config().BucketName"}
func InitializeRiskOpsService(onboardingClient onboarding.OnboardingClient, userClient user.UsersClient, kycClient kyc.KycClient, vkycClient vkyc.VKYCClient, locationClient location.LocationClient, actorClient actor.ActorClient, livenessClient liveness.LivenessClient, caClient connected_account.ConnectedAccountClient, authClient auth.AuthClient, riskClient risk.RiskClient, userLocationClient location2.LocationClient, employmentClient employment.EmploymentClient, emailParserClient emailparser.EmailParserClient, palCXClient cx6.CxClient, screenerClient screener.ScreenerClient, caseManagementClient case_management.CaseManagementClient, bcClient bankcust.BankCustomerServiceClient, s3Client types2.LivenessVideoConfigS3Client, genConf *genconf.Config, redListClient redlist.RedListClient, payClient pay.PayClient, txnCategorizerClient categorizer.TxnCategorizerClient, savingsClient savings.SavingsClient, riskProfileClient profile3.ProfileClient, eventBroker events.Broker, preApprovedLoanClient preapprovedloan2.PreApprovedLoanClient, nameCheckClient namecheck.UNNameCheckClient, fennelClient fennel.FennelFeatureStoreClient, scienapticClient scienaptic.ScienapticClient, riskS3Client types2.RiskS3Client, orderClient order.OrderServiceClient, accountPIClient account_pi.AccountPIRelationClient, actorActivityClient actor_activity.ActorActivityClient, cxTicketClient ticket.TicketClient, sgApiGwKycClient kyc3.KYCClient, leaClient lea.LeaClient, config2 *config.Config) *risk_ops.Service {
	s3S3Client := types2.LivenessVideoConfigS3ClientProvider(s3Client)
	commentsHelper := comments.NewCommentsHelper(caseManagementClient)
	annotationsHelper := annotations.NewAnnotationsHelper(caseManagementClient)
	accountFreeze := actions4.NewAccountFreeze(caseManagementClient, onboardingClient, commentsHelper, annotationsHelper)
	accountUnfreeze := actions4.NewAccountUnfreeze(caseManagementClient, onboardingClient, commentsHelper, annotationsHelper)
	moveToReview := actions4.NewMoveToReview(caseManagementClient, commentsHelper, annotationsHelper)
	requestUserInfo := actions4.NewRequestUserInfo(caseManagementClient, commentsHelper, genConf, accountFreeze)
	handlerImpl := watchlist.NewWatchlistHandler(redListClient)
	uiHandlerImpl := watchlist.NewUIHandler(handlerImpl, genConf)
	passAccount := actions4.NewPassAccount(caseManagementClient, commentsHelper, uiHandlerImpl)
	passOnboarding := actions4.NewPassOnboarding(caseManagementClient, onboardingClient, commentsHelper, uiHandlerImpl)
	failOnboarding := actions4.NewFailOnboarding(caseManagementClient, onboardingClient, commentsHelper)
	retryLiveness := actions4.NewRetryLiveness(caseManagementClient, onboardingClient, commentsHelper, livenessClient)
	snooze := actions4.NewSnooze(caseManagementClient, onboardingClient, commentsHelper, annotationsHelper)
	failAFU := actions4.NewFailAFU(caseManagementClient, commentsHelper, accountFreeze, uiHandlerImpl)
	passAFU := actions4.NewPassAFU(caseManagementClient, commentsHelper, uiHandlerImpl)
	investigateLEAActor := actions4.NewInvestigateLEAActor(caseManagementClient, commentsHelper, annotationsHelper)
	rejectEscalation := actions4.NewRejectEscalation(caseManagementClient, commentsHelper, annotationsHelper)
	addLien := actions4.NewAddLien(caseManagementClient, savingsClient, config2, genConf)
	actionFactory := actions4.NewActionFactory(accountFreeze, accountUnfreeze, moveToReview, requestUserInfo, passAccount, passOnboarding, failOnboarding, retryLiveness, snooze, failAFU, passAFU, investigateLEAActor, rejectEscalation, addLien)
	action := review.NewAction(actionFactory, caseManagementClient, onboardingClient)
	orderWithTransactionManagerImpl := transaction2.NewOrderWithTransactionManagerImpl(payClient)
	selectedOrderWithTransactionManagerImpl := transaction2.NewSelectedOrderWithTransactionManagerImpl(orderClient, accountPIClient)
	fetcherFactoryImpl := transaction2.NewFetcherFactory(genConf, orderWithTransactionManagerImpl, selectedOrderWithTransactionManagerImpl)
	entityDetailManagerImpl := actor2.NewEntityDetailManagerImpl(actorClient)
	activityCategoryManagerImpl := categorizer2.NewActivityCategoryManagerImpl(txnCategorizerClient)
	riskReviewTransactionBuilderImpl := builder.NewRiskReviewTransactionBuilderImpl(riskClient)
	dataFetcherImpl := fetcher.NewDataFetcherImpl(fetcherFactoryImpl, entityDetailManagerImpl, activityCategoryManagerImpl, riskReviewTransactionBuilderImpl, accountPIClient, caseManagementClient)
	helperImpl := fetchers.NewHelperImpl(nameCheckClient, userClient, preApprovedLoanClient)
	loansInfoFetcher := fetchers.NewLoansInfoFetcher(helperImpl, preApprovedLoanClient, userClient)
	factoryImpl := fetchers.NewFactoryImpl(loansInfoFetcher)
	fetcherImpl := products.NewFetcherImpl(caseManagementClient, userClient, factoryImpl)
	featureStore := wire.InitializeFeatureStore(fennelClient, scienapticClient, userClient)
	iFeatureStore := IFeatureStoreProvider(featureStore)
	service := risk_ops.NewRiskOpsService(onboardingClient, userClient, kycClient, vkycClient, locationClient, actorClient, livenessClient, caClient, authClient, riskClient, userLocationClient, employmentClient, emailParserClient, palCXClient, screenerClient, caseManagementClient, bcClient, s3S3Client, action, annotationsHelper, commentsHelper, dataFetcherImpl, savingsClient, riskProfileClient, eventBroker, fetcherImpl, iFeatureStore, genConf, riskS3Client, actorActivityClient, handlerImpl, cxTicketClient, sgApiGwKycClient, leaClient)
	return service
}

func InitializeRiskOpsWealthService(wOnbClient cx3.WealthCxServiceClient) *risk_ops_wealth.Service {
	service := risk_ops_wealth.NewService(wOnbClient)
	return service
}

func InitializeConnectedAccountService(config2 *config.Config, genConfig *genconf.Config, dbConn types.SherlockPGDB, userClient user.UsersClient, caClient connected_account.ConnectedAccountClient) *connected_account2.Service {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(dbConn)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(dbConn)
	authFactorRetryLimit := getAuthFactoryRetryLimit(config2)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(dbConn)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConfig, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	service := connected_account2.NewConnectedAccountService(caClient, authEngine)
	return service
}

// config: {"s3Client": "SalaryProgramLeadManagementConfig().SalaryProgramS3BucketName","s3ClientB2B": "SalaryProgramLeadManagementConfig().SalaryProgramB2BS3BucketName"}
func InitializeSalaryB2BService(client leadsquared.LeadManagementClient, s3Client types2.SalaryProgramNonProdS3Client, s3ClientB2B types2.SalaryProgramB2BS3Client, authClient auth.AuthClient, commsClient comms.CommsClient, conf *config.Config) *salaryb2b2.SalaryB2BService {
	salaryProgramLeadManagementConfig := getSalaryProgramLeadManagementConfig(conf)
	salaryB2BService := salaryb2b2.NewSalaryB2BService(client, s3Client, s3ClientB2B, authClient, commsClient, salaryProgramLeadManagementConfig)
	return salaryB2BService
}

func InitializeTicketConsumerService(db types.SherlockPGDB, conf *config.Config, chatClient chat.ChatsClient, vmClient vendormapping.VendorMappingServiceClient, paymentClient payment.PaymentClient, orderClient order.OrderServiceClient, updateTicketPublisher types2.UpdateTicketPublisher, crmIssueTrackerIntegrationPublisher types2.CrmIssueTrackerIntegrationPublisher, actorClient actor.ActorClient, userClient user.UsersClient, userGroupClient group.GroupClient, cxGenConf *genconf.Config, watsonClient watson.WatsonClient, commsClient comms.CommsClient, authClient auth.AuthClient, ticketUpdateEventPublisher types2.TicketUpdateEventPublisher, ticketClient ticket.TicketClient, eventBroker events.Broker) *consumer6.Service {
	supportTicketDao := dao7.NewSupportTicketDao(db)
	gormDB := GormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	customerIdentifier := helper2.NewCustomerIdentifier(userClient, actorClient, chatClient, vmClient)
	ticketHelper := InitializeTicketConsumerHelper(db, paymentClient, orderClient, conf)
	featureReleaseConfig := getDynFeatureReleaseConfig(cxGenConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	issueCategoryDao := dao3.NewIssueCategoryDao(db)
	issueCategoryManagerImpl := manager.NewIssueCategoryManagerImpl(issueCategoryDao)
	incidentTicketDetailDao := dao2.NewIncidentTicketDetailDao(db)
	incidentDao := dao2.NewIncidentDao(db)
	csatConfig := csatConfigProvider(cxGenConf)
	jwtTokenManager := token_manager.NewJwtTokenManager()
	baseMessageCreator := comms3.NewBaseMessageCreator(csatConfig, jwtTokenManager)
	systemTrayCreator := comms3.NewSystemTrayCreator(baseMessageCreator)
	whatsappCreator := comms3.NewWhatsappCreator(baseMessageCreator)
	emailCreator := comms3.NewEmailCreator(baseMessageCreator)
	messageCreatorFactoryImpl := comms3.NewMessageCreatorFactory(systemTrayCreator, whatsappCreator, emailCreator)
	issueConfigDaoPGDB := dao18.NewIssueConfigDaoPGDB(db, gormTxnExecutor)
	inAppCsatResponsesDao := dao7.NewInAppCsatResponsesDao(db)
	resolutionCsatEvaluator := csat.NewResolutionCsatEvaluator(supportTicketDao, issueConfigDaoPGDB, inAppCsatResponsesDao, csatConfig)
	senderImpl := comms3.NewCsatCommsSender(csatConfig, messageCreatorFactoryImpl, userClient, commsClient, resolutionCsatEvaluator, evaluator, eventBroker)
	service := consumer6.NewTicketConsumerService(conf, supportTicketDao, gormTxnExecutor, customerIdentifier, ticketHelper, updateTicketPublisher, crmIssueTrackerIntegrationPublisher, evaluator, issueCategoryManagerImpl, cxGenConf, watsonClient, commsClient, authClient, incidentTicketDetailDao, incidentDao, ticketUpdateEventPublisher, ticketClient, senderImpl)
	return service
}

func InitializeWatsonConsumerService(db types.SherlockPGDB, conf *config.Config, rlRedisStore types.RateLimiterRedisStore, celestialClient celestial.CelestialClient, signalWorkflowPublisher types2.CelestialSignalWorkflowPublisher, eventBroker events.Broker, simulatorWatsonClientClient watson_client.WatsonClientClient, mockCxWatsonClient mock_client2.MockWatsonClientServiceClient, watsonSavingsClient watson2.WatsonClient, watsonOnboardingClient watson3.WatsonClient, payIncidentManagerClient payincidentmanager.PayIncidentManagerClient, investmentWatsonClient watson4.WatsonClient, watsonDepositsClient watson5.WatsonClient, p2pWatsonClient incidentmanager.IncidentManagerClient, watsonClient watson.WatsonClient, errorActivityWatsonClient watson_info_provider.WatsonInfoProviderClient, manualTicketStageWiseCommsClient manual_ticket_stage_wise_comms.ManualTicketStageWiseCommsClient) (*consumer7.WatsonConsumerService, error) {
	iWatsonIncidentInfoCollectorFactory := WatsonIncidentInfoCollectorFactoryProvider(simulatorWatsonClientClient, mockCxWatsonClient, watsonSavingsClient, watsonOnboardingClient, payIncidentManagerClient, investmentWatsonClient, watsonDepositsClient, p2pWatsonClient, errorActivityWatsonClient, manualTicketStageWiseCommsClient)
	incidentDao := dao2.NewIncidentDao(db)
	watsonHelper := watson7.NewWatsonHelper(iWatsonIncidentInfoCollectorFactory, incidentDao, watsonClient)
	watsonHelperV2 := watson7.NewWatsonHelperV2(watsonHelper)
	helperFactoryImpl := watson7.NewHelperFactoryImpl(watsonHelper, watsonHelperV2)
	celestialProcessor := celestial2.NewCelestialProcessor(celestialClient, signalWorkflowPublisher)
	client := types.RateLimiterRedisStoreRedisClientProvider(rlRedisStore)
	slidingWindowLogWithRedisImpl := store.NewSlidingWindowLogWithRedis(client)
	rateLimitConfig := rateLimiterConfigProvider(conf)
	rateLimiterImpl, err := ratelimiter.NewRateLimiter(slidingWindowLogWithRedisImpl, rateLimitConfig)
	if err != nil {
		return nil, err
	}
	rateLimitHelper := helper3.NewRateLimitHelper(rateLimiterImpl, conf)
	watsonConsumerHelper := helper12.NewWatsonConsumerHelper(helperFactoryImpl, incidentDao, conf, celestialProcessor, rateLimitHelper, eventBroker)
	gormDB := GormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	issueConfigDaoPGDB := dao18.NewIssueConfigDaoPGDB(db, gormTxnExecutor)
	watsonConsumerHelperV2 := helper12.NewWatsonConsumerHelperV2(watsonConsumerHelper, issueConfigDaoPGDB)
	consumerHelperFactoryImpl := helper12.NewConsumerHelperFactoryImpl(watsonConsumerHelper, watsonConsumerHelperV2)
	incidentTicketDetailDao := dao2.NewIncidentTicketDetailDao(db)
	ticketStatusChangeEventProcessor := ticket_event2.NewTicketStatusChangeEventProcessor(incidentTicketDetailDao, incidentDao, consumerHelperFactoryImpl)
	issueCategoryDao := dao3.NewIssueCategoryDao(db)
	issueCategoryManagerImpl := manager.NewIssueCategoryManagerImpl(issueCategoryDao)
	ticketManualCreationEventProcessor := ticket_event2.NewTicketManualCreationEventProcessor(incidentDao, consumerHelperFactoryImpl, issueCategoryManagerImpl, issueConfigDaoPGDB, incidentTicketDetailDao, gormTxnExecutor)
	ticketManualUpdateEventProcessor := ticket_event2.NewTicketManualUpdateEventProcessor(incidentTicketDetailDao, incidentDao, consumerHelperFactoryImpl, issueConfigDaoPGDB, issueCategoryManagerImpl)
	ticketEventProcessorFactory := ticket_event2.NewTicketEventProcessorFactory(ticketStatusChangeEventProcessor, ticketManualCreationEventProcessor, ticketManualUpdateEventProcessor)
	watsonConsumerService := consumer7.NewWatsonConsumerService(conf, consumerHelperFactoryImpl, helperFactoryImpl, ticketEventProcessorFactory, incidentTicketDetailDao, incidentDao)
	return watsonConsumerService, nil
}

func InitializeSherlockUserDao(db types.SherlockPGDB) *sherlock_user_wrapper.SherlockUser {
	sherlockUserInfoDao := dao5.NewSherlockUserInfoDao(db)
	sherlockUserRoleDao := dao5.NewSherlockUserRoleDao(db)
	gormDB := GormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	sherlockUser := sherlock_user_wrapper.NewSherlockUserDao(sherlockUserInfoDao, sherlockUserRoleDao, gormTxnExecutor)
	return sherlockUser
}

func InitializeSherlockUserService(db types.SherlockPGDB, casbinClient casbin.CasbinClient) *sherlock_user.Service {
	sherlockUserInfoDao := dao5.NewSherlockUserInfoDao(db)
	sherlockUserRoleDao := dao5.NewSherlockUserRoleDao(db)
	gormDB := GormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	sherlockUser := sherlock_user_wrapper.NewSherlockUserDao(sherlockUserInfoDao, sherlockUserRoleDao, gormTxnExecutor)
	epifiUserProvisioner := provisioner.NewEpifiUserProvisioner(sherlockUser)
	casbinUserProvisioner := provisioner.NewCasbinUserProvisioner(casbinClient)
	sherlockUserProvisionerFactory := sherlock_user.NewUserProvisionerFactory(epifiUserProvisioner, casbinUserProvisioner)
	service := sherlock_user.NewSherlockUserService(sherlockUserProvisionerFactory)
	return service
}

// config: {"s3Client": "CallRecording().CallRecordingBucketName"}
func InitializeCallService(db types.SherlockPGDB, freshdeskClient freshdesk.FreshdeskClient, ticketClient ticket.TicketClient, conf *config.Config, s3Client types2.CallRecordingS3Client) *call.Service {
	callConfig := getCallConfig(conf)
	callDetailsDao := dao6.NewCallDetailsDao(db)
	ticketConfig := getTicketConfig(conf)
	s3S3Client := types2.CallRecordingS3ClientProvider(s3Client)
	callHelper := helper13.NewCallHelper(s3S3Client, conf)
	service := call.NewCallService(callConfig, callDetailsDao, freshdeskClient, ticketConfig, ticketClient, callHelper)
	return service
}

func InitializeOzonetelConsumerService(db types.SherlockPGDB, updateTicketPublisher types2.UpdateTicketPublisher, userClient user.UsersClient, actorClient actor.ActorClient, chatClient chat.ChatsClient, vmClient vendormapping.VendorMappingServiceClient, gconf *genconf.Config, commsClient comms.CommsClient, redisClient types2.CxRedisStore) *consumer8.Service {
	callDetailsDao := dao6.NewCallDetailsDao(db)
	callInitiatedProcessor := processor11.NewCallInitiatedProcessor(callDetailsDao)
	agentAssignedProcessor := processor11.NewAgentAssignedProcessor(callDetailsDao)
	customerIdentifier := helper2.NewCustomerIdentifier(userClient, actorClient, chatClient, vmClient)
	callConfig := getDynamicCallConfig(gconf)
	callEndedProcessor := processor11.NewCallEndedProcessor(callDetailsDao, updateTicketPublisher, customerIdentifier, commsClient, callConfig)
	callStageFactory := processor11.NewCallStageFactory(callInitiatedProcessor, agentAssignedProcessor, callEndedProcessor)
	client := NewRedisClient(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	service := consumer8.NewService(callStageFactory, callConfig, redisCacheStorage)
	return service
}

func InitializeCallDao(db types.SherlockPGDB) *dao6.CallDetailsDao {
	callDetailsDao := dao6.NewCallDetailsDao(db)
	return callDetailsDao
}

func InitializeP2PInvestmentService(config2 *config.Config, genConfig *genconf.Config, dbConn types.SherlockPGDB, userClient user.UsersClient, p2pInvestmentCxClient cx8.CxClient) *p2pinvestment3.Service {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(dbConn)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(dbConn)
	authFactorRetryLimit := getAuthFactoryRetryLimit(config2)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(dbConn)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConfig, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	service := p2pinvestment3.NewService(authEngine, p2pInvestmentCxClient)
	return service
}

func InitializePreApprovedLoanService(config2 *config.Config, genConfig *genconf.Config, dbConn types.SherlockPGDB, userClient user.UsersClient, palCxClient cx6.CxClient, catalogManagerClient catalog.CatalogManagerClient, ticketClient ticket.TicketClient, fdClient freshdesk.FreshdeskClient) *preapprovedloan3.Service {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(dbConn)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(dbConn)
	authFactorRetryLimit := getAuthFactoryRetryLimit(config2)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(dbConn)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConfig, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	webViewGenerator := impl.NewLamfWebViewGenerator(catalogManagerClient)
	baseWebViewGenerator := generator2.NewBaseWebViewGenerator()
	baseDataProvider := providers.NewBaseDataProvider(palCxClient)
	viewFactory := factory.NewViewFactory(webViewGenerator, baseWebViewGenerator, baseDataProvider)
	service := preapprovedloan3.NewService(authEngine, palCxClient, catalogManagerClient, viewFactory, ticketClient, userClient, fdClient)
	return service
}

func InitializeFireflyService(config2 *config.Config, genConfig *genconf.Config, dbConn types.SherlockPGDB, userClient user.UsersClient, ccCxClient cx5.CxClient, ffClient firefly.FireflyClient, ffBillingClient billing.BillingClient, ffAccountingClient accounting.AccountingClient, creditLimitEstimatorClient credit_limit_estimator.CreditLimitEstimatorClient, ffLmsClient lms2.LoanManagementSystemClient, actorClient actor.ActorClient, rewardsClient rewards.RewardsGeneratorClient, bcClient bankcust.BankCustomerServiceClient, depositClient deposit.DepositClient, projectorClient projector.ProjectorServiceClient, txnAggClient pinot.TxnAggregatesClient, client segment.SegmentationServiceClient) *firefly2.Service {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(dbConn)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(dbConn)
	authFactorRetryLimit := getAuthFactoryRetryLimit(config2)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(dbConn)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConfig, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	service := firefly2.NewService(authEngine, ccCxClient, ffClient, ffBillingClient, ffAccountingClient, creditLimitEstimatorClient, ffLmsClient, actorClient, rewardsClient, bcClient, depositClient, projectorClient, txnAggClient, client)
	return service
}

// config: {"salaryProgramS3Client": "SalaryOpsConfig().SalaryProgramS3BucketName()"}
func InitializeSalaryDataOpsService(gconf *genconf.Config, conf *config.Config, dbconn types.SherlockPGDB, usersClient user.UsersClient, actorClient actor.ActorClient, empClient employment.EmploymentClient, salaryClient salaryprogram.SalaryProgramClient, orderClient order.OrderServiceClient, piClient paymentinstrument.PiClient, vkycClient vkyc.VKYCClient, userGroupClient group.GroupClient, vgPaymentClient payment2.PaymentClient, salaryCxClient cx9.CxClient, txnCatClient categorizer.TxnCategorizerClient, bcClient bankcust.BankCustomerServiceClient, healthInsuranceClient healthinsurance.HealthInsuranceClient, salaryProgramS3Client types2.SalaryProgramS3Client, employerNameCategoriserClient employernamecategoriser.EmployerNameCategoriserClient, recurringPaymentClient recurringpayment.RecurringPaymentServiceClient) *salarydataops.Service {
	salaryOpsConfig := getDynSalaryOpsConfig(gconf)
	configSalaryOpsConfig := getSalaryOpsConfig(conf)
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(dbconn)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(dbconn)
	authFactorRetryLimit := getAuthFactoryRetryLimit(conf)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(dbconn)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, gconf, authFactorRetryLimit, usersClient, customerAuthenticationV2DAO)
	service := salarydataops.NewSalaryDataOpsService(salaryOpsConfig, configSalaryOpsConfig, usersClient, actorClient, empClient, salaryClient, orderClient, piClient, vkycClient, userGroupClient, vgPaymentClient, authEngine, salaryCxClient, txnCatClient, bcClient, healthInsuranceClient, salaryProgramS3Client, employerNameCategoriserClient, recurringPaymentClient)
	return service
}

func InitializeSherlockBannersService(db types.SherlockPGDB, conf *genconf.Config, palSherlockBannersClient sherlock_banners.PreApprovedLoanSherlockBannersClient, tieringClient tiering.TieringClient, riskProfileClient profile3.ProfileClient) *sherlock_banners2.Service {
	sherlockBannerDao := dao24.NewSherlockBannerDao(db)
	bannerMappingDao := dao24.NewBannerMappingDao(db)
	gormDB := GormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	getBannersFromDb := helper14.NewGetBannersFromDb(sherlockBannerDao, bannerMappingDao)
	iSherlockBannersCollectorFactory := sherlockBannersCollectorFactoryProvider(getBannersFromDb, palSherlockBannersClient, tieringClient, riskProfileClient)
	service := sherlock_banners2.NewSherlockBannersService(conf, sherlockBannerDao, bannerMappingDao, gormTxnExecutor, getBannersFromDb, iSherlockBannersCollectorFactory)
	return service
}

func InitializeSherlockFeedbackDetailsService(db types.SherlockPGDB, conf *config.Config) *sherlock_feedback.Service {
	sherlockFeedbackDetailsDao := dao25.NewSherlockFeedbackDetailsDao(db)
	service := sherlock_feedback.NewSherlockFeedbackService(conf, sherlockFeedbackDetailsDao)
	return service
}

func InitializeIssueResolutionFeedbackService(db types.SherlockPGDB, conf *genconf.Config, celestialClient celestial.CelestialClient, signalWorkflowPublisher types2.CelestialSignalWorkflowPublisher, ticketClient ticket.TicketClient) *issue_resolution_feedback3.Service {
	issueResolutionFeedbackDao := dao21.NewIssueResolutionFeedbackDao(db)
	issueResolutionUserResponseLogDao := dao21.NewIssueResolutionUserResponseLogDao(db)
	celestialProcessor := celestial2.NewCelestialProcessor(celestialClient, signalWorkflowPublisher)
	service := issue_resolution_feedback3.NewService(conf, issueResolutionFeedbackDao, issueResolutionUserResponseLogDao, celestialProcessor, ticketClient)
	return service
}

func InitializeActivityProcessor(db types.SherlockPGDB, commsClient comms.CommsClient, workerConf *worker.Config, actorClient actor.ActorClient, chatClient chat.ChatsClient, vmClient vendormapping.VendorMappingServiceClient, updateTicketPublisher types2.UpdateTicketPublisher, usersClient user.UsersClient, eventBroker events.Broker, infoCollectorFactory collector.IWatsonIncidentInfoCollectorFactory, watsonClient watson.WatsonClient, ticketClient ticket.TicketClient) *activity.Processor {
	issueResolutionFeedbackDao := dao21.NewIssueResolutionFeedbackDao(db)
	issueResolutionUserResponseLogDao := dao21.NewIssueResolutionUserResponseLogDao(db)
	disputeDao := dao12.NewDisputeDao(db)
	customerIdentifier := helper2.NewCustomerIdentifier(usersClient, actorClient, chatClient, vmClient)
	disputeProcessor := issue_resolution_feedback4.NewDisputeProcessor(commsClient, issueResolutionFeedbackDao, issueResolutionUserResponseLogDao, disputeDao, workerConf, customerIdentifier, updateTicketPublisher)
	issueResolutionFeedbackProcessorFactory := issue_resolution_feedback4.NewIssueResolutionFeedbackProcessorFactory(disputeProcessor)
	incidentDao := dao2.NewIncidentDao(db)
	incidentTicketDetailDao := dao2.NewIncidentTicketDetailDao(db)
	incidentCommsDetailDao := dao2.NewIncidentCommsDetailDao(db)
	emailProcessor := comms4.NewEmailProcessor(commsClient, workerConf)
	pushNotificationProcessor := comms4.NewPushNotificationProcessor(commsClient, workerConf)
	smsProcessor := comms4.NewSmsProcessor(commsClient)
	whatsappProcessor := comms4.NewWhatsappProcessor(commsClient)
	sendCommsFactory := comms4.NewSendCommsFactory(emailProcessor, pushNotificationProcessor, smsProcessor, whatsappProcessor)
	gormDB := GormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	watsonHelper := watson7.NewWatsonHelper(infoCollectorFactory, incidentDao, watsonClient)
	watsonHelperV2 := watson7.NewWatsonHelperV2(watsonHelper)
	helperFactoryImpl := watson7.NewHelperFactoryImpl(watsonHelper, watsonHelperV2)
	watsonActivityHelper := activity_helper.NewWatsonActivityHelper(workerConf, helperFactoryImpl, usersClient, ticketClient)
	issueConfigDaoPGDB := dao18.NewIssueConfigDaoPGDB(db, gormTxnExecutor)
	issueCategoryDao := dao3.NewIssueCategoryDao(db)
	issueCategoryManagerImpl := manager.NewIssueCategoryManagerImpl(issueCategoryDao)
	activityHelperV2 := activity_helper.NewActivityHelperV2(watsonActivityHelper, issueConfigDaoPGDB, issueCategoryManagerImpl)
	activityHelperFactoryImpl := activity_helper.NewActivityHelperFactoryImpl(watsonActivityHelper, activityHelperV2)
	ticketDetailsTransformationDao := dao7.NewTicketDetailsTransformationDao(db)
	supportTicketDao := dao7.NewSupportTicketDao(db)
	activityProcessor := activity.NewProcessor(issueResolutionFeedbackProcessorFactory, workerConf, infoCollectorFactory, incidentDao, incidentTicketDetailDao, incidentCommsDetailDao, sendCommsFactory, usersClient, gormTxnExecutor, eventBroker, activityHelperFactoryImpl, helperFactoryImpl, issueConfigDaoPGDB, ticketDetailsTransformationDao, supportTicketDao, ticketClient)
	return activityProcessor
}

func InitializeWatsonInfoProvider(db types.SherlockPGDB, userClient user.UsersClient) *watson_info_provider2.WatsonInfoProvider {
	gormDB := GormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	issueConfigDaoPGDB := dao18.NewIssueConfigDaoPGDB(db, gormTxnExecutor)
	watsonInfoProvider := watson_info_provider2.NewWatsonInfoProvider(issueConfigDaoPGDB, userClient)
	return watsonInfoProvider
}

func InitializeLiveChatFallbackService(db types.SherlockPGDB, gconf *genconf.Config, freshChatClient freshchat.FreshchatClient, vmClient vendormapping.VendorMappingServiceClient, userClient user.UsersClient, actorClient actor.ActorClient, redisClient types2.CxRedisStore, conf *config.Config, onbClient onboarding.OnboardingClient, salaryProgramClient salaryprogram.SalaryProgramClient, commsClient comms.CommsClient) *livechatfallback.Service {
	userPriorityPropertiesDao := dao9.NewUserPriorityPropertiesDao(redisClient)
	priority_routing_helperHelper := priority_routing_helper.NewPriorityDataHelper(userPriorityPropertiesDao, userClient, actorClient)
	priorityRoutingConfig := getPriorityRoutingConfig(conf)
	highPriorityRuleProcessor := processor.NewHighPriorityRuleProcessor(priority_routing_helperHelper, priorityRoutingConfig)
	lowPriorityRuleProcessor := processor.NewLowPriorityRuleProcessor(priority_routing_helperHelper, priorityRoutingConfig)
	currentlyOnbRuleProcessor := processor.NewCurrentlyOnbRuleProcessor(onbClient, priority_routing_helperHelper)
	salaryProgramUsersRuleProcessor := processor.NewSalaryProgramUsersRuleProcessor(salaryProgramClient)
	processorFactory := processor.NewRuleFactory(highPriorityRuleProcessor, lowPriorityRuleProcessor, currentlyOnbRuleProcessor, salaryProgramUsersRuleProcessor)
	routingEngine := routing_engine.NewRoutingEngine(processorFactory)
	freshchatUserMappingDao := dao4.NewFreshchatUserMappingDao(db)
	freshchatUserMappingHelper := helper4.NewFreshchatUserMappingHelper(gconf, routingEngine, freshchatUserMappingDao, userClient, freshChatClient, vmClient)
	service := livechatfallback.NewService(gconf, freshchatUserMappingHelper, freshChatClient, vmClient, actorClient, commsClient)
	return service
}

func InitializeCxChatConsumerService(conf *config.Config, genConfig *genconf.Config, senseforthLiveChatFallbackClient senseforth.SenseforthLiveChatFallbackClient, freshchatClient freshchat.FreshchatClient, redisClient types2.CxRedisStore, eventBroker events.Broker) *consumer9.Service {
	client := NewRedisClient(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	freshchatInfoHelper := helper4.NewFreshchatInfoHelper(freshchatClient, redisCacheStorage, conf)
	messageCreateProcessor := processor12.NewMessageCreateProcessor(senseforthLiveChatFallbackClient, freshchatInfoHelper)
	conversationAssignmentProcessor := processor12.NewConversationAssignmentProcessor(senseforthLiveChatFallbackClient, freshchatInfoHelper)
	conversationResolutionProcessor := processor12.NewConversationResolutionProcessor(senseforthLiveChatFallbackClient, freshchatInfoHelper)
	freshchatActionFactory := processor12.NewFreshchatActionFactory(messageCreateProcessor, conversationAssignmentProcessor, conversationResolutionProcessor)
	chatbotRoutingEventProcessor := processor12.NewChatbotRoutingProcessor(eventBroker)
	chatbotFetchMetadataEventProcessor := processor12.NewChatbotFetchMetadataEventProcessor(eventBroker)
	chatbotShowCategoriesEventProcessor := processor12.NewChatbotShowCategoriesEventProcessor(eventBroker)
	chatbotProcessQueryEventProcessor := processor12.NewChatbotProcessQueryEventProcessor(eventBroker)
	chatbotEvaluateEscalationEventProcessor := processor12.NewChatbotEvaluateEscalationEventProcessor(eventBroker)
	nuggetEventProcessorFactory := processor12.NewNuggetEventProcessorFactory(chatbotRoutingEventProcessor, chatbotFetchMetadataEventProcessor, chatbotShowCategoriesEventProcessor, chatbotProcessQueryEventProcessor, chatbotEvaluateEscalationEventProcessor)
	service := consumer9.NewService(freshchatActionFactory, nuggetEventProcessorFactory, genConfig)
	return service
}

func InitializeChatbotWorkflowService(cxConf *config.Config, cxGenConf *genconf.Config, faqServingClient serving.ServeFAQClient, chatClient chat.ChatsClient, vmClient vendormapping.VendorMappingServiceClient, fdClient freshdesk.FreshdeskClient, vkycClient vkyc.VKYCClient, bankCustClient bankcust.BankCustomerServiceClient, orderTxnClient cx2.CXClient, cardProvisioningClient provisioning.CardProvisioningClient, ffClient firefly.FireflyClient, ffAccountingClient accounting.AccountingClient, payClient pay.PayClient, payCxClient cx.CXClient, employmentClient employment.EmploymentClient, onbClient onboarding.OnboardingClient, livClient liveness.LivenessClient, rewardOffersClient rewardoffers.RewardOffersClient, salaryClient salaryprogram.SalaryProgramClient, inAppReferralClient inappreferral.InAppReferralClient, rewardsCreditCardTxnEventQueuePublisher types2.RewardsCreditCardTxnEventQueuePublisher, rewardsOrderUpdateEventQueuePublisher types2.RewardsOrderUpdateEventQueuePublisher, rewardsGeneratorClient rewards.RewardsGeneratorClient, accountPiClient account_pi.AccountPIRelationClient, upiOnboardingClient onboarding2.UpiOnboardingClient, actorClient actor.ActorClient, piClient paymentinstrument.PiClient, usersClient user.UsersClient, pClient payment.PaymentClient, orderClient order.OrderServiceClient, savingsClient savings.SavingsClient, depositClient deposit.DepositClient, investmentCatalogManagerClient catalog.CatalogManagerClient, p2pInvestmentClient p2pinvestment.P2PInvestmentClient, portfolioManagerClient portfolio.PortfolioManagerClient, usstocksOrderManagerClient order2.OrderManagerClient, accountBalanceClinet balance.BalanceClient, db types.SherlockPGDB, disputeExtPub types2.DisputeExternalPublisher, userGroupClient group.GroupClient, createDisputeTicketPub types2.DisputeCreateTicketPublisher, commsClient comms.CommsClient, vgDisputeClient dispute.DisputeClient, riskProfileClient profile3.ProfileClient) *workflow.Service {
	faqProcessor := fetch_data_processor.NewFaqProcessor(faqServingClient)
	customerIdentifier := helper2.NewCustomerIdentifier(usersClient, actorClient, chatClient, vmClient)
	userDetailsProcessor := fetch_data_processor.NewUserDetailsProcessor(customerIdentifier, vkycClient, bankCustClient)
	dataCollectorHelper := helper.NewDataCollectorHelper(actorClient, piClient, usersClient, pClient, orderClient, savingsClient, depositClient, investmentCatalogManagerClient, p2pInvestmentClient, portfolioManagerClient, usstocksOrderManagerClient, accountBalanceClinet, ffClient)
	transactionsDataCollectorHelper := helper.NewTransactionsDataCollectorHelper(orderTxnClient, orderClient, actorClient, savingsClient, dataCollectorHelper, payCxClient, payClient, pClient, accountPiClient, upiOnboardingClient)
	txnListProcessor := fetch_data_processor.NewTxnListProcessor(cxGenConf, transactionsDataCollectorHelper)
	disputeDao := dao12.NewDisputeDao(db)
	genconfDispute := getDynDispute(cxGenConf)
	disputeConfigDao := dao12.NewDisputeConfigDao(db)
	federalDmpDisputeDetailDao := dao12.NewFederalDmpDisputeDetailDao(db)
	gormDB := GormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	disputeHelper := helper6.NewDisputeHelper(disputeExtPub, disputeDao, pClient, piClient, orderClient, actorClient, usersClient, userGroupClient, genconfDispute, disputeConfigDao, createDisputeTicketPub, customerIdentifier, savingsClient, federalDmpDisputeDetailDao, gormTxnExecutor, commsClient, cxGenConf, vgDisputeClient)
	txnDetailsProcessor := fetch_data_processor.NewTxnDetailsProcessor(cxGenConf, transactionsDataCollectorHelper, disputeHelper)
	debitCardTrackingProcessor := fetch_data_processor.NewDebitCardTrackingProcessor(cardProvisioningClient)
	creditCardStateProcessor := fetch_data_processor.NewCreditCardStateProcessor(ffClient)
	creditCardTxnListProcessor := fetch_data_processor.NewCreditCardTxnListProcessor(ffAccountingClient)
	employmentDataProcessor := fetch_data_processor.NewEmploymentDataProcessor(employmentClient)
	fetchDisputeProcessor := fetch_data_processor.NewFetchDisputeProcessor(cxGenConf, disputeHelper)
	livenessDataProcessor := fetch_data_processor.NewLivenessDataProcessor(onbClient, livClient)
	fetchRewardOffersForUsersProcessor := fetch_data_processor.NewFetchRewardOffersForUsersProcessor(rewardOffersClient, usersClient)
	checkSalaryProgramAmazonVoucherEligibilityProcessor := fetch_data_processor.NewCheckSalaryProgramAmazonVoucherEligibilityProcessor(salaryClient, payClient)
	fetchChargesForActorProcessor := fetch_data_processor.NewFetchChargesForActorProcessor(cxGenConf, transactionsDataCollectorHelper)
	displayTxnReasonProcessor := fetch_data_processor.NewDisplayTxnReasonProcessor(cxConf, cxGenConf, payClient)
	fetchFailedTxnsProcessor := fetch_data_processor.NewFetchFailedTxnsProcessor(cxGenConf, transactionsDataCollectorHelper)
	fetchRewardEventDetailsProcessor := fetch_data_processor.NewFetchRewardEventDetailsProcessor(orderClient, ffAccountingClient, inAppReferralClient, rewardOffersClient, actorClient)
	fetchSalaryProgramRegistrationDetailsProcessor := fetch_data_processor.NewFetchSalaryProgramRegistrationDetailsProcessor(salaryClient, bankCustClient)
	fetchRewardForEventProcessor := fetch_data_processor.NewFetchRewardForEventProcessor(rewardOffersClient, rewardsGeneratorClient, orderClient, ffAccountingClient, inAppReferralClient, rewardsOrderUpdateEventQueuePublisher, rewardsCreditCardTxnEventQueuePublisher)
	balanceRefreshProcessor := fetch_data_processor.NewBalanceRefreshProcessor(accountBalanceClinet, savingsClient)
	predefinedMessageTemplateProcessor := fetch_data_processor.NewPredefinedMessageTemplateProcessor(cxGenConf, riskProfileClient)
	fetchUserTransactionsProcessor := fetch_data_processor.NewFetchUserTransactionsProcessor(orderClient, actorClient)
	fetchDataFactory := workflow.NewFetchDataFactory(faqProcessor, userDetailsProcessor, txnListProcessor, txnDetailsProcessor, debitCardTrackingProcessor, creditCardStateProcessor, creditCardTxnListProcessor, employmentDataProcessor, fetchDisputeProcessor, livenessDataProcessor, fetchRewardOffersForUsersProcessor, checkSalaryProgramAmazonVoucherEligibilityProcessor, fetchChargesForActorProcessor, displayTxnReasonProcessor, fetchFailedTxnsProcessor, fetchRewardEventDetailsProcessor, fetchSalaryProgramRegistrationDetailsProcessor, fetchRewardForEventProcessor, balanceRefreshProcessor, predefinedMessageTemplateProcessor, fetchUserTransactionsProcessor)
	createTicketProcessor := execute_action_processor.NewCreateTicketProcessor(cxGenConf, customerIdentifier, fdClient)
	executeActionFactory := workflow.NewExecuteActionFactory(createTicketProcessor)
	service := workflow.NewService(fetchDataFactory, executeActionFactory)
	return service
}

func InitializeUserIssueInfoService(redisClient types2.CxRedisStore) *user_issue_info2.Service {
	client := NewRedisClient(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	service := user_issue_info2.NewService(redisCacheStorage)
	return service
}

// config: {"s3Client": "InternationalFundTransfer().DocumentsBucketName()"}
func NewInternationalFundTransferService(config2 *config.Config, genConfig *genconf.Config, dbConn types.SherlockPGDB, userClient user.UsersClient, fgClient file_generator.FileGeneratorClient, s3Client types2.PayIFTDocumentsS3Client, iftClient internationalfundtransfer.InternationalFundTransferClient) *internationalfundtransfer3.Service {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(dbConn)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(dbConn)
	authFactorRetryLimit := getAuthFactoryRetryLimit(config2)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(dbConn)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConfig, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	s3S3Client := types2.PayIFTDocumentsS3ClientProvider(s3Client)
	service := internationalfundtransfer3.NewInternationalFundTransferService(authEngine, fgClient, s3S3Client, iftClient, genConfig)
	return service
}

func InitializeSprinklrEventsService(updateTicketPub types2.UpdateTicketPublisher, createTicketPub types2.CreateTicketPublisher, db types.SherlockPGDB, userClient user.UsersClient, actorClient actor.ActorClient, chatClient chat.ChatsClient, vmClient vendormapping.VendorMappingServiceClient) *sprinklr.EventsService {
	sprinklrCaseDetailsDao := dao11.NewSprinklrCaseDetailsDao(db)
	customerIdentifier := InitializeCustomerIdentifier(userClient, actorClient, chatClient, vmClient)
	eventsService := sprinklr.NewEventsService(updateTicketPub, createTicketPub, sprinklrCaseDetailsDao, customerIdentifier)
	return eventsService
}

func InitializeAlfredService(config2 *config.Config, genConfig *genconf.Config, dbConn types.SherlockPGDB, userClient user.UsersClient, alfredClient alfred.AlfredClient, savingsClient savings.SavingsClient, operationalStatusClient operstatus.OperationalStatusServiceClient) *alfred2.Service {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(dbConn)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(dbConn)
	authFactorRetryLimit := getAuthFactoryRetryLimit(config2)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(dbConn)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConfig, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	service := alfred2.NewService(alfredClient, authEngine, savingsClient, operationalStatusClient)
	return service
}

func InitializeTieringService(config2 *config.Config, genConfig *genconf.Config, dbConn types.SherlockPGDB, userClient user.UsersClient, tieringClient tiering.TieringClient) *tiering2.Service {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(dbConn)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(dbConn)
	authFactorRetryLimit := getAuthFactoryRetryLimit(config2)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(dbConn)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConfig, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	service := tiering2.NewService(tieringClient, authEngine)
	return service
}

func InitializeCrmIssueTrackerIntegrationService(ctx context.Context, db types.SherlockPGDB, gconf *genconf.Config, conf *config.Config, freshdeskTicketClient ticket.TicketClient, freshdeskClient freshdesk.FreshdeskClient, ffClient firefly.FireflyClient) (*crm_issue_tracker_integration.Service, error) {
	crmToIssueTrackerMappingDao := dao22.NewCrmToIssueTrackerMappingDao(db)
	ticketDetailsTransformationDao := dao7.NewTicketDetailsTransformationDao(db)
	monorailApiWrapper := initialiseMonorailHttpClient(ctx, conf)
	lendingCreditCardProcessor := monorail_description.NewLendingCreditCardProcessor(ffClient)
	monorailDescriptionFactory := monorail_description.NewMonorailDescriptionFactory(lendingCreditCardProcessor)
	freshdeskMonorailTranslator := ticket_translator.NewFreshdeskMonorailTranslator(gconf, ticketDetailsTransformationDao, crmToIssueTrackerMappingDao, freshdeskClient, monorailApiWrapper, monorailDescriptionFactory)
	ticketTranslatorFactory := ticket_translator.NewTicketTranslatorFactory(freshdeskMonorailTranslator)
	freshdeskMonorailApiHelper := helper15.NewFreshdeskMonorailApiHelper(gconf, monorailApiWrapper, freshdeskTicketClient)
	crmIssueTrackerIntegrationHelperFactory := helper15.NewCrmIssueTrackerIntegrationHelperFactory(freshdeskMonorailApiHelper)
	service := crm_issue_tracker_integration.NewService(gconf, crmToIssueTrackerMappingDao, ticketTranslatorFactory, crmIssueTrackerIntegrationHelperFactory)
	return service, nil
}

func InitializeCrmIssueTrackerIntegrationConsumerService(conf *genconf.Config, crmIssueTrackerClient crm_issue_tracker_integration2.CrmIssueTrackerIntegrationClient) *consumer10.Service {
	freshdeskTicketChangeProcessor := processor13.NewFreshdeskTicketChangeProcessor(crmIssueTrackerClient)
	freshdeskTicketConversationProcessor := processor13.NewFreshdeskTicketConversationProcessor(conf, crmIssueTrackerClient)
	monorailUpdatesCommentsProcessor := processor13.NewMonorailUpdatesCommentsProcessor(conf, crmIssueTrackerClient)
	crmIssueTrackerEventProcessorFactory := processor13.NewCrmIssueTrackerEventProcessorFactory(freshdeskTicketChangeProcessor, freshdeskTicketConversationProcessor, monorailUpdatesCommentsProcessor)
	service := consumer10.NewService(crmIssueTrackerEventProcessorFactory)
	return service
}

func InitializeDataCollectorHelper(actorClient actor.ActorClient, piClient paymentinstrument.PiClient, usersClient user.UsersClient, pClient payment.PaymentClient, orderClient order.OrderServiceClient, savingsClient savings.SavingsClient, depositClient deposit.DepositClient, investmentCatalogManagerClient catalog.CatalogManagerClient, p2pInvestmentClient p2pinvestment.P2PInvestmentClient, portfolioManagerClient portfolio.PortfolioManagerClient, usstocksOrderManagerClient order2.OrderManagerClient, accountBalanceClinet balance.BalanceClient, ffClient firefly.FireflyClient) *helper.DataCollectorHelper {
	dataCollectorHelper := helper.NewDataCollectorHelper(actorClient, piClient, usersClient, pClient, orderClient, savingsClient, depositClient, investmentCatalogManagerClient, p2pInvestmentClient, portfolioManagerClient, usstocksOrderManagerClient, accountBalanceClinet, ffClient)
	return dataCollectorHelper
}

func InitializeQuestionResponseSubscriptionClient(db types.SherlockPGDB, eventBroker events.Broker) *feedback_subscription.FeedbackSubscriptionClient {
	inAppCsatResponsesDao := dao7.NewInAppCsatResponsesDao(db)
	supportTicketDao := dao7.NewSupportTicketDao(db)
	feedbackSubscriptionClient := feedback_subscription.NewFeedbackSubscriptionClient(inAppCsatResponsesDao, supportTicketDao, eventBroker)
	return feedbackSubscriptionClient
}

func InitializeEventConsumer(db types.SherlockPGDB, genConfig *genconf.Config, watsonClient watson.WatsonClient, ticketClient ticket.TicketClient) *error_activity.Consumer {
	eventConfigDaoImpl := dao19.NewEventConfigsDaoImpl(db)
	incidentDao := dao2.NewIncidentDao(db)
	createIncidentTriggerProcessor := trigger_processor.NewCreateIncidentTriggerProcessor(watsonClient, ticketClient, genConfig, incidentDao)
	resolveIncidentTriggerProcessor := trigger_processor.NewResolveIncidentTriggerProcessor(watsonClient)
	triggerProcessorFactoryImpl := trigger_processor.NewTriggerProcessorFactoryImpl(createIncidentTriggerProcessor, resolveIncidentTriggerProcessor)
	error_activityConsumer := error_activity.NewConsumer(eventConfigDaoImpl, genConfig, triggerProcessorFactoryImpl)
	return error_activityConsumer
}

func InitializeManualTicketStageWiseCommsService(db types.SherlockPGDB, genConfig *genconf.Config) *manual_ticket_stage_wise_comms2.ManualTicketStageWiseCommsService {
	supportTicketDao := dao7.NewSupportTicketDao(db)
	gormDB := GormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	issueConfigDaoPGDB := dao18.NewIssueConfigDaoPGDB(db, gormTxnExecutor)
	manualTicketStageWiseCommsService := manual_ticket_stage_wise_comms2.NewManualTicketStageWiseCommsService(supportTicketDao, issueConfigDaoPGDB, genConfig)
	return manualTicketStageWiseCommsService
}

// config: {"cxS3Client": "CxS3Config().BucketName"}
func InitializeIssueConfigService(db types.SherlockPGDB, genConfig *genconf.Config, cxS3Client types2.CxS3Client, redisClient types2.CxRedisStore) *issue_config.IssueConfig {
	issueCategoryDao := dao3.NewIssueCategoryDao(db)
	gormDB := GormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	issueConfigDaoPGDB := dao18.NewIssueConfigDaoPGDB(db, gormTxnExecutor)
	issueCategoryManagerImpl := manager.NewIssueCategoryManagerImpl(issueCategoryDao)
	csvHelper := issue_config.NewCsvHelper(issueCategoryManagerImpl)
	s3Client := types2.CxS3ClientProvider(cxS3Client)
	client := NewRedisClient(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	issueConfig := issue_config.NewIssueConfig(issueCategoryDao, genConfig, issueConfigDaoPGDB, issueCategoryManagerImpl, csvHelper, s3Client, redisCacheStorage)
	return issueConfig
}

func InitializeIssueCategoryService(db types.SherlockPGDB, actorActivityClient actor_activity.ActorActivityClient) *issue_category.IssueCategory {
	issueCategoryId := issue_category_id_fetcher.NewIssueCategoryId()
	activityMetaData := issue_category_id_fetcher.NewActivityMetaData(actorActivityClient)
	issueCategoryIdFactoryImpl := issue_category_id_fetcher.NewIssueCategoryIdFactoryImpl(issueCategoryId, activityMetaData)
	issueCategoryDao := dao3.NewIssueCategoryDao(db)
	issueCategory := issue_category.NewIssueCategory(issueCategoryIdFactoryImpl, issueCategoryDao)
	return issueCategory
}

func InitializeSherlockScriptsService(searchClient search.ActionBarClient, cxConfig *config.Config) *sherlock_scripts.SherlockScriptsService {
	strapiConfig := strapiConfigProvider(cxConfig)
	strapiApiKey := strapiApiKeyProvider(cxConfig)
	strapiStrapi := strapi.NewStrapiClient(strapiConfig, strapiApiKey)
	sherlockScriptHelperImpl := script_helper.NewSherlockScriptHelperImpl(strapiStrapi, cxConfig)
	sherlockScriptsService := sherlock_scripts.NewSherlockScriptsService(searchClient, sherlockScriptHelperImpl)
	return sherlockScriptsService
}

func InitializeSherlockSopService(db types.SherlockPGDB, cxConfig *config.Config, updateTicketPublisher types2.UpdateTicketPublisher, searchClient search.ActionBarClient, ticketServiceClient ticket.TicketClient) *sherlock_sop.SherlockSopService {
	strapiConfig := strapiConfigProvider(cxConfig)
	strapiApiKey := strapiApiKeyProvider(cxConfig)
	strapiStrapi := strapi.NewStrapiClient(strapiConfig, strapiApiKey)
	sherlockSopHelperImpl := sop_helper.NewSherlockSopHelperImpl(strapiStrapi, cxConfig)
	sherlockStepUserResponseDaoImpl := dao26.NewSherlockStepUserResponseDaoImpl(db)
	sherlockSopService := sherlock_sop.NewSherlockSopService(sherlockSopHelperImpl, updateTicketPublisher, sherlockStepUserResponseDaoImpl, searchClient, ticketServiceClient)
	return sherlockSopService
}

func InitializeRiskChartsService(txnAggregateClient txnaggregates.TxnAggregatesClient, employmentClient employment.EmploymentClient, authClient auth.AuthClient, caseManagementClient case_management.CaseManagementClient, savingsClient savings.SavingsClient, conf *genconf.Config, client pinot2.EODBalanceClient) *chart.Service {
	service := chart.NewService(txnAggregateClient, caseManagementClient, savingsClient, employmentClient, authClient, conf, client)
	return service
}

func InitializeCallIvrService(db types.SherlockPGDB, genConfig *genconf.Config, obClient onboarding.OnboardingClient, userClient user.UsersClient, savingsClient savings.SavingsClient, commsClient comms.CommsClient, redisClient types2.CxRedisStore, tieringClient tiering.TieringClient, riskProfileClient profile3.ProfileClient, ticketClient ticket.TicketClient, eventBroker events.Broker, actorClient actor.ActorClient, userGroupClient group.GroupClient, preApprovedLoanClient preapprovedloan2.PreApprovedLoanClient, compClient compliance.ComplianceClient, ffClient firefly.FireflyClient, cpClient provisioning.CardProvisioningClient, ccClient control.CardControlClient) *call_ivr.Service {
	client := NewRedisClient(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	callIvrDetailsDaoImpl := dao23.NewCallIvrDetailsDaoImpl(db)
	featureReleaseConfig := getDynFeatureReleaseConfig(genConfig)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	blockerImpl := blocker.NewBlocker(userClient, redisCacheStorage, genConfig, riskProfileClient, commsClient, tieringClient, ticketClient, eventBroker, evaluator, client, preApprovedLoanClient, compClient, ffClient)
	service := call_ivr.NewCallIvrService(genConfig, obClient, userClient, savingsClient, commsClient, redisCacheStorage, callIvrDetailsDaoImpl, blockerImpl, riskProfileClient, evaluator, ccClient, cpClient, ffClient, eventBroker)
	return service
}

func InitializeVKYCCallService(vkycCallClientToOnboardingServer pkg.VkycCallClientToOnboardingServer, vkycCallClientToSGApiGatewayServer pkg.VkycCallClientToSGApiGatewayServer, config2 *config.Config, genConfig *genconf.Config, userClient user.UsersClient, dbConn types.SherlockPGDB, vgNameCheckClient namecheck.UNNameCheckClient) *vkyccall3.Service {
	vKycCallClientWrapper := pkg.NewVkycCallClientWrapper(vkycCallClientToOnboardingServer, vkycCallClientToSGApiGatewayServer)
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(dbConn)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(dbConn)
	authFactorRetryLimit := getAuthFactoryRetryLimit(config2)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(dbConn)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConfig, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	federalNRIServiceProvider := serviceprovider.NewFederalNRIServiceProvider(vgNameCheckClient)
	stockGuardianServiceProvider := serviceprovider.NewStockGuardianServiceProvider()
	federalNRIQatarServiceProvider := serviceprovider.NewFederalNRIQatarServiceProvider(vgNameCheckClient)
	serviceproviderFactory := serviceprovider.NewFactory(federalNRIServiceProvider, stockGuardianServiceProvider, federalNRIQatarServiceProvider)
	service := vkyccall3.NewService(vKycCallClientWrapper, authEngine, serviceproviderFactory, vgNameCheckClient)
	return service
}

func InitialiseSGKycService(sgApiGwKycClient kyc3.KYCClient) *kyc4.Service {
	service := kyc4.NewService(sgApiGwKycClient)
	return service
}

func InitialiseEscalationsService(db types.SherlockPGDB, genConf *genconf.Config) *escalations.Service {
	escalationDao := dao13.NewEscalationDao(db)
	escalationConfig := escalationsConfigProvider(genConf)
	service := escalations.NewService(escalationDao, escalationConfig)
	return service
}

func InitializeNudgeParserService(segmentClient segment.SegmentationServiceClient) *nudge_parser.Service {
	service := nudge_parser.NewService(segmentClient)
	return service
}

func InitializeSavingsService(savingsClient savings.SavingsClient, db types.SherlockPGDB, genConf *genconf.Config, conf *config.Config, userClient user.UsersClient) *savings2.Service {
	customerAuthenticationDAO := dao.NewCustomerAuthenticationDAO(db)
	authFactorStatesDAO := dao.NewAuthFactorStatesDAO(db)
	authFactorRetryLimit := getAuthFactoryRetryLimit(conf)
	customerAuthenticationV2DAO := dao.NewCustomerAuthenticationV2DAO(db)
	authEngine := auth_engine.NewAuthEngine(customerAuthenticationDAO, authFactorStatesDAO, genConf, authFactorRetryLimit, userClient, customerAuthenticationV2DAO)
	service := savings2.NewService(authEngine, savingsClient, userClient)
	return service
}

// wire.go:

func WatsonIncidentInfoCollectorFactoryProvider(
	simulatorWatsonClientClient watson_client.WatsonClientClient,
	mockCxWatsonClient mock_client2.MockWatsonClientServiceClient,
	watsonSavingsClient watson2.WatsonClient,
	watsonOnboardingClient watson3.WatsonClient,
	payIncidentManagerClient payincidentmanager.PayIncidentManagerClient,
	investmentWatsonClient watson4.WatsonClient,
	watsonDepositsClient watson5.WatsonClient,
	p2pWatsonClient incidentmanager.IncidentManagerClient,
	errorActivityWatsonClient watson_info_provider.WatsonInfoProviderClient,
	manualTicketStageWiseCommsClient manual_ticket_stage_wise_comms.ManualTicketStageWiseCommsClient,
) collector.IWatsonIncidentInfoCollectorFactory {
	WatsonIncidentInfoCollectorFactory := collector.NewWatsonIncidentInfoCollectorFactory()

	WatsonIncidentInfoCollectorFactory.RegisterWatsonIncidentInfoCollector(typesv2.ServiceName_SIMULATOR_GRPC_SERVICE, simulatorWatsonClientClient)
	WatsonIncidentInfoCollectorFactory.RegisterWatsonIncidentInfoCollector(typesv2.ServiceName_CX_SERVICE, mockCxWatsonClient)
	WatsonIncidentInfoCollectorFactory.RegisterWatsonIncidentInfoCollector(typesv2.ServiceName_SAVINGS_SERVICE, watsonSavingsClient)
	WatsonIncidentInfoCollectorFactory.RegisterWatsonIncidentInfoCollector(typesv2.ServiceName_ONBOARDING_SERVICE, watsonOnboardingClient)
	WatsonIncidentInfoCollectorFactory.RegisterWatsonIncidentInfoCollector(typesv2.ServiceName_ORDER_SERVICE, payIncidentManagerClient)
	WatsonIncidentInfoCollectorFactory.RegisterWatsonIncidentInfoCollector(typesv2.ServiceName_INVESTMENT_SERVICE, investmentWatsonClient)
	WatsonIncidentInfoCollectorFactory.RegisterWatsonIncidentInfoCollector(typesv2.ServiceName_DEPOSIT_SERVICE, watsonDepositsClient)
	WatsonIncidentInfoCollectorFactory.RegisterWatsonIncidentInfoCollector(typesv2.ServiceName_P2P_INVESTMENT_SERVICE, p2pWatsonClient)
	WatsonIncidentInfoCollectorFactory.RegisterWatsonIncidentInfoCollector(typesv2.ServiceName_ERROR_ACTIVITY_SERVICE, errorActivityWatsonClient)
	WatsonIncidentInfoCollectorFactory.RegisterWatsonIncidentInfoCollector(typesv2.ServiceName_WATSON_MANUAL_TICKET_HANDLING_SERVICE, manualTicketStageWiseCommsClient)
	return WatsonIncidentInfoCollectorFactory
}

func CxCacheStorageProvider(redisStore types2.CxRedisStore) types2.CxCacheStore {
	cacheStorage := cache.NewRedisCacheStorage(redisStore)
	return cacheStorage
}

func uploadCreditMISToVendorPublisherProvider(ctx context.Context, conf *config.Config, sqsClient *sqs.Client) types2.UploadCreditMISToVendorPublisher {
	if !cfg.IsProdEnv(conf.Application.Environment) {
		pub, err := sqs2.NewPublisherWithConfig(ctx, conf.UploadCreditMISToVendorPublisher, sqsClient, nil)
		if err != nil {
			panic(err)
		}
		return pub
	}
	return nil
}

func accessControlFuncProvider() interceptor.AccessControlFunction {
	return interceptor.EnforceAccessControl
}

func getCxDescriptorProvider() interceptor.GetCxDescriptor {
	return interceptor.GetCxMethodDescriptor
}

func rateLimiterConfigProvider(conf *config.Config) *cfg.RateLimitConfig {
	return conf.RlConfig
}

func GormDBProvider(db types.SherlockPGDB) *gorm.DB {
	return db
}

func sherlockBannersCollectorFactoryProvider(
	getBannersHelper helper14.GetBannersHelper,
	palSherlockBannersClient sherlock_banners.PreApprovedLoanSherlockBannersClient,
	tieringClient tiering.TieringClient,
	riskProfileClient profile3.ProfileClient,
) collector2.ISherlockBannersCollectorFactory {
	sherlockBannersCollectorFactory := collector2.NewSherlockBannersCollectorFactory()
	cxSherlockBannersCollector := collector2.NewCxSherlockBannersCollector(getBannersHelper)
	sherlockBannersCollectorFactory.RegisterSherlockBannersCollector(typesv2.ServiceName_CX_SERVICE, cxSherlockBannersCollector)
	sherlockBannersCollectorFactory.RegisterSherlockBannersCollector(typesv2.ServiceName_PRE_APPROVED_LOAN_SERVICE, palSherlockBannersClient)
	sherlockBannersCollectorFactory.RegisterSherlockBannersCollector(typesv2.ServiceName_TIERING_SERVICE, tieringClient)
	sherlockBannersCollectorFactory.RegisterSherlockBannersCollector(typesv2.ServiceName_RISK_SERVICE, riskProfileClient)
	return sherlockBannersCollectorFactory
}

func NewRedisClient(cxRedisStore types2.CxRedisStore) *redis.Client {
	return cxRedisStore
}

func getAuthFactoryRetryLimit(conf *config.Config) *config.AuthFactorRetryLimit {
	return conf.AuthFactorRetryLimit
}

func getEmailVerificationConf(conf *config.Config) *config.EmailVerification {
	return conf.EmailVerification
}

func getCustomerAuthConf(conf *config.Config) *config.CustomerAuth {
	return conf.CustomerAuth
}

func getTransactionConf(conf *config.Config) *config.Transaction {
	return conf.Transaction
}

func getOrderConfig(conf *config.Config) *config.OrderConfig {
	return conf.OrderConfig
}

func getMobilePromptVerificationConf(conf *config.Config) *config.MobilePromptVerification {
	return conf.MobilePromptVerification
}

func getSherlockConf(conf *config.Config) *config.Sherlock {
	return conf.Sherlock
}

func getSecrets(conf *config.Config) *cfg.Secrets {
	return conf.Secrets
}

func getHttpClientForSherlock(conf *config.Config) *http.Client {
	tr := &http.Transport{
		MaxIdleConns:    conf.Sherlock.ClientMaxIdleConns,
		IdleConnTimeout: time.Duration(conf.Sherlock.ClientIdleConnTimeout) * time.Second,
	}

	httpClient := &http.Client{Transport: tr}
	return httpClient
}

func iftSlackAlertClientProvider(conf *config.Config) types2.IftSlackAlertClient {
	var slackClient *slack.Client
	if cfg.IsProdEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[config.IFTReportsSlackBotOauthToken], slack.OptionDebug(false))
	} else if cfg.IsSimulatedEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[config.IFTReportsSlackBotOauthToken], slack.OptionDebug(true))
	}
	return slackClient
}

func iftConfigProvider(conf *config.Config) *config.InternationalFundsTransferConfig {
	return conf.InternationalFundsTransferConfig
}

func getAuthFunc(conf *config.Config) interceptor.AuthFunction {
	env := conf.Application.Environment
	if env == cfg.TestEnv {
		return interceptor.MockVerifyAccessToken
	}
	return interceptor.VerifyAccessToken
}

func getCognitoIDPClient(ctx context.Context, conf *config.Config) *cognitoidentityprovider.Client {
	awsConf, err := config2.NewAWSConfig(ctx, conf.Aws.Region, true)
	if err != nil {
		panic(err)
	}
	return cognitoidentityprovider.NewFromConfig(awsConf)
}

func getCognitoUserPoolId(conf *config.Config) string {
	return conf.Cognito.UserPoolId
}

func getAuthValidationConfig(conf *config.Config) *config.AuthValidation {
	return conf.AuthValidation
}

func getCallRoutingConfig(gconf *genconf.Config) *genconf.CallRoutingConfig {
	return gconf.CallRoutingConfig()
}

func getPriorityRoutingConfig(conf *config.Config) *config.PriorityRoutingConfig {
	return conf.PriorityRoutingConfig
}

func getFeatureReleaseConfig(conf *config.Config) *config3.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig
}

func getDynFeatureReleaseConfig(gconf *genconf.Config) *genconf2.FeatureReleaseConfig {
	return gconf.FeatureReleaseConfig()
}

func csatConfigProvider(conf *genconf.Config) *genconf.CsatConfig {
	return conf.TicketConfig().CsatConfig()
}

func getCallConfig(conf *config.Config) *config.CallConfig {
	return conf.CallConfig
}

func getDynamicCallConfig(gconf *genconf.Config) *genconf.CallConfig {
	return gconf.CallConfig()
}

func getFreshChatConfig(conf *config.Config) *config.FreshChatConfig {
	return conf.FreshChatConfig
}

func getDynSalaryOpsConfig(gconf *genconf.Config) *genconf.SalaryOpsConfig {
	return gconf.SalaryOpsConfig()
}

func getSalaryOpsConfig(conf *config.Config) *config.SalaryOpsConfig {
	return conf.SalaryOpsConfig
}

func getTicketConfig(conf *config.Config) *config.TicketConfig {
	return conf.TicketConfig
}

func getDynTicketConfig(gconf *genconf.Config) *genconf.TicketConfig {
	return gconf.TicketConfig()
}

func getDynPayout(gconf *genconf.Config) *genconf.Payout {
	return gconf.Payout()
}

func getDynDispute(gconf *genconf.Config) *genconf.Dispute {
	return gconf.Dispute()
}

func getDispute(conf *config.Config) *config.Dispute {
	return conf.Dispute
}

func getAuditLog(conf *config.Config) *config.AuditLog {
	return conf.AuditLog
}

func getWatsonConfig(conf *config.Config) *config.WatsonConfig {
	return conf.WatsonConfig
}

func getKYCConfig(conf *config.Config) *config.KYCConfig {
	return conf.KYCConfig
}

func getSherlockUserRequestsConfig(conf *config.Config) *config.SherlockUserRequestsConfig {
	return conf.SherlockUserRequestsConfig
}

func getOnboardingStageDetailsMapping(conf *config.Config) map[string]*config.OnboardingStageDetails {
	return conf.OnboardingStageDetailsMapping
}

func getUsStocksOpsConfig(conf *config.Config) *config.UsStocksOpsConfig {
	return conf.UsStocksOpsConfig
}

func getAppLogsNotificationContent(conf *config.Config) *config.AppLogsNotificationContent {
	return conf.AppLogsNotificationContent
}

func getCommsConf(conf *config.Config) *config.Comms {
	return conf.Comms
}

func getBulkTicketJobConfig(conf *config.Config) *config.BulkTicketJobConfig {
	return conf.BulkTicketJobConfig
}

func getReferralConfig(conf *config.Config) *config.ReferralConfig {
	return conf.ReferralConfig
}

func getSalaryProgramLeadManagementConfig(conf *config.Config) *config.SalaryProgramLeadManagementConfig {
	return conf.SalaryProgramLeadManagementConfig
}

func getDynAppLog(gconf *genconf.Config) *genconf.AppLog {
	return gconf.AppLog()
}

// Initialise the HTTP client to invoke Monorail APIs (deployed on GCP) with the required credentials
func initialiseMonorailHttpClient(ctx context.Context, conf *config.Config) *api_wrapper.MonorailApiWrapper {
	keyJson := conf.Secrets.Ids[config.MonorailServiceAccountKey]
	key := &payload.ServiceAccountKey{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(keyJson), key)
	if err != nil {
		logger.ErrorNoCtx("failed to unmarshal monorail service account key", zap.Error(err))
		return nil
	}
	return api_wrapper.NewMonorailApiWrapper(ctx, conf.MonorailConfig, key)
}

// Initialise the HTTP client to invoke Airflow APIs with the required credentials
func initialiseAirflowHttpClient(conf *config.Config) (*wrapper.AirflowApiWrapper, error) {
	keyJson := conf.Secrets.Ids[config.AirflowUsernamePassword]
	key := &payload2.AirflowCredentials{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(keyJson), key)
	if err != nil {
		logger.ErrorNoCtx("failed to unmarshal airflow credentials", zap.Error(err))
		return nil, err
	}
	httpClient := &http.Client{
		Transport: &BasicAuthTransport{
			Username: key.Username,
			Password: key.Password,
		},
	}
	return wrapper.NewAirflowApiWrapper(conf.AirflowConfig, httpClient), nil
}

type BasicAuthTransport struct {
	Username string
	Password string
}

func (t *BasicAuthTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	req.SetBasicAuth(t.Username, t.Password)
	return http.DefaultTransport.RoundTrip(req)
}

func newS3Client(ctx context.Context, region, bucket string) (*s3.Client, error) {
	awsConfig, err := config2.NewAWSConfig(ctx, region, true)
	if err != nil {
		return nil, err
	}
	return s3.NewClient(awsConfig, bucket), nil
}

func getS3CallRecordingClient(ctx context.Context, conf *config.Config) (ticket2.S3RecordingClient, error) {
	return newS3Client(ctx, conf.Aws.Region, conf.CallRecording.CallRecordingBucketName)
}

func getS3TranscriptionClient(ctx context.Context, conf *config.Config) (ticket2.S3TranscriptClient, error) {
	return newS3Client(ctx, conf.Aws.Region, conf.CallRecording.CallTranscriptionBucketName)
}

func getDBStateCollectorFactory(
	commsDbStateClient developer.CommsDbStatesClient,
	devActorEntityClient developer2.DevActorClient,
	devDepositClient developer3.DevDepositClient,
	devKycClient developer4.DevKYCClient,
	devUserClient developer5.DevUserClient,
	orderDbStateClient developer6.DevClient,
	piDbStateClient developer7.DevPaymentIntrumentClient,
	devCardClient developer8.CardDbStatesClient,
	devLivenessClient developer9.DevLivenessClient,
	devSavingsClient developer10.SavingsDbStatesClient,
	upiDbStateClient developer11.DevClient,
	devInapphelpClient developer12.DevInapphelpClient,
	devCxClient developer13.DevCXClient,
	casbinDevClient developer14.DevCasbinClient,
	insightsDevClient developer15.DevInsightsClient,
	categorizerDevClient developer16.DevCategorizerClient,
	rewardsDevClient developer17.RewardsDevClient,
	devAuthClient developer18.DevAuthClient,
	vmDevClient developer19.DevVendorMappingClient,
	timelineDevClient developer20.DevTimelineClient,
	casperDevClient developer21.CasperDevClient,
	rmsDevClient developer22.RMSDbStatesClient,
	devMerchantClient developer23.DevMerchantClient,
	fitttDevClient developer24.FITTTDbStatesClient,
	caDevClient developer25.DevConnectedAccClient,
	devInappreferralClient developer26.DevInAppReferralClient,
	woOnbDevClient developer27.DevWealthOnboardingClient,
	investmentDBStateClient developer28.MutualFundDbStatesClient,
	devrecurringPaymentClient developer29.RecurringPaymentDevClient,
	enachDevClient developer30.EnachDevClient,
	devP2PInvestmentClient developer31.DevP2PInvestmentClient,
	devSegmentClient developer32.SegmentDbStatesClient,
	devNudgeClient developer33.NudgeDbStatesClient,
	salaryProgramDevClient developer34.SalaryProgramDevClient,
	analyserDevClient developer35.DevAnalyserClient,
	preApprovedDevClient developer36.DevPreApprovedLoanClient,
	ccDevClient developer37.DevFireflyClient,
	celestialDevClient developer38.DeveloperClient,
	riskDevClient developer39.DeveloperClient,
	scrnrDevClient developer40.DeveloperClient,
	devAuthOrchClient developer41.DevOrchestratorClient,
	payDevClient developer42.DevClient,
	usStocksDBStatesClient developer43.DBStateClient,
	tieringDevClient developer44.TieringDevServiceClient,
	amlDevClient developer45.AmlDevServiceClient,
	alfredDevClient developer46.DeveloperClient,
	panDevClient developer47.DeveloperClient,
	questDevClient developer48.QuestDbStatesClient,
	upcomingTxnsDevClient developer49.DevUpcomingTransactionsClient,
	healthEngineDevClient developer50.HealthEngineDevClient,
	cmsDevClient developer51.CmsDevClient,
	devBcClient developer52.DevBankCustClient,
	epifiTechVkycCallCollector vkyccall.EpifiTechVkycCallCollector,
	stockguardianVkycCallCollector vkyccall.StockguardianVkycCallCollector,
	epifiTechOmegleCollector omegle.EpifiTechOmegleCollector,
	stockguardianOmegleCollector omegle.StockguardianOmegleCollector,
	devEmpClient developer53.DevEmploymentClient,
	collectionDeveloperClient developer54.DeveloperClient,
	stockguardianApiGatewayDbStateCollector dbstate2.StockguardianApiGatewayDbStateCollector,
	devClient developer55.DeveloperClient,
	leadsDevClient developer56.DevLeadClient,
	crDevClient developer57.DevCreditReportClient,
	accountsDevClient developer58.AccountsDbStatesClient,
	npsDevClient developer59.NpsDbStatesClient,
	securitiesDevClient developer60.SecuritiesDevClient,
	salaryEstDevClient developer61.DevSalaryEstimationClient,
) collector3.ICollectorFactory {

	dbStateCollectorFactory := collector3.NewCollectorFactory()

	dbStateCollectorFactory.RegisterCollector(db_state.Service_COMMS, commsDbStateClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_ACTOR, devActorEntityClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_DEPOSIT, devDepositClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_KYC, devKycClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_USER, devUserClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_ORDER, orderDbStateClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_PAYMENT_INSTRUMENT, piDbStateClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_CARD, devCardClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_LIVENESS, devLivenessClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_SAVINGS, devSavingsClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_UPI, upiDbStateClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_INAPPHELP, devInapphelpClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_CX, devCxClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_CASBIN, casbinDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_INSIGHTS, insightsDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_CATEGORIZER, categorizerDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_REWARDS, rewardsDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_AUTH, devAuthClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_VENDOR_MAPPING, vmDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_TIMELINE, timelineDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_CASPER, casperDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_RMS, rmsDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_MERCHANT, devMerchantClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_FITTT, fitttDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_CONNECTED_ACCOUNT, caDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_IN_APP_REFERRAL, devInappreferralClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_WEALTH_ONBOARDING, woOnbDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_INVESTMENT, investmentDBStateClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_RECURRING_PAYMENT, devrecurringPaymentClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_ENACH, enachDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_P2P_INVESTMENT, devP2PInvestmentClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_SEGMENT, devSegmentClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_NUDGE, devNudgeClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_SALARY_PROGRAM, salaryProgramDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_ANALYSER, analyserDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_PRE_APPROVED_LOAN, preApprovedDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_FIREFLY, ccDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_CELESTIAL, celestialDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_RISK, riskDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_SCREENER, scrnrDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_AUTH_ORCHESTRATOR, devAuthOrchClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_PAY, payDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_USSTOCKS, usStocksDBStatesClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_TIERING, tieringDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_AML, amlDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_ALFRED, alfredDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_PAN, panDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_QUEST, questDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_UPCOMING_TRANSACTIONS, upcomingTxnsDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_HEALTH_ENGINE, healthEngineDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_CMS, cmsDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_BANK_CUSTOMER, devBcClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_OMEGLE, epifiTechOmegleCollector)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_STOCKGUARDIAN_OMEGLE, stockguardianOmegleCollector)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_VKYC_CALL, epifiTechVkycCallCollector)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_STOCKGUARDIAN_VKYC_CALL, stockguardianVkycCallCollector)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_EMPLOYMENT, devEmpClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_COLLECTION, collectionDeveloperClient)

	dbStateCollectorFactory.RegisterCollector(db_state.Service_STOCKGUARDIAN_CREDIT_REPORT, stockguardianApiGatewayDbStateCollector)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_STOCKGUARDIAN_CREDIT_RISK, stockguardianApiGatewayDbStateCollector)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_STOCKGUARDIAN_E_SIGN, stockguardianApiGatewayDbStateCollector)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_STOCKGUARDIAN_IN_HOUSE_BRE, stockguardianApiGatewayDbStateCollector)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_STOCKGUARDIAN_LMS, stockguardianApiGatewayDbStateCollector)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_STOCKGUARDIAN_APPLICATION, stockguardianApiGatewayDbStateCollector)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_STOCKGUARDIAN_BRE, stockguardianApiGatewayDbStateCollector)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_STOCKGUARDIAN_DOCS, stockguardianApiGatewayDbStateCollector)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_STOCKGUARDIAN_MATRIX, stockguardianApiGatewayDbStateCollector)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_STOCKGUARDIAN_CUSTOMER, stockguardianApiGatewayDbStateCollector)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_STOCKGUARDIAN_APPLICANT, stockguardianApiGatewayDbStateCollector)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_STOCKGUARDIAN_KYC_VENDOR_DATA, stockguardianApiGatewayDbStateCollector)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_TSP_USER, devClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_LEADS, leadsDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_CREDIT_REPORT, crDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_ACCOUNTS, accountsDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_NPS, npsDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_SECURITIES, securitiesDevClient)
	dbStateCollectorFactory.RegisterCollector(db_state.Service_SALARY_ESTIMATION, salaryEstDevClient)
	return dbStateCollectorFactory
}

func IFeatureStoreProvider(store2 *featurestore.FeatureStore) featurestore.IFeatureStore {
	return store2
}

func strapiConfigProvider(cxConfig *config.Config) *cfg.StrapiConfig {
	return cxConfig.StrapiConfig
}

func strapiApiKeyProvider(cxConfig *config.Config) types2.StrapiApiKey {
	return types2.StrapiApiKey(cxConfig.Secrets.Ids[config.StrapiApiKey])
}

func escalationsConfigProvider(conf *genconf.Config) *genconf.EscalationConfig {
	return conf.EscalationConfig()
}

func IsNewOperationalStatusAPIEnabled(conf *config.Config) accountstatus.UseNewOperationalStatusAPIFlag {
	return accountstatus.UseNewOperationalStatusAPIFlag(conf.IsNewOperationalStatusAPIEnabled)
}

// Provider for *opensearch.Client
func openSearchClientProvider(conf *genconf.Config) *opensearch.Client {
	awsConfig, cfgErr := config4.LoadDefaultConfig(context.Background(), config4.WithRegion(conf.Aws().Region))
	if cfgErr != nil {
		logger.Panic("error in loading aws config", zap.Error(cfgErr))
	}
	client := sts.NewFromConfig(awsConfig)
	creds := stscreds.NewAssumeRoleProvider(client, conf.EsConfig().RoleArn)
	awsConfig.Credentials = creds

	signer, errSigner := opensearch2.NewAWSSigner(awsConfig)
	if errSigner != nil {
		logger.Panic("err in getting AWS signer", zap.Error(errSigner))
	}

	esClient, errESClient := opensearch2.NewESClientWithRoleAuth(conf.EsConfig().ESEndpoint, signer)
	if errESClient != nil {
		logger.Panic("error in creating es client", zap.Error(errESClient))
	}

	return esClient
}

// Provider for *logsource.OpenSearchLogService
func NewOpenSearchLogService(client *opensearch.Client) *logsource.OpenSearchLogService {
	return logsource.NewOpenSearchLogService(client)
}
