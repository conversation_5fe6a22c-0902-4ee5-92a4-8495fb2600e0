//go:generate conf_gen github.com/epifi/gamma/cx/config Config

package config

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"
	"time"

	"github.com/knadh/koanf"
	"github.com/pkg/errors"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/cfg"
	cmdcfg "github.com/epifi/be-common/pkg/cmd/config"
	"github.com/epifi/be-common/pkg/keycloak"
	"github.com/epifi/be-common/pkg/logger"

	_ "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/screen_option_dependency"
	release "github.com/epifi/gamma/pkg/feature/release/config"
	payPkg "github.com/epifi/gamma/pkg/pay"
)

const (
	SherlockApiKey               = "SherlockApiKey"
	DbUsernamePassword           = "DbUsernamePassword"
	FreshchatAppId               = "FreshchatAppId"
	FreshchatAppKey              = "FreshchatAppKey"
	FederalPoolAccountNo         = "FederalPoolAccountNo"
	MonorailServiceAccountKey    = "MonorailServiceAccountKey"
	AirflowUsernamePassword      = "AirflowUsernamePassword"
	kafkaCredentials             = "KafkaCredentials"
	kafkaCaCertificate           = "KafkaCaCertificate"
	IFTReportsSlackBotOauthToken = "IFTReportsSlackBotOauthToken"
	StrapiApiKey                 = "StrapiApiKey"
)

var (
	once   sync.Once
	config *Config
	err    error
	kConf  *koanf.Koanf
)

var (
	_, b, _, _ = runtime.Caller(0)
)

func Load() (*Config, error) {
	once.Do(func() {
		config, kConf, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, *koanf.Koanf, error) {
	conf := &Config{}

	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()

	// loads config from file
	k, configPath, err := cfg.LoadConfigUsingKoanf(testConfigDirPath, cfg.CX_SERVICE)

	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to load dynamic config")
	}

	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to refresh dynamic config")
	}

	err = readAndSetEnv(conf)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to read and set env")
	}

	// validations for necessary properties which are bare minimum for server to start
	if conf.Application == nil || conf.Application.Environment == "" || conf.Application.Name == "" {
		logger.ErrorNoCtx("failed to load application env and name in config, please check indentation and properties")
	}
	if conf.Server == nil || conf.Server.Ports.GrpcPort == 0 || conf.Server.Ports.HttpPort == 0 {
		logger.ErrorNoCtx("failed to load server ports in config, please check indentation and properties")
	}

	secretsWithPgdbCerts := cfg.AddPgdbSslCertSecretIds(conf.EpifiDb, conf.Secrets.Ids)
	keyToSecret, err := cfg.LoadSecrets(secretsWithPgdbCerts, conf.Application.Environment, conf.Aws.Region)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to load secrets from SM")
	}
	if err := updateDefaultConfig(conf, keyToSecret); err != nil {
		return nil, nil, errors.Wrap(err, "failed to update default config")
	}
	for _, disputeConf := range conf.Dispute.ConfigVersionToDisputeConfigMap {
		disputeConf.DecisionTreeConfig.QuestionsCSVPath = fmt.Sprintf("%s/%s", *configPath, disputeConf.DecisionTreeConfig.QuestionsCSVPath)
		disputeConf.DecisionTreeConfig.DecisionTreeCSVPath = fmt.Sprintf("%s/%s", *configPath, disputeConf.DecisionTreeConfig.DecisionTreeCSVPath)
		disputeConf.DecisionTreeConfig.AppDecisionTreeCSVPath = fmt.Sprintf("%s/%s", *configPath, disputeConf.DecisionTreeConfig.AppDecisionTreeCSVPath)
	}
	return conf, k, nil
}

func GetLocalKConf() *koanf.Koanf {
	return kConf
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Ports.GrpcPort = intVal
	}

	if val, ok := os.LookupEnv("PGDB_HOST"); ok {
		c.EpifiDb.Host = val
	}

	if val, ok := os.LookupEnv("REDIS_HOST"); ok {
		c.RedisOptions.Addr = val
	}

	return nil
}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *Config, keyToSecret map[string]string) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_RDS)
	cfg.UpdateDbEndpointInConfig(c.EpifiDb, dbServerEndpoint)

	if err := readAndSetEnv(c); err != nil {
		return fmt.Errorf("failed to read and set env var: %w", err)
	}

	cfg.UpdatePGDBSecretValues(c.EpifiDb, c.Secrets, keyToSecret)
	// update db username and password in db config
	// if env is development keys are not fetched from SM hence update from yml file itself
	if c.Application.Environment == cfg.TestEnv || c.Application.Environment == cfg.DevelopmentEnv {
		cfg.UpdateDbUsernamePasswordInConfig(c.EpifiDb, c.Secrets.Ids[DbUsernamePassword])
		return nil
	}
	if _, ok := keyToSecret[DbUsernamePassword]; !ok {
		return fmt.Errorf("db username password not fetched from secrets manager")
	}
	cfg.UpdateDbUsernamePasswordInConfig(c.EpifiDb, keyToSecret[DbUsernamePassword])

	// update kafka username, password and ca cert for all kafka consumer groups
	if err := updateKafkaCgConfig(c.RudderEventKafkaConsumerGroup, keyToSecret); err != nil {
		return err
	}
	return nil
}

func updateKafkaCgConfig(kafkaCgConfig *cfg.KafkaConsumerGroup, keyToSecret map[string]string) error {
	// Apply the fetched credentials
	if kafkaCgConfig.SASLConfig.Enabled {
		if _, ok := keyToSecret[kafkaCredentials]; !ok {
			return fmt.Errorf("kafka cg username password not fetched from secrets manager")
		}
		cfg.UpdateKafkaCgUsernamePasswordInConfig(kafkaCgConfig, keyToSecret[kafkaCredentials])
	}

	if kafkaCgConfig.TLSConfig.Enabled {
		if _, ok := keyToSecret[kafkaCaCertificate]; !ok {
			return fmt.Errorf("kafka ca certificate not fetched from secrets manager")
		}
		kafkaCgConfig.TLSConfig.Certificate = keyToSecret[kafkaCaCertificate]
	}
	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

//go:generate conf_gen github.com/epifi/gamma/cx/config Config
type Config struct {
	Application                                          *Application
	Server                                               *Server
	Logging                                              *cfg.Logging
	EpifiDb                                              *cfg.DB
	Aws                                                  *Aws
	Cognito                                              *Cognito
	EmailVerification                                    *EmailVerification        `dynamic:"true"`
	MobilePromptVerification                             *MobilePromptVerification `dynamic:"true"`
	AuthFactorRetryLimit                                 *AuthFactorRetryLimit
	CustomerAuth                                         *CustomerAuth `dynamic:"true"`
	FreshdeskTicketPublisher                             *cfg.SqsPublisher
	FreshdeskTicketSubscriber                            *cfg.SqsSubscriber `dynamic:"true"`
	FreshdeskContactPublisher                            *cfg.SqsPublisher
	FreshdeskContactSubscriber                           *cfg.SqsSubscriber `dynamic:"true"`
	WatsonIncidentReportingPublisher                     *cfg.SqsPublisher
	WatsonIncidentReportingSubscriber                    *cfg.SqsSubscriber `dynamic:"true"`
	WatsonIncidentResolutionPublisher                    *cfg.SqsPublisher
	WatsonIncidentResolutionSubscriber                   *cfg.SqsSubscriber `dynamic:"true"`
	WatsonTicketEventPublisher                           *cfg.SqsPublisher
	WatsonTicketEventSubscriber                          *cfg.SqsSubscriber `dynamic:"true"`
	WatsonCreateTicketSubscriber                         *cfg.SqsSubscriber `dynamic:"true"`
	DisputePublisher                                     *cfg.SqsPublisher
	DisputeSubscriber                                    *cfg.SqsSubscriber `dynamic:"true"`
	DisputeCreateTicketPublisher                         *cfg.SqsPublisher
	DisputeCreateTicketSubscriber                        *cfg.SqsSubscriber `dynamic:"true"`
	DisputeUpdateTicketPublisher                         *cfg.SqsPublisher
	DisputeUpdateTicketSubscriber                        *cfg.SqsSubscriber `dynamic:"true"`
	DisputeAddNoteTicketPublisher                        *cfg.SqsPublisher
	DisputeAddNoteTicketSubscriber                       *cfg.SqsSubscriber `dynamic:"true"`
	DisputeExternalPublisher                             *cfg.SqsPublisher
	RMSEventPublisher                                    *cfg.SqsPublisher
	RewardsManualGiveawayEventPublisher                  *cfg.SqsPublisher
	DevActionPublisher                                   *cfg.SqsPublisher
	DevActionSubscriber                                  *cfg.SqsSubscriber `dynamic:"true"`
	FreshdeskTicketDataEventSubscriberFifo               *cfg.SqsSubscriber `dynamic:"true" iam:"external-consumer"`
	FreshdeskTicketDataEventSubscriber                   *cfg.SqsSubscriber `dynamic:"true" iam:"external-consumer"`
	UpdateTicketPublisher                                *cfg.SqsPublisher
	CreateTicketPublisher                                *cfg.SqsPublisher
	CelestialSignalWorkflowPublisher                     *cfg.SqsPublisher
	IFTFileProcessorEventPublisher                       *cfg.SqsPublisher
	RiskCasePublisher                                    *cfg.SqsPublisher
	RiskDisputePublisher                                 *cfg.SqsPublisher
	CrmIssueTrackerIntegrationPublisher                  *cfg.SqsPublisher
	CrmIssueTrackerIntegrationSubscriber                 *cfg.SqsSubscriber `dynamic:"true"`
	AuditLog                                             *AuditLog
	Transaction                                          *Transaction
	Sherlock                                             *Sherlock
	Secrets                                              *cfg.Secrets
	Dispute                                              *Dispute `dynamic:"true"`
	RedisOptions                                         *cfg.RedisOptions
	AppLog                                               *AppLog `dynamic:"true"`
	Flags                                                *Flags
	Comms                                                *Comms
	Payout                                               *Payout `dynamic:"true"`
	PayoutStatusCheckPublisher                           *cfg.SqsPublisher
	PayoutStatusCheckSubscriber                          *cfg.SqsSubscriber `dynamic:"true"`
	DisputeNotificationTemplates                         map[string]map[string]map[string]map[string]map[string]*DisputeNotificationContent
	OnboardingStageDetailsMapping                        map[string]*OnboardingStageDetails
	AppLogsNotificationContent                           *AppLogsNotificationContent
	WaitlistSubscriber                                   *cfg.SqsSubscriber `dynamic:"true"`
	KYCConfig                                            *KYCConfig
	RateLimit                                            *RateLimit
	RlConfig                                             *cfg.RateLimitConfig
	UsePkgRateLimiter                                    bool `dynamic:"true"`
	CallRecording                                        *CallRecording
	RudderStack                                          *cfg.RudderStackBroker
	CxEventAmountCategories                              *[]CxEventAmountCategory
	OrderConfig                                          *OrderConfig
	ReferralConfig                                       *ReferralConfig
	LivenessVideoConfig                                  *LivenessVideoConfig
	SupportTicketFreshdeskConfig                         *cfg.Freshdesk // need this config for converting freshdesk vendor protos to cx support ticket proto
	ProcessTicketJobConfig                               *cfg.CxTicketAutoResolutionJobConfig
	CxS3Config                                           *CxS3Config
	RiskS3Config                                         *RiskS3Config
	DataS3Config                                         *DataS3Config
	EpifiIconS3Config                                    *EpifiIconS3Config
	MaxCountThresholdForFetchingBulkUserInfo             int                         `dynamic:"true"`
	BulkUserInfoViaEmailConfig                           *BulkUserInfoViaEmailConfig `dynamic:"true"`
	UpiDisputeAutoUpdateEventSubscriber                  *cfg.SqsSubscriber          `dynamic:"true"`
	PriorityRoutingConfig                                *PriorityRoutingConfig
	FreshChatConfig                                      *FreshChatConfig
	AuthValidation                                       *AuthValidation
	UpdateTicketSubscriber                               *cfg.SqsSubscriber `dynamic:"true"`
	CreateTicketSubscriber                               *cfg.SqsSubscriber `dynamic:"true"`
	BulkTicketJobConfig                                  *BulkTicketJobConfig
	Tracing                                              *cfg.Tracing
	Profiling                                            *cfg.Profiling
	UploadCreditMISToVendorPublisher                     *cfg.SqsPublisher
	TicketConfig                                         *TicketConfig      `dynamic:"true"`
	CallConfig                                           *CallConfig        `dynamic:"true"`
	OzonetelCallEventSubscriber                          *cfg.SqsSubscriber `dynamic:"true"`
	BulkAccValidationViaEmailConfig                      *BulkAccValidationViaEmailConfig
	CallRoutingConfig                                    *CallRoutingConfig             `dynamic:"true"`
	FeatureReleaseConfig                                 *release.FeatureReleaseConfig  `dynamic:"true"`
	IssueResolutionFeedbackConfig                        *IssueResolutionFeedbackConfig `dynamic:"true"`
	ChatBotConfig                                        *ChatBotConfig                 `dynamic:"true"`
	FreshchatEventSubscriber                             *cfg.SqsSubscriber             `dynamic:"true"`
	NuggetEventSubscriber                                *cfg.SqsSubscriber             `dynamic:"true"`
	AccountFreezeStatusConfig                            *AccountFreezeStatusConfig
	SherlockFeedbackDetailsConfig                        *SherlockFeedbackDetailsConfig
	RiskConfig                                           *RiskConfig                `dynamic:"true"`
	InternationalFundTransfer                            *InternationalFundTransfer `dynamic:"true"`
	SalaryOpsConfig                                      *SalaryOpsConfig           `dynamic:"true"`
	IsRedactionEnabledForDBStates                        bool                       `dynamic:"true"`
	LandingPageConfig                                    *LandingPageConfig         `dynamic:"true"`
	SalaryProgramLeadManagementConfig                    *SalaryProgramLeadManagementConfig
	WatsonConfig                                         *WatsonConfig
	SprinklrConfig                                       *SprinklrConfig
	UsStocksOpsConfig                                    *UsStocksOpsConfig
	TicketReconciliationEventSubscriber                  *cfg.SqsSubscriber     `dynamic:"true"`
	DevActionHelperConfig                                *DevActionHelperConfig `dynamic:"true"`
	ClosedAccountConfig                                  *ClosedAccountConfig
	OverrideBankActions                                  *OverrideBankActions                  `dynamic:"true"`
	ReviewAction                                         *ReviewActionConfig                   `dynamic:"true"`
	VendorAccountPennyDropViaEmailConfig                 *VendorAccountPennyDropViaEmailConfig `dynamic:"true"`
	MonorailConfig                                       *cfg.MonorailConfig
	AirflowConfig                                        *cfg.AirflowConfig
	FreshdeskMonorailIntegrationConfig                   *FreshdeskMonorailIntegrationConfig
	EmployerDbConfig                                     *EmployerDbConfig    `dynamic:"true"`
	GRPCWebServerConfig                                  *GRPCWebServerConfig `dynamic:"true"`
	EmailValidationRegex                                 string
	SherlockUserRequestsConfig                           *SherlockUserRequestsConfig
	IssueCategoryIdForCategory                           *IssueCategoryIdForCategory
	SherlockBannersConfig                                *SherlockBannersConfig `dynamic:"true"`
	RewardsOrderUpdateEventQueuePublisher                *cfg.SqsPublisher
	RewardsCreditCardTxnEventQueuePublisher              *cfg.SqsPublisher
	CallRoutingEventPublisher                            *cfg.SnsPublisher
	TicketUpdateEventPublisher                           *cfg.SnsPublisher
	RudderEventKafkaConsumerGroup                        *cfg.KafkaConsumerGroup     `dynamic:"true"`
	ErrorActivityConfig                                  *ErrorActivityConfig        `dynamic:"true"`
	AgentPromptConfig                                    *AgentPromptConfig          `dynamic:"true"`
	RiskOpsInstalledAppsConfig                           *RiskOpsInstalledAppsConfig `dynamic:"true"`
	RiskFennelConfig                                     *RiskFennelConfig           `dynamic:"true"`
	StageWiseCommsConfig                                 *StageWiseCommsConfig       `dynamic:"true"`
	IssueConfigServiceConfig                             *IssueConfigServiceConfig   `dynamic:"true"`
	CasperItcDownloadFileQueuePublisher                  *cfg.SqsPublisher
	InternationalFundsTransferConfig                     *InternationalFundsTransferConfig
	S3EventSubscriber                                    *cfg.SqsSubscriber          `dynamic:"true"`
	S3EventConsumerConfig                                *S3EventConsumerConfig      `dynamic:"true"`
	RiskTxnReviewRolloutConfig                           *RiskTxnReviewRolloutConfig `dynamic:"true"`
	StrapiConfig                                         *cfg.StrapiConfig
	RiskOutcallFormRolloutConfig                         *RiskOutcallFormRolloutConfig `dynamic:"true"`
	OrderUpdateEventForTxnCategorizationPublisher        *cfg.SqsPublisher
	AATxnCategorizationPublisher                         *cfg.SqsPublisher
	CCTxnCategorizationPublisher                         *cfg.SqsPublisher
	CallIvrConfig                                        *CallIvrConfig                                        `dynamic:"true"`
	CaseManagementActorActivities                        *CaseManagementActorActivities                        `dynamic:"true"`
	WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail *WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail `dynamic:"true"`
	CXFreshdeskTicketBaseURL                             string                                                `dynamic:"true"`
	WatchlistReasons                                     []string                                              `dynamic:"true"`
	ContactUsModelResponseConfig                         *ContactUsModelResponseConfig                         `dynamic:"true"`
	S3BucketNameForFileGenerator                         *Filegenerator                                        `dynamic:"true"`
	EnableBalanceMetricsOnCaseManagement                 bool                                                  `dynamic:"true"`
	EnableOutCallDataInCaseManagementForRiskOps          bool                                                  `dynamic:"true"`
	DbStateConfig                                        *DbStateConfig                                        `dynamic:"true"`
	CreateTicketEventPublisher                           *cfg.SnsPublisher
	EscalationConfig                                     *EscalationConfig  `dynamic:"true"`
	FederalEscalationUpdateEventSubscriber               *cfg.SqsSubscriber `dynamic:"true"`
	FederalEscalationCreationEventSubscriber             *cfg.SqsSubscriber `dynamic:"true"`
	FederalEscalationCreateEventPublisher                *cfg.SqsPublisher
	FederalEscalationConfig                              *FederalEscalationConfig  `dynamic:"true"`
	SaClosureEligibilityConf                             *SaClosureEligibilityConf `dynamic:"true"`
	LienConfig                                           *LienConfig
	IsNewOperationalStatusAPIEnabled                     bool `dynamic:"true"`
	EsConfig                                             *EsConfig
	BigQueryConfig                                       *BigQueryConfig
	NuggetEventConfig                                    *NuggetEventConfig `dynamic:"true"`
	DevActionValidationConfig                            *DevActionValidationConfig `dynamic:"true"`
}

type Application struct {
	Environment   string
	Name          string
	IsSecureRedis bool
}

type Server struct {
	Ports *cfg.ServerPorts
}

type Aws struct {
	Region string
}

type EsConfig struct {
	RoleArn    string
	ESEndpoint string
}

type SaClosureEligibilityConf struct {
	MaxProcessingDuration time.Duration `dynamic:"true"`
	QpsRateLimit          uint32        `dynamic:"true"`
	MaxConcurrentWorkers  uint32        `dynamic:"true"`
	MaxActorsAllowedInCsv uint32        `dynamic:"true"`
}

type Cognito struct {
	UserPoolId string
	ClientId   string
}

type EmailVerification struct {
	VerificationUrl string `dynamic:"true"`
	FromEmail       string `dynamic:"true"`
	FromEmailName   string `dynamic:"true"`
}

type MobilePromptVerification struct {
	NotificationTitle string `dynamic:"true"`
	NotificationBody  string `dynamic:"true"`
	Validity          int64  `dynamic:"true"`
}

type AuthFactorRetryLimit struct {
	DOB                     int
	MobilePrompt            int
	EmailVerification       int
	TransactionAmount       int
	LastFivePanCharacters   int
	PermanentAddressPinCode int
	FathersName             int
	MothersName             int
	Default                 int
}

type CustomerAuth struct {
	AuthValidityDuration         string
	EmailValidityDuration        string
	AgentCacheValidityDuration   time.Duration
	IsAgentCachingEnabled        bool `dynamic:"true"`
	MobilePromptValidityDuration string
	MaxResetCount                int
	// priority of auth factors as per business logic
	// This is needed since client always needs the list returned in sorted order
	AuthFactorPriorityByPlatform map[string]*AuthFactorPriorityByPlatform `dynamic:"true"`
	IsInAppNotificationEnabled   bool                                     `dynamic:"true"`
	InAppNotificationTemplate    *InAppNotificationTemplate               `dynamic:"true"`
	// AuthFactorCacheKey is the key used store the auth factor for an actor in cache that will be used later
	// to create actual auth factor in db, when agent will try to access the ticket.
	// this is needed because at the time of creation of authId we need agent email
	// but in case of auth that is triggered externally to customer authentication service, for example
	// in app auth or IVR based auth, we don't have agent email available
	AuthFactorCacheKey              string        `dynamic:"true"`
	AuthFactorCacheValidityDuration time.Duration `dynamic:"true"`
	// IsSkippingAuthEnabledAfterExternalAuth flag decides whether we will skip manual auth that is done by agent,
	// after any externally triggered auth is successful like IVR based auth
	IsSkippingAuthEnabledAfterExternalAuth bool `dynamic:"true"`
}

type CallIvrConfig struct {
	// IsIvrEnabled indicates whether the users will go through a IVR flow
	IsIvrEnabled bool `dynamic:"true"`
	// IvrPollCountCacheKey will be used to store the number of polls
	// done for an actor id, after a certain number of polls, we can move the actor to other IVR question
	IvrPollCountCacheKey string `dynamic:"true"`
	// IvrPollCountCacheDuration denotes the duration of time for which the poll count
	// will be stored in the cache
	IvrPollCountCacheDuration time.Duration `dynamic:"true"`
	// IvrPollCountThreshold denotes the number of times we will allow the user auth status
	// to be polled before informing the user that we were not able to authenticate them
	IvrPollCountThreshold int32 `dynamic:"true"`

	// MaxInvalidInputCount denotes the maximum number of invalid inputs a user can give, after that will disconnect the call.
	MaxInvalidInputCount int32 `dynamic:"true"`
	MaxRepeatInputCount  int32 `dynamic:"true"`
	// IsCardBlockingEnabled controls whether the actual card blocking API should be called in the IVR flow
	// If false, the flow will work as normal but skip the actual card blocking API call
	IsCardBlockingEnabled bool `dynamic:"true"`
}

type InAppNotificationTemplate struct {
	// corresponds to notification time stamp color for fcm common template
	NotificationTimestampFontColor string `dynamic:"true"`

	// corresponds to icon url values to be required in icon attributes
	IconUrl string `dynamic:"true"`

	// corresponds to icon url values to be required in icon bg color
	IconBGColor string `dynamic:"true"`

	// corresponds to icon url values to be required in icon font color
	TitleFontColor string `dynamic:"true"`

	// corresponds to background color for fcm common template
	BGColor string `dynamic:"true"`

	// corresponds to notification dismiss icon bg color for fcm common template
	NotificationDismissIconBgColor string `dynamic:"true"`

	// corresponds to notification dismiss icon color for fcm common template
	NotificationDismissIconColor string `dynamic:"true"`

	// corresponds to notification shadow icon color for fcm common template
	NotificationShadowColor string `dynamic:"true"`

	// corresponds to mobile prompt title for PNs
	MobilePromptTitlePN string `dynamic:"true"`

	// corresponds to mobile prompt title for InApp notification
	MobilePromptTitleInApp string `dynamic:"true"`

	// corresponds to mobile prompt body for InApp notification
	MobilePromptBody string `dynamic:"true"`
}

type AuthFactorPriorityByPlatform struct {
	MinAppVersion uint32           `dynamic:"true"`
	PriorityMap   map[string]int32 `dynamic:"true"`
}
type AuditLog struct {
	// default limit on number of rows in audit logs response
	DefaultLimit int32
}

type Transaction struct {
	ToleranceValue              int64
	NumTxnToFetch               int32
	PageSize                    int32
	PageSizeForExistingMandates int32
}

type Comms struct {
	PageSize int32
}

type Sherlock struct {
	SherlockCallbackURL   string
	ClientMaxIdleConns    int
	ClientIdleConnTimeout int
}

type ControlledReleaseConfig struct {
	IsGroupCheckEnabled bool            `dynamic:"true"`
	EnabledGroupMap     map[string]bool `dynamic:"true"`
	MinAppVersion       int             `dynamic:"true"`
	MaxAppVersion       int             `dynamic:"true"`
}

type Dispute struct {
	GeneratedCsvFolder                  string
	S3BucketName                        string `iam:"s3-readwrite"`
	MaxThresholdDurationForEscalation   string
	MaxAttemptCountForReverseProcessing int
	DisputeUDIRConfig                   *DisputeUDIRConfig
	// this const indicates the question code for upi receiver type question i.e p2p vs p2m
	// we need to identify this question in code since we don't want to show this question to user and
	// programmatically answer it using payment details
	UPIReceiverTypeQuestionCode string
	// default config version to be used if restricted access is disabled or no version mapping found for given user group
	DefaultDisputeConfigVersion string
	// flag to indicate if restricted release is enabled for any dispute config version
	// if this flag is set to false we will directly use default config version specified and won't check for user groups
	// if set to true we will fetch user groups and check for user group to config version mapping
	IsRestrictedReleaseEnabledForConfigVersion     bool
	DisputeConfigVersionToSherlockReleaseConfigMap map[string]*ControlledReleaseConfig
	// map will contain release config against dispute config versions
	// key will be dispute config version enum as string, value will be release config struct
	// we will check eligibility from the latest dispute config version to the lowest one and select the one which is passing the conditions
	// if more than one config versions are passing the check then we will use the latest version out of them
	// if none of the versions are passing the check then we will fallback to the default dispute config version
	DisputeConfigVersionAndPlatformToReleaseConfMap map[string]map[string]*ControlledReleaseConfig
	ConfigVersionToDisputeConfigMap                 map[string]*DisputeVersionSpecificConfig
	IsIssueResolutionFeedbackCommsEnabledForDispute bool `dynamic:"true"`
	DisputeAppCopies                                *DisputeAppCopies
	DisputeJobConfig                                *DisputeJobConfig `dynamic:"true"`
	MaxPageSize                                     int32
	MaximumDaysDuration                             time.Duration
	// if this flag is set to true, we will use V2 impl for GetNextQuestionForApp RPC
	IsGetNextQuestionsForAppV2Enabled bool `dynamic:"true"`
	// if this flag is set to true, we will use V2 impl for GetNextQuestion RPC
	IsGetNextQuestionsV2Enabled bool `dynamic:"true"`
	// if this flag is set to true
	// we will evaluate if given UPI txn belongs to external provenance before querying dispute config table
	IsUpiExternalProvenanceEvaluationEnabled bool `dynamic:"true"`
	// map of config versions for which we need to evaluate
	// previous attempt status value for given txn id
	ConfigVersionValidityToPreviousAttemptStatusEvaluationMap map[string]bool
	// cool off period before we check status of dispute on Federal's DMP System
	MinThresholdDurationForStatusCheck string
	// error message thrown when a Freshdesk Ticket is already marked for given dispute
	TicketAlreadyExistsErrMsg string
	// HTML formatted document link tag which has to be returned to Sherlock
	DisputeDocumentLinkTagForSherlock string
	// A map to indicate if a given issue category Id is eligible for Issue resolution feedback ingestion
	IssueCategoryEligibilityForIssueResolutionFeedback map[string]bool
	// contains config variables required to send email for dmp escalated disputes
	DmpEmailConfig *DmpEmailConfig
	// contains time window details during which raising a dispute is allowed
	DMPRaiseDisputeWindowConfig *DMPRaiseDisputeWindowConfig
	// TTL for idempotency in dispute request
	DisputeIdempotencyTTL string `dynamic:"true"`
}

type DMPRaiseDisputeWindowConfig struct {
	DisputeTypeRaiseDisputeWindowConfig map[string]map[string]time.Duration
}

type DmpEmailConfig struct {
	// email id from which email has to be sent: <EMAIL>
	FromEmailId string
	// name which has to be displayed associated to that email: Fi care
	FromEmailName string
}

type DisputeJobConfig struct {
	// delay time between processing 2 dispute tickets to avoid hitting freshdesk rate limits
	ReverseProcessingDelayDurationBetweenTickets string `dynamic:"true"`
	// max time period after which job should time out and stop processing disputes
	ReverseProcessingJobTimeout string `dynamic:"true"`
	// max time period after which job should time out and stop escalating disputes
	EscalationJobTimeout string
}

type DisputeAppCopies struct {
	// screen title to be shown on questionnaire screens
	QuestionnaireScreenTitle string
	// preview title to be shown on questions preview screen
	PreviewScreenTitle string
	// map of answer data type to placeholder text to be shown in the app
	// we should use this map to get placeholder text in case of optional questions
	OptionalAnswerPlaceholderTextMap map[string]string
	// map of answer data type to placeholder text to be shown in the app
	// we should use this map to get placeholder text in case of mandatory questions
	MandatoryAnswerPlaceholderTextMap map[string]string
	// content copies for bottom sheet elements
	BottomSheetCopies *BottomSheetCopies
	// dispute section title to be shown on the app
	DisputeDetailsTitle string
}

type BottomSheetContent struct {
	Title       string
	Description string
}

type BottomSheetCopies struct {
	// CTA label for ok action in dispute bottom sheet
	OkCTALabel string
	// CTA label for raise dispute action
	RaiseDisputeCTALabel string
	// CTA label for help action
	HelpCTALabel string
	// CTA label for report fraud action
	ReportFraudCTALabel string
	// content to be used if txn is in cool off currently
	TxnInCoolOff *BottomSheetContent
	// bottom sheet description to be shown to user if given txn channel or protocol is not eligible for disputes from app
	TxnChannelNotAllowed *BottomSheetContent
	// title and description for bottom sheet if dispute already exists for given txn
	DisputeAlreadyExists *BottomSheetContent
	// title and description for bottom sheet if actor is receiver for the txn
	ActorIsReceiver *BottomSheetContent
	// bottom sheet content to be be used if raising dispute is not allowed for given txn
	RaisingDisputeNotAllowed *BottomSheetContent
	// bottom sheet context to be shown if it's an off app txn
	OffAppTxn *BottomSheetContent
	// bottom sheet to be shown if raising dispute is allowed for given txn
	RaisingDisputeAllowed *BottomSheetContent
	// bottom sheet to be shown if raising dispute is allowed for given txn to report it as fraud
	RaisingDisputeAllowedWithReportFraud *BottomSheetContent
}

type DisputeDecisionTreeConfig struct {
	QuestionsCSVPath       string
	DecisionTreeCSVPath    string
	AppDecisionTreeCSVPath string
}

// DisputeVersionSpecificConfig should to be used for config fields which will change across different config versions
// whatever config values we expect to change with config version can be added in this struct
type DisputeVersionSpecificConfig struct {
	// this will contain decision tree config for given dispute config version
	// each dispute config version can have different decision tree config
	// and we will use the appropriate one based on version used currently
	DecisionTreeConfig *DisputeDecisionTreeConfig
	// we will keep a config of which payment protocols are allowed from the app for raising disputes
	// key will be payment protocol as string and bool value will indicate should that payment protocol be allowed from the app
	// if mapping is not present we will consider that payment protocol as not allowed
	PaymentProtocolAllowedFromApp map[string]bool
}

type DisputeUDIRConfig struct {
	// map containing details of dispute stages to be shown on app
	DisputeStageDetailsMap               map[string]*DisputeStageDetails
	UdirComplaintActionToDisputeStageMap map[string]string
	AllowForceRefreshInApp               bool
	// this field will contain mapping from udir complaint action to dispute state
	// whenever we get an auto update events or force refresh, we will need to check for complaint action
	// sent by NPCI and update the dispute state accordingly
	ComplaintActionToDisputeStateMapping map[string]string
	// set this to true to enable issue resolution feedback comms
	IsIssueResolutionFeedbackCommsEnabledForDispute bool
	DisputeSourceToInitiationModeMap                map[string]string
}

type AppLog struct {
	LogTTL             string `dynamic:"true"`
	MaxLogCountPerUser int64
	LogChunkSize       int
}

type Flags struct {
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool
}

type Payout struct {
	StatusCheckDelay                   string         // this is the delay duration between 2 status check events for payouts
	MaxNumberOfPayoutsAllowedPerTicket uint64         // this is the maximum number of payouts allowed per ticket id
	CashPayout                         *CashPayout    `dynamic:"true"`
	FiCoinsPayout                      *FiCoinsPayout `dynamic:"true"`
}

type CashPayout struct {
	MaxPayoutValueAllowedPerTicket uint64
	MinPayoutValueAllowedPerTicket uint64
	// SingleAmountLimitForApproval for a ticket, if this value is breached, payout request will go for approval
	SingleAmountLimitForApproval int64
	// MaxPayoutAllowedInTimeframe is the maximum payout value allowed in the duration defined by TimeframeDuration
	MaxPayoutAllowedInTimeframe int64
	// Duration for which timeframe check will be applicable
	TimeframeDuration                string
	FromActorIdForOrder              string
	FromPiIdForOrder                 string
	EnableB2CTransactionViaCelestial bool `dynamic:"true"`
}

type FiCoinsPayout struct {
	// Maximum payout value per ticker id
	MaxPayoutValueAllowedPerTicket uint64
	// SingleAmountLimitForApproval for a ticket, if this value is breached, payout request will go for approval
	SingleAmountLimitForApproval int64
	// map of requester role to payout timeframe duration constraint to PayoutAmountDisbursalConstraint
	MaxAmountDisbursalInDurationByRole map[string]map[string]PayoutAmountDisbursalConstraint
	MaxAmountDisbursalPerUser          *PayoutAmountDisbursalConstraint
	IsPayoutViaFiCoinsEnabled          bool `dynamic:"true"`
	// Offer id provided by rewards team for awarding fi coins
	FiCoinsRewardOfferId string
	// Whitelisted payout values - request with only whitelisted payout value will be considered valid
	AllowedValues []uint64
}

type PayoutAmountDisbursalConstraint struct {
	// Max amount allowed to be disbursed according to the constraint
	MaxAmountAllowedToBeDisbursed uint64
	// Action to be taken if the MaxAmountAllowedToBeDisbursed value is breached
	MaxAmountBreachFallbackAction string
}

type DisputeNotificationContent struct {
	Title    string
	Template string
}

type OnboardingStageDetails struct {
	Order       int32
	Description string
}

type AppLogsNotificationContent struct {
	Title    string
	Template string
}

type KYCConfig struct {
	SummaryInterpretationMap  map[string]map[string]string
	CallInfoInterpretationMap map[string]map[string]string
}

type RateLimit struct {
	// max number of request a user can make in a min for all sherlock apis in total
	MaxRequestsPerMinPerUser int64
	// max number of request a user can make in a min for all
	MaxRequestPerMinPerUserPerApiDefault int64
	// map of rpc method name to max request count
	// if mapping is not added for a rpc method MaxRequestPerMinPerUserPerApiDefault will be used
	MaxRequestPerMinPerUserPerApiMap map[string]int64
}

type CallRecording struct {
	CallRecordingBucketName     string `iam:"s3-readonly"`
	CallTranscriptionBucketName string `iam:"s3-readonly"`
}

type CxEventAmountCategory struct {
	Name      string
	MinAmount int64
	MaxAmount int64
}

type OrderConfig struct {
	TxnCountForLastNTxns int
}

type ReferralConfig struct {
	RefereePageSize int32
}

type LivenessVideoConfig struct {
	S3BucketName string `iam:"s3-readwrite"`
}

type ProcessTicketJobConfig struct {
	// map of processor type to max number of tickets to be processed
	// key will be processor type enum defined in cx/jobs/enums.proto as string
	MaxTicketsThresholdMap map[string]int64

	// job run stats email parameters which are passed to comms client
	JobStatsEmailParam *JobStatsEmailParam

	// number of days till which tickets are fetched
	NumberOfDays int64

	// map to control ticket processing for respective processors
	IsTicketProcessorEnabled map[string]bool
}

type JobStatsEmailParam struct {
	FromEmailId        string
	FromEmailName      string
	ReceiverMailIdList map[string]map[string]*MailInfo
	EmailMsg           map[string]string
}

type MailInfo struct {
	EmailName string
	EmailId   string
}

type CxS3Config struct {
	BucketName                       string `iam:"s3-readwrite"`
	BulkUserDetailsFolderName        string
	MarketingCampaignUsersFolderName string
	// folders to be used by risk case upload dev action
	// this folder will be used for putting the files to be processed by air-flow job downstream
	RiskCasesForProcessingFolderName string
	// this folder will be used for moving the files from processing folder to processed folder once it is processed by the jobs
	RiskCasesProcessedFolderName string
	// Folder used for storing logs uploaded from the Fi app
	AppLogsFolderName string
	// this folder will be used for storing CSV files containing data attribute lists by the data extraction dev action
	DataExtractionFolderName string
}

type RiskS3Config struct {
	BucketName string `iam:"s3-readwrite"`
}

type EpifiIconS3Config struct {
	BucketName string `iam:"s3-readwrite"`
}

type DataS3Config struct {
	BucketName string `iam:"s3-readwrite" s3_prefix_field:"S3Prefix"`
	S3Prefix   string
	// static segment file upload via jenkins job
	StaticSegmentSrcFolderPath string
	// static segment file path for redis consumption
	StaticSegmentDestFolderPath string
	// schema name for segment master customer record table
	SegmentMasterTableSchemaName string
	// table name for segment master customer record
	SegmentMasterTableName string
	// selected field from segment master customer record, i.e., actor_id
	SegmentMasterTableSelectedField string
}

type BigQueryConfig struct {
	//  GCP project name where the user attributes table resides
	SegmentUserAttributeProject string
	// dataset name within the project for user attributes table
	SegmentUserAttributeDataset string
	// name of the BigQuery table containing user attributes
	SegmentUserAttributeTable string
	// selected field from segment user attributes table, i.e., actor_id
	SegmentMasterTableRequiredField string
}

type DisputeStageDetails struct {
	// display name to be shown on app for given stage
	DisplayName string
	// order rank of stage
	// this will indicate in what order stages needs to be shown on app
	Rank int32
}

type PriorityRoutingConfig struct {
	ThresholdBalance int64
}

type FreshChatConfig struct {
	UserCategoryEnumToFreshChatLabelMapping   map[string]string
	FreshChatCustomUserEmailFormat            string
	FreshChatCustomUserEmailSuffix            string
	FreshChatDefaultEmailIdSuffix             string
	FreshChatMaxLengthOfOtherEmailAddressList int
	AgentCacheValidityDuration                time.Duration
	FreshChatDomain                           string
}

type AuthValidation struct {
	MethodListForSkippingAccessControlValidation []string
	// SkipAuthForTestAutomationAgent needs to be enabled in envs where the test automation agent will be used to access CX APIs
	// directly from the automation tool. This is required because the test automation agent does not have a valid JWT token.
	// This is supported for Unary RPCs only.
	SkipAuthForTestAutomationAgent bool
	// TestAutomationAgentEmail is the list of email ids of the test automation agent for which we need to skip auth. This is used only when
	// SkipAuthForTestAutomationAgent is enabled.
	TestAutomationAgentEmails []string
}

type BulkUserInfoViaEmailConfig struct {
	FromEmailId                string `dynamic:"true"`
	FromEmailName              string `dynamic:"true"`
	MaxCountThreshold          int    `dynamic:"true"`
	FieldToCsvColNameMap       map[string]string
	RoleToAllowedRespFieldsMap map[string]map[string]bool
}

type BulkTicketJobConfig struct {
	// max number of tickets that can be processed in a bulk job
	MaxTicketThreshold int
}

type TicketConfig struct {
	URL                              string
	IsStatusResolvedMap              map[int64]bool
	BulkResolutionModeValue          string
	ShowTicketsInAppConfig           *ShowTicketsInAppConfig `dynamic:"true"`
	SLAConfig                        *SLAConfig              `dynamic:"true"`
	EscalationTeamEnumToValueMapping map[string]string
	ProductCategoryFieldId           int64
	TicketFieldCacheValidityDuration time.Duration `dynamic:"true"`
	// IsTicketListLoggingEnabled is used to temporarily log ticket list in response of getSupportTicketsForSherlock rpc
	IsTicketListLoggingEnabled bool `dynamic:"true"`
	// IsTicketListLoggingEnabled denotes whether to log an identifier for all events received from Freshdesk
	IsTicketEventLoggingEnabled bool `dynamic:"true"`
	// CsatConfig contains parameters related to triggering the CSAT survey via different channels
	CsatConfig *CsatConfig `dynamic:"true"`
	// IsDefaultTitleAndDescriptionEnabledForInAppTicket flag indicates if DefaultAppTicketTitle and DefaultAppTicketDescription will be
	// populated for in app ticket if corresponding title and description are not found in db
	IsDefaultTitleAndDescriptionEnabledForInAppTicket bool   `dynamic:"true"`
	DefaultAppTicketTitle                             string `dynamic:"true"`
	DefaultAppTicketDescription                       string `dynamic:"true"`
	// user's latest ticket is shown in home page, hence caching it
	LatestTicketCacheValidityDuration time.Duration `dynamic:"true"`
	// feature flag to decide whether to publish ticket update event to SNS topic
	IsTicketUpdateEventPublishingEnabled bool `dynamic:"true"`
}

type CsatConfig struct {
	// title of the push notification which is triggered for in app CSAT
	PushNotificationTitle string `dynamic:"true"`
	// description of the push notification which is triggered for in app CSAT
	PushNotificationDescription string `dynamic:"true"`
	// AllowedTicketStatusesForCsat contains the list of status enums in string format
	// for which push notification will be triggered to get in app CSAT
	AllowedTicketStatusesForCsat  []string      `dynamic:"true"`
	AllowedCommsTypeForCsat       []string      `dynamic:"true"`
	CommsExternalRefIdPrefix      string        `dynamic:"true"`
	WebFormUrl                    string        `dynamic:"true"`
	IsCsatCollectionEnabledViaWeb bool          `dynamic:"true"`
	CsatEligibilityWindow         time.Duration `dynamic:"true"`
	PageLimit                     int           `dynamic:"true"`
	PageSize                      uint32        `dynamic:"true"`
}

type ShowTicketsInAppConfig struct {
	CutOffDateToShowTickets             time.Time     `dynamic:"true"`
	TimeLimitForUpdatingTicketDetails   time.Duration `dynamic:"true"`
	WhitelistedProductCategories        []string      `dynamic:"true"`
	MandatoryFieldsRequiredToShowTicket []string      `dynamic:"true"`
	CreatedByEnumToValueMapping         map[string]string
	CreationModeEnumToValueMapping      map[string]string
}

type SLAConfig struct {
	// IsSLACalculationEnabledInTicketConsumer flag controls whether SLA calculation will happen in ticket consumer
	IsSLACalculationEnabledInTicketConsumer bool `dynamic:"true"`
	// IsExpectedResolutionByFieldDeterminedUsingSLA flag controls whether expected resolution time
	// would be populated in GetSupportTicketForApp RPC response
	IsExpectedResolutionByFieldDeterminedUsingSLA bool `dynamic:"true"`
	// IsEventPublishedToUpdateExpectedResolutionTimeFieldOnFreshdesk flag determines whether event will be published in common consumer
	// to update expected resolution time on freshdesk "expected resolution date" custom field
	IsEventPublishedToUpdateExpectedResolutionTimeFieldOnFreshdesk bool `dynamic:"true"`
}

type LandingPageConfig struct {
	// IsExpectedResolutionTimeFieldPopulatedInLandingPageService flag determines if expected resolution time
	// field would be populated in response of landing page service
	IsExpectedResolutionTimeFieldPopulatedInLandingPageService bool `dynamic:"true"`
	// config for showing recent user queries in landing page
	RecentUserQueryConfig *RecentUserQueryConfig `dynamic:"true"`
	// list of tabs available in sherlock v2 home
	UserDetailTabsEnumToStringMapping map[string]string
}

type RecentUserQueryConfig struct {
	NumberOfQueriesToDisplay int64  `dynamic:"true"`
	DateFormat               string `dynamic:"true"`
	TimeFormat               string `dynamic:"true"`
}

type CallConfig struct {
	MaximumDaysDuration        time.Duration
	MaxPageSize                int64
	CallStartTicketSubject     string
	CallStartTicketDescription string
	UpdateTicketErrMsg         string
	TicketAlreadyExistsErrMsg  string
	AgentAssignmentErrMsg      string
	TicketDbUpdateErrMsg       string
	FreshdeskTicketCreationErr string
	CallEndTicketDescription   string
	CallEndTicketPrivateNote   string
	// if this flag is set to true, we will log events in consumer using info logs
	// this should be used only for debugging purpose to validate delta between raw ozonetel events and parsed events
	// this flag should be by default turned off
	IsConsumerEventLoggingEnabled bool `dynamic:"true"`
	// call recording file path in epifi-ozonetel S3 bucket
	RecordingFilePath string
	// error message to display if the recording file is not found in epifi ozonetel S3 bucket
	RecordingFileNotFoundErrMsg string
	// HTML formatted call recording link tag which has to be returned to Sherlock
	RecordingLinkTagForSherlock string
	AbandonedCallConfig         *AbandonedCallConfig `dynamic:"true"`
	// feature flag to represent whether the call blocking functionality is enabled
	// [deprecated] moved inside call blocker config
	IsCallBlockerEnabled            bool `dynamic:"true"`
	IsCallBlockingEnabledViaIvrFlow bool `dynamic:"true"`
	// config to test various cases of call blocking
	// this is done as non-prod campaign setup for ozonetel is missing.
	// we need to test every case that is only possible in production environment,
	// this config will help us bypass checks (risk / user related) for CX_INTERNAL user group and perform end to end testing
	CallBlockerTestConfig *CallBlockerTestConfig `dynamic:"true"`
	CallBlockerConfig     *CallBlockerConfig     `dynamic:"true"`
}

type CallBlockerConfig struct {
	// feature flag to represent whether the call blocking functionality is enabled
	IsCallBlockerEnabled            bool     `dynamic:"true"`
	IsCallBlockingEnabledViaIvrFlow bool     `dynamic:"true"`
	CallDropOffNotificationTitle    string   `dynamic:"true"`
	CallDropOffNotificationBody     string   `dynamic:"true"`
	ContactUsFlowSmsLink            string   `dynamic:"true"`
	FiAppDownloadLink               string   `dynamic:"true"`
	BlockTierList                   []string `dynamic:"true"`
	// triaged tier means list of tier we want to allow but only if they meet certain criteria
	// (ex. user has an active loan)
	TriagedTierList []string      `dynamic:"true"`
	RequestTimeout  time.Duration `dynamic:"true"`
}

type CallBlockerTestConfig struct {
	UnRegisteredUser  bool `dynamic:"true"`
	AppAccessBlocked  bool `dynamic:"true"`
	UserReportedIssue bool `dynamic:"true"`
	IsCreditFreeze    bool `dynamic:"true"`
	IsStandardTier    bool `dynamic:"true"`
	IsRiskBlocked     bool `dynamic:"true"`
}

type BulkAccValidationViaEmailConfig struct {
	FromEmailId   string
	FromEmailName string
	ToEmailId     string
	ToEmailName   string
}

type VendorAccountPennyDropViaEmailConfig struct {
	FromEmailId   string
	FromEmailName string
	ToEmailId     string
	ToEmailName   string `dynamic:"true"`
}

type CallRoutingConfig struct {
	// flag to enable or disable call language preference feature
	CallLangPrefReleaseConfig *ReleaseConfig
	// list of languages to show to the user to choose as a preference
	CallLangPreferencesList []string `dynamic:"true"`
	// list of languages to show for the user to suggest us
	CallLangSuggestionsList []string `dynamic:"true"`
	// blocked account call routing config
	BlockedAccountCallRoutingConfig *BlockedAccountCallRoutingConfig `dynamic:"true"`
	// flag to enable or disable routing users based on manual user-routing channel mappings added via dev action
	IsManualRoutingEnabled bool `dynamic:"true"`
	// map to store priority mapping for each user segment within a routing channel
	// not setting this field to dynamic right now since facing an issue in nested mapping and struct based nested mapping
	SegmentIdToPriorityMapForRoutingChannel map[string]map[string]int
	// maintains list of segment id applicable for given routing channel
	// keeping this as separate data structure to maintain unmodified format of list of segment id
	// keeping segment id as map key, makes it go lowercase while reading it from the config
	// we will require orignal segment id string while passing it to segment service
	// TODO (Diparth): figure out way to avoid maintaining two separate data structure
	// for routing channel -> segment id list and routing-channel[segid] -> priority
	RoutingChannelToSegmentIdList map[string][]string
	// flag to enable or disable routing salary program users to a dedicated channel
	IsSalaryProgramRoutingEnabled bool `dynamic:"true"`
	// flag to enable or disable routing credit card users to a dedicated channel
	IsCreditCardRoutingEnabled bool `dynamic:"true"`
	// flag to enable or disable routing loan users to a dedicated channel
	IsActiveLoanRoutingEnabled bool `dynamic:"true"`
	// flag to enable or disable priority routing at global level
	IsPriorityRoutingEnabled bool `dynamic:"true"`
	// default priority for any user
	DefaultPriorityValue int `dynamic:"true"`
	// max priority value
	// 1 is the highest priority and 9 is the lowest priority
	// lower the priority value, higher the priority in the queue
	MaxPriorityValue int `dynamic:"true"`
	// minimum time threshold for a user who has waited in the queue
	QueueWaitTimeDurationThreshold string `dynamic:"true"`
	// minimum call drop off count threshold for a user in the queue
	CallDropOffCountThreshold int `dynamic:"true"`
	// factor by which priority value has to be halved for a user
	HalvingFactor int `dynamic:"true"`
	// number of past call records which has to be queried for a user
	PastCallRecordsLookupSize int `dynamic:"true"`
	// total duration in past for which call details has to be fetched
	PastCallRecordsLookupDuration string `dynamic:"true"`
	// config for pre-recorded message use case
	// utilized when we need to play a recorded message to a set of users
	// and then drop the call
	PreRecordedMessageConfig *PreRecordedMessageConfig `dynamic:"true"`
	// IsRecordedMessageEventEnabled indicates whether a event will be published to the topic
	// after we have decided to greet an actor with a recorded message
	// other services will use this event for triggering async flows at their end, ex: sending comms
	IsRecordedMessageEventEnabled bool `dynamic:"true"`
	// if a user has reported an issue through app,
	// we are caching the priority of their issue and prioritize their call based on their reported issue
	IssuePriorityCacheConfig *IssuePriorityCacheConfig `dynamic:"true"`
}

type IssuePriorityCacheConfig struct {
	IsEnabled bool   `dynamic:"true"`
	Key       string `dynamic:"true"`
	// if a user has already reported an issue with priority
	// provided in this list and their call will be given max priority
	AllowMaxPriorityForIssue []string `dynamic:"true"`
}
type PreRecordedMessageConfig struct {
	IsHighRiskMessageEnabled         bool `dynamic:"true"`
	IsScreenerRejectedMessageEnabled bool `dynamic:"true"`
}

type BlockedAccountCallRoutingConfig struct {
	// flag to enable or disable blocked account routing in prod
	IsRoutingEnabled bool `dynamic:"true"`
	// map of freeze status to reason code which should be used to check if acc is blocked
	FreezeStatusToReasonCodeMap map[string]string `dynamic:"true"`
	// map of access revoke reason code which should be used to check if acc is blocked
	AccessRevokeReasonMap map[string]bool `dynamic:"true"`
}

type ReleaseConfig struct {
	// to restrict the release of a feature based on platform or user group
	IsRestrictedReleaseEnabled bool
	// if IsRestrictedReleaseEnabled is false then IsEnabledForPlatform and IsEnabledForUserGroup will not be evaluated
	// to enable/disable the feature for specific app platform
	IsEnabledForPlatform map[string]bool
	// to enable/disable the feature on app for user groups eg: INTERNAL users
	IsEnabledForUserGroup map[string]bool
}

type IssueResolutionFeedbackConfig struct {
	IsEnabled     bool                   `dynamic:"true"`
	DisputeConfig *DisputeFeedbackConfig `dynamic:"true"`
}

type DisputeFeedbackConfig struct {
	WaitDurationAfterFinalComms      time.Duration     `dynamic:"true"`
	IssueCategoryIdToTransactionType map[string]string `dynamic:"true"`
}
type ChatBotConfig struct {
	LiveChatFallbackConfig                               *LiveChatFallbackConfig `dynamic:"true"`
	SenseforthChatInitInfo                               *SenseforthChatInitInfo `dynamic:"true"`
	IsForceFallbackToDefaultEnabled                      bool                    `dynamic:"true"`
	DefaultInAppChatView                                 string                  `dynamic:"true"`
	MaxClientSideFailureCountAllowedForAutoRetry         int64                   `dynamic:"true"`
	MaxTimeDurationThresholdForLastSuccessfulSessionTime time.Duration           `dynamic:"true"`
	NotificationTemplatesMap                             map[string]*ChatBotNotificationContent
	// flag to enable or disable release evaluation check
	IsReleaseEvaluationEnabled   bool `dynamic:"true"`
	IsExtraLoggingEnabled        bool `dynamic:"true"`
	CreateTicketTag              string
	NumOfTxnsToBeFetched         int                   `dynamic:"true"`
	TxnListDisplayFormat         *ChatBotDisplayFormat `dynamic:"true"`
	NumOfChargesToBeDisplayed    int                   `dynamic:"true"`
	TxnReasonDisplayMap          map[string]string
	NumOfFailedTxnsToBeDisplayed int
	NumberOfATMTxnsToBeFetched   int
	// flag to enable Predefined Message Templates
	IsPredefinedMessageTemplateEnabled bool
	// flag to enable post risk onboarding message
	IsPostOnboardingHighRiskMessageEnabled bool
	// Message for Users with High risk post onboarding
	PostOnboardingHighRiskUserMessage string
	// feature flag to determine whether to use the context code provided by client or not
	IsContextCodePassingFromClientEnabled bool `dynamic:"true"`
	// flag to enable freshchat experiment
	IsFreshChatExperimentEnabled bool `dynamic:"true"`
	// list of actor ids enabled for freshchat issue tree experiment
	ActorIdsEnabledFreshchatIssueTreeExperiment []string `dynamic:"true"`
	// Deeplink uri used to init nugget sdk
	NuggetDeeplinkUri string `dynamic:"true"`
	// actor ids enabled for nugget chatbot
	ActorIdsEnabledForForceNuggetChatbot map[string]bool `dynamic:"true"`
	// Namespace required for nugget chatbot init
	NuggetNamespace string `dynamic:"true"`
}

type ChatBotNotificationContent struct {
	Title string
	Body  string
}

// The string format to which an object has to be converted in order to display it to user on Chat Bot
type ChatBotDisplayFormat struct {
	// The order of the fields in the string format
	FieldsOrder []string `dynamic:"true"`
	// Delimiter to be used between these fields
	Delimiter string `dynamic:"true"`
}

type LiveChatFallbackConfig struct {
	ChannelId string `dynamic:"true"`
}

type SenseforthChatInitInfo struct {
	WebViewURLMap map[string]string `dynamic:"true"`
}

type AccountFreezeStatusConfig struct {
	CreditFreezeBannerElementId string
}

type SherlockFeedbackDetailsConfig struct {
	PageSize int
}

type RiskConfig struct {
	DevActionConfig           *UploadRiskCaseDevActionConfig `dynamic:"true"`
	EnableBackendDrivenCharts bool                           `dynamic:"true"`
}

type UploadRiskCaseDevActionConfig struct {
	// batch size for number of cases to be published in a single queue event
	RiskCaseEventBatchSize int      `dynamic:"true"`
	SupportedPayloadTypes  []string `dynamic:"true"`
}

type InternationalFundTransfer struct {
	DocumentsBucketName      string `dynamic:"true" iam:"s3-readonly"`
	EnableLRSCheckFromVendor bool   `dynamic:"true"`
}

type SalaryOpsConfig struct {
	// mapping of salary txn verification failure reasons category enum string to user display string
	VerificationFailureReasonsCategoryToDisplayString map[string]string
	// mapping of salary txn verification failure reasons sub-category enum string to user display string
	VerificationFailureReasonsSubCategoryToDisplayString map[string]string
	// filter containing criteria for a txn to consider for salary txn.
	SalaryTransactionFilters               *SalaryTransactionFilters `dynamic:"true"`
	NonIncomeRelatedTxnCategoryOntologyIds []string
	SalaryProgramHealthInsuranceConfig     *SalaryProgramHealthInsuranceConfig `dynamic:"true"`
	SalaryProgramS3BucketName              string                              `iam:"s3-readonly"`
	MaxBEPaginatedCallsForFiltering        int                                 `dynamic:"true"`
}

type SalaryProgramLeadManagementConfig struct {
	SalaryProgramS3BucketName    string `iam:"s3-readwrite"`
	SalaryProgramB2BS3BucketName string `iam:"s3-readwrite"`
	LeadDetailsExcelSheetPathB2B string
}

type SalaryProgramHealthInsuranceConfig struct {
	PolicyFAQsDocS3Path                      string `dynamic:"true"`
	PolicyClaimProcessDocS3Path              string `dynamic:"true"`
	InclusionExclusionAndHowItWorksDocS3Path string `dynamic:"true"`
	TncsDocS3Path                            string `dynamic:"true"`
}

type SalaryTransactionFilters struct {
	MinSalaryAmount             int64    `dynamic:"true"`
	AllowedTransactionProtocols []string `dynamic:"true"`
	// min required duration from last verified salary txn for a transaction to be considered for salary txn
	MinReqDurationFromLastVerification time.Duration `dynamic:"true"`
	// max allowed duration from last verified salary txn for a transaction to be considered for salary txn
	MaxAllowedDurationFromLastVerification time.Duration `dynamic:"true"`
}

type SprinklrConfig struct {
	// string used for formatting subject of freshdesk ticket created from sprinklr event
	FreshdeskTicketSubject string
	// string used for formatting description of freshdesk ticket created from sprinklr event
	FreshdeskTicketDescription string
}

type WatsonConfig struct {
	// IsWatsonSystemEnabled is to determine whether watson service is on or off
	IsWatsonSystemEnabled bool

	// IncidentCategoryDetailsConfig will contain kill flag for each L1, L2 combination
	IncidentCategoryDetailsConfig map[string]map[string]*IncidentCategoryDetailsConfig

	// AlternativeIncidentCategoryIdMapping contains mapping for new to old incident category id
	// this is done to make rpc contract simpler for client while migration
	// clients can send new incident category id, and Watson will handle for incident created via older ids
	AlternativeIncidentCategoryIdMapping map[string]string
}

type IncidentCategoryDetailsConfig struct {
	IsProcessingEnabled bool
	// Indicates whether ticket status comms is enabled for the given status.
	// This is used to decide whether to send ticket status change signal to the workflow
	IsTicketStatusCommsEnabled map[string]bool
}

type UsStocksOpsConfig struct {
	OrderTypeStageStatusMap map[string]string
	ExpectedEtaForDividend  time.Duration
	UseAccountActivities    bool
}

type InternationalFundsTransferConfig struct {
	ForexRateReportSlackChannelId string
}

type DevActionHelperConfig struct {
	// IsBulkResourceAccessibilityCheckEnabled decides whether accessibility of dev actions
	// is checked using BulkCheckResourceAccessibility RPC from casbin service
	IsBulkResourceAccessibilityCheckEnabled bool `dynamic:"true"`
}

type ClosedAccountConfig struct {
	// AutoResolveAmount is the amount below which account closure tickets will be auto resolved
	AutoResolveAmount float32
	// IssueCategoryId is for SubCategory_SUB_CATEGORY_ACCOUNTS_MIN_KYC_EXPIRY_BALANCE_REFUND
	IssueCategoryId string
}

type OverrideBankActions struct {
	// Max requests sets the maximum allowable length of the input array for the override action.
	MaxRequests int `dynamic:"true"`
}

type ReviewActionConfig struct {
	// list of common question to a user in outcall flow
	CommonQuestionsToUser []string `dynamic:"true"`
}

type FreshdeskMonorailIntegrationConfig struct {
	// Owners assigned to the monorail component, this will contain all the people who should be the part of given component (area)
	// For example: Product Managers, Engineers, Process Excellence team etc.
	// note: first email will be marked as owner of monorail rest of the people will be added in cc
	MonorailComponentOwnerMap map[string][]string
	// to generate monorail issue subject from freshdesk ticket
	MonorailIssueSubjectFormat string
	// The default labels to be added to the monorail ticket
	MonorailIssueDefaultLabels []string
	// Not all Monorail comments will be forward to Freshdesk.
	// Only those comments starting with this prefix will be sent to Freshdesk as private notes
	PrefixForForwardingMonorailComment string
	// The prefix to be used for adding a private note on Freshdesk Ticket based on a Monorail Comment
	MonorailToFreshdeskPrivateNotePrefix string
	// The prefix to be used for a adding a comment on Monorail Issue based on a Freshdesk Private note
	FreshdeskToMonorailCommentPrefix string
	// Google Drive folder Id for storing the attachments to share from freshdesk to monorail
	// Attachments from freshdesk ticket will be uploaded to this folder and a link to the file is posted to the monorail issue
	GoogleDriveFolderIdForAttachments string
}

type EmployerDbConfig struct {
	// elastic search host url
	EsHostUrl string
	// time range till which indexing of added employers will take place
	IndexingTimeDuration time.Duration `dynamic:"true"`
}

type GRPCWebServerConfig struct {
	JarvisInterceptorConf *cmdcfg.JarvisInterceptorConf `dynamic:"true"`
	HttpCorsOptions       *cmdcfg.HttpCorsOptions       `dynamic:"true"`
	KeycloakAuth          *keycloak.Config
}

type SherlockUserRequestsConfig struct {
	DateTimePickerConfig    *cfg.DateTimePicker
	AccountTypeEnumToString map[string]string
}

// IssueCategoryIdForCategory holds Issue category ids for some categories used in the Backend for creating/updating a freshdesk ticket
// Naming convention for fields in IssueCategoryIdForCategory:
//   - For easy readability, we name the variable depicting the L1, L2, L3. i.e. L1<name>L2<name>L3<name>.
//   - If L2 / L3 are empty we don't mention them in variable name eg: L1OffAppTransactions
type IssueCategoryIdForCategory struct {
	L1InAppTransactions  string // In-App Transactions        | -              | -
	L1OffAppTransactions string // Off-App Transactions       | -              | -
	L1DebitCard          string // Debit Card 				  | -              | -
	L1DebitCardL2Atm     string // Debit Card                 | ATM Related    | -
	L1DebitCardL2PosEcom string // Debit Card                 | POS/ECOM       | -
}

type SherlockBannersConfig struct {
	// Sherlock banners service will invoke these enabled services real-time to fetch any banners configured for given input parameters
	// This a map of Service Name and a bool indicating whether it is enabled or not
	// The service name should be String form of enum types.ServiceName (api/types/service_name.proto)
	IsServiceEnabledForDynamicFetching map[string]bool `dynamic:"true"`
	// We use priority order cascaded on multiple criteria. Currently, ServiceName is the only criteria
	// Each list is in decreasing order of priority
	// The service name should be String form of enum types.ServiceName (api/types/service_name.proto)
	PriorityOrder map[string][]string
}

type ErrorActivityConfig struct {
	// Feature flag to determine whether errors processed needs to be piped to watson or not
	IsPipingErrorEventToWatsonEnabled bool `dynamic:"true"`
	// cool-off period that system should wait for, before creating new incident of same type for same user
	DefaultIncidentCreationCoolOffPeriod time.Duration `dynamic:"true"`
	// key used to get issue category id from event payload
	IssueCategoryIdEventPayloadKey string `dynamic:"true"`
	// key used to get client request id from event payload
	ClientRequestIdEventPayloadKey string `dynamic:"true"`
	// key used to get is resolution event boolean from event payload
	IsResolutionEventBooleanEventPayloadKey string `dynamic:"true"`
}

type AgentPromptConfig struct {
	AgentPromptInfoMap map[string]*AgentPromptInfo `dynamic:"true"`
}

type AgentPromptInfo struct {
	IsPromptEnabled     bool                 `dynamic:"true"`
	PromptValueForAgent string               `dynamic:"true"`
	Description         string               `dynamic:"true"`
	PromptCommsTemplate *PromptCommsTemplate `dynamic:"true"`
}
type PromptCommsTemplate struct {
	Title       string `dynamic:"true"`
	Description string `dynamic:"true"`
}
type RiskOpsInstalledAppsConfig struct {
	IsEnabled bool `dynamic:"true"`
	// minimum threshold above which results will be significant to show to ops
	// risk score is property by DS team to fetch riskyness for that app
	RiskScoreMinThreshold float64
	// minimum threshold above which results will be significant to show to ops
	// field depicts that the current app was with how many LEA users before
	NumberOfLEAWithAppInstalledMinThreshold int
}

type RiskFennelConfig struct {
	APIVersion int `dynamic:"true"`
}

type StageWiseCommsConfig struct {
	// IsGenericCommsEnabled flag decides if fallback comms are enabled for stagewise comms
	IsGenericCommsEnabled bool `dynamic:"true"`
	// IsIssueConfigSpecificCommsEnabled flag decides if issue config specific comms are enabled
	// if this flag is disabled for all the ticket status, comms will depend on
	// IsGenericCommsEnabled flag
	IsIssueConfigSpecificCommsEnabled bool `dynamic:"true"`
	// IsPublishingManualTicketCreationEventEnabled flag decides if event will be published
	// to send stage wise comms when a ticket is created on freshdesk
	IsPublishingManualTicketCreationEventEnabled bool `dynamic:"true"`
	// IsPublishingManualTicketUpdateEventEnabled flag decides if event will be published
	// to send stage wise comms when a ticket is updated on freshdesk
	IsPublishingManualTicketUpdateEventEnabled bool `dynamic:"true"`
}

type IssueConfigServiceConfig struct {
	ConfigTypeMapping map[string]string `dynamic:"true"`
	// IssueConfigLevelCacheKey and IssueConfigLevelCacheValidityDuration determine the cache key as well as the respective
	// validity duration, for which an issue config level would be persisted
	IssueConfigLevelCacheKey              string        `dynamic:"true"`
	IssueConfigLevelCacheValidityDuration time.Duration `dynamic:"true"`
	UseNewCategoryMappingForLLMScreen     bool          `dynamic:"true"`
	IssueCategoryCreatedFromTime          time.Time     `dynamic:"true"`
	IssueCategoryCreatedToTime            time.Time     `dynamic:"true"`
	IsCacheEnabled                        bool          `dynamic:"true"`
}

type RiskTxnReviewRolloutConfig struct {
	// Setting this flag true will enable new selected order rpc for all analyst
	IsSelectedOrderRpcEnabledForAll bool `dynamic:"true"`
	// If the above flag is set to false, factory will check for logged in agent email and use the new rpc if part of whitelist
	SelectedOrderRpcWhitelistedEmails []string `dynamic:"true"`
}

type S3EventConsumerConfig struct {
	BucketName                           string `dynamic:"true"  iam:"s3-readonly"`
	CallSummarizationFilePath            string `dynamic:"true"`
	IsCallSummarizationProcessingEnabled bool   `dynamic:"true"`
}

type RiskOutcallFormRolloutConfig struct {
	// All checks will be performed in order.

	// Setting this flag true will disable outcall form option for all
	DisableForAllAgents bool `dynamic:"true"`
	// Represents the maximum number of forms that can be sent to a user.
	MaxFormsPerUser int `dynamic:"true"`
	// Form will be shown in cases with these review types.
	WhitelistedReviewTypes []string `dynamic:"true"`
	// Only these questionnaire templates will be shown for outcall form.
	WhitelistedQuestionnaireTemplates []string `dynamic:"true"`
}

type AbandonedCallConfig struct {
	// flag to decide whether to sent comms to user if the call is abandoned
	IsAbandonedCallCommsEnabled bool                               `dynamic:"true"`
	NotificationTemplate        *payPkg.NotificationTemplateParams `dynamic:"true"`
}

type CaseManagementActorActivities struct {
	IsEnabled bool `dynamic:"true"`
	// context timeout for RPC to avoid any issues with latencies
	Timeout time.Duration `dynamic:"true"`
	// allowed review types to display actor activities
	AllowedReviewTypes []string `dynamic:"true"`
}

type WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail struct {
	AllowedAccessLevels []string `dynamic:"true"`
}

type ContactUsModelResponseConfig struct {
	ResponseCacheKey              string        `dynamic:"true"`
	ResponseCacheValidityDuration time.Duration `dynamic:"true"`
}

type Filegenerator struct {
	CamsS3Bucket  string `dynamic:"true" iam:"s3-readwrite"`
	KarvyS3Bucket string `dynamic:"true" iam:"s3-readwrite"`
}

type DbStateConfig struct {
	// feature flag to check whether role-based access control is enabled
	IsRbacEnabled bool `dynamic:"true"`
}

type EscalationConfig struct {
	// feature flag to decide whether escalations via Backend is enabled or not
	IsEscalationEnabled bool `dynamic:"true"`
}

type FederalEscalationConfig struct {
	FederalEscalationAttachmentBucketName string `iam:"s3-readwrite"`
	QueueId                               string `dynamic:"true"`
	IsUpdateConsumerEnabled               bool   `dynamic:"true"`
	QPHRateLimit                          uint32 `dynamic:"true"`
}

type LienConfig struct {
	LienAmount          *moneyPb.Money
	LienDurationInHours int `dynamic:"true"`
	// feature flag to check whether lien is enabled for the given reason
	AllowedReasons        []string
	LienRolloutPercentage int
}

type NuggetEventConfig struct {
	EventMap map[string]*EventDetails `dynamic:"true"`
}

type EventDetails struct {
	NuggetEventName    string `dynamic:"true"`
	NuggetSubEventName string `dynamic:"true"`
	// map[field-name][field-json-path]
	FieldPathMap map[string]string `dynamic:"true"`
}

// DevActionValidationConfig defines configuration for validating non-compliant terms in dev actions
type DevActionValidationConfig struct {
	// IsNonCompliantTermsValidationEnabled controls whether non-compliant terms validation is active
	// When false, all validation is skipped regardless of other settings
	IsNonCompliantTermsValidationEnabled bool `dynamic:"true"`
	// NonCompliantTermsToApprovedTermsMap maps non-compliant terms to their approved alternatives
	// The keys are regex patterns only
	NonCompliantTermsToApprovedTermsMap map[string]string `dynamic:"true"`
	// DevActionsToSkipValidation maps dev action names to field names that should skip validation
	// If a dev action is not in this map, all fields will be validated
	// If a dev action is in this map with an empty slice, all fields will be validated
	// If a dev action is in this map with field names, only those fields will skip validation
	DevActionsToSkipValidation map[string]*DevActionValidationSkipFields `dynamic:"true"`
}

// DevActionValidationSkipFields is a wrapper type for []string to support dynamic config
// This is needed because map[string][]string is not supported in dynamic config
type DevActionValidationSkipFields struct {
	Fields []string `dynamic:"true"`
}
