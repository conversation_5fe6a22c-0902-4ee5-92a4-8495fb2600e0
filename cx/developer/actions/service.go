package actions

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/developer/helper"
	cxLogger "github.com/epifi/gamma/cx/logger"

	rpcPb "github.com/epifi/be-common/api/rpc"
	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
)

type DevActions struct {
	fac       *DevActionFactory
	devHelper helper.IDevHelper
	genconf   *genconf.Config
}

func NewDevActions(fac *DevActionFactory, devHelper helper.IDevHelper, genconf *genconf.Config) *DevActions {
	return &DevActions{
		fac:       fac,
		devHelper: devHelper,
		genconf:   genconf,
	}
}

var _ actionPb.DevActionsServer = &DevActions{}

func (d *DevActions) validateNonCompliantWords(ctx context.Context, content string) string {
	validationConfig := d.genconf.DevActionValidationConfig()
	if validationConfig == nil {
		return ""
	}

	// Get the non-compliant terms map
	nonCompliantTermsMap := validationConfig.NonCompliantTermsToApprovedTermsMap()
	if nonCompliantTermsMap == nil {
		return ""
	}

	// Check each non-compliant term against the content using regex patterns
	// todo: Move regex compilation outside loop if performance becomes bottleneck (acceptable for current QPS)
	// https://github.com/epiFi/tickets/issues/58516
	var errorMsg string
	nonCompliantTermsMap.Range(func(pattern, approvedTerm string) bool {
		// Compile and use regex pattern
		regex, err := regexp.Compile(pattern)
		if err != nil {
			// Log regex compilation error but continue with other patterns
			cxLogger.Error(ctx, "failed to compile regex pattern",
				zap.String("pattern", pattern),
				zap.Error(err))
			return true // continue with next pattern
		}

		if regex.MatchString(content) {
			// Find the actual matched text to show in the error message
			matches := regex.FindString(content)
			if matches != "" {
				errorMsg = fmt.Sprintf("Non-compliant terminology detected: '%s'. Please use: '%s'.", matches, approvedTerm)
			} else {
				// Fallback to the original pattern if no match found (shouldn't happen)
				errorMsg = fmt.Sprintf("Non-compliant terminology detected: '%s'. Please use: '%s'.", pattern, approvedTerm)
			}
			return false // stop iteration
		}
		return true // continue with next pattern
	})

	return errorMsg
}

func (d *DevActions) validateFilterTextFields(ctx context.Context, filters []*dsPb.Filter, devActionName string) string {
	var validationErrors []string

	if d.genconf == nil {
		return ""
	}

	validationConfig := d.genconf.DevActionValidationConfig()
	if validationConfig == nil {
		return ""
	}

	// Check if non-compliant terms validation is enabled
	if !validationConfig.IsNonCompliantTermsValidationEnabled() {
		return ""
	}

	// Get fields to skip validation for this dev action (from config)
	var (
		skipConfig   *genconf.DevActionValidationSkipFields
		fieldsToSkip []string
	)

	if skipConfigMap := validationConfig.DevActionsToSkipValidation(); skipConfigMap != nil {
		skipConfig = skipConfigMap.Get(devActionName)
		if skipConfig != nil {
			fieldsToSkip = skipConfig.Fields().Slice()
		}
	}

	// Validate text fields in filters
	for _, filter := range filters {
		parameterName := filter.GetParameterName()

		// Skip validation if this field is in the skip list
		if lo.Contains(fieldsToSkip, parameterName) {
			continue
		}

		// Check for string values (STRING and MULTILINE_TEXT types)
		if filter.GetStringValue() != "" {
			if errorMsg := d.validateNonCompliantWords(ctx, filter.GetStringValue()); errorMsg != "" {
				validationErrors = append(validationErrors, fmt.Sprintf("Field '%s': %s", parameterName, errorMsg))
			}
		}
	}

	// Return all validation errors combined
	if len(validationErrors) > 0 {
		var numberedErrors []string
		for i, error := range validationErrors {
			numberedErrors = append(numberedErrors, fmt.Sprintf("ERROR %d: %s", i+1, error))
		}
		return strings.Join(numberedErrors, "\r\n")
	}

	return ""
}

func (d *DevActions) GetAvailableActions(ctx context.Context, request *actionPb.GetAvailableActionsRequest) (
	*actionPb.GetAvailableActionsResponse, error) {
	var actionList []string
	for action, _ := range actionPb.DeveloperActions_value {
		if action != actionPb.DeveloperActions_DEVELOPER_ACTIONS_UNSPECIFIED.String() {
			actionList = append(actionList, action)
		}
	}
	filteredActions, err := d.devHelper.GetFilteredDevActionsForAccess(ctx, request.GetHeader().GetAgentEmail(), actionList, request.GetHeader().GetAccessLevel())
	if err != nil {
		cxLogger.Error(ctx, "error while getting filtered actions based on user access", zap.Error(err))
		return &actionPb.GetAvailableActionsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("Error while checking access for actions"),
		}, nil
	}
	return &actionPb.GetAvailableActionsResponse{
		Status:        rpcPb.StatusOk(),
		DevActionList: filteredActions,
	}, nil
}

func (d *DevActions) GetParameterListForAction(ctx context.Context,
	req *actionPb.GetParameterListForActionRequest) (*actionPb.GetParameterListForActionResponse, error) {
	// need to format dev action name as client sends the request without DEV_ACTION_ prefix
	formattedDevActionName := helper.GetFormattedActionName(req.GetDevAction())
	isAccessAvailable, accessAvailabilityErr := d.devHelper.HasAccessToDevAction(ctx, req.GetHeader().GetAgentEmail(), formattedDevActionName, req.GetHeader().GetAccessLevel())
	if accessAvailabilityErr != nil {
		cxLogger.Error(ctx, "error while checking user access to dev action", zap.Error(accessAvailabilityErr),
			zap.String("action", req.GetDevAction()))
		return &actionPb.GetParameterListForActionResponse{Status: rpcPb.StatusInternalWithDebugMsg("error while checking access")}, nil
	}
	if !isAccessAvailable {
		return &actionPb.GetParameterListForActionResponse{Status: rpcPb.StatusPermissionDenied()}, nil
	}
	devAction, isDevActionAvailable := actionPb.DeveloperActions_value[req.GetDevAction()]
	if !isDevActionAvailable {
		cxLogger.Error(ctx, "invalid action passed in request", zap.String("action", req.GetDevAction()))
		return &actionPb.GetParameterListForActionResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("action passed is not available"),
		}, nil
	}
	paramFetcher, paramFetchErr := d.fac.getActionParamListImpl(actionPb.DeveloperActions(devAction))
	if paramFetchErr != nil {
		cxLogger.Error(ctx, "action passed implementation is not available", zap.Error(paramFetchErr),
			zap.String("action", req.GetDevAction()))
		return &actionPb.GetParameterListForActionResponse{Status: rpcPb.StatusInternal()}, nil
	}
	paramList, paramListErr := paramFetcher.FetchParamList(ctx, actionPb.DeveloperActions(devAction), &actionPb.DevActionMeta{
		UserEmailId:     req.GetHeader().GetAgentEmail(),
		UserAccessLevel: req.GetHeader().GetAccessLevel(),
		MonorailId:      req.GetHeader().GetMonorailId(),
	})
	if paramListErr != nil {
		cxLogger.Error(ctx, "unable to fetch parameter list", zap.Error(paramListErr),
			zap.String("action", req.GetDevAction()))
		return &actionPb.GetParameterListForActionResponse{Status: rpcPb.StatusInternal()}, nil
	}

	return &actionPb.GetParameterListForActionResponse{
		Status:        rpcPb.StatusOk(),
		ParameterList: paramList,
	}, nil
}

func (d *DevActions) ExecuteAction(ctx context.Context,
	req *actionPb.ExecuteActionRequest) (*actionPb.ExecuteActionResponse, error) {
	// need to format dev action as client sends the request without DEV_ACTION_ prefix
	formattedDevActionName := helper.GetFormattedActionName(req.GetDevAction())
	isAccessAvailable, accessAvailabilityErr := d.devHelper.HasAccessToDevAction(ctx, req.GetHeader().GetAgentEmail(), formattedDevActionName, req.GetHeader().GetAccessLevel())
	if accessAvailabilityErr != nil {
		cxLogger.Error(ctx, "error while checking user access to dev action", zap.Error(accessAvailabilityErr),
			zap.String("action", req.GetDevAction()))
		return &actionPb.ExecuteActionResponse{Status: rpcPb.StatusInternal()}, nil
	}
	if !isAccessAvailable {
		return &actionPb.ExecuteActionResponse{Status: rpcPb.StatusPermissionDenied()}, nil
	}

	// Validate text fields in filters for non-compliant words
	if errorMsg := d.validateFilterTextFields(ctx, req.GetFilters(), req.GetDevAction()); errorMsg != "" {
		cxLogger.Error(ctx, "non-compliant content detected in dev action filters",
			zap.String("action", req.GetDevAction()),
			zap.String("user", req.GetHeader().GetAgentEmail()),
			zap.String("errorMsg", errorMsg))
		return &actionPb.ExecuteActionResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg(errorMsg),
		}, nil
	}

	devAction, isDevActionAvailable := actionPb.DeveloperActions_value[req.GetDevAction()]
	if !isDevActionAvailable {
		return &actionPb.ExecuteActionResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("action passed is not available"),
		}, nil
	}
	executeActionImpl, executeActionErr := d.fac.getExecuteActionImpl(actionPb.DeveloperActions(devAction))
	if executeActionErr != nil {
		cxLogger.Error(ctx, "action passed implementation is not available", zap.Error(executeActionErr),
			zap.String("action", req.GetDevAction()))
		return &actionPb.ExecuteActionResponse{Status: rpcPb.StatusInternal()}, nil
	}

	jsonResp, err := executeActionImpl.ExecuteAction(ctx, actionPb.DeveloperActions(devAction), req.GetFilters(), &actionPb.DevActionMeta{
		UserEmailId:     req.GetHeader().GetAgentEmail(),
		UserAccessLevel: req.GetHeader().GetAccessLevel(),
		MonorailId:      req.GetHeader().GetMonorailId(),
		Reason:          req.GetReason(),
	})
	if err != nil {
		cxLogger.Error(ctx, "unable to execute action", zap.Error(err),
			zap.String("action", req.GetDevAction()))
		return &actionPb.ExecuteActionResponse{Status: rpcPb.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	return &actionPb.ExecuteActionResponse{
		Status:       rpcPb.StatusOk(),
		JsonResponse: jsonResp,
	}, nil
}
