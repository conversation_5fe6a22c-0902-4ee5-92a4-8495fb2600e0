package actions

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/developer/helper"
)

// buildTestConfig creates a real genconf.Config and sets DevActionValidationConfig fields
func buildTestConfig(t *testing.T, enabled bool, terms map[string]string, skip map[string][]string) *genconf.Config {
	// Create config
	conf, _ := genconf.NewConfig()
	vconf := conf.DevActionValidationConfig()

	// Feature flag
	if err := vconf.SetIsNonCompliantTermsValidationEnabled(enabled, false, nil); err != nil {
		t.Fatalf("failed to set feature flag: %v", err)
	}

	// Regex map
	if terms != nil {
		if err := vconf.SetNonCompliantTermsToApprovedTermsMap(terms, false, nil); err != nil {
			t.Fatalf("failed to set terms map: %v", err)
		}
	}

	// Skip map
	if skip != nil {
		staticSkip := make(map[string]*config.DevActionValidationSkipFields)
		for action, fields := range skip {
			staticSkip[action] = &config.DevActionValidationSkipFields{Fields: fields}
		}
		if err := vconf.SetDevActionsToSkipValidation(staticSkip, false, nil); err != nil {
			t.Fatalf("failed to set skip config: %v", err)
		}
	}

	return conf
}

func TestDevActions_validateFilterTextFields(t *testing.T) {
	type fields struct {
		fac       *DevActionFactory
		devHelper helper.IDevHelper
		genconf   *genconf.Config
	}
	type args struct {
		ctx           context.Context
		filters       []*dsPb.Filter
		devActionName string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		{
			name: "validation disabled - returns empty string",
			fields: fields{
				fac:       nil,
				devHelper: nil,
				genconf:   buildTestConfig(t, false, nil, nil),
			},
			args: args{
				ctx: context.Background(),
				filters: []*dsPb.Filter{
					{
						ParameterName: "description",
						Value:         &dsPb.Filter_StringValue{StringValue: "User has RBI information"},
					},
				},
				devActionName: "UPDATE_USER_PROFILE",
			},
			want: "",
		},
		{
			name: "validation enabled - no non-compliant terms - returns empty string",
			fields: fields{
				fac:       nil,
				devHelper: nil,
				genconf:   buildTestConfig(t, true, map[string]string{"(?i)\\bRBI\\b": "Regulatory Authority"}, nil),
			},
			args: args{
				ctx: context.Background(),
				filters: []*dsPb.Filter{
					{
						ParameterName: "description",
						Value:         &dsPb.Filter_StringValue{StringValue: "User has regular information"},
					},
				},
				devActionName: "UPDATE_USER_PROFILE",
			},
			want: "",
		},
		{
			name: "validation enabled - single non-compliant term - returns error",
			fields: fields{
				fac:       nil,
				devHelper: nil,
				genconf:   buildTestConfig(t, true, map[string]string{"(?i)\\bRBI\\b": "Regulatory Authority"}, nil),
			},
			args: args{
				ctx: context.Background(),
				filters: []*dsPb.Filter{
					{
						ParameterName: "description",
						Value:         &dsPb.Filter_StringValue{StringValue: "User has RBI information"},
					},
				},
				devActionName: "UPDATE_USER_PROFILE",
			},
			want: "ERROR 1: Field 'description': Non-compliant terminology detected: 'RBI'. Please use: 'Regulatory Authority'.",
		},
		{
			name: "validation enabled - multiple non-compliant terms - returns numbered errors",
			fields: fields{
				fac:       nil,
				devHelper: nil,
				genconf: buildTestConfig(t, true, map[string]string{
					"(?i)\\bRBI\\b":                      "Regulatory Authority",
					"(?i)\\bFi\\s+Savings\\s+Account\\b": "Federal Bank Savings Account on Fi",
				}, nil),
			},
			args: args{
				ctx: context.Background(),
				filters: []*dsPb.Filter{
					{
						ParameterName: "description",
						Value:         &dsPb.Filter_StringValue{StringValue: "User has RBI information"},
					},
					{
						ParameterName: "title",
						Value:         &dsPb.Filter_StringValue{StringValue: "User has Fi Savings Account"},
					},
				},
				devActionName: "UPDATE_USER_PROFILE",
			},
			want: "ERROR 1: Field 'description': Non-compliant terminology detected: 'RBI'. Please use: 'Regulatory Authority'.\r\nERROR 2: Field 'title': Non-compliant terminology detected: 'Fi Savings Account'. Please use: 'Federal Bank Savings Account on Fi'.",
		},
		{
			name: "validation enabled - case insensitive regex matching",
			fields: fields{
				fac:       nil,
				devHelper: nil,
				genconf:   buildTestConfig(t, true, map[string]string{"(?i)\\bRBI\\b": "Regulatory Authority"}, nil),
			},
			args: args{
				ctx: context.Background(),
				filters: []*dsPb.Filter{
					{
						ParameterName: "description",
						Value:         &dsPb.Filter_StringValue{StringValue: "User has rbi information"},
					},
				},
				devActionName: "UPDATE_USER_PROFILE",
			},
			want: "ERROR 1: Field 'description': Non-compliant terminology detected: 'rbi'. Please use: 'Regulatory Authority'.",
		},
		{
			name: "validation enabled - empty string value - skips validation",
			fields: fields{
				fac:       nil,
				devHelper: nil,
				genconf:   buildTestConfig(t, true, map[string]string{"(?i)\\bRBI\\b": "Regulatory Authority"}, nil),
			},
			args: args{
				ctx: context.Background(),
				filters: []*dsPb.Filter{
					{
						ParameterName: "description",
						Value:         &dsPb.Filter_StringValue{StringValue: ""},
					},
				},
				devActionName: "UPDATE_USER_PROFILE",
			},
			want: "",
		},
		{
			name: "validation enabled - nil config - returns empty string",
			fields: fields{
				fac:       nil,
				devHelper: nil,
				genconf:   nil,
			},
			args: args{
				ctx: context.Background(),
				filters: []*dsPb.Filter{
					{
						ParameterName: "description",
						Value:         &dsPb.Filter_StringValue{StringValue: "User has RBI information"},
					},
				},
				devActionName: "UPDATE_USER_PROFILE",
			},
			want: "",
		},
		{
			name: "validation enabled - multiple fields with mixed content",
			fields: fields{
				fac:       nil,
				devHelper: nil,
				genconf:   buildTestConfig(t, true, map[string]string{"(?i)\\bRBI\\b": "Regulatory Authority"}, nil),
			},
			args: args{
				ctx: context.Background(),
				filters: []*dsPb.Filter{
					{
						ParameterName: "description",
						Value:         &dsPb.Filter_StringValue{StringValue: "User has RBI information"},
					},
					{
						ParameterName: "notes",
						Value:         &dsPb.Filter_StringValue{StringValue: "Clean notes"},
					},
					{
						ParameterName: "title",
						Value:         &dsPb.Filter_StringValue{StringValue: "Another RBI reference"},
					},
				},
				devActionName: "UPDATE_USER_PROFILE",
			},
			want: "ERROR 1: Field 'description': Non-compliant terminology detected: 'RBI'. Please use: 'Regulatory Authority'.\r\nERROR 2: Field 'title': Non-compliant terminology detected: 'RBI'. Please use: 'Regulatory Authority'.",
		},
		{
			name: "validation enabled - skip specific fields for dev action - skips validation for those fields",
			fields: fields{
				fac:       nil,
				devHelper: nil,
				genconf: buildTestConfig(t, true, map[string]string{"(?i)\\bRBI\\b": "Regulatory Authority"}, map[string][]string{
					"UPDATE_USER_PROFILE": {"description", "notes"},
				}),
			},
			args: args{
				ctx: context.Background(),
				filters: []*dsPb.Filter{
					{
						ParameterName: "description",
						Value:         &dsPb.Filter_StringValue{StringValue: "User has RBI information"}, // Should be skipped
					},
					{
						ParameterName: "title",
						Value:         &dsPb.Filter_StringValue{StringValue: "User has RBI information"}, // Not skipped
					},
				},
				devActionName: "UPDATE_USER_PROFILE",
			},
			want: "ERROR 1: Field 'title': Non-compliant terminology detected: 'RBI'. Please use: 'Regulatory Authority'.",
		},
		{
			name: "validation enabled - dev action not in skip map - validates all fields",
			fields: fields{
				fac:       nil,
				devHelper: nil,
				genconf: buildTestConfig(t, true, map[string]string{"(?i)\\bRBI\\b": "Regulatory Authority"}, map[string][]string{
					"CREATE_TICKET": {"internal_notes"}, // Unrelated dev action
				}),
			},
			args: args{
				ctx: context.Background(),
				filters: []*dsPb.Filter{
					{
						ParameterName: "description",
						Value:         &dsPb.Filter_StringValue{StringValue: "User has RBI information"},
					},
				},
				devActionName: "UPDATE_USER_PROFILE", // Not in skip map
			},
			want: "ERROR 1: Field 'description': Non-compliant terminology detected: 'RBI'. Please use: 'Regulatory Authority'.",
		},
		{
			name: "validation enabled - skip map with empty fields - validates all fields",
			fields: fields{
				fac:       nil,
				devHelper: nil,
				genconf: buildTestConfig(t, true, map[string]string{"(?i)\\bRBI\\b": "Regulatory Authority"}, map[string][]string{
					"UPDATE_USER_PROFILE": {}, // Empty slice
				}),
			},
			args: args{
				ctx: context.Background(),
				filters: []*dsPb.Filter{
					{
						ParameterName: "description",
						Value:         &dsPb.Filter_StringValue{StringValue: "User has RBI information"},
					},
				},
				devActionName: "UPDATE_USER_PROFILE",
			},
			want: "ERROR 1: Field 'description': Non-compliant terminology detected: 'RBI'. Please use: 'Regulatory Authority'.",
		},
		{
			name: "validation disabled - skip config ignored",
			fields: fields{
				fac:       nil,
				devHelper: nil,
				genconf: buildTestConfig(t, false, map[string]string{"(?i)\\bRBI\\b": "Regulatory Authority"}, map[string][]string{
					"UPDATE_USER_PROFILE": {"description"},
				}),
			},
			args: args{
				ctx: context.Background(),
				filters: []*dsPb.Filter{
					{
						ParameterName: "description",
						Value:         &dsPb.Filter_StringValue{StringValue: "User has RBI information"},
					},
				},
				devActionName: "UPDATE_USER_PROFILE",
			},
			want: "", // No validation runs
		},
		{
			name: "validation enabled - skip for another dev action - validates current",
			fields: fields{
				fac:       nil,
				devHelper: nil,
				genconf: buildTestConfig(t, true, map[string]string{"(?i)\\bRBI\\b": "Regulatory Authority"}, map[string][]string{
					"SEND_COMMUNICATION": {"message_body"},
				}),
			},
			args: args{
				ctx: context.Background(),
				filters: []*dsPb.Filter{
					{
						ParameterName: "description",
						Value:         &dsPb.Filter_StringValue{StringValue: "User has RBI information"},
					},
				},
				devActionName: "UPDATE_USER_PROFILE", // Skip is for different action
			},
			want: "ERROR 1: Field 'description': Non-compliant terminology detected: 'RBI'. Please use: 'Regulatory Authority'.",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &DevActions{
				fac:       tt.fields.fac,
				devHelper: tt.fields.devHelper,
				genconf:   tt.fields.genconf,
			}
			assert.Equalf(t, tt.want, d.validateFilterTextFields(tt.args.ctx, tt.args.filters, tt.args.devActionName), "validateFilterTextFields(%v, %v, %v)", tt.args.ctx, tt.args.filters, tt.args.devActionName)
		})
	}
}
