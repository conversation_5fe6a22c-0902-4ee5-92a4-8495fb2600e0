package helper

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	"github.com/epifi/be-common/pkg/epifigrpc"
	casbinPb "github.com/epifi/gamma/api/casbin"
	"github.com/epifi/gamma/cx/config/genconf"
)

//go:generate mockgen -destination=mocks/mock_helper.go -package=mocks github.com/epifi/gamma/cx/developer/helper IDevHelper
type IDevHelper interface {
	HasAccessToDevAction(ctx context.Context, userEmail string, action string, accessLevel casbinPb.AccessLevel) (bool, error)
	GetFilteredDevActionsForAccess(ctx context.Context, userEmail string,
		actions []string, accessLevel casbinPb.AccessLevel) ([]string, error)
}

type DevHelper struct {
	casbinClient casbinPb.CasbinClient
	genConf      *genconf.Config
}

func NewDevHelper(casbinClient casbinPb.CasbinClient, genConf *genconf.Config) *DevHelper {
	return &DevHelper{
		casbinClient: casbinClient,
		genConf:      genConf,
	}
}

func (h *DevHelper) HasAccessToDevAction(ctx context.Context, userEmail string, action string, accessLevel casbinPb.AccessLevel) (bool, error) {
	if h.genConf.AuthValidation().SkipAuthForTestAutomationAgent && lo.Contains(h.genConf.AuthValidation().TestAutomationAgentEmails, userEmail) {
		return true, nil
	}
	resp, err := h.casbinClient.VerifyUserPermissions(ctx, &casbinPb.VerifyUserPermissionsRequest{
		UserId:      userEmail,
		Resource:    action,
		AccessLevel: accessLevel,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		if resp.GetStatus().IsPermissionDenied() {
			return false, nil
		}
		return false, errors.Wrap(err, "failed to check permission in casbin")
	}
	return true, nil
}

func GetFormattedActionName(action string) string {
	return fmt.Sprintf("DEV_ACTION_%s", action)
}

func GetOriginalActionName(action string) string {
	return strings.TrimPrefix(action, "DEV_ACTION_")
}

func (h *DevHelper) getFormattedActionNames(actions []string) []string {
	var formattedActionNames []string
	for _, devActionName := range actions {
		formattedActionNames = append(formattedActionNames, GetFormattedActionName(devActionName))
	}
	return formattedActionNames
}

// FilterDevActionsV1 method utilised older way of checking accessibility of a dev action
// Here we call VerifyUserPermissions rpc from casbin service for each dev action, which causes higher load times and latency
func (h *DevHelper) FilterDevActionsV1(ctx context.Context, formattedActionNames []string, accessLevel casbinPb.AccessLevel, userEmail string) ([]string, error) {
	var filteredDevActions []string
	for _, action := range formattedActionNames {
		ok, err := h.HasAccessToDevAction(ctx, userEmail, action, accessLevel)
		// break execution and return error if some error occurred while checking access
		if err != nil {
			return nil, errors.Wrap(err, "error while verification for access to dev action")
		}
		// append only if user has access to dev action
		if ok {
			filteredDevActions = append(filteredDevActions, GetOriginalActionName(action))
		}
	}
	return filteredDevActions, nil
}

// FilterDevActionsV2 method utlises BulkCheckResourceAccessibility rpc from casbin resource,
// which checks accessibility in a single RPC call which helps in reducing load times and latency while loading
// dev actions list on sherlock
func (h *DevHelper) FilterDevActionsV2(ctx context.Context, formattedActionNames []string, accessLevel casbinPb.AccessLevel) ([]string, error) {
	bulkVerifyUserPermissionResp, bulkVerifyUserPermissionErr := h.casbinClient.BulkCheckResourceAccessibility(ctx, &casbinPb.BulkCheckResourceAccessibilityRequest{
		AccessLevel:  accessLevel,
		ResourceList: formattedActionNames,
	})
	if err := epifigrpc.RPCError(bulkVerifyUserPermissionResp, bulkVerifyUserPermissionErr); err != nil {
		return nil, errors.Wrap(err, "error while bulk verification of access for dev actions")
	}
	var filteredDevActions []string
	// filtering allowed dev actions and also trimming the added prefix
	for _, resourceVerificationInfo := range bulkVerifyUserPermissionResp.GetResourceVerificationInfoList() {
		// if resource is accessible append to filteredDevActions list
		if resourceVerificationInfo.GetIsResourceAccessible() == commontypes.BooleanEnum_TRUE {
			filteredDevActions = append(filteredDevActions, GetOriginalActionName(resourceVerificationInfo.GetResource()))
		}
	}
	return filteredDevActions, nil
}

func (h *DevHelper) GetFilteredDevActionsForAccess(ctx context.Context, userEmail string,
	actions []string, accessLevel casbinPb.AccessLevel) ([]string, error) {
	formattedActionNames := h.getFormattedActionNames(actions)
	// filteredDevActions will contain list of allowed dev actions
	var filteredDevActions []string
	var filteredDevActionsErr error
	// In case bulk resource accessibility check is enabled, we will utilise FilterDevActionsV2 to decrease load times
	if h.genConf.DevActionHelperConfig().IsBulkResourceAccessibilityCheckEnabled() {
		filteredDevActions, filteredDevActionsErr = h.FilterDevActionsV2(ctx, formattedActionNames, accessLevel)
	} else {
		// If bulk resource accessibility check is disabled in config, we will switch to older way of verification
		filteredDevActions, filteredDevActionsErr = h.FilterDevActionsV1(ctx, formattedActionNames, accessLevel, userEmail)
	}
	if filteredDevActionsErr != nil {
		return nil, errors.Wrap(filteredDevActionsErr, "error while filtering dev actions")
	}
	return filteredDevActions, nil
}
