// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/epifi/gamma/cx/developer/helper (interfaces: IDevHelper)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	casbin "github.com/epifi/gamma/api/casbin"
	gomock "github.com/golang/mock/gomock"
)

// MockIDevHelper is a mock of IDevHelper interface.
type MockIDevHelper struct {
	ctrl     *gomock.Controller
	recorder *MockIDevHelperMockRecorder
}

// MockIDevHelperMockRecorder is the mock recorder for MockIDevHelper.
type MockIDevHelperMockRecorder struct {
	mock *MockIDevHelper
}

// NewMockIDevHelper creates a new mock instance.
func NewMockIDevHelper(ctrl *gomock.Controller) *MockIDevHelper {
	mock := &MockIDevHelper{ctrl: ctrl}
	mock.recorder = &MockIDevHelperMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIDevHelper) EXPECT() *MockIDevHelperMockRecorder {
	return m.recorder
}

// GetFilteredDevActionsForAccess mocks base method.
func (m *MockIDevHelper) GetFilteredDevActionsForAccess(arg0 context.Context, arg1 string, arg2 []string, arg3 casbin.AccessLevel) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFilteredDevActionsForAccess", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFilteredDevActionsForAccess indicates an expected call of GetFilteredDevActionsForAccess.
func (mr *MockIDevHelperMockRecorder) GetFilteredDevActionsForAccess(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFilteredDevActionsForAccess", reflect.TypeOf((*MockIDevHelper)(nil).GetFilteredDevActionsForAccess), arg0, arg1, arg2, arg3)
}

// HasAccessToDevAction mocks base method.
func (m *MockIDevHelper) HasAccessToDevAction(arg0 context.Context, arg1, arg2 string, arg3 casbin.AccessLevel) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HasAccessToDevAction", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HasAccessToDevAction indicates an expected call of HasAccessToDevAction.
func (mr *MockIDevHelperMockRecorder) HasAccessToDevAction(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HasAccessToDevAction", reflect.TypeOf((*MockIDevHelper)(nil).HasAccessToDevAction), arg0, arg1, arg2, arg3)
}
