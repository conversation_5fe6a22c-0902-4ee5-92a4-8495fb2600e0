package metrics

import (
	"github.com/prometheus/client_golang/prometheus"

	casperPb "github.com/epifi/gamma/api/casper"
	exchangerPb "github.com/epifi/gamma/api/casper/exchanger"

	evrPb "github.com/epifi/gamma/api/casper/external_vendor_redemption"
	redemptionPb "github.com/epifi/gamma/api/casper/redemption"
	"github.com/epifi/gamma/casper/exchanger/redemption_statemachine/order_fulfillment"
	"github.com/epifi/gamma/casper/offervendor"
)

var MetricsRecorder = NewCasperMetricsRecorder()

// CasperMetrics represents a collection of metrics to be registered on a
// Prometheus metrics registry for casper application level metrics.
type CasperMetrics struct {
	// counter to measure status updates of offer redemption from vendor.
	vendorOfferRedemptionStatusUpdatesCounter *prometheus.CounterVec
	// counter to measure the value in INR for the successfully redeemed offers
	redeemedOfferValueCounter *prometheus.CounterVec
	// counter to measure fulfillment status updates of exchanger offer redemptions.
	exchangerOfferRedemptionFulfillmentStatusUpdatesCounter *prometheus.CounterVec
	// counter to measure reward units generated for exchangerOfferOrders. Depending on the reward-type, following values are instrumented:
	// Fi-coins: absolute units of fi-coins,
	// Cash: absolute units of cash,
	// EGV: value of the EGV in INR,
	// Physical merch: value of the physical merch in INR
	exchangerOfferOrderRewardUnitsGeneratedCounter *prometheus.CounterVec
	// counter to measure fi-coins spent by users for redeeming exchanger offers.
	exchangerOfferFiCoinsSpentOnRedemptionCounter *prometheus.CounterVec
	// counter to measure redemption status updates for different redemption modes
	redeemedOfferStatusUpdatesForRedemptionModesCounter *prometheus.CounterVec
	// counter to measure redemption status updates for different offer types
	redeemedOfferStatusUpdatesForOfferTypesCounter *prometheus.CounterVec
	// counter to measure redemption status updates for different vendors
	redeemedOfferStatusUpdatesForVendorsCounter *prometheus.CounterVec
	// counter to measure status updates of fi store redemptions (vendor created redemptions)
	fiStoreRedemptionsStatusUpdatesCounter *prometheus.CounterVec
	// counter to measure fi coin transactions (vendor created transactions)
	fiCoinTransactionsCounter *prometheus.CounterVec
}

func NewCasperMetricsRecorder() *CasperMetrics {
	casperMetrics := &CasperMetrics{
		vendorOfferRedemptionStatusUpdatesCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "casper_vendor_offer_redemption_status_updates_total",
				Help: "Total count of offer redemption status updates from vendor",
			},
			[]string{"vendor_name", "offer_type", "updated_status"},
			// vendor_name denotes the vendor whose offer was redeemed.
			// offer_type denotes the type of offer redeemed.
			// updated_status denotes the status update received from vendor for an offer redemption.
		),
		exchangerOfferRedemptionFulfillmentStatusUpdatesCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "casper_exchanger_offer_redemption_fulfillment_status_updates_total",
				Help: "Total count of exchanger offer redemption fulfillment status updates",
			},
			[]string{"reward_type", "updated_fulfillment_status"},
			// reward_type denotes the type of reward won from exchanger that is to be fulfilled.
			// updated_fulfillment_status denotes the updated fulfillment status.
		),
		exchangerOfferOrderRewardUnitsGeneratedCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "casper_exchanger_offer_order_reward_units_generated_total",
				Help: "Total count of reward units generated for exchanger offer orders",
			},
			[]string{"reward_type"},
			// reward_type denotes type of reward whose units are counted.
		),
		exchangerOfferFiCoinsSpentOnRedemptionCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "casper_exchanger_fi_coins_spend_on_redemption_total",
				Help: "Total count of Fi-Coins spent by users on redeeming exchanger offers",
			},
			[]string{},
		),
		redeemedOfferValueCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "casper_redeemed_offer_value_total",
				Help: "Total value in INR of redeemed offers",
			},
			// offer_type denotes the type offer redeemed
			[]string{"offer_type"},
		),
		redeemedOfferStatusUpdatesForRedemptionModesCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "casper_redeemed_offer_status_updates_for_redemption_modes_total",
				Help: "Total count of status updates with redemption modes for redeemed offers",
			},
			// redemption_mode denotes the mode using which redemption was made
			// updated_status is the new status of the redeemed offer
			[]string{"redemption_mode", "updated_status"},
		),
		redeemedOfferStatusUpdatesForOfferTypesCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "casper_redeemed_offer_status_updates_for_offer_types_total",
				Help: "Total count of status updates for offer types for redeemed offers",
			},
			// updated_status is the new status of the redeemed offer
			[]string{"offer_type", "updated_status"},
		),
		redeemedOfferStatusUpdatesForVendorsCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "casper_redeemed_offer_status_updates_for_vendors_total",
				Help: "Total count of status updates for different vendors for redeemed offers",
			},
			// updated_status is the new status of the redeemed offer
			[]string{"vendor_name", "updated_status"},
		),
		fiStoreRedemptionsStatusUpdatesCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "casper_fi_store_redemptions_status_updates_total",
				Help: "Total count of fi store redemptions status updates",
			},
			[]string{"vendor", "category", "updated_status"},
			// vendor denotes the vendor from which redemption was made.
			// category denotes the category of the redemption.
			// updated_status denotes the status to which the redemption was updated to. (note: will be used for newly created redemptions as well)
		),
		fiCoinTransactionsCounter: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "casper_fi_coin_transactions_total",
				Help: "Total count of fi coin transactions",
			},
			[]string{"vendor", "category", "txn_type", "status"},
			// vendor denotes the vendor for which txn was made.
			// category denotes the category of the redemption.
			// txn_type denotes the type of the txn.
			// status denotes the status of the txn.
		),
	}
	prometheus.MustRegister(casperMetrics.vendorOfferRedemptionStatusUpdatesCounter)
	prometheus.MustRegister(casperMetrics.exchangerOfferRedemptionFulfillmentStatusUpdatesCounter)
	prometheus.MustRegister(casperMetrics.exchangerOfferOrderRewardUnitsGeneratedCounter)
	prometheus.MustRegister(casperMetrics.exchangerOfferFiCoinsSpentOnRedemptionCounter)
	prometheus.MustRegister(casperMetrics.redeemedOfferValueCounter)
	prometheus.MustRegister(casperMetrics.redeemedOfferStatusUpdatesForRedemptionModesCounter)
	prometheus.MustRegister(casperMetrics.redeemedOfferStatusUpdatesForOfferTypesCounter)
	prometheus.MustRegister(casperMetrics.redeemedOfferStatusUpdatesForVendorsCounter)
	prometheus.MustRegister(casperMetrics.fiStoreRedemptionsStatusUpdatesCounter)
	prometheus.MustRegister(casperMetrics.fiCoinTransactionsCounter)

	// initialise metrics to 0
	initialise(casperMetrics)

	return casperMetrics
}

// initialise metrics to 0
func initialise(cm *CasperMetrics) {
	// initialize vendorOfferRedemptionStatusUpdatesCounter metric to 0
	for vendorName := range casperPb.OfferVendor_value {
		for offerType := range casperPb.OfferType_value {
			for _, vendorOrderStatus := range offervendor.AllVendorRedemptionStatuses {
				cm.vendorOfferRedemptionStatusUpdatesCounter.WithLabelValues(vendorName, offerType, string(vendorOrderStatus))
			}
		}
	}

	// initialize exchangerOfferRedemptionFulfillmentStatusUpdatesCounter metric to 0
	for rewardType := range exchangerPb.RewardType_value {
		for _, fulfillmentStatus := range order_fulfillment.AllFulfillmentStatuses {
			cm.exchangerOfferRedemptionFulfillmentStatusUpdatesCounter.WithLabelValues(rewardType, string(fulfillmentStatus))
		}
	}

	// initialize exchangerOfferOrderRewardUnitsGeneratedCounter metric to 0
	for rewardType := range exchangerPb.RewardType_value {
		cm.exchangerOfferOrderRewardUnitsGeneratedCounter.WithLabelValues(rewardType)
	}

	// initialize redeemedOfferValueCounter metric to 0 for all the offer_types
	for offerType := range casperPb.OfferType_value {
		cm.redeemedOfferValueCounter.WithLabelValues(offerType)
	}

	// initialize redeemedOfferStatusUpdatesForRedemptionModesCounter metric to 0 for all the redemption_modes, statuses
	for redemptionMode := range casperPb.OfferRedemptionMode_value {
		for status := range redemptionPb.OfferRedemptionState_value {
			cm.redeemedOfferStatusUpdatesForRedemptionModesCounter.WithLabelValues(redemptionMode, status)
		}
	}

	// initialize redeemedOfferStatusUpdatesForOfferTypesCounter metric to 0 for all the offerType, statuses
	for offerType := range casperPb.OfferType_value {
		for status := range redemptionPb.OfferRedemptionState_value {
			cm.redeemedOfferStatusUpdatesForOfferTypesCounter.WithLabelValues(offerType, status)
		}
	}

	// initialize redeemedOfferStatusUpdatesForVendorsCounter metric to 0 for all the vendor, statuses
	for vendor := range casperPb.OfferVendor_value {
		for status := range redemptionPb.OfferRedemptionState_value {
			cm.redeemedOfferStatusUpdatesForVendorsCounter.WithLabelValues(vendor, status)
		}
	}

	// initialize fiStoreRedemptionsStatusUpdatesCounter metric to 0
	for vendor := range evrPb.Vendor_value {
		for category := range evrPb.Category_value {
			for status := range evrPb.OrderStatus_value {
				cm.fiStoreRedemptionsStatusUpdatesCounter.WithLabelValues(vendor, category, status)
			}
		}
	}

	// initialize fiCoinTransactionsCounter metric to 0
	for vendor := range evrPb.Vendor_value {
		for category := range evrPb.Category_value {
			for txnType := range evrPb.TxnType_value {
				statuses := []string{"COMPLETED", "FAILED", "PENDING", "SUCCESS"}
				for _, status := range statuses {
					cm.fiCoinTransactionsCounter.WithLabelValues(vendor, category, txnType, status)
				}
			}
		}
	}
}

func (cm *CasperMetrics) RecordVendorOfferRedemptionStatusUpdate(vendorName, offerType, vendorRedemptionStatus string) {
	cm.vendorOfferRedemptionStatusUpdatesCounter.WithLabelValues(vendorName, offerType, vendorRedemptionStatus).Inc()
}

func (cm *CasperMetrics) RecordExchangerOfferRedemptionFulfillmentStatusUpdate(rewardType, fulfillmentStatus string) {
	cm.exchangerOfferRedemptionFulfillmentStatusUpdatesCounter.WithLabelValues(rewardType, fulfillmentStatus).Inc()
}

func (cm *CasperMetrics) RecordExchangerOrderGeneratedRewardUnits(rewardType string, units float64) {
	cm.exchangerOfferOrderRewardUnitsGeneratedCounter.WithLabelValues(rewardType).Add(units)
}

func (cm *CasperMetrics) RecordExchangerOfferFiCoinsSpentOnRedemptionCounter(units float64) {
	cm.exchangerOfferFiCoinsSpentOnRedemptionCounter.WithLabelValues().Add(units)
}

func (cm *CasperMetrics) RecordRedeemedOfferValueCounter(offerType string, units float64) {
	cm.redeemedOfferValueCounter.WithLabelValues(offerType).Add(units)
}

func (cm *CasperMetrics) RecordRedeemedOfferStatusUpdatesForRedemptionModesCounter(redemptionMode, status string) {
	cm.redeemedOfferStatusUpdatesForRedemptionModesCounter.WithLabelValues(redemptionMode, status).Inc()
}

func (cm *CasperMetrics) RecordRedeemedOfferStatusUpdatesForOfferTypesCounter(offerType, status string) {
	cm.redeemedOfferStatusUpdatesForOfferTypesCounter.WithLabelValues(offerType, status).Inc()
}

func (cm *CasperMetrics) RecordRedeemedOfferStatusUpdatesForVendorsCounter(vendor, status string) {
	cm.redeemedOfferStatusUpdatesForVendorsCounter.WithLabelValues(vendor, status).Inc()
}

func (cm *CasperMetrics) RecordFiStoreRedemptionStatusUpdate(vendor, category, status string) {
	cm.fiStoreRedemptionsStatusUpdatesCounter.WithLabelValues(vendor, category, status).Inc()
}

func (cm *CasperMetrics) RecordFiCoinTransaction(vendor, category, txnType, status string) {
	cm.fiCoinTransactionsCounter.WithLabelValues(vendor, category, txnType, status).Inc()
}
