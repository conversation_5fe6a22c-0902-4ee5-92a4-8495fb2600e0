package external_vendor_redemption

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/jonboulle/clockwork"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	accrualPb "github.com/epifi/gamma/api/accrual"
	casperPb "github.com/epifi/gamma/api/casper"
	evrPb "github.com/epifi/gamma/api/casper/external_vendor_redemption"
	notificationPb "github.com/epifi/gamma/api/casper/external_vendor_redemption/notification"
	fireflypb "github.com/epifi/gamma/api/firefly"
	rewardsPb "github.com/epifi/gamma/api/rewards"

	accrualconf "github.com/epifi/gamma/accrual/config"
	"github.com/epifi/gamma/casper/config/genconf"
	casperEvents "github.com/epifi/gamma/casper/events"
	"github.com/epifi/gamma/casper/external_vendor_redemption/dao"
	"github.com/epifi/gamma/casper/external_vendor_redemption/dao/model"
	"github.com/epifi/gamma/casper/metrics"
	"github.com/epifi/gamma/casper/wire/types"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"
)

type Service struct {
	dynConf                           *genconf.Config
	externalVendorRedemptionDao       dao.ExternalVendorRedemptionDao
	fiStoreRedemptionDao              dao.FiStoreRedemptionDao
	dbTxnExecutor                     storagev2.TxnExecutor
	accrualClient                     accrualPb.AccrualClient
	fiStoreOrderNotificationPublisher types.FiStoreOrderNotificationSqsPublisher
	fmAllFields                       *fieldmaskpb.FieldMask
	fmRefIdFields                     *fieldmaskpb.FieldMask
	fiStoreFmAllFields                *fieldmaskpb.FieldMask
	fiStoreUpdateFmFields             *fieldmaskpb.FieldMask
	eventBroker                       events.Broker
	fireflyClient                     fireflypb.FireflyClient
	clock                             clockwork.Clock
}

type Opt func(s *Service)

func WithClock(clock clockwork.Clock) Opt {
	return func(s *Service) {
		s.clock = clock
	}
}

func NewExternalVendorRedemptionService(
	dynConf *genconf.Config,
	externalVendorRedemptionDao dao.ExternalVendorRedemptionDao,
	fiStoreRedemptionDao dao.FiStoreRedemptionDao,
	fiStoreOrderNotificationPublisher types.FiStoreOrderNotificationSqsPublisher,
	dbTxnExecutor storagev2.TxnExecutor,
	accrualClient accrualPb.AccrualClient,
	eventBroker events.Broker,
	fireflyClient fireflypb.FireflyClient,
	opts ...Opt,
) (*Service, error) {
	// field mask for all fields
	fmAllFields, err := fieldmaskpb.New(&evrPb.ExternalVendorRedemption{}, "id", "actor_id", "vendor", "category", "txn_type", "txn_category", "fi_coin_units", "vendor_ref_id", "vendor_order_id", "processing_ref_id", "created_at", "updated_at")
	if err != nil {
		return nil, err
	}
	// field mask for processing ref only
	fmRefIdFields, err := fieldmaskpb.New(&evrPb.ExternalVendorRedemption{}, "processing_ref_id")
	if err != nil {
		return nil, err
	}
	// field mask for all fi store column fields
	fiStoreFmAllFields, err := fieldmaskpb.New(&evrPb.FiStoreRedemption{}, "id", "actor_id", "vendor", "vendor_ref_id", "product_id", "payment_instrument_identifier", "product_price", "discount_price", "spent_cash_units", "spent_fi_coin_units", "order_status", "category", "redemption_meta_data", "order_timestamp", "created_at", "updated_at")
	if err != nil {
		return nil, err
	}
	// field mask for update order status & tracking link
	fiStoreUpdateFmFields, err := fieldmaskpb.New(&evrPb.FiStoreRedemption{}, "order_status", "redemption_meta_data")
	if err != nil {
		return nil, err
	}
	s := &Service{
		dynConf:                           dynConf,
		externalVendorRedemptionDao:       externalVendorRedemptionDao,
		fiStoreRedemptionDao:              fiStoreRedemptionDao,
		dbTxnExecutor:                     dbTxnExecutor,
		accrualClient:                     accrualClient,
		fiStoreOrderNotificationPublisher: fiStoreOrderNotificationPublisher,
		fmAllFields:                       fmAllFields,
		fmRefIdFields:                     fmRefIdFields,
		fiStoreFmAllFields:                fiStoreFmAllFields,
		fiStoreUpdateFmFields:             fiStoreUpdateFmFields,
		eventBroker:                       eventBroker,
		fireflyClient:                     fireflyClient,
		clock:                             clockwork.NewRealClock(),
	}
	for _, opt := range opts {
		opt(s)
	}
	return s, nil
}

var _ evrPb.ExternalVendorRedemptionServiceServer = &Service{}

const (
	TxnCompleted = "COMPLETED"
	TxnFailed    = "FAILED"
	TxnPending   = "PENDING"
)

var InSufficientBalanceInAccountError = errors.New("insufficient balance in account")

// GetUserFiCoinsBalance fetches actor's fi coins balance
func (s *Service) GetUserFiCoinsBalance(ctx context.Context, request *evrPb.GetUserFiCoinsBalanceRequest) (*evrPb.GetUserFiCoinsBalanceResponse, error) {
	accountDetailsResp, err := s.accrualClient.GetAccountDetails(ctx, &accrualPb.GetAccountDetailsRequest{
		ActorId:     request.GetActorId(),
		AccountType: accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestamppb.Now()),
	})
	if rpcErr := epifigrpc.RPCError(accountDetailsResp, err); rpcErr != nil {
		logger.Error(ctx, "error while fetching account details for given actor id", zap.Error(rpcErr))
		return &evrPb.GetUserFiCoinsBalanceResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	return &evrPb.GetUserFiCoinsBalanceResponse{
		Status:  rpc.StatusOk(),
		Balance: int64(accountDetailsResp.GetCurrentBalance()),
	}, nil
}

// TransactFiCoins will do credit & debit transactions for given request
// TODO: refactor this function
// nolint: funlen, gocritic
func (s *Service) TransactFiCoins(ctx context.Context, request *evrPb.TransactFiCoinsRequest) (*evrPb.TransactFiCoinsResponse, error) {
	var (
		vendor, category, txnType, vendorRefId = request.GetVendor(), request.GetCategory(), request.GetTxnType(), request.GetVendorRefId()
	)
	redemptions, _, err := s.externalVendorRedemptionDao.GetExternalVendorRedemptions(ctx, &model.Filters{
		VendorRefId: vendorRefId,
		TxnType:     txnType,
		Vendor:      vendor,
		Category:    category,
	}, s.fmAllFields, nil, 2)
	if err != nil {
		metrics.MetricsRecorder.RecordFiCoinTransaction(vendor.String(), category.String(), txnType.String(), TxnFailed)
		logger.Error(ctx, "error while fetching external vendor redemptions", zap.Error(err))
		return &evrPb.TransactFiCoinsResponse{
			Status:    rpc.StatusInternalWithDebugMsg(err.Error()),
			TxnStatus: TxnFailed,
			SubStatus: "Transaction failed due to fetching record in db",
		}, nil
	}
	switch {
	// if no record found, then we should call accrual transact RPC to make transaction and store in db.
	case len(redemptions) == 0:
		txnId, txnStatus, err := s.performTransaction(ctx, request)
		if err != nil {
			metrics.MetricsRecorder.RecordFiCoinTransaction(vendor.String(), category.String(), txnType.String(), TxnFailed)
			if errors.Is(err, InSufficientBalanceInAccountError) {
				return &evrPb.TransactFiCoinsResponse{
					Status:    rpc.StatusFailedPreconditionWithDebugMsg(err.Error()),
					TxnStatus: TxnFailed,
					SubStatus: "Failed to perform transaction, due to insufficient balance in account",
				}, nil
			}
			return &evrPb.TransactFiCoinsResponse{
				Status:    rpc.StatusInternalWithDebugMsg(err.Error()),
				TxnStatus: TxnFailed,
				SubStatus: "Failed to perform transaction",
			}, nil
		}
		if txnStatus == accrualPb.TransactionStatus_TRANSACTION_STATUS_NOT_PERFORMED || txnStatus == accrualPb.TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED {
			metrics.MetricsRecorder.RecordFiCoinTransaction(vendor.String(), category.String(), txnType.String(), TxnFailed)
			return &evrPb.TransactFiCoinsResponse{
				Status:    rpc.StatusInternalWithDebugMsg("txn call not successful"),
				TxnStatus: TxnFailed,
				SubStatus: "Failed to perform transaction",
			}, nil
		}
		subStatus := ""
		accountDetailsResp, err := s.accrualClient.GetAccountDetails(ctx, &accrualPb.GetAccountDetailsRequest{
			ActorId:     request.GetActorId(),
			AccountType: accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestamppb.Now()),
		})
		if rpcErr := epifigrpc.RPCError(accountDetailsResp, err); rpcErr != nil {
			// In case of error log it, but no need of throwing error in RPC call.
			subStatus = "Failed to fetch fi coins balance"
			logger.Error(ctx, "error while fetching available balance", zap.String("actorId", request.GetActorId()), zap.Error(rpcErr))
		}
		// create entry in db for this txn
		vendorRedemption, err := s.externalVendorRedemptionDao.Create(ctx, &evrPb.ExternalVendorRedemption{
			ActorId:         request.GetActorId(),
			Vendor:          vendor,
			Category:        category,
			TxnType:         txnType,
			TxnCategory:     request.GetTxnCategory(),
			FiCoinUnits:     request.GetAmount(),
			VendorRefId:     vendorRefId,
			VendorOrderId:   request.GetVendorOrderId(),
			ProcessingRefId: txnId,
		})
		if err != nil {
			logger.Error(ctx, "error while creating vendor redemption record", zap.Error(err))
			metrics.MetricsRecorder.RecordFiCoinTransaction(vendor.String(), category.String(), txnType.String(), TxnPending)
			return &evrPb.TransactFiCoinsResponse{
				Status:    rpc.StatusInternalWithDebugMsg(err.Error()),
				TxnStatus: TxnPending,
				SubStatus: "Failed to create record in db",
			}, nil
		}
		status := s.getTxnStatusBasedOnRefId(vendorRedemption.GetProcessingRefId())

		// send in-app notification for successful debit or credit txn
		s.publishApplicableOrderNotification(ctx, request.GetActorId(), vendorRedemption.GetId(), vendor, category, evrPb.OrderStatus_ORDER_STATUS_UNSPECIFIED, txnType)

		metrics.MetricsRecorder.RecordFiCoinTransaction(vendor.String(), category.String(), txnType.String(), status)
		return &evrPb.TransactFiCoinsResponse{
			Status:                rpc.StatusOk(),
			ProcessingRefId:       vendorRedemption.GetProcessingRefId(),
			TxnStatus:             status,
			UpdatedFiCoinsBalance: accountDetailsResp.GetCurrentBalance(),
			SubStatus:             subStatus,
		}, nil

	// In case of no error (record exist), return redemption details
	default:
		redemption := redemptions[0]
		subStatus := ""
		// fetch updated available balance of actor
		accountDetailsResp, err := s.accrualClient.GetAccountDetails(ctx, &accrualPb.GetAccountDetailsRequest{
			ActorId:     request.GetActorId(),
			AccountType: accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestamppb.Now()),
		})
		if rpcErr := epifigrpc.RPCError(accountDetailsResp, err); rpcErr != nil {
			subStatus = "Failed to fetch fi coins balance"
			logger.Error(ctx, "error while fetching available balance", zap.String("actorId", request.GetActorId()), zap.Error(rpcErr))
		}
		redemption, err = s.checkAndReturnRedemptionDetails(ctx, request, redemption)
		if err != nil {
			logger.Error(ctx, "error while checking redemption details", zap.String("actorId", request.GetActorId()), zap.Error(err))
			metrics.MetricsRecorder.RecordFiCoinTransaction(vendor.String(), category.String(), txnType.String(), TxnPending)
			return &evrPb.TransactFiCoinsResponse{
				Status:    rpc.StatusInternalWithDebugMsg(err.Error()),
				TxnStatus: TxnPending,
				SubStatus: "Failed to fetch txn details",
			}, nil
		}
		txnStatus := s.getTxnStatusBasedOnRefId(redemption.GetProcessingRefId())
		metrics.MetricsRecorder.RecordFiCoinTransaction(vendor.String(), category.String(), txnType.String(), txnStatus)
		return &evrPb.TransactFiCoinsResponse{
			Status:                rpc.StatusOk(),
			ProcessingRefId:       redemption.GetProcessingRefId(),
			TxnStatus:             txnStatus,
			UpdatedFiCoinsBalance: accountDetailsResp.GetCurrentBalance(),
			SubStatus:             subStatus,
		}, nil
	}
}

func (s *Service) checkAndReturnRedemptionDetails(ctx context.Context, request *evrPb.TransactFiCoinsRequest, redemption *evrPb.ExternalVendorRedemption) (*evrPb.ExternalVendorRedemption, error) {
	if redemption.GetProcessingRefId() == "" {
		refId := s.getRefId(request)
		txnStatus, txnId, err := s.checkTransactionStatus(ctx, refId)
		if err != nil {
			return nil, err
		}
		if txnStatus == accrualPb.TransactionStatus_TRANSACTION_STATUS_COMPLETED {
			// as initially processing id is empty & if txn status is completed then it will return txn id.
			// update in db processing id equal to txn id
			redemption.ProcessingRefId = txnId
			err = s.externalVendorRedemptionDao.Update(ctx, redemption, s.fmRefIdFields)
			if err != nil {
				logger.Error(ctx, "error while updating vendor redemption record", zap.Error(err))
				return nil, err
			}
			return redemption, nil
		}
	}
	return redemption, nil
}

// for credit txn type we append `REVERSAL-` to ref id
func (s *Service) getRefId(req *evrPb.TransactFiCoinsRequest) string {
	if req.GetTxnType() == evrPb.TxnType_CREDIT && (req.GetTxnCategory() == evrPb.TxnCategory_REFUND || req.GetTxnCategory() == evrPb.TxnCategory_PARTIAL_REFUND) {
		return s.getRefIdForReversalDebitTxn(req.GetVendorRefId())
	}
	return req.GetVendorRefId()
}

// performTransaction performs the credit & debit txns
func (s *Service) performTransaction(ctx context.Context, req *evrPb.TransactFiCoinsRequest) (string, accrualPb.TransactionStatus, error) {
	var (
		txnId     string
		txnStatus accrualPb.TransactionStatus
		txnErr    error
	)

	switch req.GetTxnType() {
	case evrPb.TxnType_DEBIT:
		txnId, txnStatus, txnErr = s.performDebitTransaction(ctx, req)
		if txnErr != nil {
			logger.Error(ctx, "error while performing debit txn", zap.Error(txnErr))
			return "", accrualPb.TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED, txnErr
		}
	case evrPb.TxnType_CREDIT:
		txnId, txnStatus, txnErr = s.performCreditTransaction(ctx, req)
		if txnErr != nil {
			logger.Error(ctx, "error while performing credit txn", zap.Error(txnErr))
			return "", accrualPb.TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED, txnErr
		}
	default:
		logger.Info(ctx, "unsupported txn type provided", zap.String("TxnType", req.GetTxnType().String()))
		return "", accrualPb.TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED, fmt.Errorf("unsupported txn type provided %v", req.GetTxnType())
	}
	return txnId, txnStatus, nil
}

// performs debit txn
func (s *Service) performDebitTransaction(ctx context.Context, req *evrPb.TransactFiCoinsRequest) (string, accrualPb.TransactionStatus, error) {
	if req.GetTxnCategory() != evrPb.TxnCategory_PURCHASE {
		return "", accrualPb.TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED, fmt.Errorf("unsupported txn category for DEBIT txn type, got: %v", req.GetTxnCategory())
	}
	txnStatus, txnId, err := s.checkTransactionStatus(ctx, req.GetVendorRefId())
	if err != nil {
		return "", txnStatus, err
	}
	switch txnStatus {
	// In case of not performed, perform the txn.
	case accrualPb.TransactionStatus_TRANSACTION_STATUS_NOT_PERFORMED:
		debitReq := &accrualPb.TransactRequest{
			RequestRefId:    req.GetVendorRefId(),
			ActorId:         req.GetActorId(),
			Amount:          req.GetAmount(),
			AccountType:     accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestamppb.Now()),
			TransactionType: accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT,
		}
		debitResp, err := s.accrualClient.Transact(ctx, debitReq)
		if rpcErr := epifigrpc.RPCError(debitResp, err); rpcErr != nil {
			logger.Error(ctx, "rpc error debiting to accrual account", zap.String("actorId", req.GetActorId()), zap.String("refId", req.GetVendorRefId()), zap.Error(rpcErr))
			if debitResp.GetStatus().IsFailedPrecondition() {
				return "", accrualPb.TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED, InSufficientBalanceInAccountError
			}
			return "", accrualPb.TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED, rpcErr
		}
		return debitResp.GetTransactionId(), debitResp.GetTransactionStatus(), nil
	default:
		return txnId, txnStatus, nil
	}
}

// performs credit txn
func (s *Service) performCreditTransaction(ctx context.Context, request *evrPb.TransactFiCoinsRequest) (string, accrualPb.TransactionStatus, error) {
	redemption, redemptionErr := s.getExternalVendorRedemption(ctx, request.GetVendorRefId(), evrPb.TxnType_DEBIT, request.GetVendor(), request.GetCategory())
	if redemptionErr != nil {
		return "", accrualPb.TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED, redemptionErr
	}
	if err := s.validateTxnAmount(ctx, request.GetAmount(), redemption, request.GetTxnCategory()); err != nil {
		return "", accrualPb.TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED, err
	}
	if err := s.getActorIdFromVendorRefId(ctx, request); err != nil {
		logger.Error(ctx, "error while validating actor id", zap.Error(err))
		return "", accrualPb.TransactionStatus_TRANSACTION_STATUS_NOT_PERFORMED, err
	}
	// NOTE: here refId for credit txn types will be having `REVERSAL-` as prefix to avoid idempotence failure in accrual txn table.
	refId := s.getRefIdForReversalDebitTxn(request.GetVendorRefId())
	txnStatus, txnId, err := s.checkTransactionStatus(ctx, refId)
	if err != nil {
		return "", accrualPb.TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED, err
	}
	switch txnStatus {
	// In case of not performed, perform the txn
	case accrualPb.TransactionStatus_TRANSACTION_STATUS_NOT_PERFORMED:
		creditAmount, txnMetaData, err := accrualPkg.ConvertFiCoinsToFiPointsForCreditTxn(ctx, s.clock, request.GetAmount(), redemption.GetCreatedAt(), request.GetActorId(), s.fireflyClient)
		if err != nil {
			return "", accrualPb.TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED, fmt.Errorf("error while converting fi coins to fi points: %w", err)
		}

		// TODO: update expiry time as last debited txn expiry time. Currently passing expiry time as current + 2 years.
		// this may cause fi coins expiry misuse. For eg. purchasing & refunding multiple times will lead to increase expiry time of fi coins.
		var (
			accountType = accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestamppb.Now())
			creditReq   = &accrualPb.TransactRequest{
				RequestRefId:     refId,
				ActorId:          request.GetActorId(),
				Amount:           creditAmount,
				AmountExpiryTime: accrualconf.FetchDefaultCurrencyExpiryTimestamp(accountType),
				AccountType:      accountType,
				TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
				TxnMetaData:      txnMetaData,
			}
		)
		creditResp, err := s.accrualClient.Transact(ctx, creditReq)
		if rpcErr := epifigrpc.RPCError(creditResp, err); rpcErr != nil {
			logger.Error(ctx, "rpc error crediting to accrual account", zap.String("actorId", request.GetActorId()), zap.String("refId", request.GetVendorRefId()), zap.Error(rpcErr))
			return "", accrualPb.TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED, rpcErr
		}
		return creditResp.GetTransactionId(), creditResp.GetTransactionStatus(), nil
	default:
		return txnId, txnStatus, nil
	}
}

func (s *Service) publishApplicableOrderNotification(ctx context.Context, actorId, redemptionId string, vendor evrPb.Vendor, category evrPb.Category, orderStatus evrPb.OrderStatus, txnType evrPb.TxnType) {
	msgId, err := s.publishOrderNotificationEvent(ctx, actorId, redemptionId, notificationPb.Medium_NOTIFICATION, category, vendor, orderStatus, txnType)
	switch {
	case err != nil:
		logger.Error(ctx, "error while publishing order notification event to queue", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("redemptionId", redemptionId), zap.Error(err))
	case msgId != "":
		logger.Info(ctx, "successfully published order notification event to queue", zap.String(logger.MESSAGE_ID, msgId), zap.String(logger.ACTOR_ID_V2, actorId), zap.String("redemptionId", redemptionId))
	default:
		logger.Debug(ctx, "not published the order notification event, flag is turned off", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("redemptionId", redemptionId))
	}
}

func (s *Service) checkTransactionStatus(ctx context.Context, refId string) (accrualPb.TransactionStatus, string, error) {
	txnStatusResp, err := s.accrualClient.CheckTransactionStatus(ctx, &accrualPb.CheckStatusRequest{RequestRefId: refId})
	if rpcErr := epifigrpc.RPCError(txnStatusResp, err); rpcErr != nil {
		logger.Error(ctx, "rpc error checking txn status", zap.Error(rpcErr))
		return accrualPb.TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED, "", rpcErr
	}
	return txnStatusResp.GetTransactionStatus(), txnStatusResp.GetTxnId(), nil
}

// validate that the credit amount is less than debit amount.
// Cases:
// 1. If by chance we got any refund amount greater than purchased amount, then we shouldn't do transaction.
// 2. We check this condition for REFUND, PARTIAL_REFUND txn categories only.
func (s *Service) validateTxnAmount(_ context.Context, amount int32, redemption *evrPb.ExternalVendorRedemption, txnCategory evrPb.TxnCategory) error {
	switch txnCategory {
	case evrPb.TxnCategory_REFUND:
		if amount != redemption.GetFiCoinUnits() {
			return fmt.Errorf("requested refund amount %v, is not equal to purchased amount %v", amount, redemption.GetFiCoinUnits())
		}
	case evrPb.TxnCategory_PARTIAL_REFUND:
		if amount > redemption.GetFiCoinUnits() {
			return fmt.Errorf("requested partial refund amount %v, is greater than purchased amount %v", amount, redemption.GetFiCoinUnits())
		}
	default:
		return fmt.Errorf("unsupported txn category for CREDIT txn type, got: %v", txnCategory)
	}
	return nil
}

// nolint: unparam
func (s *Service) getExternalVendorRedemption(ctx context.Context, refId string, txnType evrPb.TxnType, vendor evrPb.Vendor, category evrPb.Category) (*evrPb.ExternalVendorRedemption, error) {
	debitRedemptions, _, err := s.externalVendorRedemptionDao.GetExternalVendorRedemptions(ctx, &model.Filters{
		VendorRefId: refId,
		TxnType:     txnType,
		Vendor:      vendor,
		Category:    category,
	}, s.fmAllFields, nil, 2)
	if err != nil {
		return nil, fmt.Errorf("error while fetching vendor redemption debit record for ref_id: %v, err: %v", refId, err)
	}
	if len(debitRedemptions) == 0 {
		return nil, fmt.Errorf("relative debited txn record not found for ref_id %v", refId)
	}
	// we always get one debit redemption only, because of idempotence check in table.
	return debitRedemptions[0], nil
}

// Only when txn completed then only we get processing ref id.
// Return TxnCompleted when refId is not empty, otherwise TxnPending
func (s *Service) getTxnStatusBasedOnRefId(refId string) string {
	if refId != "" {
		return TxnCompleted
	}
	return TxnPending
}

func (s *Service) getRefIdForReversalDebitTxn(refId string) string {
	return fmt.Sprintf("REVERSAL-%s", refId)
}

// For cases like refund & partial refund, if we get actor id as empty, then we need to fetch actor id from db using vendor_ref_id.
func (s *Service) getActorIdFromVendorRefId(ctx context.Context, req *evrPb.TransactFiCoinsRequest) error {
	if req.GetActorId() == "" && (req.GetTxnCategory() == evrPb.TxnCategory_REFUND || req.GetTxnCategory() == evrPb.TxnCategory_PARTIAL_REFUND) {
		redemptions, _, err := s.externalVendorRedemptionDao.GetExternalVendorRedemptions(ctx, &model.Filters{
			VendorRefId: req.GetVendorRefId(),
			TxnType:     evrPb.TxnType_DEBIT,
			Vendor:      req.GetVendor(),
			Category:    req.GetCategory(),
		}, s.fmAllFields, nil, 2)
		if err != nil {
			return err
		}
		if len(redemptions) == 0 {
			return fmt.Errorf("no debit txn found for given vendor ref id: %v", req.GetVendorRefId())
		}
		req.ActorId = redemptions[0].GetActorId()
	}
	return nil
}

// CreateFiStoreRedemptions creates the redemption request data row in DB if not exist.
func (s *Service) CreateFiStoreRedemptions(ctx context.Context, req *evrPb.CreateFiStoreRedemptionsRequest) (*evrPb.CreateFiStoreRedemptionsResponse, error) {
	var fiStoreRedemptions []*evrPb.FiStoreRedemption

	// Inserting redemption details via transaction here,
	// if one record failed to create then we ask vendor again to send the data
	// as if we persist some records and not persists other records & vendor didn't send the data then we might be having incomplete txn details
	// better to have nothing rather than persisting only half information.
	txnErr := s.dbTxnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		for _, redemption := range req.GetRedemptionDetails() {
			res, createErr := s.fiStoreRedemptionDao.Create(txnCtx, &evrPb.FiStoreRedemption{
				ActorId:                     req.GetActorId(),
				Vendor:                      req.GetVendor(),
				VendorRefId:                 req.GetVendorRefId(),
				ProductId:                   redemption.GetProductId(),
				PaymentInstrumentIdentifier: req.GetPaymentInstrumentIdentifier(),
				ProductPrice:                redemption.GetProductPrice(),
				SpentCashUnits:              redemption.GetSpentCashUnits(),
				SpentFiCoinUnits:            redemption.GetSpentFiCoinUnits(),
				OrderStatus:                 redemption.GetOrderStatus(),
				Category:                    req.GetCategory(),
				DiscountPrice:               redemption.GetDiscountPrice(),
				RedemptionMetaData: &evrPb.RedemptionMetaData{
					ProductName:                      strings.TrimSpace(redemption.GetProductName()),
					BrandName:                        strings.TrimSpace(redemption.GetBrandName()),
					SubCategory:                      redemption.GetSubCategory(),
					Quantity:                         redemption.GetQuantity(),
					OrderTrackingLink:                redemption.GetOrderTrackingLink(),
					SpentFiCoinUnitsEquivalentToCash: redemption.GetSpentFiCoinUnitsEquivalentToCash(),
					LastUpdatedBy:                    redemption.GetLastUpdatedBy(),
				},
				OrderTimestamp: req.GetOrderTimestamp(),
			})
			switch {
			// if error is duplicate row entry, ignore the error.
			case createErr != nil && dao.IsRecordAlreadyExist(createErr):
				logger.Error(ctx, "error while creating redemption record in db due to record already exist", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String("vendor", req.GetVendor().String()), zap.Error(createErr))
				continue
			case createErr != nil:
				return createErr
			default:
				fiStoreRedemptions = append(fiStoreRedemptions, res)
			}
		}
		return nil
	})
	if txnErr != nil {
		logger.Error(ctx, "error while creating redemption record in db", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String("vendor", req.GetVendor().String()), zap.Error(txnErr))
		return &evrPb.CreateFiStoreRedemptionsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while creating redemption records in db"),
		}, nil
	}

	// publish the order notification events to queue for notifying users via comms.
	for _, redemption := range fiStoreRedemptions {
		// record metrics for fi store redemption creation
		s.recordFiStoreRedemptionMetrics(redemption.GetVendor().String(), redemption.GetCategory().String(), redemption.GetOrderStatus().String())

		// publish rudder event for order confirmation
		s.publishEligibleRedemptionRudderEvent(ctx, redemption)

		// not sending notification if specified explicitly.
		if req.GetShouldSuppressNotification() {
			break
		}
		// check if the order status is valid for sending notification and publish the event.
		s.publishApplicableOrderNotification(ctx, req.GetActorId(), redemption.GetId(), req.GetVendor(), req.GetCategory(), redemption.GetOrderStatus(), evrPb.TxnType_TXN_TYPE_UNSPECIFIED)
	}

	return &evrPb.CreateFiStoreRedemptionsResponse{
		Status: rpc.StatusOk(),
	}, nil
}

// UpdateFiStoreRedemptions updates the redemption request data row in DB if existed.
func (s *Service) UpdateFiStoreRedemptions(ctx context.Context, req *evrPb.UpdateFiStoreRedemptionsRequest) (*evrPb.UpdateFiStoreRedemptionsResponse, error) {
	// get record id if exist
	existingRedemptions, _, err := s.fiStoreRedemptionDao.GetByFilters(ctx, &model.FiStoreRedemptionsFilters{
		Vendor:      req.GetVendor(),
		VendorRefId: req.GetVendorRefId(),
		ProductId:   []string{req.GetProductId()},
	}, s.fiStoreFmAllFields, nil, 30)
	if err != nil {
		logger.Error(ctx, "error while fetching redemptions", zap.String("vendor", req.GetVendor().String()), zap.Error(err))
		return &evrPb.UpdateFiStoreRedemptionsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while fetching redemptions"),
		}, nil
	}
	if len(existingRedemptions) == 0 {
		logger.Debug(ctx, "redemptions record does not exist", zap.String("vendor", req.GetVendor().String()), zap.Error(err))
		return &evrPb.UpdateFiStoreRedemptionsResponse{
			Status: rpc.StatusRecordNotFoundWithDebugMsg("record does not exist"),
		}, nil
	}

	// update the record.
	updatedRedemption := existingRedemptions[0]

	// check if the update request is redundant
	if s.checkIfRedundantUpdateRequest(req, updatedRedemption) {
		return &evrPb.UpdateFiStoreRedemptionsResponse{
			Status: rpc.StatusOkWithDebugMsg("redundant update request"),
		}, nil
	}

	updatedRedemption.GetRedemptionMetaData().LastUpdatedBy = req.GetLastUpdatedBy()
	if req.GetOrderStatus() != evrPb.OrderStatus_ORDER_STATUS_UNSPECIFIED {
		updatedRedemption.OrderStatus = req.GetOrderStatus()
	}
	if req.GetOrderTrackingLink() != "" {
		updatedRedemption.GetRedemptionMetaData().OrderTrackingLink = req.GetOrderTrackingLink()
	}

	updateErr := s.fiStoreRedemptionDao.Update(ctx, updatedRedemption, s.fiStoreUpdateFmFields)
	if updateErr != nil {
		// if error is `row not updated`, ignore & return success
		if errors.Is(updateErr, epifierrors.ErrRowNotUpdated) {
			return &evrPb.UpdateFiStoreRedemptionsResponse{
				Status: rpc.StatusOk(),
			}, nil
		}
		logger.Error(ctx, "error while updating redemption record", zap.String("vendor", req.GetVendor().String()), zap.Error(updateErr))
		return &evrPb.UpdateFiStoreRedemptionsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while updating redemption record"),
		}, nil
	}

	// record metrics for fi store redemption status update
	s.recordFiStoreRedemptionMetrics(updatedRedemption.GetVendor().String(), updatedRedemption.GetCategory().String(), updatedRedemption.GetOrderStatus().String())

	// publish rudder event for order confirmation
	s.publishEligibleRedemptionRudderEvent(ctx, updatedRedemption)

	// publish the order update notification events to queue for notifying users via comms if applicable.
	// we are publishing update notifications only if below conditions are true:
	// 1. notification is not suppressed explicitly.
	// 2. ( order status is cancelled ) or ( order status is shipped and order tracking link is provided ).
	if !req.GetShouldSuppressNotification() {
		s.publishApplicableOrderNotification(ctx, updatedRedemption.GetActorId(), updatedRedemption.GetId(), updatedRedemption.GetVendor(), updatedRedemption.GetCategory(), updatedRedemption.GetOrderStatus(), evrPb.TxnType_TXN_TYPE_UNSPECIFIED)
	}

	return &evrPb.UpdateFiStoreRedemptionsResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) publishOrderNotificationEvent(ctx context.Context, actorId, redemptionId string, notificationMedium notificationPb.Medium, category evrPb.Category, vendor evrPb.Vendor, orderStatus evrPb.OrderStatus, txnType evrPb.TxnType) (string, error) {
	// get notification trigger based on order status
	notificationTrigger, err := getNotificationTrigger(orderStatus, txnType)
	if err != nil {
		return "", fmt.Errorf("error while getting notification trigger: %v", err)
	}

	// get notification category based on category & vendor
	notificationCategory, err := getNotificationCategory(vendor, category)
	if err != nil {
		return "", fmt.Errorf("error while getting notification category: %v", err)
	}

	// create order notification event
	orderNotificationEvent := &notificationPb.OrderNotificationEvent{
		ActorId:              actorId,
		RedemptionId:         redemptionId,
		NotificationTrigger:  notificationTrigger,
		Medium:               notificationMedium,
		NotificationCategory: notificationCategory,
		EventTimestamp:       timestamppb.Now(),
	}

	// once we get the fi store order redemption details, push order event to notification queue.
	msgId, err := s.fiStoreOrderNotificationPublisher.Publish(ctx, orderNotificationEvent)
	if err != nil {
		return "", err
	}

	return msgId, nil
}

func getNotificationTrigger(orderStatus evrPb.OrderStatus, txnType evrPb.TxnType) (notificationPb.NotificationTrigger, error) {
	switch orderStatus {
	case evrPb.OrderStatus_ORDER_STATUS_CONFIRMED:
		return notificationPb.NotificationTrigger_NOTIFICATION_TRIGGER_ORDER_CONFIRMATION, nil
	case evrPb.OrderStatus_ORDER_STATUS_SHIPPED:
		return notificationPb.NotificationTrigger_NOTIFICATION_TRIGGER_ORDER_STATUS_UPDATE, nil
	case evrPb.OrderStatus_ORDER_STATUS_CANCELLED:
		return notificationPb.NotificationTrigger_NOTIFICATION_TRIGGER_ORDER_CANCELLATION, nil
	default:
		switch {
		case txnType == evrPb.TxnType_DEBIT:
			return notificationPb.NotificationTrigger_NOTIFICATION_TRIGGER_ORDER_PLACED, nil
		case txnType == evrPb.TxnType_CREDIT:
			return notificationPb.NotificationTrigger_NOTIFICATION_TRIGGER_ORDER_REFUND, nil
		default:
			return notificationPb.NotificationTrigger_NOTIFICATION_TRIGGER_UNSPECIFIED, fmt.Errorf("unsupported or invalid order status and txn type")
		}
	}
}

func getNotificationCategory(vendor evrPb.Vendor, category evrPb.Category) (notificationPb.NotificationCategory, error) {
	switch {
	case vendor == evrPb.Vendor_DPANDA && category == evrPb.Category_CATEGORY_ECOM:
		return notificationPb.NotificationCategory_NOTIFICATION_CATEGORY_ECOM_STORE, nil
	case vendor == evrPb.Vendor_RAZORPAY && category == evrPb.Category_CATEGORY_ECOM:
		return notificationPb.NotificationCategory_NOTIFICATION_CATEGORY_ECOM_DISCOUNTS, nil
	case vendor == evrPb.Vendor_POSHVINE && category == evrPb.Category_CATEGORY_GIFT_CARDS:
		return notificationPb.NotificationCategory_NOTIFICATION_CATEGORY_GIFT_CARDS, nil
	case vendor == evrPb.Vendor_POSHVINE && category == evrPb.Category_CATEGORY_MILES_EXCHANGE:
		return notificationPb.NotificationCategory_NOTIFICATION_CATEGORY_MILES_EXCHANGE, nil
	default:
		return notificationPb.NotificationCategory_NOTIFICATION_CATEGORY_UNSPECIFIED, fmt.Errorf("unsupported or invalid category")
	}
}

// GetFiStoreRedemptions fetches the fi store redemptions for given request from DB.
func (s *Service) GetFiStoreRedemptions(ctx context.Context, req *evrPb.GetFiStoreRedemptionsRequest) (*evrPb.GetFiStoreRedemptionsResponse, error) {
	if req.GetActorId() == "" {
		return &evrPb.GetFiStoreRedemptionsResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("actor id should not be empty"),
		}, nil
	}
	pageToken, getPageTokenErr := pagination.GetPageToken(req.GetPageCtxRequest())
	if getPageTokenErr != nil {
		logger.Error(ctx, "error while getting page token from pageCtxReq", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(getPageTokenErr))
		return &evrPb.GetFiStoreRedemptionsResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("error while getting page token from pageCtxReq"),
		}, nil
	}
	pageSize := 30 // default page size
	if req.GetPageCtxRequest() != nil {
		pageSize = int(req.GetPageCtxRequest().GetPageSize())
	}
	redemptions, pageCtxResp, err := s.fiStoreRedemptionDao.GetByFilters(ctx, &model.FiStoreRedemptionsFilters{
		ActorId:      req.GetActorId(),
		VendorRefId:  req.GetFilters().GetVendorRefId(),
		Categories:   req.GetFilters().GetCategories(),
		FromTime:     req.GetFilters().GetTimeWindow().GetFromTime(),
		TillTime:     req.GetFilters().GetTimeWindow().GetTillTime(),
		RedemptionId: req.GetFilters().GetRedemptionId(),
		OrderStatus:  req.GetFilters().GetOrderStatuses(),
	}, s.fiStoreFmAllFields, pageToken, uint32(pageSize))
	if err != nil {
		logger.Error(ctx, "error while fetching fi store redemptions from GetByFilters", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return &evrPb.GetFiStoreRedemptionsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while fetching fi store redemptions from GetByFilters"),
		}, nil
	}

	return &evrPb.GetFiStoreRedemptionsResponse{
		Status:          rpc.StatusOk(),
		Redemptions:     redemptions,
		PageCtxResponse: pageCtxResp,
	}, nil
}

// GetExternalVendorRedemptions fetches the external vendor redemptions for given request from DB.
func (s *Service) GetExternalVendorRedemptions(ctx context.Context, req *evrPb.GetExternalVendorRedemptionsRequest) (*evrPb.GetExternalVendorRedemptionsResponse, error) {
	if req.GetActorId() == "" {
		return &evrPb.GetExternalVendorRedemptionsResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("actor id should not be empty"),
		}, nil
	}
	pageToken, getPageTokenErr := pagination.GetPageToken(req.GetPageCtxRequest())
	if getPageTokenErr != nil {
		logger.Error(ctx, "error while getting page token from pageCtxReq", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(getPageTokenErr))
		return &evrPb.GetExternalVendorRedemptionsResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("error while getting page token from pageCtxReq"),
		}, nil
	}
	pageSize := 30 // default page size
	if req.GetPageCtxRequest() != nil {
		pageSize = int(req.GetPageCtxRequest().GetPageSize())
	}
	redemptions, pageCtxResp, err := s.externalVendorRedemptionDao.GetExternalVendorRedemptions(ctx, &model.Filters{
		ActorId:       req.GetActorId(),
		VendorOrderId: req.GetFilters().GetVendorOrderId(),
		VendorRefId:   req.GetFilters().GetVendorRefId(),
		TxnType:       req.GetFilters().GetTxnType(),
		Vendor:        req.GetFilters().GetVendor(),
		RedemptionId:  req.GetFilters().GetRedemptionId(),
		Category:      req.GetFilters().GetCategory(),
	}, s.fmAllFields, pageToken, uint32(pageSize))
	if err != nil {
		logger.Error(ctx, "error while fetching external vendor redemptions", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return &evrPb.GetExternalVendorRedemptionsResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	return &evrPb.GetExternalVendorRedemptionsResponse{
		Status:                    rpc.StatusOk(),
		ExternalVendorRedemptions: redemptions,
		PageCtxResponse:           pageCtxResp,
	}, nil
}

func (s *Service) checkIfRedundantUpdateRequest(updateReq *evrPb.UpdateFiStoreRedemptionsRequest, redemption *evrPb.FiStoreRedemption) bool {
	if updateReq.GetOrderStatus() == redemption.GetOrderStatus() && updateReq.GetOrderTrackingLink() == redemption.GetRedemptionMetaData().GetOrderTrackingLink() {
		return true
	}
	return false
}

// publishEligibleRedemptionRudderEvent publishes the rudder event for confirmed redemption.
// NOTE : This event is used for tracking the confirmed redeemed offer, we are using the same for fi store redemptions too.
// The following are the attribute mapping used in the event:
// 1. OfferRedemptionId : fsr id
// 2. OfferId           : empty
// 3. OfferName         : fsr brand name
// 4. OfferPropertyType : OfferType_EXTERNAL_VENDOR (as fsr offers on catalog as external vendor)
// 5. Status            : only pushed on terminal states : CONFIRMED and FAILED for POSHVINE and CONFIRMED and CONFIRMED for DPANDA
// 6. Vendor            : fsr vendor
func (s *Service) publishEligibleRedemptionRudderEvent(ctx context.Context, fiStoreRedemption *evrPb.FiStoreRedemption) {
	if isFiStoreRedemptionValidConfirmOfferRedemptionEvent(fiStoreRedemption) {
		goroutine.RunWithCtx(epificontext.WithEventAttributesV2(ctx), func(gctx context.Context) {
			offerName := fiStoreRedemption.GetRedemptionMetaData().GetBrandName()
			if offerName == "" {
				offerName = fiStoreRedemption.GetRedemptionMetaData().GetProductName()
			}
			s.eventBroker.AddToBatch(gctx, casperEvents.NewConfirmRedemptionServerEvent(fiStoreRedemption.GetActorId(), fiStoreRedemption.GetId(), "", offerName, casperPb.OfferType_EXTERNAL_VENDOR.String(), fiStoreRedemption.GetOrderStatus().String(), fiStoreRedemption.GetVendor().String()))
		})
	}
}

// isFiStoreRedemptionValidConfirmOfferRedemptionEvent checks if the fsr is a valid event for confirm redemption event.
func isFiStoreRedemptionValidConfirmOfferRedemptionEvent(fiStoreRedemption *evrPb.FiStoreRedemption) bool {
	orderStatus := fiStoreRedemption.GetOrderStatus()
	switch fiStoreRedemption.GetVendor() {
	case evrPb.Vendor_POSHVINE:
		if orderStatus == evrPb.OrderStatus_ORDER_STATUS_CONFIRMED || orderStatus == evrPb.OrderStatus_ORDER_STATUS_FAILED {
			return true
		}
	case evrPb.Vendor_DPANDA:
		if orderStatus == evrPb.OrderStatus_ORDER_STATUS_CONFIRMED {
			return true
		}
	default:
		return false
	}
	return false
}

func (s *Service) recordFiStoreRedemptionMetrics(vendor, category, status string) {
	metrics.MetricsRecorder.RecordFiStoreRedemptionStatusUpdate(vendor, category, status)
}
