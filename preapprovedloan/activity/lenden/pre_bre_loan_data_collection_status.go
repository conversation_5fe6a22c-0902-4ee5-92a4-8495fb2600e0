//nolint:dupl
package lenden

import (
	"context"

	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/pkg/errors"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
)

// todo(naveed) move to sync proxy
func (p *Processor) LendenPreBreLoanDataCollectionStatus(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		ctxWithOwnership := epificontext.WithOwnership(ctx, common.Ownership_LOANS_LENDEN)
		dataReceived := false

		// get loec for the old flow
		loecs, err := p.loecDao.GetActiveLoecsByActorIdLoanProgramsAndStatuses(ctxWithOwnership, lse.GetActorId(), []palPb.LoanProgram{}, []palPb.LoanOfferEligibilityCriteriaStatus{
			palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED,
		}, 0, false)
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, errors.Wrap(epifierrors.ErrTransient, "failed to get active loecs")
		}
		for _, loec := range loecs {
			for _, dataReq := range loec.GetDataRequirementDetails().GetDataRequirements() {
				if dataReq.GetDataRequirementType() == palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_LENDEN_PRE_BRE_LOAN_DATA_COLLECTION {
					if dataReq.GetIsCollected() {
						dataReceived = true
					}
				}
			}
		}

		// get lr for the new flow after application movement
		lr, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if lrErr != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, errors.Wrap(epifierrors.ErrTransient, "failed to get loan request")
		}
		preBreLoanData := lr.GetDetails().GetLoanApplicationDetails().GetLdcLoanApplicationDetails().GetPreBreDataLoanPreferences()
		if preBreLoanData.GetLoanAmount() > 0 && preBreLoanData.GetInterest() > 0 {
			dataReceived = true
		}

		if !dataReceived {
			return nil, errors.Wrap(epifierrors.ErrTransient, "pre bre loan data not collected")
		}
		return res, nil
	})
	return actRes, actErr
}
