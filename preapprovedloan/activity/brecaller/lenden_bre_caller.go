package brecaller

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	commonTypesPb "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	caPb "github.com/epifi/gamma/api/connected_account"
	caEnumsPb "github.com/epifi/gamma/api/connected_account/enums"
	caExtPb "github.com/epifi/gamma/api/connected_account/external"
	palpb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/salaryestimation"
	"github.com/epifi/gamma/api/savings"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	igVendorPb "github.com/epifi/gamma/api/vendors/aa/analytics/ignosis"
	"github.com/epifi/gamma/pkg/feature/release"
	lendenPkg "github.com/epifi/gamma/pkg/loans/lenden"
	"github.com/epifi/gamma/preapprovedloan/activity/datacompleteness/helper"
	palWorkerGConf "github.com/epifi/gamma/preapprovedloan/config/worker/genconf"
	"github.com/epifi/gamma/preapprovedloan/dao"
	palEvents "github.com/epifi/gamma/preapprovedloan/events"
	palHelper "github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/userdata"
)

const (
	// Minimum loan amount that user can choose – 50,000. However, loans below 1 Lakh shall be given a max tenure of 12 months.
	// Keeping the minimum as 1 lakh until tenure capping based on amount is implemented
	ldcOfferValidityDays      = 30
	ldcOfferMinLoanAmount     = 5_000
	ldcOfferMaxLoanAmount     = 50_000
	ldcOfferMinTenureInMonths = 1
	ldcOfferMaxTenureInMonths = 12
	ldcStartingInterestRate   = 13
)

var (
	maxAllowedVendorResponseSize = grpc.MaxCallRecvMsgSize(128 * 1024 * 1024)
)

type LendenBreCaller struct {
	lendenVgClient         lenden.LendenClient
	rpcHelper              *palHelper.RpcHelper
	loanApplicantDao       dao.LoanApplicantDao
	loanOffersDao          dao.LoanOffersDao
	txnExecutorProvider    *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor]
	userDataProvider       userdata.IUserDataProvider
	loecDao                dao.LoanOfferEligibilityCriteriaDao
	salaryEstimationClient salaryestimation.SalaryEstimationClient
	caClient               caPb.ConnectedAccountClient
	userClient             user.UsersClient
	eventBroker            events.Broker
	onbClient              onbPb.OnboardingClient
	savingsClient          savings.SavingsClient
	loanRequestDao         dao.LoanRequestsDao
	dynConf                *palWorkerGConf.Config
	releaseEvaluator       *release.Evaluator
}

func NewLendenBreCaller(
	lendenVgClient lenden.LendenClient,
	rpcHelper *palHelper.RpcHelper,
	loanApplicantDao dao.LoanApplicantDao,
	loanOffersDao dao.LoanOffersDao,
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	userDataProvider userdata.IUserDataProvider,
	loecDao dao.LoanOfferEligibilityCriteriaDao,
	salaryEstimationClient salaryestimation.SalaryEstimationClient,
	caClient caPb.ConnectedAccountClient,
	userClient user.UsersClient,
	eventBroker events.Broker,
	onbClient onbPb.OnboardingClient,
	savingsClient savings.SavingsClient,
	loanRequestDao dao.LoanRequestsDao,
	dynConf *palWorkerGConf.Config,
	releaseEvaluator *release.Evaluator,
) *LendenBreCaller {
	return &LendenBreCaller{
		lendenVgClient:         lendenVgClient,
		rpcHelper:              rpcHelper,
		loanApplicantDao:       loanApplicantDao,
		loanOffersDao:          loanOffersDao,
		txnExecutorProvider:    txnExecutorProvider,
		userDataProvider:       userDataProvider,
		loecDao:                loecDao,
		salaryEstimationClient: salaryEstimationClient,
		caClient:               caClient,
		userClient:             userClient,
		eventBroker:            eventBroker,
		onbClient:              onbClient,
		savingsClient:          savingsClient,
		loanRequestDao:         loanRequestDao,
		dynConf:                dynConf,
		releaseEvaluator:       releaseEvaluator,
	}
}

// nolint:funlen
func (s *LendenBreCaller) CheckAndGetLoanOffer(ctx context.Context, req *CheckAndGetLoanOfferRequest) (*CheckAndGetLoanOfferResponse, error) {
	actorId := req.Loec.GetActorId()
	isLdcApplicationMovementEnabled, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_FEATURE_LDC_APPLICATION_MOVEMENT).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error in evaluating feature for ldc application movement change", zap.Error(err), zap.String(logger.FEATURE, typesPb.Feature_FEATURE_LDC_APPLICATION_MOVEMENT.String()))
		return nil, errors.Wrap(err, "error in evaluating feature for ldc application movement change")
	}
	if helper.IsDataCollected(req.Loec, palpb.DataRequirementType_DATA_REQUIREMENT_TYPE_LENDEN_PRE_BRE_LOAN_DATA_COLLECTION) && !isLdcApplicationMovementEnabled {
		return s.CheckAndGetLoanOfferV0(ctx, req)
	}

	req.Loec.Status = palpb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED
	req.Loec.SubStatus = palpb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI
	return &CheckAndGetLoanOfferResponse{
		LoanOffer: &palpb.LoanOffer{
			ActorId:       req.Loec.GetActorId(),
			VendorOfferId: uuid.NewString(),
			Vendor:        palpb.Vendor_LENDEN,
			OfferConstraints: &palpb.OfferConstraints{
				MaxLoanAmount:   moneyPkg.AmountINR(ldcOfferMaxLoanAmount).GetPb(),
				MinLoanAmount:   moneyPkg.AmountINR(ldcOfferMinLoanAmount).GetPb(),
				MinTenureMonths: ldcOfferMinTenureInMonths,
				MaxTenureMonths: ldcOfferMaxTenureInMonths,
			},
			ProcessingInfo: &palpb.OfferProcessingInfo{
				InterestRate: []*palpb.RangeData{
					{
						Start: ldcOfferMinLoanAmount,
						End:   ldcOfferMaxLoanAmount,
						Value: &palpb.RangeData_Percentage{Percentage: ldcStartingInterestRate},
					},
				},
			},
			ValidSince:                     timestamppb.New(time.Now()),
			ValidTill:                      timestamppb.New(time.Now().Add(ldcOfferValidityDays * 24 * time.Hour)),
			LoanOfferEligibilityCriteriaId: req.Loec.GetId(),
			LoanProgram:                    palpb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
			LoanOfferType:                  palpb.LoanOfferType_LOAN_OFFER_TYPE_PRE_QUALIFIED,
		},
		UpdatedLoec: req.Loec,
		UpdateFieldMask: []palpb.LoanOfferEligibilityCriteriaFieldMask{
			palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS,
			palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS,
		},
	}, nil
}

func (s *LendenBreCaller) CheckAndGetLoanOfferV0(ctx context.Context, req *CheckAndGetLoanOfferRequest) (*CheckAndGetLoanOfferResponse, error) {
	lg := activity.GetLogger(ctx)
	ownership := palHelper.GetPalOwnership(req.Loec.GetVendor())
	ctxWithOwnership := epificontext.WithOwnership(ctx, ownership)

	loanApplicant, err := s.loanApplicantDao.GetByActorId(ctxWithOwnership, req.Loec.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "failed to get loan applicant")
	}
	checkHardEligibilityResp, checkHardEligibilityErr := s.lendenVgClient.CheckHardEligibility(ctx, &lenden.CheckHardEligibilityRequest{
		LoanId: req.Loec.GetVendorResponse().GetLoanId(),
		UserId: loanApplicant.GetVendorApplicantId(),
	})
	if te := epifigrpc.RPCError(checkHardEligibilityResp, checkHardEligibilityErr); te != nil {
		return nil, errors.Wrap(te, "failed to check eligibility")
	}

	if checkHardEligibilityResp.GetEligibilityStatus() == lenden.EligibilityStatus_ELIGIBILITY_STATUS_APPROVED {
		// Get what vendor_offer_ids will be created
		newVendorOfferIds := make(map[string]bool)
		for _, vendorOffer := range checkHardEligibilityResp.GetOfferData().GetOffers() {
			vendorOfferId := req.Loec.GetVendorResponse().GetLoanId() + "_" + vendorOffer.GetOfferCode()
			newVendorOfferIds[vendorOfferId] = true
		}

		// Only deactivate offers that won't be recreated
		oldOffers, oldOfferErr := s.loanOffersDao.GetActiveOffersByActorIdAndLoanPrograms(ctxWithOwnership, req.Loec.GetActorId(), []palpb.LoanProgram{req.Loec.GetLoanProgram()})
		if oldOfferErr != nil && !errors.Is(oldOfferErr, epifierrors.ErrRecordNotFound) {
			return nil, errors.Wrap(oldOfferErr, "failed to fetch old loan offer for actor and loan program")
		}
		for _, oldLoanOffer := range oldOffers {
			// Only deactivate if this vendor_offer_id won't be created again
			if !newVendorOfferIds[oldLoanOffer.GetVendorOfferId()] {
				if err = s.loanOffersDao.DeactivateLoanOffer(ctxWithOwnership, oldLoanOffer.GetId()); err != nil {
					return nil, errors.Wrap(err, "failed to deactivate already existing offer")
				}
			}
		}

		createOffersErr := s.createLoanOffers(ctxWithOwnership, checkHardEligibilityResp, req.Loec, nil)
		if createOffersErr != nil {
			return nil, errors.Wrap(createOffersErr, "failed to create loan offers from BRE response")
		}
		lg.Info("lenden approved and gave loan offers", zap.String(logger.ACTOR_ID_V2, req.Loec.GetActorId()))
		req.Loec.Status = palpb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED
		req.Loec.SubStatus = palpb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_VENDOR
		return &CheckAndGetLoanOfferResponse{
			UpdatedLoec: req.Loec,
			UpdateFieldMask: []palpb.LoanOfferEligibilityCriteriaFieldMask{palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS,
				palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS},
			LoanOffer: nil, // not sending loan offers cuz its already created here only
		}, nil
	} else {
		req.Loec.Status = palpb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED
		return &CheckAndGetLoanOfferResponse{
			UpdatedLoec:     req.Loec,
			UpdateFieldMask: []palpb.LoanOfferEligibilityCriteriaFieldMask{palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS},
		}, nil
	}
}

func (s *LendenBreCaller) createLoanOffers(ctx context.Context, offerResp *lenden.CheckHardEligibilityResponse, loec *palpb.LoanOfferEligibilityCriteria, lr *palpb.LoanRequest) error {
	var loanOffers []*palpb.LoanOffer

	txnExec, txnExecErr := palHelper.GetTxnExecutorByOwnership(ctx, s.txnExecutorProvider)
	if txnExecErr != nil {
		return errors.Wrap(txnExecErr, "failed to get txn executor by ownership")
	}

	txnErr := txnExec.RunTxn(ctx, func(txnCtx context.Context) error {
		for _, vendorOffer := range offerResp.GetOfferData().GetOffers() {
			newOffer := getLoanOfferFromBreResponse(vendorOffer, offerResp, loec, lr)
			lo, err := s.loanOffersDao.Create(ctx, newOffer)
			if err != nil {
				// Handle users who are stuck with deactivated offers causing unique constraint violations
				if errors.Is(err, epifierrors.ErrAlreadyExists) {
					activity.GetLogger(ctx).Info("offer already exists, trying to find and reactivate existing offer",
						zap.String("vendor_offer_id", newOffer.GetVendorOfferId()),
						zap.String("actor_id", newOffer.GetActorId()))

					// Try to get the existing offer and reactivate it
					existingOffer, getErr := s.loanOffersDao.GetByVendorAndVendorOfferId(ctx, newOffer.GetVendor(), newOffer.GetVendorOfferId())
					if getErr == nil && existingOffer.GetDeactivatedAt() != nil {
						// Reactivate the existing offer by updating deactivated_at to nil
						existingOffer.DeactivatedAt = nil
						updateErr := s.loanOffersDao.Update(ctx, existingOffer, []palpb.LoanOfferFieldMask{palpb.LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_DEACTIVATED_AT})
						if updateErr != nil {
							activity.GetLogger(ctx).Error("failed to reactivate existing offer", zap.Error(updateErr))
							return errors.Wrap(updateErr, "error in creating offer and failed to reactivate existing")
						}
						loanOffers = append(loanOffers, existingOffer)
						activity.GetLogger(ctx).Info("successfully reactivated existing offer",
							zap.String("offer_id", existingOffer.GetId()),
							zap.String("vendor_offer_id", newOffer.GetVendorOfferId()))
						continue
					}
				}
				return errors.Wrap(err, "error in creating offer")
			}
			loanOffers = append(loanOffers, lo)
		}
		return nil
	})
	if txnErr != nil {
		return errors.Wrap(txnErr, "failed in txn block while creating offers")
	}

	return nil
}

func getLoanOfferFromBreResponse(vendorOfferResp *lenden.P2POfferDetails, offerResp *lenden.CheckHardEligibilityResponse, loec *palpb.LoanOfferEligibilityCriteria, lr *palpb.LoanRequest) *palpb.LoanOffer {
	applicableTenureRange := []*palpb.LendenLoanSlab{}
	for _, tenureRange := range offerResp.GetOfferData().GetApplicableTenures() {
		applicableTenureRange = append(applicableTenureRange, &palpb.LendenLoanSlab{
			MinAmount: tenureRange.GetMinAmount(),
			MaxAmount: tenureRange.GetMaxAmount(),
			TenureRange: &palpb.LendenLoanSlabTenureRange{
				MinTenureInMonths: tenureRange.GetMinTenure(),
				MaxTenureInMonths: tenureRange.GetMaxTenure(),
			},
		})
	}

	var (
		minTenure int32 = math.MaxInt32
		maxTenure int32 = math.MinInt32
	)
	for _, tenure := range offerResp.GetOfferData().GetApplicableTenures() {
		if tenure.GetMinTenure() < minTenure {
			minTenure = tenure.GetMinTenure()
		}
		if tenure.GetMaxTenure() > maxTenure {
			maxTenure = tenure.GetMaxTenure()
		}
	}

	var aaAnalyticsBankDetails *palpb.AaAnalysisBankDetails
	isViaAaFlow := false
	for _, data := range loec.GetDataRequirementDetails().GetDataRequirements() {
		if (data.GetDataRequirementType() == palpb.DataRequirementType_DATA_REQUIREMENT_TYPE_ESTIMATE_INCOME) && (data.GetIsCollected()) {
			isViaAaFlow = true
			aaAnalyticsBankDetails = &palpb.AaAnalysisBankDetails{
				AccountHolderName: data.GetAaAnalysisBankDetails().GetAccountHolderName(),
				AccountNumber:     data.GetAaAnalysisBankDetails().GetAccountNumber(),
				Ifsc:              data.GetAaAnalysisBankDetails().GetIfsc(),
				Type:              data.GetAaAnalysisBankDetails().GetType(),
				BankName:          data.GetAaAnalysisBankDetails().GetBankName(),
			}
		}
	}
	ldcAaData := lr.GetDetails().GetLoanApplicationDetails().GetLdcLoanApplicationDetails().GetAaData()
	if ldcAaData.GetIsAaDataNeeded() {
		isViaAaFlow = true
		aaAnalyticsBankDetails = &palpb.AaAnalysisBankDetails{
			AccountHolderName: ldcAaData.GetAccountHolderName(),
			AccountNumber:     ldcAaData.GetAccountNumber(),
			Ifsc:              ldcAaData.GetIfsc(),
			Type:              ldcAaData.GetType(),
			BankName:          ldcAaData.GetBankName(),
		}
	}
	if !isViaAaFlow {
		aaAnalyticsBankDetails = nil
	}

	return &palpb.LoanOffer{
		ActorId:       loec.GetActorId(),
		Vendor:        palpb.Vendor_LENDEN,
		VendorOfferId: loec.GetVendorResponse().GetLoanId() + "_" + vendorOfferResp.GetOfferCode(),
		OfferConstraints: &palpb.OfferConstraints{
			MinLoanAmount: offerResp.GetOfferData().GetMinAmount(),
			MaxLoanAmount: offerResp.GetOfferData().GetMaxAmount(),
			// Note: Lenden does not provide max EMI amount, this value should not be used for Lenden.
			MaxEmiAmount:    moneyPkg.ZeroINR().GetPb(),
			MinTenureMonths: minTenure,
			MaxTenureMonths: maxTenure,
			AdditionalConstraints: &palpb.OfferConstraints_LendenConstraintInfo{
				LendenConstraintInfo: &palpb.LendenConstraintInfo{
					AllowedRoiValues: vendorOfferResp.GetModifyRoiList(),
					LoanSlabs:        applicableTenureRange,
					BankDetails:      aaAnalyticsBankDetails,
				},
			},
			MinLoanAmountStepSize: moneyPkg.ParseInt(offerResp.GetOfferData().GetOfferSelectionMultiple(), moneyPkg.RupeeCurrencyCode),
		},
		ProcessingInfo: &palpb.OfferProcessingInfo{
			ApplicationId: loec.GetVendorResponse().GetLoanId(),
			ProcessingFee: []*palpb.RangeData{
				{
					Value: &palpb.RangeData_Percentage{Percentage: float64(lendenPkg.ProcessingFeePercentageValue)},
					Start: offerResp.GetOfferData().GetMinAmount().GetUnits(),
					End:   offerResp.GetOfferData().GetMaxAmount().GetUnits(),
				},
			},
			InterestRate: []*palpb.RangeData{
				{
					Value: &palpb.RangeData_Percentage{Percentage: float64(vendorOfferResp.GetRoi())},
					Start: offerResp.GetOfferData().GetMinAmount().GetUnits(),
					End:   offerResp.GetOfferData().GetMaxAmount().GetUnits(),
				},
			},
			Gst: lendenPkg.GSTPercentageValue,
		},
		ValidSince:    timestamppb.Now(),
		ValidTill:     timestamppb.New(time.Now().AddDate(0, lendenPkg.OfferValidityMonths, 0)),
		LoanProgram:   palpb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
		LoanOfferType: palpb.LoanOfferType_LOAN_OFFER_TYPE_HARD,
		OfferDisplayInfo: &palpb.OfferDisplayInfo{
			FundingProbability:       getFundingProbabilityEnum(vendorOfferResp.GetFundingProbability()),
			ExpectedTimeToGetFunding: vendorOfferResp.GetExpectedTimeToGetFunding(),
			IsRecommended:            vendorOfferResp.GetIsRecommended(),
		},
		LoanOfferEligibilityCriteriaId: loec.GetId(),
	}
}

func getFundingProbabilityEnum(fundingProbability string) palpb.Probability {
	switch fundingProbability {
	case "LOW":
		return palpb.Probability_PROBABILITY_LOW
	case "MEDIUM":
		return palpb.Probability_PROBABILITY_MEDIUM
	case "HIGH":
		return palpb.Probability_PROBABILITY_HIGH
	default:
		return palpb.Probability_PROBABILITY_UNSPECIFIED
	}
}

func (s *LendenBreCaller) FetchHardVendorLoanOffer(ctx context.Context, req *CheckAndGetLoanOfferRequest) (*CheckAndGetLoanOfferResponse, error) {
	la, err := s.loanApplicantDao.GetByActorId(ctx, req.Loec.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "failed to get loan applicant")
	}

	lr, lrErr := s.loanRequestDao.GetById(ctx, req.Lse.GetRefId())
	if lrErr != nil {
		return nil, errors.Wrap(lrErr, "failed to get loan request by id")
	}

	loec, loecFieldMask, err := s.initiateLoanApplicationAtLenden(ctx, req.Loec, la, lr)
	if err != nil {
		return nil, errors.Wrapf(epifierrors.ErrTransient, "error in initiating loan application with lenden, err : %v", err)
	}
	if loecFieldMask != nil {
		return &CheckAndGetLoanOfferResponse{
			UpdatedLoec:     loec,
			UpdateFieldMask: loecFieldMask,
		}, nil
	}

	loec, fieldMask, err := s.callLendenBre(ctx, loec, la, lr)
	if err != nil {
		return nil, errors.Wrap(err, "error in calling lenden bre")
	}
	return &CheckAndGetLoanOfferResponse{
		UpdatedLoec:     loec,
		UpdateFieldMask: fieldMask,
	}, nil
}

func (s *LendenBreCaller) initiateLoanApplicationAtLenden(ctx context.Context, loec *palpb.LoanOfferEligibilityCriteria, la *palpb.LoanApplicant, lr *palpb.LoanRequest) (*palpb.LoanOfferEligibilityCriteria, []palpb.LoanOfferEligibilityCriteriaFieldMask, error) {
	preLoanData := lr.GetDetails().GetLoanApplicationDetails().GetLdcLoanApplicationDetails().GetPreBreDataLoanPreferences()
	loanAmountSelected := preLoanData.GetLoanAmount()
	interestRateSelected := preLoanData.GetInterest()

	if loanAmountSelected <= 0 {
		return nil, nil, errors.Errorf("unexpected loan amount selected: %d", loanAmountSelected)
	}
	if interestRateSelected <= 0 {
		return nil, nil, errors.Errorf("unexpected interest rate selected: %d", interestRateSelected)
	}
	monthlyInterestRatePercentageValue := math.Round(float64(interestRateSelected)/12*100) / 100
	if math.IsInf(monthlyInterestRatePercentageValue, 0) {
		return nil, nil, errors.New("result is infinite (possible overflow)")
	}
	if monthlyInterestRatePercentageValue < 0 {
		return nil, nil, errors.Errorf("unexpected negative result after rounding: %f", monthlyInterestRatePercentageValue)
	}
	userDetails, err := s.userDataProvider.GetDefaultUserData(ctx, &userdata.GetDefaultUserDataRequest{ActorId: loec.GetActorId()})
	if err != nil {
		return nil, nil, errors.Wrap(err, fmt.Sprintf("failed to fetch user data by actor ID, err: %v", err))
	}

	// creating user at lenden end
	if err := s.createLendenUserIfNotCreated(ctx, loec, userDetails, la); err != nil {
		return nil, nil, errors.Wrap(err, "failed to call lenden bre and create user")
	}

	// dedupe check
	if loec.GetVendorResponse().GetLoanId() == "" {
		applyForLoanResp, applyForLoanErr := s.lendenVgClient.ApplyForLoan(ctx, &lenden.ApplyForLoanRequest{
			Header: &vendorgateway.RequestHeader{Vendor: vendorgateway.Vendor_LENDEN},
			// TODO(Brijesh): Store reference first in table and then use it
			ReferenceId: uuid.NewString(),
			RequestedAmount: &money.Money{
				Units:        loanAmountSelected,
				CurrencyCode: typesPb.CurrencyCode_INR.String(),
			},
			Interest: &lenden.ApplyForLoanRequest_Interest{
				Type:      lenden.InterestType_INTEREST_TYPE_FLAT,
				Frequency: lenden.InterestFrequency_INTEREST_FREQUENCY_MONTHLY,
				Value:     float32(monthlyInterestRatePercentageValue),
			},
			UserId:      la.GetVendorApplicantId(),
			AddressType: lenden.AddressType_LOAN_COMMUNICATION,
			Address: &commonTypesPb.PostalAddress{
				PostalCode:         userDetails.GetAddress().GetPostalCode(),
				AdministrativeArea: userDetails.GetAddress().GetAdministrativeArea(),
				AddressLines:       userDetails.GetAddress().GetAddressLines(),
			},
		})
		if te := epifigrpc.RPCError(applyForLoanResp, applyForLoanErr); te != nil && !errors.Is(te, epifierrors.ErrAlreadyExists) {
			if applyForLoanResp.GetStatus().GetCode() == uint32(lenden.ApplyForLoanResponse_MAX_LOAN_ACCOUNT_REACHED_AT_VENDOR) {
				loec.SubStatus = palpb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_MAX_ACTIVE_LOANS_WITH_LENDER_REACHED
				loec.Status = palpb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED
				return loec, []palpb.LoanOfferEligibilityCriteriaFieldMask{
					palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS,
					palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS,
				}, nil
			}
			return nil, nil, errors.Wrap(te, fmt.Sprintf("failed to apply for loan: %v", te))
		}

		loec.VendorResponse = &palpb.VendorResponse{
			LoanId: applyForLoanResp.GetLoanId(),
		}
		if err := s.loecDao.Update(ctx, loec, []palpb.LoanOfferEligibilityCriteriaFieldMask{
			palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_VENDOR_RESPONSE,
		}); err != nil {
			return nil, nil, errors.Wrap(err, "failed to update loec")
		}
	}
	return loec, nil, nil
}

func (s *LendenBreCaller) createLendenUserIfNotCreated(ctx context.Context, loec *palpb.LoanOfferEligibilityCriteria, userDetails *userdata.GetDefaultUserDataResponse, la *palpb.LoanApplicant) error {
	// dedupe check
	if la.GetVendorApplicantId() != "" {
		return nil
	}

	userIpAddress, ipAddressErr := s.rpcHelper.FetchIpAddress(ctx, loec.GetActorId())
	if ipAddressErr != nil {
		return errors.Wrap(ipAddressErr, fmt.Sprintf("unable to fetch the ip address, err: %v", ipAddressErr))
	}

	userDP, udpErr := s.userClient.GetUserDeviceProperties(ctx, &user.GetUserDevicePropertiesRequest{
		ActorId: loec.GetActorId(),
		PropertyTypes: []typesPb.DeviceProperty{
			typesPb.DeviceProperty_DEVICE_PROP_DEVICE_ID,
		},
	})
	if te := epifigrpc.RPCError(userDP, udpErr); te != nil {
		logger.Error(ctx, "error in fetching device details for the user", zap.Error(te))
		return errors.Wrapf(te, "error getting device id,err: %v", te)
	}
	deviceId := userDP.GetPropValue(typesPb.DeviceProperty_DEVICE_PROP_DEVICE_ID).GetDeviceId()

	if la.GetVendorRequestId() == "" {
		la.VendorRequestId = uuid.NewString()
		err := s.loanApplicantDao.Update(ctx, la, []palpb.LoanApplicantFieldMask{palpb.LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_VENDOR_REQUEST_ID})
		if err != nil {
			return errors.Wrap(err, "error updating vendor request id")
		}
	}
	createUserResp, err := s.lendenVgClient.CreateUser(ctx, &lenden.CreateUserRequest{
		Header:                     &vendorgateway.RequestHeader{Vendor: vendorgateway.Vendor_LENDEN},
		Name:                       userDetails.GetBestName(),
		Pan:                        userDetails.GetPan(),
		PhoneNumber:                userDetails.GetMobileNumber(),
		Email:                      userDetails.GetEmail(),
		Dob:                        userDetails.GetGivenDateOfBirth(),
		ReferenceId:                la.GetVendorRequestId(),
		EmploymentOrganizationName: userDetails.GetEmploymentDetails().GetOrganizationName(),
		// TODO(Brijesh): Validate that consents are collected
		ConsentTypeList: []lenden.ConsentType{
			lenden.ConsentType_CONSENT_TYPE_PERMISSIONS,
			lenden.ConsentType_CONSENT_TYPE_POLITICALLY_EXPOSED,
			lenden.ConsentType_CONSENT_TYPE_LOAN_CONSENT,
			lenden.ConsentType_CONSENT_TYPE_USER_ACCEPTANCE_BUREAU,
		},
		UserIp:   userIpAddress,
		DeviceId: deviceId,
		// TODO(Brijesh): Use the creation time of the consent that is latest among the list of consents
		ConsentTime:    timestamppb.Now(),
		EmploymentType: getEmploymentTypeOfUser(userDetails.GetEmploymentDetails().GetEmploymentType()),
	})
	if err = epifigrpc.RPCError(createUserResp, err); err != nil && !createUserResp.GetStatus().IsAlreadyExists() {
		return errors.Wrap(err, "error creating user")
	}
	if createUserResp.GetUserId() == "" {
		return errors.New("user id not found in create user response")
	}
	la.VendorApplicantId = createUserResp.GetUserId()
	updateLaErr := s.loanApplicantDao.Update(ctx, la, []palpb.LoanApplicantFieldMask{palpb.LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_VENDOR_APPLICANT_ID})
	if updateLaErr != nil {
		return errors.Wrap(updateLaErr, "failed to update loan account")
	}
	return nil
}

func (s *LendenBreCaller) callLendenBre(ctx context.Context, loec *palpb.LoanOfferEligibilityCriteria, loanApplicant *palpb.LoanApplicant, lr *palpb.LoanRequest) (*palpb.LoanOfferEligibilityCriteria, []palpb.LoanOfferEligibilityCriteriaFieldMask, error) {
	lg := activity.GetLogger(ctx)

	aaData := lr.GetDetails().GetLoanApplicationDetails().GetLdcLoanApplicationDetails().GetAaData()
	if aaData.GetIsAaDataNeeded() {
		lg.Info("data is needed for estimating income, parsing analysis response")
		parsedAnalysis, err := s.parseAccountAnalysis(ctx, loec.GetActorId())
		if err != nil {
			return nil, nil, errors.Wrap(err, "error parsing account analysis")
		}
		if parsedAnalysis.aaAccountDetails == nil {
			return nil, nil, errors.New("account details not found for analysis")
		}
		parsedAnalysisData := parsedAnalysis.aaAccountDetails.loecDataRequirementBankDetails
		aaData.AccountHolderName = parsedAnalysisData.AccountHolderName
		aaData.AccountNumber = parsedAnalysisData.AccountNumber
		aaData.BankName = parsedAnalysisData.BankName
		aaData.Type = parsedAnalysisData.Type
		aaData.Ifsc = parsedAnalysisData.Ifsc

		postExternalDataRes, err := s.lendenVgClient.PostExternalData(ctx, &lenden.PostExternalDataRequest{
			LoanId:      loec.GetVendorResponse().GetLoanId(),
			UserId:      loanApplicant.GetVendorApplicantId(),
			Data:        parsedAnalysis.ldcVgAnalyticsDataJson,
			BankDetails: parsedAnalysis.aaAccountDetails.ldcVgBankDetails,
			Type:        lenden.EligibilityDataType_ELIGIBILITY_DATA_TYPE_BANK_STATEMENT,
		}, maxAllowedVendorResponseSize)
		if err = epifigrpc.RPCError(postExternalDataRes, err); err != nil {
			return nil, nil, errors.Wrap(err, "error posting external data")
		}
	}

	checkHardEligibilityResp, checkHardEligibilityErr := s.lendenVgClient.CheckHardEligibility(ctx, &lenden.CheckHardEligibilityRequest{
		LoanId: loec.GetVendorResponse().GetLoanId(),
		UserId: loanApplicant.GetVendorApplicantId(),
	})
	if te := epifigrpc.RPCError(checkHardEligibilityResp, checkHardEligibilityErr); te != nil {
		return nil, nil, errors.Wrap(te, "failed to check eligibility")
	}
	// Send an event with a field called LDC eligibility status that can have 3 values: approved, rejected, need more data
	var eligibilityStatus string
	switch checkHardEligibilityResp.GetEligibilityStatus() {
	case lenden.EligibilityStatus_ELIGIBILITY_STATUS_NEED_MORE_DETAILS_FOR_EVALUATION:
		if lr.GetDetails().GetLoanApplicationDetails() == nil {
			lr.GetDetails().GetLoanApplicationDetails().Details = &palpb.LoanApplicationDetails_LdcLoanApplicationDetails{
				LdcLoanApplicationDetails: &palpb.LdcLoanApplicationDetails{},
			}
		}
		if lr.GetDetails().GetLoanApplicationDetails().GetLdcLoanApplicationDetails().GetAaData() == nil {
			lr.GetDetails().GetLoanApplicationDetails().GetLdcLoanApplicationDetails().AaData = &palpb.LdcLoanApplicationDetails_AaAnalysisBankDetails{
				IsAaDataNeeded: true,
			}
		}
		updateErr := s.loanRequestDao.Update(ctx, lr, []palpb.LoanRequestFieldMask{palpb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_DETAILS})
		if updateErr != nil {
			return nil, nil, errors.Wrap(updateErr, "error in updating loan request")
		}
		// Publish LDC eligibility status event
		eligibilityStatus = "need_more_data"
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), palEvents.NewVendorEligibilityStatus(
				loec.GetActorId(),
				loec.GetVendor().String(),
				loec.GetLoanProgram().String(),
				eligibilityStatus,
			))
		})
		return loec, []palpb.LoanOfferEligibilityCriteriaFieldMask{palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_DATA_REQUIREMENT_DETAILS}, nil
	case lenden.EligibilityStatus_ELIGIBILITY_STATUS_APPROVED:
		// Get what vendor_offer_ids will be created
		newVendorOfferIds := make(map[string]bool)
		for _, vendorOffer := range checkHardEligibilityResp.GetOfferData().GetOffers() {
			vendorOfferId := loec.GetVendorResponse().GetLoanId() + "_" + vendorOffer.GetOfferCode()
			newVendorOfferIds[vendorOfferId] = true
		}

		// Only deactivate offers that won't be recreated
		oldOffers, oldOfferErr := s.loanOffersDao.GetActiveOffersByActorIdAndLoanPrograms(ctx, loec.GetActorId(), []palpb.LoanProgram{loec.GetLoanProgram()})
		if oldOfferErr != nil && !errors.Is(oldOfferErr, epifierrors.ErrRecordNotFound) {
			return nil, nil, errors.Wrap(oldOfferErr, "failed to fetch old loan offer for actor and loan program")
		}
		for _, oldLoanOffer := range oldOffers {
			// Only deactivate if this vendor_offer_id won't be created again
			if !newVendorOfferIds[oldLoanOffer.GetVendorOfferId()] {
				if err := s.loanOffersDao.DeactivateLoanOffer(ctx, oldLoanOffer.GetId()); err != nil {
					return nil, nil, errors.Wrap(err, "failed to deactivate already existing offer")
				}
			}
		}

		// Create loan offers from BRE response
		createOffersErr := s.createLoanOffers(ctx, checkHardEligibilityResp, loec, lr)
		if createOffersErr != nil {
			return nil, nil, errors.Wrap(createOffersErr, "failed to create loan offers from BRE response")
		}

		loec.SubStatus = palpb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_VENDOR
		loec.Status = palpb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED
		// Publish LDC eligibility status event
		eligibilityStatus = "approved"
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), palEvents.NewVendorEligibilityStatus(
				loec.GetActorId(),
				loec.GetVendor().String(),
				loec.GetLoanProgram().String(),
				eligibilityStatus,
			))
		})
		return loec, []palpb.LoanOfferEligibilityCriteriaFieldMask{
			palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS,
			palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS,
			palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_DATA_REQUIREMENT_DETAILS,
		}, nil
	case lenden.EligibilityStatus_ELIGIBILITY_STATUS_REJECTED:
		lg.Info("lenden rejected user for loan", zap.String(logger.ACTOR_ID_V2, loec.GetActorId()), zap.String(logger.LOAN_OFFER_ID, loec.GetVendorResponse().GetLoanId()))
		loec.SubStatus = palpb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_VENDOR
		loec.Status = palpb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED
		// Publish LDC eligibility status event
		eligibilityStatus = "rejected"
		goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
			s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), palEvents.NewVendorEligibilityStatus(
				loec.GetActorId(),
				loec.GetVendor().String(),
				loec.GetLoanProgram().String(),
				eligibilityStatus,
			))
		})
		return loec, []palpb.LoanOfferEligibilityCriteriaFieldMask{
			palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS,
			palpb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS,
		}, nil
	}
	return loec, []palpb.LoanOfferEligibilityCriteriaFieldMask{}, nil
}

type parsedAnalysisDetails struct {
	// Sub-analytics node of analysis JSON from Ignosis
	ldcVgAnalyticsDataJson []byte

	// Details of the AA salary account fetched from connected account service
	aaAccountDetails *aaAccountDetails
}

func (s *LendenBreCaller) parseAccountAnalysis(ctx context.Context, actorId string) (*parsedAnalysisDetails, error) {
	salaryEstRes, err := s.salaryEstimationClient.GetSalary(ctx, &salaryestimation.GetSalaryRequest{ActorId: actorId})
	if err = epifigrpc.RPCError(salaryEstRes, err); err != nil {
		return nil, errors.Wrap(err, "error getting analysis URL")
	}
	zippedData, err := getAnalysis(salaryEstRes.GetL1AnalysisSignedUrl())
	if err != nil {
		return nil, errors.Wrap(err, "error getting zipped analysis data")
	}
	jsonData, err := palHelper.DecompressJson(zippedData)
	if err != nil {
		return nil, errors.Wrap(err, "error decompressing zipped data")
	}
	modifiedAnalysisRes := &igVendorPb.ModifiedAnalysisResponse{}
	err = protojson.Unmarshal(jsonData, modifiedAnalysisRes)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling modified analysis res to JSON")
	}
	// TODO(Brijesh): Optimise the number of marshal-unmarshal operations
	subAnalyticsRes := &structpb.Struct{}
	err = protojson.Unmarshal(modifiedAnalysisRes.GetSubAnalyticsResponse(), subAnalyticsRes)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling sub analytics response to protobuf struct")
	}
	subAnalyticsResJson, err := json.Marshal(subAnalyticsRes)
	if err != nil {
		return nil, errors.Wrap(err, "error marshalling sub analytics response to JSON")
	}
	aaAnalysedBankAccountDetails := &igVendorPb.AaAnalysedBankAccountDetailsInSubAnalysis_SubAnalyticsResponse{}
	err = protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(subAnalyticsResJson, aaAnalysedBankAccountDetails)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling sub analytics response JSON")
	}
	accountProfiles := aaAnalysedBankAccountDetails.GetAccountProfiles()
	if len(accountProfiles) == 0 {
		return nil, errors.New("no account profiles found in analysed bank account details")
	}
	linkedAccountsRes, err := s.caClient.GetLinkedAaAccounts(ctx, &caPb.GetLinkedAaAccountsRequest{ActorId: actorId})
	if err = epifigrpc.RPCError(linkedAccountsRes, err); err != nil {
		return nil, errors.Wrap(err, "error getting linked AA accounts")
	}
	var analysedAccountRefs []string
	for _, profile := range accountProfiles {
		analysedAccountRefs = append(analysedAccountRefs, profile.GetAccount().GetLinkedAccRef())
	}
	var analysedAccountDetails *caExtPb.AccountDetails
	for _, linkedAccount := range linkedAccountsRes.GetAccounts() {
		for _, analysedAccountRef := range analysedAccountRefs {
			if linkedAccount.GetAccInstrumentType() != caEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT {
				continue
			}
			if linkedAccount.GetLinkedAccountRef() == analysedAccountRef {
				analysedAccountDetails = linkedAccount
				break
			}
		}
	}
	if analysedAccountDetails == nil {
		return nil, errors.Errorf("no linked account ref matched with analysed account refs: %s", strings.Join(analysedAccountRefs, ","))
	}
	accountDetails, err := s.getAccountDetails(ctx, analysedAccountDetails)
	if err != nil {
		return nil, errors.Wrapf(err, "error getting account details for analysed account id: %s", analysedAccountDetails.GetAccountId())
	}
	type LDCAnalyticsData struct {
		Analytics json.RawMessage `json:"analytics"`
	}
	ldcVgAnalyticsDataJson, err := json.Marshal(&LDCAnalyticsData{Analytics: modifiedAnalysisRes.GetSubAnalyticsResponse()})
	if err != nil {
		return nil, errors.Wrap(err, "error marshalling LDC AA analytics response")
	}
	return &parsedAnalysisDetails{
		ldcVgAnalyticsDataJson: ldcVgAnalyticsDataJson,
		aaAccountDetails:       accountDetails,
	}, nil
}

type aaAccountDetails struct {
	loecDataRequirementBankDetails *palpb.DataRequirement_AaAnalysisBankDetails
	ldcVgBankDetails               *lenden.BankDetails
}

func (s *LendenBreCaller) getAccountDetails(ctx context.Context, account *caExtPb.AccountDetails) (*aaAccountDetails, error) {
	accDetailsRes, err := s.caClient.GetAccountDetails(ctx, &caPb.GetAccountDetailsRequest{
		AccountId: account.GetAccountId(),
		AccountDetailsMaskList: []caExtPb.AccountDetailsMask{
			caExtPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_PROFILE,
			caExtPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_SUMMARY,
		},
	})
	if err = epifigrpc.RPCError(accDetailsRes, err); err != nil {
		return nil, errors.Wrap(err, "error getting account details")
	}
	depositAccountType := accDetailsRes.GetAccountDetails().GetAccountType().GetDepositAccountType()
	var (
		accountTypeVal   string
		ldcVgAccountType lenden.AccountType
	)
	switch depositAccountType {
	case caEnumsPb.DepositAccountType_DEPOSIT_ACCOUNT_TYPE_SAVINGS:
		accountTypeVal = "SAVINGS"
		ldcVgAccountType = lenden.AccountType_ACCOUNT_TYPE_SAVINGS
	case caEnumsPb.DepositAccountType_DEPOSIT_ACCOUNT_TYPE_CURRENT:
		accountTypeVal = "CURRENT"
		ldcVgAccountType = lenden.AccountType_ACCOUNT_TYPE_CURRENT
	default:
		return nil, errors.Errorf("unexpected deposit account type: %s", depositAccountType.String())
	}
	fipMetaRes, err := s.caClient.GetFipMeta(ctx, &caPb.GetFipMetaRequest{
		Identifiers: []*caPb.FipMetaIdentifier{
			{Identifier: &caPb.FipMetaIdentifier_FipId{FipId: account.GetFipId()}},
		},
	})
	if err = epifigrpc.RPCError(fipMetaRes, err); err != nil {
		return nil, errors.Wrapf(err, "error getting metadata for fip id: %s", account.GetFipId())
	}
	var accountFipMeta *caExtPb.FipMeta
	for _, fipMeta := range fipMetaRes.GetFipMetaList() {
		if fipMeta.GetFipId() == account.GetFipId() {
			accountFipMeta = fipMeta
		}
	}
	if accountFipMeta == nil {
		return nil, errors.Errorf("no metadata found for fip id: %s", account.GetFipId())
	}
	accountHolders := accDetailsRes.GetDepositProfile().GetHolderDetailsList()
	if len(accountHolders) == 0 {
		return nil, errors.New("no deposit account holders found")
	}
	if len(accountHolders) > 1 {
		return nil, errors.Errorf("more than one account holders found: %d", len(accountHolders))
	}
	firstAccountHolder := accountHolders[0]

	// cleaning account holder name as we have received some names with phone numbers and special characters
	accountHolderName := cleanAccountHolderName(firstAccountHolder.GetName().ToSentenceCaseString())
	if accountHolderName == "" {
		return nil, errors.New("account holder name is empty after cleaning")
	}

	loecDataRequirementBankDetails := &palpb.DataRequirement_AaAnalysisBankDetails{
		AccountHolderName: accountHolderName,
		AccountNumber:     accDetailsRes.GetAccountDetails().GetMaskedAccountNumber(),
		Type:              accountTypeVal,
		Ifsc:              accDetailsRes.GetAccountDetails().GetIfscCode(),
		BankName:          accountFipMeta.Name,
	}
	ldcVgBankDetails := &lenden.BankDetails{
		HolderName:    accountHolderName,
		AccountNumber: accDetailsRes.GetAccountDetails().GetMaskedAccountNumber(),
		Ifsc:          accDetailsRes.GetAccountDetails().GetIfscCode(),
		Type:          ldcVgAccountType,
	}
	return &aaAccountDetails{
		loecDataRequirementBankDetails: loecDataRequirementBankDetails,
		ldcVgBankDetails:               ldcVgBankDetails,
	}, nil
}

// CleanAccountHolderName sanitizes and formats a raw input string
func cleanAccountHolderName(accountHolderName string) string {
	// Remove any special characters except spaces and letters
	reSpecial := regexp.MustCompile(`[^a-zA-Z\s]`)
	accountHolderName = reSpecial.ReplaceAllString(accountHolderName, "")

	// Replace multiple spaces with a single space
	reMultiSpace := regexp.MustCompile(`\s+`)
	accountHolderName = reMultiSpace.ReplaceAllString(accountHolderName, " ")

	// Trim leading and trailing spaces
	accountHolderName = strings.TrimSpace(accountHolderName)

	// Step 5: return
	return accountHolderName
}

func getAnalysis(analysisUrl string) (b []byte, err error) {
	// nolint: gosec
	res, err := http.Get(analysisUrl)
	if err != nil {
		return nil, errors.Wrap(err, "")
	}
	defer func() {
		closeErr := res.Body.Close()
		if err != nil {
			b = nil
			err = closeErr
		}
	}()
	if res.StatusCode != http.StatusOK {
		return nil, errors.Errorf("unexpected status code: %d", res.StatusCode)
	}
	body, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, errors.Wrap(err, "error reading analysis response body")
	}
	return body, nil
}

func getEmploymentTypeOfUser(employmentType typesPb.EmploymentType) lenden.LendenEmploymentType {
	switch employmentType {
	case typesPb.EmploymentType_EMPLOYMENT_TYPE_SALARIED:
		return lenden.LendenEmploymentType_LENDEN_EMPLOYMENT_TYPE_SALARIED
	case typesPb.EmploymentType_EMPLOYMENT_TYPE_SELF_EMPLOYED:
		return lenden.LendenEmploymentType_LENDEN_EMPLOYMENT_TYPE_SELF_EMPLOYED
	default:
		return lenden.LendenEmploymentType_LENDEN_EMPLOYMENT_TYPE_UNSPECIFIED
	}
}
