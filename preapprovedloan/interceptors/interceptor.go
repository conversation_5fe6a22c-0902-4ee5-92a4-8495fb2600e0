package interceptors

import (
	"context"
	"strings"

	"google.golang.org/grpc"
	"google.golang.org/protobuf/reflect/protoreflect"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"

	palBePb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/helper"
)

const actorIdKey = "actor_id"

type ReqWithActorId interface {
	GetActorId() string
}

type ReqWithLoanHeader interface {
	GetLoanHeader() *palBePb.LoanHeader
	ProtoReflect() protoreflect.Message
}

// NewUnaryCtxFromLoanHeader - In case loan header in the request received is empty, this interceptor figures out appropriate loan header for the user
// assuming that there is only a single active loan header based on some priority order defined in GetLandingInfoV2.
// With the recent changes that allow user to see multiple offers, have multiple loan accounts active, we cannot identify which loan header to use.
// TODO: Figure out an alternate solution for all the cases that need this interceptor and remove the interceptor.
func NewUnaryCtxFromLoanHeader(palClient palBePb.PreApprovedLoanClient) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		if !strings.Contains(info.FullMethod, "preapprovedloan.PreApprovedLoan") {
			return handler(ctx, req)
		}

		// In case of LandingInfoV2, it is expected that loan header won't be present in the request and backend
		// decision engine has to figure it out. Hence, skipping setting the ownership for this RPC.
		if strings.Contains(info.FullMethod, "GetLandingInfoV2") ||
			strings.Contains(info.FullMethod, "GetLoanUserDetails") ||
			strings.Contains(info.FullMethod, "GetLoanSummaryForHome") ||
			strings.Contains(info.FullMethod, "FetchDynamicElements") ||
			strings.Contains(info.FullMethod, "ProcessBillzyPaymentEvent") ||
			strings.Contains(info.FullMethod, "DynamicElementCallback") ||
			strings.Contains(info.FullMethod, "GetActiveLoanRequests") ||
			strings.Contains(info.FullMethod, "GetLoanAccountDetails") ||
			strings.Contains(info.FullMethod, "GetLoanOffers") ||
			strings.Contains(info.FullMethod, "GetDashboardV2") ||
			strings.Contains(info.FullMethod, "GetLandingInfoV3") ||
			strings.Contains(info.FullMethod, "CancelApplication") {
			return handler(ctx, req)
		}

		lhReq, ok := req.(ReqWithLoanHeader)
		if ok && lhReq.GetLoanHeader().GetVendor() != palBePb.Vendor_VENDOR_UNSPECIFIED {
			vendorInHeader := lhReq.GetLoanHeader().GetVendor()
			ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(vendorInHeader))
			return handler(ctx, req)
		}
		if !ok {
			return handler(ctx, req)
		}

		// TODO(Sundeep): Added this for backwards compatiblity, but remove this once we are sure that most users have
		// updated their clients to m138-rc1 release & show force update for other users.
		act, ok := req.(ReqWithActorId)
		var actorId string
		// do nothing if status is not present or empty
		if !ok || act == nil || act.GetActorId() == "" {
			actorId = epificontext.ActorIdFromContext(ctx)
		} else {
			actorId = act.GetActorId()
		}
		loanProgram := palBePb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED

		lhRpc, ok := req.(ReqWithLoanHeader)
		if ok {
			if lhRpc.GetLoanHeader().GetLoanProgram() != palBePb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
				loanProgram = lhRpc.GetLoanHeader().GetLoanProgram()
			}
		}

		li, err := palClient.GetLandingInfoV2(ctx, &palBePb.GetLandingInfoV2Request{
			ActorId: actorId,
			LoanHeader: &palBePb.LoanHeader{
				LoanProgram: loanProgram,
			},
		})
		if err = epifigrpc.RPCError(li, err); err == nil {
			ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(li.GetLoanHeader().GetVendor()))
			if ok && li.GetLoanHeader() != nil {
				msg := lhRpc.ProtoReflect()
				newValue := protoreflect.ValueOf(li.GetLoanHeader().ProtoReflect())
				msg.Set(msg.Descriptor().Fields().ByName("loan_header"), newValue)
			}
		}
		return handler(ctx, req)
	}
}
