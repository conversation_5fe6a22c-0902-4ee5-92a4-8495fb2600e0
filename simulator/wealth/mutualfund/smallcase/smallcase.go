//nolint:contextcheck

package smallcase

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"math/rand"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/labstack/gommon/random"
	"github.com/mohae/deepcopy"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/structpb"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	"github.com/epifi/gamma/api/simulator/mutualfund"
	"github.com/epifi/gamma/api/vendors/mfcentral"
	"github.com/epifi/gamma/api/vendors/smallcase"
	"github.com/epifi/gamma/simulator/config/genconf"
	"github.com/epifi/gamma/simulator/dao/mutual_fund/impl"
	mfcentralToken "github.com/epifi/gamma/vendorgateway/wealth/mutualfund/token/mf_central"
	"github.com/epifi/gamma/vendorgateway/wire/types"
)

const gateway = "fimoney-stag"

type Service struct {
	conf             *genconf.Config
	h                *http.Client
	EncryptAndSign   *mfcentralToken.EncryptAndSign
	VerifyAndDecrypt *mfcentralToken.VerifyAndDecrypt
	mfUsersDao       *impl.MfUsersDao
	mfRequestsDao    *impl.MfRequestsDao
}

func NewSmallCaseService(conf *genconf.Config, h *http.Client, handler *vendorapi.HTTPRequestHandler, mfUsersDao *impl.MfUsersDao, mfRequestsDao *impl.MfRequestsDao) *Service {
	return &Service{
		conf:             conf,
		h:                h,
		EncryptAndSign:   mfcentralToken.NewEncryptAndSign(handler, types.EncryptAndSignUrl(conf.SmallCase().EncryptAndSignUrl)),
		VerifyAndDecrypt: mfcentralToken.NewVerifyAndDecrypt(handler, types.VerifyAndDecryptUrl(conf.SmallCase().VerifyAndDecryptUrl)),
		mfRequestsDao:    mfRequestsDao,
		mfUsersDao:       mfUsersDao,
	}
}

// CreateTransaction creates a mock mf central transaction after verifying the payload
func (s *Service) CreateTransaction(w http.ResponseWriter, request *http.Request) {
	ctx := context.WithValue(context.Background(), "trace_id", uuid.New())
	logger.Info(ctx, "received request for smallcase CreateTransaction")
	sendErrResp := func() {
		resp := &smallcase.CreateTransactionResponse{
			Success: false,
		}
		sendResponse(w, resp, http.StatusBadRequest)
	}
	reqData, err := ioutil.ReadAll(request.Body)
	if err != nil {
		logger.ErrorNoCtx("failed to read smallcase create transaction request", zap.Error(err))
		sendErrResp()
		return
	}

	createTxnReq := &smallcase.CreateTransactionRequest{}

	err = protojson.Unmarshal(reqData, createTxnReq)
	if err != nil {
		logger.Error(ctx, "failed to unmarshal smallcase create transaction request", zap.Error(err))
		sendErrResp()
		return
	}

	// verification
	if createTxnReq.GetIntent() != "MF_HOLDINGS_IMPORT" || createTxnReq.GetAssetConfig().GetFromDate() != "2000-01-01" {
		logger.Error(ctx, "validation failed for smallcase create transaction request", zap.Any(logger.REQUEST, createTxnReq))
		sendErrResp()
		return
	}

	resp := &smallcase.CreateTransactionResponse{
		Success: true,
		Data: &smallcase.CreateTransactionData{
			TransactionId: uuid.New().String(),
			ExpireAt:      time.Now().Add(time.Hour).Format(time.RFC3339),
		},
	}

	sendResponse(w, resp, http.StatusOK)
}

// InitiateHoldingsImport verified the payload and responds with mock holdings import initiation data
//
//nolint:contextcheck,funlen
func (s *Service) InitiateHoldingsImport(w http.ResponseWriter, request *http.Request) {
	ctx := context.WithValue(context.Background(), "trace_id", uuid.New())
	logger.Info(ctx, "received request for smallcase InitiateHoldingsImport")

	var (
		httpStatus int
		isSuccess  bool
	)
	sendErrResp := func() {
		resp := &smallcase.GetOTPResponse{
			Success: false,
		}
		sendResponse(w, resp, http.StatusBadRequest)
	}
	reqData, err := ioutil.ReadAll(request.Body)
	if err != nil {
		logger.Error(ctx, "failed to read smallcase initiate holdings import request", zap.Error(err))
		sendErrResp()
		return
	}

	getOtpReq := &smallcase.GetOTPRequest{}

	err = protojson.Unmarshal(reqData, getOtpReq)
	if err != nil {
		logger.Error(ctx, "failed to unmarshal smallcase initiate holdings import request", zap.Error(err))
		sendErrResp()
		return
	}

	// validate
	if getOtpReq.GetTransactionId() == "" || getOtpReq.GetStep() != "GET_OTP" || getOtpReq.GetGateway() != gateway {
		logger.Error(ctx, "validation failed smallcase initiate holdings import request", zap.Any(logger.REQUEST, getOtpReq))
		investorConsentResponse := &mfcentral.SubmitCASDetailsResponse{
			Errors: []*mfcentral.Error{{
				Code:    "422",
				Message: "Input Validation Failed",
			}},
		}

		// encrypt mf central payload
		encryptedPaylod, sign, encryptErr := s.encryptAndSignProto(ctx, investorConsentResponse)
		if encryptErr != nil {
			logger.Error(ctx, "failed to encrypt error case SubmitCASDetailsResponse", zap.Error(err))
			sendErrResp()
			return
		}
		// smallcase resp
		resp := &smallcase.GetOTPResponse{
			Success: false,
			Data: &smallcase.GetOTPPayLoadData{
				Response:  encryptedPaylod,
				Signature: sign,
			},
		}

		sendResponse(w, resp, http.StatusUnprocessableEntity)
		return
	}

	// decrypt mf central payload
	decryptedPayload, err := s.VerifyAndDecrypt.VerifyAndDecrypt(ctx, getOtpReq.GetPayLoad().GetRequest(), getOtpReq.GetPayLoad().GetSignature())
	if err != nil {
		logger.Error(ctx, "failed decrypt get otp payload", zap.Error(err))
		sendErrResp()
		return
	}

	submitCASDetailsReq := &mfcentral.SubmitCASDetailsRequest{}
	err = protojson.Unmarshal([]byte(decryptedPayload), submitCASDetailsReq)
	if err != nil {
		logger.Error(ctx, "failed to unmarshal SubmitCASDetailsRequest", zap.Error(err))
		sendErrResp()
		return
	}

	var investorConsentResponse *mfcentral.SubmitCASDetailsResponse
	mobileNumber := submitCASDetailsReq.GetMobileNumber()
	email := submitCASDetailsReq.GetEmailId()
	// pattern for invalid mobile numbers
	switch {
	case s.conf.MFHoldingsImport().SimulateInvalidPanMobile().Get(mobileNumber) || mobileNumber == "1234567890":
		logger.Info(ctx, "simulating invalid phone number", zap.Any(logger.PHONE_NUMBER, mobileNumber))
		investorConsentResponse = &mfcentral.SubmitCASDetailsResponse{
			Errors: []*mfcentral.Error{
				{
					Code:    "422",
					Message: "Invalid PAN/PEKRN, Mobile/Email combination",
				},
			},
		}
		httpStatus = http.StatusUnprocessableEntity
		isSuccess = false
	case mobileNumber == "7777777777":
		logger.Error(ctx, "duplicate client reference number error simulation", zap.Any(logger.PHONE_NUMBER, submitCASDetailsReq.GetMobileNumber()))
		investorConsentResponse = &mfcentral.SubmitCASDetailsResponse{
			Errors: []*mfcentral.Error{
				{
					Code:    "422",
					Message: "Duplicate clientRefNo",
				},
			},
		}
		httpStatus = http.StatusUnprocessableEntity
		isSuccess = false
	case s.conf.MFHoldingsImport().SimulateInvalidPanEmail().Get(email) || email == "<EMAIL>":
		logger.Error(ctx, "invalid email error simulation", zap.Any("email", submitCASDetailsReq.GetEmailId()))
		investorConsentResponse = &mfcentral.SubmitCASDetailsResponse{
			Errors: []*mfcentral.Error{
				{
					Code:    "422",
					Message: "Invalid PAN/PEKRN, Mobile/Email combination",
				},
			},
		}
		httpStatus = http.StatusUnprocessableEntity
		isSuccess = false
	default:
		investorConsentResponse = &mfcentral.SubmitCASDetailsResponse{
			RequestId:             1234,
			OtpReference:          "5678",
			UserSubjectReference:  "unknown",
			ClientReferenceNumber: submitCASDetailsReq.GetClientReferenceNumber(),
		}
		httpStatus = http.StatusOK
		isSuccess = true
	}
	phoneNumber := func(phoneNumber string) string {
		// Define regular expressions to match the prefixes
		regex91 := regexp.MustCompile(`^\+91|\b0`) // Match +91 or 0 at the beginning
		cleanedNumber := regex91.ReplaceAllString(phoneNumber, "")

		return cleanedNumber
	}(submitCASDetailsReq.GetMobileNumber())

	user, err := s.mfUsersDao.Create(ctx, &mutualfund.MutualFundUser{
		Id:    uuid.NewString(),
		Phone: phoneNumber,
		Email: phoneNumber + "@email.com",
	})
	if err != nil && !errors.Is(err, epifierrors.ErrDuplicateEntry) {
		logger.Error(ctx, "failed to fetch/crate entry for mf users", zap.Error(err))
		sendErrResp()
		return
	}
	if errors.Is(err, epifierrors.ErrDuplicateEntry) {
		user, err = s.mfUsersDao.GetByPhoneNumber(ctx, phoneNumber)
		if err != nil {
			logger.Error(ctx, "failed to get mf user for phone number", zap.Error(err), zap.String(logger.PHONE_NUMBER, phoneNumber))
			sendErrResp()
			return
		}
	}
	if err = s.mfRequestsDao.DeleteByUserId(ctx, user.GetId()); err != nil {
		logger.Error(ctx, "failed to delete request entries for user", zap.Error(err))
		sendErrResp()
		return
	}

	if _, err = s.mfRequestsDao.Create(ctx, &mutualfund.MutualFundRequest{
		RequestId:   uuid.NewString(),
		Type:        mutualfund.MfRequestType_MF_REQUEST_TYPE_CAS_DETAILED,
		Status:      mutualfund.MfRequestStatus_MF_REQUEST_STATUS_INITIATED,
		UserId:      user.GetId(),
		ClientRefId: submitCASDetailsReq.GetClientReferenceNumber(),
	}); err != nil {
		logger.Error(ctx, "failed to create request entry for user", zap.Error(err))
		sendErrResp()
		return
	}
	// encrypt mf central payload
	encryptedPaylod, sign, err := s.encryptAndSignProto(ctx, investorConsentResponse)
	if err != nil {
		logger.Error(ctx, "failed to encrypt SubmitCASDetailsResponse", zap.Error(err))
		sendErrResp()
		return
	}
	// smallcase resp
	resp := &smallcase.GetOTPResponse{
		Success: isSuccess,
		Data: &smallcase.GetOTPPayLoadData{
			Response:  encryptedPaylod,
			Signature: sign,
		},
	}

	sendResponse(w, resp, httpStatus)
}

func (s *Service) encryptAndSignProto(ctx context.Context, msg proto.Message) (string, string, error) {
	mfCentralResp, err := protojson.Marshal(msg)
	if err != nil {
		return "", "", fmt.Errorf("failed to marshal proto: %w", err)
	}

	encryptedPayload, sign, err := s.EncryptAndSign.EncryptAndSign(ctx, string(mfCentralResp))
	if err != nil {
		return "", "", fmt.Errorf("failed to encrypt payload: %w", err)
	}

	return encryptedPayload, sign, nil
}

// TriggerHoldingsImportFetch verifies the request and sends mock mf payload to the webhook callback url
//
//nolint:contextcheck,funlen
func (s *Service) TriggerHoldingsImportFetch(w http.ResponseWriter, request *http.Request) {
	ctx := context.WithValue(context.Background(), "trace_id", uuid.New())
	logger.Info(ctx, "received request for smallcase TriggerHoldingsImportFetch")
	sendErrResp := func() {
		resp := &smallcase.VerifyOTPResponse{
			Success: false,
		}
		sendResponse(w, resp, http.StatusBadRequest)
	}
	reqData, err := ioutil.ReadAll(request.Body)
	if err != nil {
		logger.Error(ctx, "failed to read smallcase verify otp request", zap.Error(err))
		sendErrResp()
		return
	}

	verifyOtpReq := &smallcase.VerifyOTPRequest{}

	err = protojson.Unmarshal(reqData, verifyOtpReq)
	if err != nil {
		logger.Error(ctx, "failed to unmarshal smallcase verify otp request", zap.Error(err))
		sendErrResp()
		return
	}

	// validate
	if verifyOtpReq.GetTransactionId() == "" || verifyOtpReq.GetStep() != "VERIFY_OTP" || verifyOtpReq.GetGateway() != gateway {
		logger.ErrorNoCtx("validation failed smallcase initiate verify otp request", zap.Any(logger.REQUEST, verifyOtpReq))
		sendErrResp()
		return
	}
	decryptedPayload, err := s.VerifyAndDecrypt.VerifyAndDecrypt(ctx, verifyOtpReq.GetPayLoad().GetRequest(), verifyOtpReq.GetPayLoad().GetSignature())
	if err != nil {
		logger.Error(ctx, "failed decrypt get otp payload", zap.Error(err))
		sendErrResp()
		return
	}

	investorConsentReq := &mfcentral.InvestorConsentRequest{}
	err = protojson.Unmarshal([]byte(decryptedPayload), investorConsentReq)
	if err != nil {
		logger.Error(ctx, "failed to unmarshal InvestorConsentRequest", zap.Error(err))
		sendErrResp()
		return
	}
	if investorConsentReq.GetEnteredOtp() == "777777" {
		// send error response to test otp failures
		investorConsentRes := &mfcentral.InvestorConsentResponse{
			Errors: []*mfcentral.Error{{
				Code:    "406",
				Message: "Entered OTP appears to be incorrect. Please try again.",
			}},
		}
		encryptedPayload, sign, encryptErr := s.encryptAndSignProto(ctx, investorConsentRes)
		if encryptErr != nil {
			logger.Error(ctx, "failed to encrypt error case InvestorConsentResponse", zap.Error(err))
			sendErrResp()
			return
		}
		resp := &smallcase.VerifyOTPResponse{
			Success: false,
			Errors:  "error in verifying otp",
			Data: &smallcase.VerifyOTPPayLoadData{
				Response:  encryptedPayload,
				Signature: sign,
			},
		}
		sendResponse(w, resp, http.StatusNotAcceptable)
		return
	}

	if investorConsentReq.GetEnteredOtp() == "900001" {
		investorConsentRes := &mfcentral.InvestorConsentResponse{
			Errors: []*mfcentral.Error{{
				Code:    "406",
				Message: "no portfolio found",
			}},
		}
		encryptedPayload, sign, encryptErr := s.encryptAndSignProto(ctx, investorConsentRes)
		if encryptErr != nil {
			logger.Error(ctx, "failed to encrypt error case InvestorConsentResponse", zap.Error(err))
			sendErrResp()
			return
		}
		resp := &smallcase.VerifyOTPResponse{
			Success: false,
			Errors:  "could not fetch portfolio",
			Data: &smallcase.VerifyOTPPayLoadData{
				Response:  encryptedPayload,
				Signature: sign,
			},
		}
		sendResponse(w, resp, http.StatusNoContent)
		return
	}
	if investorConsentReq.GetEnteredOtp() == "900002" {
		investorConsentRes := &mfcentral.InvestorConsentResponse{
			Errors: []*mfcentral.Error{{
				Code:    "406",
				Message: "low portfolio found",
			}},
		}
		encryptedPayload, sign, encryptErr := s.encryptAndSignProto(ctx, investorConsentRes)
		if encryptErr != nil {
			logger.Error(ctx, "failed to encrypt error case InvestorConsentResponse", zap.Error(err))
			sendErrResp()
			return
		}
		resp := &smallcase.VerifyOTPResponse{
			Success: false,
			Errors:  "could not fetch portfolio",
			Data: &smallcase.VerifyOTPPayLoadData{
				Response:  encryptedPayload,
				Signature: sign,
			},
		}
		sendResponse(w, resp, http.StatusNoContent)
		return
	}

	resp := &smallcase.VerifyOTPResponse{
		Success: true,
	}

	sendResponse(w, resp, http.StatusOK)
	req, err := s.mfRequestsDao.GetByClientRefId(ctx, investorConsentReq.GetClientReferenceNumber())
	if err != nil {
		logger.Error(ctx, "failed to fetch folio request", zap.Error(err))
		sendErrResp()
		return
	}
	user, err := s.mfUsersDao.GetById(ctx, req.GetUserId())
	if err != nil {
		logger.Error(ctx, "failed to fetch user", zap.Error(err))
		sendErrResp()
		return
	}
	var folioDetails []*mutualfund.FolioDetailsEntity
	holdingsResp := sampleMfHoldings
	otp := investorConsentReq.GetEnteredOtp()
	// For all LAMF cases the OTP starts with 9
	switch {
	case otp == "555555":
		holdingsResp = detailedSampleMFHoldings
		break
	case otp == "444444":
		holdingsResp = midSizeSample
		break
	case otp == "900000":
		holdingsResp = ""
		break
	case otp == "900001":
		holdingsResp = lamfLowBalance
		break
	case otp == "900002":
		holdingsResp = lamfPartialBalanceFailure
	case otp == "111111":
		holdingsResp, err = populateSecretData()
		if err != nil {
			logger.Error(ctx, "error while parsing secret data", zap.Error(err))
		}
	case otp[0:3] == "999":
		// These cases are for LAMF CAS summary and NFT flow.
		// These only contain DT summary details
		switch {
		case otp == "999000":
			// portfolio of 40k - folio with user's primary phone and email
			folioDetails = pf1
			for _, folio := range folioDetails {
				folio.PhoneNumber = user.GetPhone()
				folio.EmailId = user.GetEmail()
			}
		case otp == "999001":
			// Total portfolio available amount of 90L
			// folios of 20k available amount with user's email and phone
			// folios of 10k available amount with user's phone and random email
			// folios of 10k available amount with user's email and random phone
			// folios of 40k available amount with random email and phone
			folioDetails = s.getLowCasSummaryPortfolio(user)
		case otp == "999002":
			// Total portfolio available amount of 5L
			// folios of 2L available amount with user's email and phone
			// folios of 1L available amount with user's phone and random email
			// folios of 1L available amount with user's email and random phone
			// folios of 1L available amount with random email and phone
			folioDetails = s.getMixedCasSummaryPortfolio(user)
		default:
			// portfolio of 5L - folio with user's primary phone and email
			folioDetails = pf3
			for _, folio := range folioDetails {
				folio.PhoneNumber = user.GetPhone()
				folio.EmailId = user.GetEmail()
			}
		}
		holdingsResp, err = s.convertFolioDataToCasDetailedResponseJson(investorConsentReq.GetRequestId(), user, folioDetails)
		if err != nil {
			logger.Error(ctx, "failed to convert folio data to cas detailed response json", zap.Error(err))
			sendErrResp()
			return
		}
	case otp[0] == '9':
		holdingsResp = lamfOptimalBalance
		break
	default:
		holdingsResp = sampleMfHoldings
	}
	user.FolioDetails = &mutualfund.MutualFundFolioDetails{
		FolioList: folioDetails,
	}
	if err = s.mfUsersDao.Update(ctx, user, []mutualfund.MfUserFieldMask{
		mutualfund.MfUserFieldMask_MF_USER_FIELD_MASK_FOLIO_DETAILS,
	}); err != nil {
		logger.Error(ctx, "failed to update user", zap.Error(err))
		sendErrResp()
		return
	}
	req.Status = mutualfund.MfRequestStatus_MF_REQUEST_STATUS_SUCCESS
	if err = s.mfRequestsDao.Update(ctx, req, []mutualfund.MfRequestFieldMask{
		mutualfund.MfRequestFieldMask_MF_REQUEST_FIELD_MASK_STATUS,
	}); err != nil {
		logger.Error(ctx, "failed to update request", zap.Error(err))
		sendErrResp()
		return
	}

	encryptedPayload, sign, err := s.EncryptAndSign.EncryptAndSign(ctx, holdingsResp)
	if err != nil {
		logger.Error(ctx, "failed to encrypt sample mf holdings payload")
		sendErrResp()
		return
	}
	webhookResp := &smallcase.ProcessMFHoldingsWebhookRequest{
		Data: &smallcase.Data{
			Signature: sign,
			Response:  encryptedPayload,
		},
		TransactionId: verifyOtpReq.GetTransactionId(),
		Gateway:       gateway,
	}

	payload, err := protojson.Marshal(webhookResp)
	if err != nil {
		logger.ErrorNoCtx("failed to marshal smallcase mf holding import webhook payload", zap.Error(err))
		sendErrResp()
		return
	}

	s.sendMFHoldingsToCallback(ctx, payload)
}

func populateSecretData() (string, error) {
	sampleDtlSummary := &mfcentral.DtSummary{
		Email:          "<EMAIL>",
		Amc:            "P",
		AmcName:        "ICICI Prudential Mutual Fund",
		Folio:          "1234567",
		Scheme:         "8189",
		SchemeName:     "ICICI Prudential Nifty 50 Index Fund - Direct Plan Growth ",
		KycStatus:      "3",
		BrokerCode:     "INA200015185",
		BrokerName:     "",
		RtaCode:        "CAMS",
		DecimalUnits:   "EPIFI WEALTH PRIVATE LIMITED",
		DecimalAmount:  "3",
		DecimalNav:     "2",
		LastTrxnDate:   "04-OCT-2022",
		OpeningBal:     "0",
		MarketValue:    "6923.57",
		Nav:            "185.6431",
		ClosingBalance: "1243",
		LastNavDate:    "08-JUN-2023",
		IsDemat:        "N",
		AssetType:      "EQUITY",
		Isin:           "INF109K012M7",
		NomineeStatus:  "Y",
	}
	sampleData := &mfcentral.Data{
		DtSummary: []*mfcentral.DtSummary{
			sampleDtlSummary,
		},
		DtTransaction: make([]*mfcentral.DtTransaction, 0),
	}
	sampleTxn := &mfcentral.DtTransaction{
		Email:         "<EMAIL>",
		Amc:           "P",
		AmcName:       "ICICI Prudential Mutual Fund",
		Folio:         "1234567",
		Scheme:        "8189",
		SchemeName:    "ICICI Prudential Nifty 50 Index Fund - Direct Plan Growth ",
		TrxnDesc:      "Purchase - INA1234567",
		TrxnAmount:    "10027",
		SttTax:        "0",
		Tax:           "0",
		TotalTax:      "0",
		TrxnMode:      "N",
		PurchasePrice: "165.7187",
		StampDuty:     "0",
		TrxnCharge:    "0",
		TrxnTypeFlag:  "FP",
		Isin:          "INF109K012M7",
	}
	for i := 0; i < 12; i++ {
		copySample := deepcopy.Copy(sampleTxn).(*mfcentral.DtTransaction)
		copySample.TransactionDate = strings.ToUpper(time.Now().AddDate(0, -i, 0).Format("02-Jan-2006"))
		copySample.PostedDate = copySample.GetTransactionDate()
		if i%4 == 0 {
			copySample.TrxnUnits = "-1"
			sampleData.DtTransaction = append(sampleData.DtTransaction, deepcopy.Copy(copySample).(*mfcentral.DtTransaction))
			if i == 8 {
				copySample.TrxnUnits = "1"
				sampleData.DtTransaction = append(sampleData.DtTransaction, deepcopy.Copy(copySample).(*mfcentral.DtTransaction))
			}
		} else {
			copySample.TrxnUnits = "1"
			sampleData.DtTransaction = append(sampleData.DtTransaction, copySample)
		}
	}
	resp := &mfcentral.MFCentralPayload{
		Data: []*mfcentral.Data{
			sampleData,
		},
	}
	responseJson, err := protojson.Marshal(resp)
	if err != nil {
		return "", fmt.Errorf("failed to marshal secret data: %w", err)
	}

	return string(responseJson), nil
}

// sendResponse sends a marshaled proto as response
func sendResponse(w http.ResponseWriter, resp proto.Message, httpStatusCode int) {
	data, err := protojson.Marshal(resp)
	if err != nil {
		return
	}
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(httpStatusCode)
	_, _ = w.Write(data)
}

// sendMFHoldingsToCallback sends a post request to webhook url
func (s *Service) sendMFHoldingsToCallback(ctx context.Context, payload []byte) {
	logger.Info(ctx, "sending payload to MFHoldingsImportCallbackEndpoint", zap.Any(logger.PAYLOAD, string(payload)))
	request, _ := http.NewRequestWithContext(context.Background(), http.MethodPost, s.conf.SmallCase().MFHoldingsImportCallbackEndpoint, bytes.NewBuffer(payload))
	request.Header.Add("Content-Type", "application/json")
	resp, err := s.h.Do(request)
	if err != nil {
		return
	}
	if resp.StatusCode != http.StatusOK {
		logger.Error(ctx, fmt.Sprintf("Not OK response while publishing payload to webhook: %s",
			s.conf.SmallCase().MFHoldingsImportCallbackEndpoint), zap.Any(logger.RESPONSE, resp))
	}
	defer func() {
		_ = resp.Body.Close()
	}()
}

func (s *Service) convertFolioDataToCasDetailedResponseJson(reqId int64, userDetails *mutualfund.MutualFundUser,
	folios []*mutualfund.FolioDetailsEntity) (string, error) {
	var casDetailedResponse = &mfcentral.MFCentralPayload{
		ReqId: strconv.FormatInt(reqId, 10),
		Email: userDetails.GetEmail(),
		Data:  []*mfcentral.Data{},
	}
	var dtSummaryList []*mfcentral.DtSummary

	for _, folioSummary := range folios {
		var (
			isDemat, nav, units, marketValue string
		)
		if folioSummary.GetIsDemat() {
			isDemat = "Y"
		} else {
			isDemat = "N"
		}
		nav = strconv.FormatFloat(float64(folioSummary.GetNav()), 'f', 4, 64)
		units = strconv.FormatFloat(float64(folioSummary.GetUnits()), 'f', 4, 64)
		marketValue = strconv.FormatFloat(float64(folioSummary.GetTotalAmount()), 'f', 4, 64)

		var dtSummary = &mfcentral.DtSummary{
			Email:          folioSummary.GetEmailId(),
			Amc:            folioSummary.GetAmc(),
			Folio:          folioSummary.GetFolioId(),
			Scheme:         folioSummary.GetSchemeCode(),
			RtaCode:        folioSummary.GetRtaName(),
			ClosingBalance: units,
			Nav:            nav,
			MarketValue:    marketValue,
			IsDemat:        isDemat,
			Isin:           folioSummary.GetIsin(),
			// dummy values
			LastTrxnDate: "20-JAN-2022",
		}
		dtSummaryList = append(dtSummaryList, dtSummary)
	}
	casDetailedResponse.Data = append(casDetailedResponse.GetData(), &mfcentral.Data{
		DtSummary: dtSummaryList,
		DtTransaction: []*mfcentral.DtTransaction{
			dummyTxn,
		},
	})

	casDetailResponseJson, err := protojson.Marshal(casDetailedResponse)
	if err != nil {
		return "", fmt.Errorf("failed to marshal cas detailed response: %w", err)
	}

	return string(casDetailResponseJson), nil
}

func (s *Service) getLowCasSummaryPortfolio(user *mutualfund.MutualFundUser) []*mutualfund.FolioDetailsEntity {
	var folioDetails []*mutualfund.FolioDetailsEntity
	for _, folio := range pf1 {
		folio.PhoneNumber = getRandomPhoneNumber()
		folio.EmailId = getRandomEmail()
		folioDetails = append(folioDetails, folio)
	}
	for _, folio := range pf7 {
		folio.PhoneNumber = user.GetPhone()
		folio.EmailId = getRandomEmail()
		folioDetails = append(folioDetails, folio)
	}
	for _, folio := range pf8 {
		folio.PhoneNumber = getRandomPhoneNumber()
		folio.EmailId = user.GetEmail()
		folioDetails = append(folioDetails, folio)
	}
	for _, folio := range pf9 {
		folio.PhoneNumber = user.GetPhone()
		folio.EmailId = user.GetEmail()
		folioDetails = append(folioDetails, folio)
	}

	return folioDetails
}

func (s *Service) getMixedCasSummaryPortfolio(user *mutualfund.MutualFundUser) []*mutualfund.FolioDetailsEntity {
	var folioDetails []*mutualfund.FolioDetailsEntity
	for _, folio := range pf1 {
		folio.PhoneNumber = getRandomPhoneNumber()
		folio.EmailId = getRandomEmail()
		folioDetails = append(folioDetails, folio)
	}
	for _, folio := range pf2 {
		folio.PhoneNumber = getRandomPhoneNumber()
		folio.EmailId = getRandomEmail()
		folioDetails = append(folioDetails, folio)
	}
	for _, folio := range pf6 {
		folio.PhoneNumber = user.GetPhone()
		folio.EmailId = getRandomEmail()
		folioDetails = append(folioDetails, folio)
	}
	for _, folio := range pf5 {
		folio.PhoneNumber = getRandomPhoneNumber()
		folio.EmailId = user.GetEmail()
		folioDetails = append(folioDetails, folio)
	}
	for _, folio := range pf4 {
		folio.PhoneNumber = user.GetPhone()
		folio.EmailId = user.GetEmail()
		folioDetails = append(folioDetails, folio)
	}

	return folioDetails
}

func getRandomPhoneNumber() string {
	phoneNumber := rand.Intn(9999999999-1000000000) + 1000000000
	return fmt.Sprintf("%d", phoneNumber)
}

func getRandomEmail() string {
	return fmt.Sprintf("%<EMAIL>", random.String(10))
}

func (s *Service) GetMFAnalytics(w http.ResponseWriter, request *http.Request) {
	//nolint:staticcheck
	ctx := context.WithValue(context.Background(), "trace_id", uuid.New())
	logger.Info(ctx, "received request for smallcase GetMfAnalyticsV2")
	sendErrResp := func(errorType string) {
		resp := &smallcase.GetMfAnalyticsResponse{
			Success:   false,
			ErrorType: errorType,
		}
		sendResponse(w, resp, http.StatusBadRequest)
	}
	reqData, err := io.ReadAll(request.Body)
	if err != nil {
		logger.Error(ctx, "failed to read smallcase getMfAnalyticsRequest", zap.Error(err))
		sendErrResp("failed to read smallcase getMfAnalyticsRequest")
		return
	}

	getMfAnalyticsRequest := &smallcase.GetMfAnalyticsRequest{}
	err = protojson.Unmarshal(reqData, getMfAnalyticsRequest)
	if err != nil {
		logger.Error(ctx, "failed to unmarshal smallcase getMfAnalyticsRequest", zap.Error(err))
		sendErrResp("failed to unmarshal smallcase getMfAnalyticsRequest")
		return
	}
	logger.Info(ctx, "received request for smallcase GetMfAnalyticsV2", zap.Any("getMfAnalyticsRequest", string(reqData)))

	smallCaseResp := &smallcase.GetMfAnalyticsResponse{
		Success: true,
		Data: &smallcase.MFAnalyticsResponseData{
			Summary:      s.convertSummaryData(getMfAnalyticsRequest),
			Transactions: s.convertTransactionData(getMfAnalyticsRequest),
		},
	}
	sendResponse(w, smallCaseResp, http.StatusOK)
}

// convertSummaryData converts DtSummary data to MFAnalyticsSummary
func (s *Service) convertSummaryData(req *smallcase.GetMfAnalyticsRequest) *smallcase.MFAnalyticsSummary {
	if req.GetData().GetPayload() == nil {
		return &smallcase.MFAnalyticsSummary{}
	}
	isinToSummariesMap := make(map[string][]*mfcentral.DtSummary)
	for _, summary := range req.GetData().GetPayload().GetDtSummary() {
		isin := summary.GetIsin()
		isinToSummariesMap[isin] = append(isinToSummariesMap[isin], summary)
	}
	var (
		schemeDetails       []*smallcase.SchemeDetails
		totalPortfolioValue float64
		totalInvestedValue  float64
	)
	for isin, summaries := range isinToSummariesMap {
		firstSummary := summaries[0]
		var (
			folios              []*smallcase.MFFolioDetails
			schemeUnits         float64
			schemeCurrentValue  float64
			schemeInvestedValue float64
		)
		for _, summary := range summaries {
			folio := s.convertDtSummaryToFolio(summary)
			folios = append(folios, folio)
			schemeUnits += folio.GetUnits()
			schemeCurrentValue += folio.GetCurrentValue()
			schemeInvestedValue += folio.GetInvestedValue()
		}
		nav, _ := strconv.ParseFloat(firstSummary.GetNav(), 64)
		schemeDetail := &smallcase.SchemeDetails{
			Isin:            isin,
			Folios:          folios,
			Scheme:          firstSummary.GetSchemeName(),
			SchemeCode:      firstSummary.GetScheme(),
			Amc:             firstSummary.GetAmcName(),
			AmcCode:         firstSummary.GetAmc(),
			AssetType:       firstSummary.GetAssetType(),
			Units:           schemeUnits,
			Nav:             nav,
			CurrentValue:    schemeCurrentValue,
			FundType:        s.getFundTypeFromAssetType(firstSummary.GetAssetType()),
			Analytics:       s.generateMockSchemeAnalytics(schemeInvestedValue, schemeCurrentValue),
			ExitLoadRemarks: "Nil",
		}
		schemeDetails = append(schemeDetails, schemeDetail)
		totalPortfolioValue += schemeCurrentValue
		totalInvestedValue += schemeInvestedValue
	}
	return &smallcase.MFAnalyticsSummary{
		SchemeDetails: schemeDetails,
		Portfolio:     s.generateMockPortfolioAnalytics(totalInvestedValue, totalPortfolioValue, len(schemeDetails)),
	}
}

func (s *Service) convertDtSummaryToFolio(summary *mfcentral.DtSummary) *smallcase.MFFolioDetails {
	// Parse string fields to float64
	units, _ := strconv.ParseFloat(summary.GetClosingBalance(), 64)
	marketValue, _ := strconv.ParseFloat(summary.GetMarketValue(), 64)
	nav, _ := strconv.ParseFloat(summary.GetNav(), 64)
	openingBal, _ := strconv.ParseFloat(summary.GetOpeningBal(), 64)

	// Use marketValue as invested value (what was actually invested)
	// Calculate current value based on simulated performance
	investedValue := marketValue
	performanceFactor := 1.15
	currentValue := investedValue * performanceFactor

	return &smallcase.MFFolioDetails{
		FolioNumber:              summary.GetFolio(),
		BrokerName:               summary.GetBrokerName(),
		BrokerCode:               summary.GetBrokerCode(),
		IsDemat:                  summary.GetIsDemat(),
		Units:                    units,
		InvestedValue:            investedValue,
		CurrentValue:             currentValue,
		CurrentReturns:           currentValue - investedValue,
		TotalReturns:             currentValue - investedValue,
		RealisedReturns:          0,
		AveragePrice:             nav,
		CurrentReturnsPercentage: s.calculatePercentage(currentValue-investedValue, investedValue),
		Xirr:                     structpb.NewNumberValue(6.6),
		RtaCode:                  summary.GetRtaCode(),
		KycStatus:                summary.GetKycStatus(),
		Email:                    summary.GetEmail(),
		DecimalUnits:             0,
		DecimalAmount:            0,
		DecimalNav:               0,
		LastTrxnDate:             summary.GetLastTrxnDate(),
		OpeningBal:               openingBal,
		ClosingBalance:           units,
		LastNavDate:              summary.GetLastNavDate(),
		Remarks:                  "",
	}
}

// convertTransactionData converts DtTransaction data to MFTransaction
func (s *Service) convertTransactionData(req *smallcase.GetMfAnalyticsRequest) []*smallcase.MFTransaction {
	if req.GetData() == nil || req.GetData().GetPayload() == nil {
		return []*smallcase.MFTransaction{}
	}

	var transactions []*smallcase.MFTransaction
	for _, dt := range req.GetData().GetPayload().GetDtTransaction() {
		transaction := s.convertDtTransactionToMFTransaction(dt)
		transactions = append(transactions, transaction)
	}

	return transactions
}

func (s *Service) convertDtTransactionToMFTransaction(dt *mfcentral.DtTransaction) *smallcase.MFTransaction {
	// Parse string fields to float64
	trxnAmount, _ := strconv.ParseFloat(dt.GetTrxnAmount(), 64)
	trxnUnits, _ := strconv.ParseFloat(dt.GetTrxnUnits(), 64)
	purchasePrice, _ := strconv.ParseFloat(dt.GetPurchasePrice(), 64)
	stampDuty, _ := strconv.ParseFloat(dt.GetStampDuty(), 64)
	totalTax, _ := strconv.ParseFloat(dt.GetTotalTax(), 64)
	trxnCharge, _ := strconv.ParseFloat(dt.GetTrxnCharge(), 64)
	sttTax, _ := strconv.ParseFloat(dt.GetSttTax(), 64)
	tax, _ := strconv.ParseFloat(dt.GetTax(), 64)
	return &smallcase.MFTransaction{
		Isin:            dt.GetIsin(),
		FolioNumber:     dt.GetFolio(),
		Amc:             dt.GetAmcName(),
		AmcCode:         dt.GetAmc(),
		SchemeCode:      dt.GetScheme(),
		Scheme:          dt.GetSchemeName(),
		TransactionDate: dt.GetTransactionDate(),
		PostedDate:      dt.GetPostedDate(),
		TrxnDescription: dt.GetTrxnDesc(),
		TrxnAmount:      trxnAmount,
		TrxnUnits:       trxnUnits,
		PurchasePrice:   purchasePrice,
		StampDuty:       stampDuty,
		TotalTax:        totalTax,
		TransactionMode: dt.GetTrxnMode(),
		CashFlow:        "",
		Analytics:       s.generateMockTransactionAnalytics(dt.GetTrxnDesc()),
		CheckDigit:      0,
		Email:           dt.GetEmail(),
		TrxnCharge:      trxnCharge,
		SttTax:          sttTax,
		Tax:             tax,
	}
}

func (s *Service) generateMockSchemeAnalytics(investedValue, currentValue float64) *smallcase.MFSchemeAnalytics {
	returns := currentValue - investedValue
	returnsPercentage := s.calculatePercentage(returns, investedValue)

	// Generate realistic sector allocations (15 sectors totaling 100%)
	sectorAllocations := []*smallcase.MFSectorAllocationPercentage{
		{SectorName: "Private Banks", Value: 18.5},
		{SectorName: "IT Services & Consulting", Value: 15.2},
		{SectorName: "Oil & Gas - Refining & Marketing", Value: 9.8},
		{SectorName: "Construction & Engineering", Value: 8.3},
		{SectorName: "Pharmaceuticals", Value: 7.1},
		{SectorName: "Telecom Services", Value: 6.4},
		{SectorName: "Four Wheelers", Value: 5.9},
		{SectorName: "FMCG - Household Products", Value: 5.2},
		{SectorName: "Power Generation", Value: 4.8},
		{SectorName: "Specialized Finance", Value: 4.3},
		{SectorName: "Iron & Steel", Value: 3.7},
		{SectorName: "Insurance", Value: 3.2},
		{SectorName: "Two Wheelers", Value: 2.8},
		{SectorName: "Investment Banking & Brokerage", Value: 2.4},
		{SectorName: "Others", Value: 2.4},
	}
	return &smallcase.MFSchemeAnalytics{
		InvestedValue:              investedValue,
		AveragePrice:               s.calculatePercentage(investedValue, currentValue),
		Timestamp:                  time.Now().Format(time.RFC3339),
		RealisedReturns:            0,
		TotalReturns:               returns,
		CurrentReturns:             returns,
		CurrentReturnsPercentage:   returnsPercentage,
		SectorAllocationPercentage: sectorAllocations,
		SchemeWeight:               (currentValue / 100000) * 10,
		Xirr:                       structpb.NewNumberValue(returnsPercentage * 1.2),
		Remarks:                    "",
	}
}

func (s *Service) generateMockPortfolioAnalytics(totalInvested, totalCurrent float64, schemeCount int) *smallcase.MFPortfolioAnalytics {
	returns := totalCurrent - totalInvested
	portfolioSectorAllocations := []*smallcase.MFSectorAllocationPercentage{
		{SectorName: "Private Banks", Value: 16.8},
		{SectorName: "IT Services & Consulting", Value: 14.3},
		{SectorName: "Oil & Gas - Refining & Marketing", Value: 10.1},
		{SectorName: "Construction & Engineering", Value: 9.2},
		{SectorName: "Pharmaceuticals", Value: 8.5},
		{SectorName: "Telecom Services", Value: 7.8},
		{SectorName: "Four Wheelers", Value: 6.9},
		{SectorName: "FMCG - Household Products", Value: 6.2},
		{SectorName: "Power Generation", Value: 5.4},
		{SectorName: "Specialized Finance", Value: 4.7},
		{SectorName: "Iron & Steel", Value: 4.1},
		{SectorName: "Insurance", Value: 3.6},
		{SectorName: "Others", Value: 2.4},
	}
	return &smallcase.MFPortfolioAnalytics{
		Xirr:                       structpb.NewNumberValue(15.45),
		SchemeCount:                int64(schemeCount),
		RealisedReturns:            0,
		TotalReturns:               returns,
		CurrentReturns:             returns,
		SectorAllocationPercentage: portfolioSectorAllocations,
	}
}

func (s *Service) generateMockTransactionAnalytics(trxnDesc string) *smallcase.MFTransactionAnalytics {
	transactionType := "DEPOSIT"
	if strings.Contains(strings.ToLower(trxnDesc), "redeem") {
		transactionType = "WITHDRAWAL"
	}
	return &smallcase.MFTransactionAnalytics{
		Exited:                 false,
		TransactionType:        transactionType,
		TransactionStatus:      "SUCCESS",
		TransactionDescription: "PURCHASE",
	}
}

func (s *Service) getFundTypeFromAssetType(assetType string) string {
	switch strings.ToUpper(assetType) {
	case "EQUITY":
		return "Equity"
	case "DEBT":
		return "Debt"
	case "HYBRID":
		return "Hybrid"
	case "CASH":
		return "Liquid"
	default:
		return "Other"
	}
}

func (s *Service) calculatePercentage(value, base float64) float64 {
	if base == 0 {
		return 0
	}
	return (value / base) * 100
}
