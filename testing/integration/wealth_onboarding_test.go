package integration

import (
	"context"
	"testing"

	"github.com/epifi/gamma/testing/integration/app"

	"github.com/stretchr/testify/require"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/owners"
	"github.com/epifi/gamma/scripts/crud/userdata"
)

func WealthOnboardingFlowPan_**********(t *testing.T) {
	defer Recover(t)
	a := require.New(t)
	ctx := context.Background()

	user, releaseUserFn := GetPooledUser(ctx, a)
	defer releaseUserFn()

	// in case the user is already wealth onboarded, delete the wealth data to do wealth onboarding again
	err := userdata.DeleteWealthData(user.Data.Phone, epifiDbV2, simulatorDb, epifiWealthDbV2, nil, actorPgdb)
	a.NoError(err, "failed to delete wealth data")

	wts := app.NewWealthOnbTestSuite(user, feWealthClient, feConsentClient, epifiDbV2, beWealthClient)

	logger.Info(ctx, "GetActorIdForUser for getting ActorId of user")
	actorId, err := userdata.GetActorIdForUser(epifiDbV2, actorPgdb, user.Data.Phone)
	if err != nil {
		logger.ErrorNoCtx("error getting actor id for pooled user", zap.Error(err))
		return
	}

	wts.InitiateWealthOnb(ctx, a, actorId)
}

func WealthOnboardingFlowPan_**********(t *testing.T) {
	defer Recover(t)
	a := require.New(t)
	ctx := context.Background()

	user, releaseUserFn := GetTestUser(ctx, a)
	defer releaseUserFn()

	// in case the user is already wealth onboarded, delete the wealth data to do wealth onboarding again
	err := userdata.DeleteWealthData(user.Data.Phone, epifiDbV2, simulatorDb, epifiWealthDbV2, nil, actorPgdb)
	a.NoError(err, "failed to delete wealth data")

	wts := app.NewWealthOnbTestSuite(user, feWealthClient, feConsentClient, epifiDbV2, beWealthClient)

	logger.Info(ctx, "GetActorIdForUser for getting ActorId of user")
	actorId, err := userdata.GetActorIdForUser(epifiDbV2, actorPgdb, user.Data.Phone)
	if err != nil {
		logger.ErrorNoCtx("error getting actor id for pooled user", zap.Error(err))
		return
	}

	wts.WealthOnboardingFor**********(ctx, a, actorId)
}

func WealthTestCases() []*TestCase {
	return []*TestCase{
		{
			TestProperty: &TestProperty{
				Description: "This is the test for Wealth Onboarding for PAN **********",
				BU:          owners.BUSINESS_UNIT_WEALTH,
				Scope:       []Scope{Smoke, Acceptance},
			},
			Test: WealthOnboardingFlowPan_**********,
		},
		{
			TestProperty: &TestProperty{
				Description: "This is the test for Wealth Onboarding for PAN **********",
				BU:          owners.BUSINESS_UNIT_WEALTH,
				Scope:       []Scope{Smoke, Acceptance},
			},
			Test: WealthOnboardingFlowPan_**********,
		},
	}
}
