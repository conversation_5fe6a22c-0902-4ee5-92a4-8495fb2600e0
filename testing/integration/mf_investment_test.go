package integration

import (
	"context"
	"testing"

	"github.com/epifi/gamma/testing/integration/app"

	"github.com/epifi/be-common/pkg/owners"

	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/pkg/logger"
	accountsPb "github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/frontend/header"
	feTimelinePb "github.com/epifi/gamma/api/frontend/timeline"
	"github.com/epifi/gamma/scripts/crud/userdata"
)

func InvestmentMF_SIOrder_Success(t *testing.T) {
	defer Recover(t)
	t.Skip()
	a := require.New(t)
	ctx := context.Background()
	logger.Init(conf.Application.Environment)

	logger.Info(ctx, "GetPooledUser for creating a user")
	user, releaseUserFn := GetPooledUser(ctx, a)
	defer releaseUserFn()

	logger.Info(ctx, "GetActorIdForUser for getting ActorId of user")
	actorId, err := userdata.GetActorIdForUser(epifiDbV2, actorPgdb, user.Data.Phone)
	if err != nil {
		logger.ErrorNoCtx("error getting actor id for pooled user", zap.Error(err))
		return
	}

	logger.Info(ctx, "InitiateWealthOnb for doing wealth onboarding process for actor")
	wts := app.NewWealthOnbTestSuite(user, feWealthClient, feConsentClient, epifiDbV2, beWealthClient)
	wts.InitiateWealthOnb(ctx, a, actorId)

	logger.Info(ctx, "for adding fund in actor account balance")
	var pooledUsers []*app.PooledUserWithRelease
	pooledUsers = append(pooledUsers, &app.PooledUserWithRelease{User: user, ReleaseUserFn: releaseUserFn})
	var reqHs []*header.RequestHeader

	for _, pu := range pooledUsers {
		reqHs = append(reqHs, proto.Clone(pu.User.RequestHeader).(*header.RequestHeader))
	}

	pts := app.NewPayTestSuite(feTxnClient, nil, feSimulationClient, reqHs, nil, nil, nil)
	pts.AddFunds(ctx, t)

	logger.Info(ctx, "CreateSI for getting recurringPaymentId")
	authHeader := user.RequestHeader

	siTs := app.NewSITestSuite(feRecurringPaymentClient, beRecurringPaymentClient, authHeader, feTimelineClient)
	recurringPaymentId := siTs.CreateSI(ctx, t, &feTimelinePb.ResolveTimelineRequest_Account{
		Name:          "ICICI Prudential AMC Ltd",
		AccountNumber: "IPRUMFEPIFI",
		IfscCode:      "HDFC0000060",
		AccountType:   accountsPb.Type_CURRENT,
	})

	logger.Info(ctx, "invoking InitiateInvestmentMF")
	isrv := app.NewInvestmentTestSuite(OrderManagerClient, schedulerClient, PaymentHandlerClient, fileGenerationAttemptDao, fileGeneratorClient)
	isrv.InitiateInvestmentMF(ctx, a, actorId, recurringPaymentId)
	logger.Info(ctx, "working successfully")
}

func MfInvestmentTestCases() []*TestCase {
	return []*TestCase{
		{
			TestProperty: &TestProperty{
				Description: "This is all test for mf investment si order success flows",
				BU:          owners.BUSINESS_UNIT_WEALTH,
				Scope:       []Scope{Smoke, Acceptance},
			},
			Test: InvestmentMF_SIOrder_Success,
		},
	}
}
