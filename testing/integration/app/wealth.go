// nolint:unparam,depguard
package app

import (
	"context"
	"fmt"
	"strings"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/frontend/consent"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	wonbFePb "github.com/epifi/gamma/api/frontend/wealthonboarding"
	clientstate "github.com/epifi/gamma/api/frontend/wealthonboarding/clientstate"
	wonbBePb "github.com/epifi/gamma/api/wealthonboarding"
	userModel "github.com/epifi/gamma/user/dao/model"
	"github.com/golang/protobuf/proto"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"gorm.io/gorm"
)

type WealthOnbTestSuite struct {
	user            *PooledUser
	epifiDb         *gorm.DB
	feWealthClient  wonbFePb.WealthOnboardingClient
	feConsentClient consent.ConsentClient
	beWealthClient  wonbBePb.WealthOnboardingClient
}

func NewWealthOnbTestSuite(
	user *PooledUser,
	feWealthClient wonbFePb.WealthOnboardingClient,
	feConsentClient consent.ConsentClient,
	epifiDb *gorm.DB,
	beWealthClient wonbBePb.WealthOnboardingClient,
) *WealthOnbTestSuite {
	return &WealthOnbTestSuite{
		user:            user,
		epifiDb:         epifiDb,
		feWealthClient:  feWealthClient,
		feConsentClient: feConsentClient,
		beWealthClient:  beWealthClient,
	}
}

// PAN **********: Happy-flow PAN used to complete Wealth Onboarding end-to-end.
// Submitting this PAN enables the journey to collect PAN/DOB, handle nominee
// declaration, complete risk survey/consent, handle advisory agreement
// (skip/sign), and reach ONBOARDING_STATUS_COMPLETED.
func (w *WealthOnbTestSuite) InitiateWealthOnb(ctx context.Context, assert *require.Assertions, actorId string) {
	// Set app version code to "100000" to prevent app update flow from triggering
	// This ensures the wealth onboarding test runs without interruption from app version checks
	ctx = epificontext.CtxWithAppVersionCode(ctx, "100000")
	// Update user record for the happy flow
	updateErr := w.updateActorIdRecord(ctx, w.user.Data.Phone.NationalNumber, "**********", &commontypes.Name{FirstName: "Jolly", LastName: "Joseph"})
	assert.NoError(updateErr)
	requestHeader := proto.Clone(w.user.RequestHeader).(*header.RequestHeader)

	// Clear any existing consents to ensure we start with a clean slate
	// This validates that the wealth onboarding flow properly requests consent first
	if err := w.epifiDb.Exec("DELETE FROM consents WHERE actor_id = ? AND consent_type IN ('FI_WEALTH_TNC', 'EPIFI_WEALTH_INVESTMENT_RISK_PROFILE');", actorId).Error; err != nil {
		logger.ErrorNoCtx("error deleting wealth onboarding consents for actor", zap.Error(err))
		return
	}

	// Step 1: Accept Terms & Conditions consent
	logger.InfoNoCtx("Step 1: Accepting Terms & Conditions consent", zap.String("actorId", actorId))
	_, consentErr := w.feWealthClient.CollectDataFromCustomer(ctx, &wonbFePb.CollectDataFromCustomerRequest{
		Req:        requestHeader,
		WealthFlow: clientstate.WealthFlow_WEALTH_FLOW_INVESTMENT,
		ConsentIds: []string{"FI_WEALTH_TNC"},
	})
	if consentErr != nil {
		logger.ErrorNoCtx("Failed to accept Terms & Conditions", zap.Error(consentErr))
	} else {
		logger.InfoNoCtx("Successfully accepted Terms & Conditions")
	}

	// Get next step after consent
	nextOnbStepRes, nextOnbStepErr := w.feWealthClient.GetNextOnboardingStep(ctx, &wonbFePb.GetNextOnboardingStatusRequest{
		Req:        requestHeader,
		WealthFlow: clientstate.WealthFlow_WEALTH_FLOW_INVESTMENT,
	})
	assert.NoError(epifigrpc.RPCError(nextOnbStepRes, nextOnbStepErr))
	logger.InfoNoCtx("nextOnbStepRes after Terms & Conditions", zap.Any("nextOnbStepRes", nextOnbStepRes.GetNextStep()))

	// Step 2: Submit PAN/DOB data
	if nextOnbStepRes.GetNextStep().GetScreen() == deeplinkPb.Screen_WEALTH_ONBOARDING_PAN_DOB_SCREEN {
		logger.InfoNoCtx("Step 2: On PAN/DOB screen, submitting PAN and DOB data", zap.String("actorId", actorId))

		_, panDobErr := w.feWealthClient.CollectDataFromCustomer(ctx, &wonbFePb.CollectDataFromCustomerRequest{
			Req:               requestHeader,
			WealthFlow:        clientstate.WealthFlow_WEALTH_FLOW_INVESTMENT,
			UserSubmittedPan:  "**********",
			UserSubmittedDob:  &date.Date{Year: 1994, Month: 1, Day: 1},
			UserSubmittedName: "jolly joseph byomkesh bakshi",
		})

		if panDobErr != nil {
			logger.ErrorNoCtx("Failed to submit PAN/DOB data", zap.Error(panDobErr))
		} else {
			logger.InfoNoCtx("Successfully submitted PAN/DOB data")
		}

		// Get next step after PAN/DOB submission
		nextOnbStepRes, nextOnbStepErr = w.feWealthClient.GetNextOnboardingStep(ctx, &wonbFePb.GetNextOnboardingStatusRequest{
			Req:        requestHeader,
			WealthFlow: clientstate.WealthFlow_WEALTH_FLOW_INVESTMENT,
		})
		assert.NoError(epifigrpc.RPCError(nextOnbStepRes, nextOnbStepErr))
		logger.InfoNoCtx("nextOnbStepRes after PAN/DOB submission", zap.Any("nextOnbStepRes", nextOnbStepRes.GetNextStep()))
	}

	// Step 3: Handle nominee declaration (skip with OTP)
	if nextOnbStepRes.GetNextStep().GetScreen() == deeplinkPb.Screen_WEALTH_ONBOARDING_CAPTURE_MISSING_DATA_SCREEN {
		logger.InfoNoCtx("Step 3: On missing data screen, handling nominee declaration", zap.String("actorId", actorId))

		// Generate OTP for nominee declaration with choice FALSE
		generateOTPRes, generateOTPErr := w.feWealthClient.GenerateOTP(ctx, &wonbFePb.GenerateOTPRequest{
			Req: requestHeader,
			OtpOptions: &wonbFePb.GenerateOTPRequest_NomineeDeclarationOtpOption{
				NomineeDeclarationOtpOption: &wonbFePb.NomineeDeclarationOTPOption{
					Choice: commontypes.BooleanEnum_FALSE, // Skip nominee declaration
				},
			},
		})

		if generateOTPErr != nil {
			logger.ErrorNoCtx("Failed to generate OTP for nominee declaration", zap.Error(generateOTPErr))
		} else {
			logger.InfoNoCtx("Successfully generated OTP for nominee declaration", zap.String("token", generateOTPRes.GetToken()), zap.String("phoneNumber", generateOTPRes.GetPhoneNumber()))

			// Extract OTP from debug message or use default
			otp := "123456" // Default fallback OTP
			if generateOTPRes.GetStatus() != nil && generateOTPRes.GetStatus().GetDebugMessage() != "" {
				debugMessage := generateOTPRes.GetStatus().GetDebugMessage()
				// Try to extract OTP from debug message (format: "OTP: 123456")
				if len(debugMessage) >= 7 && debugMessage[:4] == "OTP:" {
					otp = debugMessage[5:]       // Skip "OTP: " prefix
					otp = strings.TrimSpace(otp) // Remove any whitespace
					logger.InfoNoCtx("Successfully extracted OTP from debug message", zap.String("otp", otp))
				}
			}
			logger.InfoNoCtx("Using OTP for nominee declaration", zap.String("otp", otp))

			// Submit nominee declaration details with OTP
			_, submitErr := w.feWealthClient.CollectDataFromCustomer(ctx, &wonbFePb.CollectDataFromCustomerRequest{
				Req:        requestHeader,
				WealthFlow: clientstate.WealthFlow_WEALTH_FLOW_INVESTMENT,
				NomineeDeclarationDetails: &wonbFePb.NomineeDeclarationDetails{
					Choice: commontypes.BooleanEnum_FALSE, // Skip nominee declaration
					Token:  generateOTPRes.GetToken(),
					Otp:    otp,
				},
			})

			if submitErr != nil {
				logger.ErrorNoCtx("Failed to submit nominee declaration with OTP", zap.Error(submitErr))
			} else {
				logger.InfoNoCtx("Successfully submitted nominee declaration with OTP")
			}
		}

		// Get next step after nominee declaration
		nextOnbStepRes, nextOnbStepErr = w.feWealthClient.GetNextOnboardingStep(ctx, &wonbFePb.GetNextOnboardingStatusRequest{
			Req:        requestHeader,
			WealthFlow: clientstate.WealthFlow_WEALTH_FLOW_INVESTMENT,
		})
		assert.NoError(epifigrpc.RPCError(nextOnbStepRes, nextOnbStepErr))
		logger.InfoNoCtx("nextOnbStepRes after nominee declaration", zap.Any("nextOnbStepRes", nextOnbStepRes.GetNextStep()))
	}

	// Step 4: Complete investment risk survey
	if nextOnbStepRes.GetNextStep().GetScreen() == deeplinkPb.Screen_WEALTH_ONBOARDING_INVESTMENT_RISK_SURVEY_SCREEN {
		logger.InfoNoCtx("Step 4: On investment risk survey screen, completing the survey", zap.String("actorId", actorId))

		_, riskSurveyErr := w.feWealthClient.CollectDataFromCustomer(ctx, &wonbFePb.CollectDataFromCustomerRequest{
			Req:        requestHeader,
			WealthFlow: clientstate.WealthFlow_WEALTH_FLOW_INVESTMENT,
			RiskSurveyAnswerPayload: &wonbFePb.RiskSurveyAnswerPayload{
				QuestionAndSelectedAnswers: []*wonbFePb.QuestionAndSelectedAnswer{
					{
						QuestionType:         "RISK_SURVEY_QUESTION_TYPE_INVESTMENT_DURATION",
						SelectedAnswerValues: []string{"INVESTMENT_DURATION_LONG_TERM"},
					},
					{
						QuestionType:         "RISK_SURVEY_QUESTION_TYPE_EXISTING_INVESTMENTS",
						SelectedAnswerValues: []string{"INVESTMENT_INSTRUMENT_COMBO_STOCKS_EQUITY_MF"},
					},
					{
						QuestionType:         "RISK_SURVEY_QUESTION_TYPE_NET_WORTH",
						SelectedAnswerValues: []string{"NET_WORTH_POSITIVE"},
					},
					{
						QuestionType:         "RISK_SURVEY_QUESTION_TYPE_RISK_TOLERANCE",
						SelectedAnswerValues: []string{"RISK_TOLERANCE_VERY_HIGH"},
					},
					{
						QuestionType:         "RISK_SURVEY_QUESTION_TYPE_EXPECTED_RETURNS",
						SelectedAnswerValues: []string{"EXPECTED_RETURNS_AGGRESSIVE"},
					},
				},
			},
		})

		if riskSurveyErr != nil {
			logger.ErrorNoCtx("Failed to complete risk survey", zap.Error(riskSurveyErr))
		} else {
			logger.InfoNoCtx("Successfully completed risk survey")
		}

		// Get next step after risk survey
		nextOnbStepRes, nextOnbStepErr = w.feWealthClient.GetNextOnboardingStep(ctx, &wonbFePb.GetNextOnboardingStatusRequest{
			Req:        requestHeader,
			WealthFlow: clientstate.WealthFlow_WEALTH_FLOW_INVESTMENT,
		})
		assert.NoError(epifigrpc.RPCError(nextOnbStepRes, nextOnbStepErr))
		logger.InfoNoCtx("nextOnbStepRes after risk survey", zap.Any("nextOnbStepRes", nextOnbStepRes.GetNextStep()))
	}

	// Step 5: Accept risk profile consent
	if nextOnbStepRes.GetNextStep().GetScreen() == deeplinkPb.Screen_WEALTH_ONBOARDING_INVESTMENT_RISK_PROFILE_SCREEN {
		logger.InfoNoCtx("Step 5: On investment risk profile screen, accepting consent", zap.String("actorId", actorId))

		_, consentErr := w.feWealthClient.CollectDataFromCustomer(ctx, &wonbFePb.CollectDataFromCustomerRequest{
			Req:        requestHeader,
			WealthFlow: clientstate.WealthFlow_WEALTH_FLOW_INVESTMENT,
			ConsentIds: []string{"EPIFI_WEALTH_INVESTMENT_RISK_PROFILE"},
		})

		if consentErr != nil {
			logger.ErrorNoCtx("Failed to accept risk profile consent", zap.Error(consentErr))
		} else {
			logger.InfoNoCtx("Successfully accepted risk profile consent")
		}

		// Get next step after consent
		nextOnbStepRes, nextOnbStepErr = w.feWealthClient.GetNextOnboardingStep(ctx, &wonbFePb.GetNextOnboardingStatusRequest{
			Req:        requestHeader,
			WealthFlow: clientstate.WealthFlow_WEALTH_FLOW_INVESTMENT,
		})
		assert.NoError(epifigrpc.RPCError(nextOnbStepRes, nextOnbStepErr))
		logger.InfoNoCtx("nextOnbStepRes after risk profile consent", zap.Any("nextOnbStepRes", nextOnbStepRes.GetNextStep()))
	}

	// Poll for completion, as some steps complete asynchronously
	for i := 0; i < 30 && nextOnbStepRes.GetOnboardingStatus() != wonbFePb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED; i++ {
		time.Sleep(2 * time.Second)
		nextOnbStepRes, nextOnbStepErr = w.feWealthClient.GetNextOnboardingStep(ctx, &wonbFePb.GetNextOnboardingStatusRequest{
			Req:        requestHeader,
			WealthFlow: clientstate.WealthFlow_WEALTH_FLOW_INVESTMENT,
		})
		assert.NoError(epifigrpc.RPCError(nextOnbStepRes, nextOnbStepErr))
		logger.InfoNoCtx("Polling for completion",
			zap.Int("attempt", i+1),
			zap.String("onboardingStatus", nextOnbStepRes.GetOnboardingStatus().String()))
	}

	logger.InfoNoCtx("Final onboarding status before ConfirmComplianceData",
		zap.String("onboardingStatus", nextOnbStepRes.GetOnboardingStatus().String()))

	// Confirm compliance data to complete the flow
	ccRes, ccErr := w.feWealthClient.ConfirmComplianceData(ctx, &wonbFePb.ConfirmComplianceDataRequest{
		Req:        requestHeader,
		WealthFlow: clientstate.WealthFlow_WEALTH_FLOW_INVESTMENT,
	})

	// Log the response details for debugging
	if ccRes != nil {
		logger.InfoNoCtx("ConfirmComplianceData response",
			zap.Uint32("statusCode", ccRes.GetStatus().GetCode()),
			zap.String("shortMessage", ccRes.GetStatus().GetShortMessage()),
			zap.String("debugMessage", ccRes.GetStatus().GetDebugMessage()))
	}
	if ccErr != nil {
		logger.ErrorNoCtx("ConfirmComplianceData gRPC error", zap.Error(ccErr))
	}

	// Check if the response has a non-success status
	if ccRes != nil && !ccRes.GetStatus().IsSuccess() {
		logger.ErrorNoCtx("ConfirmComplianceData returned non-success status",
			zap.Uint32("statusCode", ccRes.GetStatus().GetCode()),
			zap.String("shortMessage", ccRes.GetStatus().GetShortMessage()))
	}

	assert.NoError(epifigrpc.RPCError(ccRes, ccErr))

	// Get final status
	nextOnbStepRes, nextOnbStepErr = w.feWealthClient.GetNextOnboardingStep(ctx, &wonbFePb.GetNextOnboardingStatusRequest{
		Req:        requestHeader,
		WealthFlow: clientstate.WealthFlow_WEALTH_FLOW_INVESTMENT,
	})
	assert.NoError(epifigrpc.RPCError(nextOnbStepRes, nextOnbStepErr))
	logger.InfoNoCtx("final onboarding status", zap.String("status", nextOnbStepRes.GetOnboardingStatus().String()))

	// Assert completion
	assert.Equal(wonbFePb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED.String(), nextOnbStepRes.GetOnboardingStatus().String())
}

func (w *WealthOnbTestSuite) updateActorIdRecord(ctx context.Context, phoneNumber uint64, pan string, name *commontypes.Name) error {

	userRes := &userModel.User{}
	queryUser := "select * from users where computed_phone_number = '%v' limit 1"
	computedPhNumber := fmt.Sprintf("91%v", phoneNumber)
	exactQuery := fmt.Sprintf(queryUser, computedPhNumber)
	err := w.epifiDb.Raw(exactQuery).Scan(&userRes).Error
	if err != nil {
		return errors.Wrap(err, "error while fetching user by phone number")
	}

	userRes.Profile.PAN = pan
	result := w.epifiDb.Model(userRes).
		Where("deleted_at_unix = ?", 0).
		Updates(userRes)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// PAN **********: Validation/negative-flow PAN used to assert PAN/DOB errors.
// Submitting this PAN is expected to show validation errors at the PAN/DOB step
// (inline or error screen), and the onboarding should NOT complete.
func (w *WealthOnbTestSuite) WealthOnboardingFor**********(ctx context.Context, assert *require.Assertions, actorId string) {
	// Set app version code to "100000" to prevent app update flow from triggering
	// This ensures the wealth onboarding test runs without interruption from app version checks
	ctx = epificontext.CtxWithAppVersionCode(ctx, "100000")
	// Update user record for the happy flow
	updateErr := w.updateActorIdRecord(ctx, w.user.Data.Phone.NationalNumber, "**********", &commontypes.Name{FirstName: "Jolly", LastName: "Joseph"})
	assert.NoError(updateErr)
	requestHeader := proto.Clone(w.user.RequestHeader).(*header.RequestHeader)

	// Clear any existing consents to ensure we start with a clean slate
	// This validates that the wealth onboarding flow properly requests consent first
	if err := w.epifiDb.Exec("DELETE FROM consents WHERE actor_id = ? AND consent_type IN ('FI_WEALTH_TNC', 'EPIFI_WEALTH_INVESTMENT_RISK_PROFILE');", actorId).Error; err != nil {
		logger.ErrorNoCtx("error deleting wealth onboarding consents for actor", zap.Error(err))
		return
	}
	logger.InfoNoCtx("Step 1: Accepting Terms & Conditions consent", zap.String("actorId", actorId))
	_, consentErr := w.feWealthClient.CollectDataFromCustomer(ctx, &wonbFePb.CollectDataFromCustomerRequest{
		Req:        requestHeader,
		WealthFlow: clientstate.WealthFlow_WEALTH_FLOW_INVESTMENT,
		ConsentIds: []string{"FI_WEALTH_TNC"},
	})
	if consentErr != nil {
		logger.ErrorNoCtx("Failed to accept Terms & Conditions", zap.Error(consentErr))
	} else {
		logger.InfoNoCtx("Successfully accepted Terms & Conditions")
	}
	// Get next step after consent
	nextOnbStepRes, nextOnbStepErr := w.feWealthClient.GetNextOnboardingStep(ctx, &wonbFePb.GetNextOnboardingStatusRequest{
		Req:        requestHeader,
		WealthFlow: clientstate.WealthFlow_WEALTH_FLOW_INVESTMENT,
	})
	// Add debug logging before the assertion
	logger.InfoNoCtx("Debug: GetNextOnboardingStep response",
		zap.Any("response", nextOnbStepRes),
		zap.Error(nextOnbStepErr),
		zap.String("actorId", actorId))
	assert.NoError(epifigrpc.RPCError(nextOnbStepRes, nextOnbStepErr))
	logger.InfoNoCtx("nextOnbStepRes after Terms & Conditions", zap.Any("nextOnbStepRes", nextOnbStepRes.GetNextStep()))

	// Step 2: Submit PAN/DOB data
	logger.InfoNoCtx("Step 2: On PAN/DOB screen, submitting PAN and DOB data", zap.String("actorId", actorId))

	req := &wonbFePb.CollectDataFromCustomerRequest{
		Req:               requestHeader,
		WealthFlow:        clientstate.WealthFlow_WEALTH_FLOW_INVESTMENT,
		UserSubmittedPan:  "**********",
		UserSubmittedDob:  &date.Date{Year: 1994, Month: 1, Day: 1},
		UserSubmittedName: "jolly joseph byomkesh bakshi",
	}

	// Make the gRPC call
	resp, err := w.feWealthClient.CollectDataFromCustomer(ctx, req)

	// Add response logging
	logger.InfoNoCtx("Debug: PAN/DOB submission response",
		zap.Any("response", resp),
		zap.Error(err),
		zap.String("actorId", actorId))

	assert.NoError(err)
	assert.NotNil(resp)

	// Assert key fields in the response using getters for safety
	assert.Equal("Success", resp.GetStatus().GetShortMessage())

	// Validate that either inline error (Android) or error screen (iOS) appears
	screen := resp.GetNextStep().GetScreen()
	switch screen {
	case deeplinkPb.Screen_WEALTH_ONBOARDING_ERROR_SCREEN:
		// iOS behavior: bottom sheet error screen
		logger.InfoNoCtx("iOS behavior: Bottom sheet error screen shown", zap.String("actorId", actorId))
	case deeplinkPb.Screen_WEALTH_ONBOARDING_PAN_DOB_SCREEN:
		// Android behavior: inline validation error on same screen
		logger.InfoNoCtx("Android behavior: Inline validation error shown", zap.String("actorId", actorId))
	default:
		// Neither error screen nor inline error appeared - test should fail
		assert.Fail("No error validation shown",
			"Expected either error screen (iOS) or PAN/DOB screen with inline error (Android), but got: %s",
			screen.String())
	}

	assert.Equal(wonbFePb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS, resp.GetOnboardingStatus())

	// Log the error screen details for verification
	screenForLogging := resp.GetNextStep().GetScreen()
	switch screenForLogging {
	case deeplinkPb.Screen_WEALTH_ONBOARDING_ERROR_SCREEN:
		logger.InfoNoCtx("Error screen details (iOS - Bottom sheet)",
			zap.String("screen", screenForLogging.String()),
			zap.String("onboardingStatus", resp.GetOnboardingStatus().String()),
			zap.String("actorId", actorId))
	case deeplinkPb.Screen_WEALTH_ONBOARDING_PAN_DOB_SCREEN:
		logger.InfoNoCtx("Inline error details (Android - Same screen)",
			zap.String("screen", screenForLogging.String()),
			zap.String("onboardingStatus", resp.GetOnboardingStatus().String()),
			zap.String("actorId", actorId))
	default:
		logger.InfoNoCtx("Unexpected screen details",
			zap.String("screen", screenForLogging.String()),
			zap.String("onboardingStatus", resp.GetOnboardingStatus().String()),
			zap.String("actorId", actorId))
	}

	// Get final onboarding status to confirm flow completion
	finalStatusRes, finalStatusErr := w.feWealthClient.GetNextOnboardingStep(ctx, &wonbFePb.GetNextOnboardingStatusRequest{
		Req:        requestHeader,
		WealthFlow: clientstate.WealthFlow_WEALTH_FLOW_INVESTMENT,
	})
	assert.NoError(epifigrpc.RPCError(finalStatusRes, finalStatusErr))

	logger.InfoNoCtx("Final onboarding status after error screen",
		zap.String("finalStatus", finalStatusRes.GetOnboardingStatus().String()),
		zap.String("actorId", actorId))

	// Assert that the flow is in the expected final state
	assert.Equal(wonbFePb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS, finalStatusRes.GetOnboardingStatus())

}
