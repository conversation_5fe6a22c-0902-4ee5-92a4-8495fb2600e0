Application:
  Environment: "prod"
  Namespace: "prod-card"
  TaskQueue: "prod-card-task-queue"
  RedisConfig:
    IsSecureRedis: true
    Options:
      Addr: "redis-10246.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:10246"
      Password: ""
    AuthDetails:
      SecretPath: "prod/redis/cards/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: firefly
    HystrixCommand:
      CommandName: "firefly_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 10000
        ExecutionTimeout: 1s
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 50
        SleepWindow: 15s
        FallbackMaxConcurrency: 10000

DebitCardPgdb:
  Username: "debit_card_pgdb_dev_user"
  Name: "debit_card_pgdb"
  DbType: "PGDB"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 10
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  AppName: "card"
  SecretName: "prod/rds/epifimetis/debit_card_pgdb_dev_user"
  GormV2:
    # TODO(abhishekprakash) Mark log level as WARN or DEBUG once this is stable
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

DbConfigMap:
  EPIFI_TECH:
    DbType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 10
    MaxIdleConn: 3
    MaxConnTtl: "30m"
    AppName: "card-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      EnableMultiDBSupport: true
      DBResolverList:
        - TableName: [ ]
          Alias: "debit_card_pgdb_conn"
          DbDsn:
            DbType: "PGDB"
            AppName: "card-worker"
            StatementTimeout: 1m
            Name: "debit_card_pgdb"
            EnableDebug: true
            SSLMode: "verify-full"
            SSLRootCert: "prod/rds/rds-ca-root-2061"
            SecretName: "prod/rds/epifimetis/debit_card_pgdb_dev_user"

UsecaseDbConfigMap:
  EPIFI_TECH:
    DbType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 10
    MaxIdleConn: 3
    MaxConnTtl: "30m"
    AppName: "card-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms
      EnableMultiDBSupport: true
      DBResolverList:
        - TableName: [ ]
          Alias: "debit_card_pgdb_conn"
          DbDsn:
            DbType: "PGDB"
            AppName: "card-worker"
            StatementTimeout: 1m
            Name: "debit_card_pgdb"
            EnableDebug: true
            SSLMode: "verify-full"
            SSLRootCert: "prod/rds/rds-ca-root-2061"
            SecretName: "prod/rds/epifimetis/debit_card_pgdb_dev_user"

WorkflowUpdatePublisher:
  TopicName: "prod-celestial-workflow-update-topic"

WorkflowParamsList:
  - WorkflowName: "RenewCard"
    ActivityParamsList:
      - ActivityName: "BlockCard"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "2s"
            MaxAttempts: 5
      - ActivityName: "CreateNewCard"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
          # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
          ExponentialBackOff:
            BaseInterval: "3.5s"
            MaxInterval: "30m"
            BackoffCoefficient: 1.5
            MaxAttempts: 24
      - ActivityName: "PollCardCreationStatus"
        ScheduleToCloseTimeout: "26h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 26 hours with max cap between retries at 2h
          # Retry interval - 3.5s 10.5s 24.5s 52.5s 1.8m 3.7m 7.4m 14.8m 29.8m 59.7m 2h 4h 6h
          ExponentialBackOff:
            BaseInterval: "3.5s"
            MaxInterval: "2h"
            BackoffCoefficient: 2.0
            MaxAttempts: 24
      - ActivityName: "InitiatePhysicalCardDispatch"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
          # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
          ExponentialBackOff:
            BaseInterval: "3.5s"
            MaxInterval: "30m"
            BackoffCoefficient: 1.5
            MaxAttempts: 24
      - ActivityName: "PollPhysicalCardDispatchStatus"
        ScheduleToCloseTimeout: "26h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 26 hours with max cap between retries at 2h
          # Retry interval - 3.5s 10.5s 24.5s 52.5s 1.8m 3.7m 7.4m 14.8m 29.8m 59.7m 2h 4h 6h
          ExponentialBackOff:
            BaseInterval: "3.5s"
            MaxInterval: "2h"
            BackoffCoefficient: 2.0
            MaxAttempts: 24
      - ActivityName: "UpdateCardRequest"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
          # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
          ExponentialBackOff:
            BaseInterval: "3.5s"
            MaxInterval: "30m"
            BackoffCoefficient: 1.5
            MaxAttempts: 24
  - WorkflowName: "TrackCardDelivery"
    ActivityParamsList:
      - ActivityName: "TrackShipment"
        ScheduleToCloseTimeout: "360h" # 15 days
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "4h"
            MaxAttempts: 90
  - WorkflowName: "OrderPhysicalCardWithCharges"
    ActivityParamsList:
      - ActivityName: "InitiateShippingAddressUpdateAndDispatchPhysicalCard"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 34m8s 1h8m16s 2h16m32s
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 68
      - ActivityName: "UpdatePhysicalCardDispatchRequest"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 34m8s 1h8m16s 2h16m32s
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 68
  - WorkflowName: "OrderPhysicalCardWithChargesV1"
    ActivityParamsList:
      - ActivityName: "PhysicalCardDispatch"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 34m8s 1h8m16s 2h16m32s
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 68
      - ActivityName: "UpdatePhysicalCardDispatchRequest"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 34m8s 1h8m16s 2h16m32s
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 68
      - ActivityName: "CheckPaymentStatus"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 34m8s 1h8m16s 2h16m32s
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 68
      - ActivityName: "CreateShippingPreference"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 34m8s 1h8m16s 2h16m32s
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 68
      - ActivityName: "UpdateShippingAddress"
        ScheduleToCloseTimeout: "168h" # 7 days
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 7 days with max cap between retries at 3h
          # First exponential retry interval 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 34m8s 1h8m16s 2h16m32s
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 68
  - WorkflowName: "OrderPhysicalCardWithChargesV2"
    ActivityParamsList:
      - ActivityName: "InitiateChargesCollection"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "60s"
        RetryParams:
          Hybrid:
            RetryStrategy1:
              RegularInterval:
                Interval: "10s"
                MaxAttempts: 50
            RetryStrategy2:
              ExponentialBackOff:
                BaseInterval: "4s"
                MaxInterval: "30m"
                BackoffCoefficient: 2.0
                MaxAttempts: 40
            MaxAttempts: 90
            CutOff: 50
      - ActivityName: "PollChargesCollectionStatus"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "60s"
        RetryParams:
          Hybrid:
            RetryStrategy1:
              RegularInterval:
                Interval: "10s"
                MaxAttempts: 50
            RetryStrategy2:
              ExponentialBackOff:
                BaseInterval: "4s"
                MaxInterval: "30m"
                BackoffCoefficient: 2.0
                MaxAttempts: 40
            MaxAttempts: 90
            CutOff: 50
      - ActivityName: "CollectDebitCardCharges"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "60s"
        RetryParams:
          Hybrid:
            RetryStrategy1:
              RegularInterval:
                Interval: "10s"
                MaxAttempts: 50
            RetryStrategy2:
              ExponentialBackOff:
                BaseInterval: "4s"
                MaxInterval: "30m"
                BackoffCoefficient: 2.0
                MaxAttempts: 40
            MaxAttempts: 90
            CutOff: 50
      - ActivityName: "PhysicalCardDispatch"
        ScheduleToCloseTimeout: "26h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 26 hours with max cap between retries at 2h
          # Retry interval - 3.5s 10.5s 24.5s 52.5s 1.8m 3.7m 7.4m 14.8m 29.8m 59.7m 2h 4h 6h
          ExponentialBackOff:
            BaseInterval: "3.5s"
            MaxInterval: "2h"
            BackoffCoefficient: 2.0
            MaxAttempts: 24
      - ActivityName: "UpdatePhysicalCardDispatchRequest"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
          # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
          ExponentialBackOff:
            BaseInterval: "3.5s"
            MaxInterval: "30m"
            BackoffCoefficient: 1.5
            MaxAttempts: 24
      - ActivityName: "CheckPaymentStatus"
        ScheduleToCloseTimeout: "26h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 26 hours with max cap between retries at 2h
          # Retry interval - 3.5s 10.5s 24.5s 52.5s 1.8m 3.7m 7.4m 14.8m 29.8m 59.7m 2h 4h 6h
          ExponentialBackOff:
            BaseInterval: "3.5s"
            MaxInterval: "2h"
            BackoffCoefficient: 2.0
            MaxAttempts: 24
      - ActivityName: "CreateShippingPreference"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 5 hours with max cap between retries at 30min
          # Retry interval - 3.5s 8s 16s 28s 46s 1m12s 1m52s 2m52s 4m22s 6m36s 10m 15m 23m 30m0s 1h 1h30m 2h 2h30m 3h 3h30m 4h 4h30m 5h
          ExponentialBackOff:
            BaseInterval: "3.5s"
            MaxInterval: "30m"
            BackoffCoefficient: 1.5
            MaxAttempts: 24
      - ActivityName: "UpdateShippingAddress"
        ScheduleToCloseTimeout: "26h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for 26 hours with max cap between retries at 2h
          # Retry interval - 3.5s 10.5s 24.5s 52.5s 1.8m 3.7m 7.4m 14.8m 29.8m 59.7m 2h 4h 6h
          ExponentialBackOff:
            BaseInterval: "3.5s"
            MaxInterval: "2h"
            BackoffCoefficient: 2.0
            MaxAttempts: 24
  - WorkflowName: "ProcessDcForexTxnRefund"
    ActivityParamsList:
      - ActivityName: "EnrichForexTxnRecord"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 30
      - ActivityName: "ValidateTxnForRefund"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 30
      - ActivityName: "CalculateRefundAmount"
        ScheduleToCloseTimeout: "30m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 30
  - WorkflowName: "ProcessAmcEligibleUsersParent"
    ChildWorkflowParamsList:
      - WorkflowName: "ProcessAmcEligibleUsersChild"
        WorkflowExecutionTimeout: "72h"
        WorkflowRunTimeout: "72h"
        ParentClosePolicy: 1 #Terminate if parent finishes
        RetryParams:
          # Exponential retry strategy that runs for ~20min with max cap between retries at 10min
          # Retry interval - 10s 20s 40s 1min20s 2min40s 5min20s 10min40s
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 20
    ActivityParamsList:
      - ActivityName: "GetAmcUserBase"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "180s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 20
      - ActivityName: "PostProcessAmcFile"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 20
  - WorkflowName: "ProcessAmcEligibleUsersChild"
    ActivityParamsList:
      - ActivityName: "GetAmcBatchParams"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "20s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 20
      - ActivityName: "ProcessAmcUserBatch"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5h"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 100
        RateLimit:
          Rate: 1
          Period: 10s
      - ActivityName: "CreateAndUploadFinalAmcFile"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "10m"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 20

RudderStack:
  IntervalInSec: 10
  BatchSize: 20
  Verbose: false

Secrets:
  Ids:
    GoogleCloudProfilingServiceAccountKey: "prod/gcloud/profiling-service-account-key"
    RudderWriteKey: "prod/rudder/internal-writekey"
    TemporalCodecAesKey: "prod/temporal/codec-encryption-key"
    SlackOauthToken: "prod/card/slack-oauth-token"

Profiling:
  StackDriverProfiling:
    ProjectId: "epifi-prod"
    EnableStackDriver: true
  PyroscopeProfiling:
    EnablePyroscope: false
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

UsePgdbConnForCardsDb: true

CardNudgeConfig:
  CARD_NUDGE_TYPE_ORDER_PHYSICAL_CARD:
    NudgeId: "90b2ec11-5913-4601-be61-2d066230f2a3"
    IsEnabled: false
  CARD_NUDGE_TYPE_TRACK_CARD_DELIVERY:
    NudgeId: "104edfdd-3b26-487e-9aeb-c090da6d565c"
    IsEnabled: true

CardsRedisStore:
  IsSecureRedis: true
  Options:
    Addr: "redis-10246.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:10246"
    Password: ""
  AuthDetails:
    SecretPath: "prod/redis/cards/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"
  ClientName: card
  HystrixCommand:
    CommandName: "card_redis_circuit_breaker_command:"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 10000
      ExecutionTimeout: 1s
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 50
      SleepWindow: 15s
      FallbackMaxConcurrency: 10000

CacheConfigs:
  CardCacheConfig:
    Prefix: "card:"
    IsCachingEnabled: true
    CacheTTl: "10m"

DcDocsBucketName: "epifi-prod-debit-card-docs"
AnalyticsDocsBucketName: "epifi-ba-metrics"

AmcConfig:
  BatchEligibleUsersAmcFileFormat: "amc/{file_gen_date}/%d/success_file_%d.csv"
  BatchFailedUsersAmcFileFormat: "amc/{file_gen_date}/%d/failure_file_%d.csv"
  AmcCharge:
    CurrencyCode: "INR"
    Units: 199
    Nanos: 0
  ParentWorkflowBatchSize: 10000
  ChildWorkflowBatchSize: 100
  RecipientEmailId: "<EMAIL>"
  SlackChannelId: "C05S8DKUDMG"
  ChargePrefixFormat: "CHRGE%d"
  Debit: "DEBIT"
  NarrationPrefixFormat: "CHRG/DEBIT CARD AMC/XXXXXX%s"
  Ocdem: "OCDEM"
  BatchJob: "VISADCARDCHARGE"
  AmcDateLayout: "02/01/2006"
  UseDocsClientForSourceFile: true
