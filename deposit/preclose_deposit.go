package deposit

import (
	"context"
	"errors"
	"fmt"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epifierrors"
	vendorPkg "github.com/epifi/gamma/pkg/vendors/federal"

	"github.com/epifi/be-common/pkg/datetime"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/deposit/metrics"

	"github.com/epifi/be-common/pkg/epificontext"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/accounts"
	depositPb "github.com/epifi/gamma/api/deposit"
	orderPb "github.com/epifi/gamma/api/order"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/deposit/config"
	depositEvents "github.com/epifi/gamma/deposit/events"
)

// On receiving preclose deposit notification perform below steps:
//  1. Mark deposit account to 'CLOSE' state if and only if account is in 'CREATED' state.
//     (In case of preclose deposit, we get this inbound notification but deposit account will be marked to preclose by callback for pre-close deposit account.)
//  2. Close deposit account's PI.
//  3. Create order with txn from deposit account PI to saving account PI
//
// NOTE: We don't update the deposit request's state to CLOSED in ProcessPrecloseDepositInboundTxn as this method will
// be called even when deposit account is auto closed from vendor's end in which case no corresponding deposit request
// will exist.
// Also, we update the deposit account's state to CLOSED only when it's state is CREATED. This will happen when deposit
// account was auto closed from vendor's end.
// nolint: funlen
func (s *Service) ProcessPrecloseDepositInboundTxn(ctx context.Context, req *depositPb.ProcessPrecloseDepositInboundTxnRequest) (*depositPb.ProcessPrecloseDepositInboundTxnResponse, error) {
	var (
		res            = &depositPb.ProcessPrecloseDepositInboundTxnResponse{}
		actor          *types.Actor
		depositAccount *depositPb.DepositAccount
		err            error
	)
	logger.Info(ctx, "Received process preclose deposit inbound txn request",
		zap.String(logger.ACCOUNT_NUMBER, req.GetDepositAccountNumber()),
	)

	depositAccountIfscCode, err := vendorPkg.GetIfscCodeForVendor(req.GetPartnerBank())
	if err != nil {
		logger.Error(ctx, "failed to get ifsc code",
			zap.String("vendor", req.GetPartnerBank().String()),
			zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// 1. Mark deposit account to 'CLOSE' state if and only if account is in 'CREATED' state.
	txnErr := s.idempotentTxnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		lockCtx := gormctxv2.NewWithLockKey(txnCtx)

		// Get deposit account requested for pre-closing with row lock
		depositAccount, err = s.DepositAccountDao.GetByAccountNumberAndIfsc(lockCtx, req.GetDepositAccountNumber(), depositAccountIfscCode)
		if err != nil {
			return fmt.Errorf("failed to get deposit account with number: %s %w", req.GetDepositAccountNumber(), err)
		}

		// Update deposit account's state to close if deposit account state is `CREATED` or `PRECLOSE_PENDING`
		// Deposit account will only be marked closed via inbound txn as there is no callback/status check for auto closure
		// Also update the maturity amount of deposit account, this will only be useful for SDs as for FDs we have the
		// maturity amount during creation itself. The maturity amount for SDs will be equal to the closure txn amount.
		if depositAccount.State == depositPb.DepositState_CREATED || depositAccount.State == depositPb.DepositState_PRECLOSE_PENDING {
			if req.IsAutoClose {
				depositAccount.State = depositPb.DepositState_CLOSED
			} else {
				depositAccount.State = depositPb.DepositState_PRECLOSED
			}
			depositAccount.MaturityAmount = req.GetTransactionDetails().GetAmount()
			depositAccount.IsAddFundsAllowed = false
			updateErr := s.DepositAccountDao.UpdateById(lockCtx, depositAccount, []depositPb.DepositAccountFieldMask{
				depositPb.DepositAccountFieldMask_DEPOSIT_ACCOUNT_STATE,
				depositPb.DepositAccountFieldMask_MATURITY_AMOUNT,
			})
			if updateErr != nil {
				return fmt.Errorf("error in updating deposit account state to close, depositAccountId: %s %w",
					depositAccount.Id, updateErr)
			}

			updateIsAddFundsAllowedErr := s.DepositAccountDao.UpdateIsAddFundsAllowed(lockCtx, depositAccount.Id, depositAccount.IsAddFundsAllowed)
			if updateIsAddFundsAllowedErr != nil {
				return fmt.Errorf("error in updating is_add_funds_allowed to false, depositAccountId: %s %w",
					depositAccount.Id, updateIsAddFundsAllowedErr)
			}
		}

		return nil
	})
	if txnErr != nil {
		logger.Error(ctx, "failed to mark deposit account as closed", zap.Error(txnErr))
		// Returning RNF so that callers can handle accordingly. Though, letting the log line above remain to debug this further.
		if errors.Is(txnErr, epifierrors.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		}
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// 2. Close deposit account's PI.
	// Get deposit account PI
	depositAccountPi, err := s.getPiByAccountNumberAndIfsc(ctx, depositAccount.GetAccountNumber(), depositAccount.GetIfscCode())
	if err != nil {
		logger.Error(ctx, "error getPiByAccountNumberAndIfsc()",
			zap.String(logger.ACCOUNT_NUMBER, req.GetDepositAccountNumber()),
			zap.Error(err),
		)
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// Close deposit account PI if not already in close state
	if depositAccountPi.State != piPb.PaymentInstrumentState_CLOSED {
		depositAccountPi.State = piPb.PaymentInstrumentState_CLOSED
		if err = s.updatePiState(ctx, depositAccountPi); err != nil {
			logger.Error(ctx, "error in updating pi to closed state",
				zap.String(logger.PI_ID, depositAccountPi.Id),
				zap.Error(err),
			)
			res.Status = rpc.StatusInternal()
		}
	}

	savingsAccountNumber := req.GetTransactionDetails().GetAccountNumber()
	savingsAccountIfscCode, err := s.getSavingsAccountIfscCode(ctx, savingsAccountNumber)
	if err != nil {
		logger.Error(ctx, "error in getSavingsAccountIfscCode in ProcessPrecloseDepositInboundTxn", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actor.GetId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// Get saving account PI
	savingAccountPi, err := s.getPiByAccountNumberAndIfsc(ctx, savingsAccountNumber, savingsAccountIfscCode)
	if err != nil {
		logger.Error(ctx, "error getPiByAccountNumberAndIfsc()",
			zap.String(logger.ACCOUNT_NUMBER, savingsAccountNumber),
			zap.Error(err),
		)
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	actor, err = s.getActorById(ctx, depositAccount.GetActorId())
	if err != nil {
		logger.Error(ctx, "error in getting actor by id",
			zap.String(logger.ACTOR_ID, depositAccount.GetActorId()),
			zap.Error(err),
		)
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// 3. Create Order and Transaction with no op workflow
	orderTags := []orderPb.OrderTag{orderPb.OrderTag_DEPOSIT, depositTypeToOrderTagMap[depositAccount.Type]}
	utr := req.GetTransactionDetails().GetId()
	remarks := fmt.Sprintf("%s %s closed", convertAccountTypeToString(depositAccount.GetType()), depositAccount.GetName())

	rawNotificationDetails := make(map[string]*paymentPb.NotificationDetails)
	if req.GetTransactionDetails() != nil {
		var valueDate *timestamppb.Timestamp = nil
		if req.GetTransactionDetails().GetValueDate() != nil {
			valueDate = timestamppb.New(*datetime.DateToTime(req.GetTransactionDetails().GetValueDate(), datetime.IST))
		}
		rawNotificationDetails[paymentPb.AccountingEntryType_CREDIT.String()] = &paymentPb.NotificationDetails{
			Particulars:   req.GetTransactionDetails().GetParticular(),
			BatchSerialId: req.GetTransactionDetails().GetBatchSerialId(),
			ValueDate:     valueDate,
		}
	}

	orderCreated, orderId, txnId, createOrderErr := s.createNoOpOrderWithTxn(
		ctx,
		depositAccountPi.Id,
		savingAccountPi.Id,
		actor.Id,
		actor.Id,
		remarks,
		utr,
		req.GetTransactionDetails().GetBatchSerialId(),
		req.GetTransactionDetails().GetCbsId(),
		req.GetTransactionDetails().GetAmount(),
		orderTags,
		req.TransactionDetails.GetTimestamp(),
		nil,
		req.GetTransactionDetails().GetTimestamp(),
		req.GetSource(),
		metrics.DepositClosed,
		rawNotificationDetails,
	)
	if createOrderErr != nil {
		logger.Error(ctx, "failed to create order or transaction for pre-close deposit account", zap.Error(createOrderErr))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// If order is successfully closed then send notification to user
	if orderCreated {
		var notificationTemplate *config.NotificationTemplateParams
		if depositAccount.GetType() == accounts.Type_SMART_DEPOSIT {
			notificationTemplate = s.notificationParams.SDClosureSuccess
		} else if depositAccount.GetType() == accounts.Type_FIXED_DEPOSIT {
			notificationTemplate = s.notificationParams.FDClosureSuccess
		} else {
			logger.Error(ctx, "failed to get notification template for deposit type",
				zap.String(logger.ACCOUNT_TYPE, depositAccount.GetType().String()),
			)
		}
		if notificationTemplate != nil {
			notificationBody := fmt.Sprintf(notificationTemplate.Body, depositAccount.GetName())
			if err := s.sendDepositNotification(ctx, actor.GetEntityId(), depositAccount, notificationTemplate, notificationBody); err != nil {
				logger.Error(ctx, "failed to send deposit closure notification",
					zap.String(logger.ACTOR_ID, actor.GetId()),
					zap.String(logger.DEPOSIT_ACCOUNT_ID, depositAccount.Id),
					zap.Error(err),
				)
			}
		}

		if err := s.sendDepositClosureSms(ctx, actor.GetEntityId(), depositAccount, req.GetTransactionDetails().GetAccountNumber(),
			req.GetTransactionDetails().GetTimestamp(), req.GetTransactionDetails().GetAmount()); err != nil {
			logger.Error(ctx, "failed to send deposit closure SMS",
				zap.String(logger.ACTOR_ID, actor.GetId()),
				zap.String(logger.DEPOSIT_ACCOUNT_ID, depositAccount.Id),
				zap.Error(err),
			)
		}
	}

	// Trigger receive inbound notification from federal for deposit closure - DepositInboundNotificationReceived
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		s.EventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), depositEvents.NewDepositInboundNotificationReceived(
			depositAccount.GetActorId(), depositAccount.GetType().String(), depositAccount.GetId(),
			getInboundType(req.IsAutoClose), orderId, txnId, depositEvents.DepositStateSuccess, ""))
	})

	res.Status = rpc.StatusOk()
	return res, nil
}

// PerformPreCloseDepositSteps performs all steps required for preclose a deposit account.
// Below are the steps:
// 1. Close PI for deposit account
// 2. Update deposit account state to preclosed if its not already preclosed, and add closureInfo to deposit account
// 3. Update deposit request state to success. This should be the last step so that if any of the above operation fails,
// this can be retried from either processCallback or createOrClose.
//
// This method is idempotent and hence if caller is queue consumer (callback service consumer) it can return
// `queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE` if err is returned.
// Similarly if caller is a order orchestrator consumer (createOrClose) it can return
// `orderDomainPb.DomainProcessingStatus_IN_PROGRESS` if err is returned.
func (s *Service) PerformPreCloseDepositSteps(ctx context.Context, depositAccount *depositPb.DepositAccount,
	depositRequest *depositPb.DepositRequest, closureInfo *depositPb.ClosureInfo) error {
	// 1. Close PI for deposit account
	// Get deposit account pi by account number and IFSC code
	depositAccountPi, err := s.getPiByAccountNumberAndIfsc(ctx, depositAccount.GetAccountNumber(), depositAccount.GetIfscCode())
	if err != nil {
		return fmt.Errorf("error getPiByAccountNumberAndIfsc(), accountNumber %s: %w",
			depositAccount.GetAccountNumber(), err)
	}

	// Close deposit PI if it's not already in the closed state
	if depositAccountPi.State != piPb.PaymentInstrumentState_CLOSED {
		depositAccountPi.State = piPb.PaymentInstrumentState_CLOSED
		if updateErr := s.updatePiState(ctx, depositAccountPi); updateErr != nil {
			return fmt.Errorf("error in updating PI to closed state, depositAccountPiId %s: %w",
				depositAccountPi.GetId(), updateErr)
		}
	}

	// Execute deposit request update and deposit account update in txn
	txnErr := s.idempotentTxnExecutor.RunIdempotentTxn(ctx, 3, func(txnCtx context.Context) error {
		// 2. Update deposit account state to preclosed if its not already preclosed
		// Also, update the closure info for deposit pre-closure
		if updateErr := s.DepositAccountDao.UpdateById(txnCtx, &depositPb.DepositAccount{
			Id:          depositAccount.GetId(),
			State:       depositPb.DepositState_PRECLOSED,
			ClosureInfo: closureInfo,
		}, []depositPb.DepositAccountFieldMask{
			depositPb.DepositAccountFieldMask_DEPOSIT_ACCOUNT_STATE,
			depositPb.DepositAccountFieldMask_CLOSURE_INFO,
		}); updateErr != nil {
			return fmt.Errorf("error in updating deposit account state to preclose, depositAccountId %s: %w",
				depositAccount.GetId(), updateErr)
		}

		updateIsAddFundsAllowedErr := s.DepositAccountDao.UpdateIsAddFundsAllowed(txnCtx, depositAccount.Id, false)
		if updateIsAddFundsAllowedErr != nil {
			return fmt.Errorf("error in updating is_add_funds_allowed to false, depositAccountId: %s %w",
				depositAccount.GetId(), updateIsAddFundsAllowedErr)
		}

		// 3. Update depositRequest state to Success
		if depositRequest.State != depositPb.RequestState_REQUEST_SUCCESS {
			if updateErr := s.UpdateRequest.UpdateById(txnCtx, &depositPb.DepositRequest{
				Id:    depositRequest.GetId(),
				State: depositPb.RequestState_REQUEST_SUCCESS,
			}, []depositPb.DepositRequestFieldMask{
				depositPb.DepositRequestFieldMask_STATE,
			}); updateErr != nil {
				return fmt.Errorf("error in updating depositRequest state to success, depositAccountId %s: %w",
					depositAccount.GetId(), updateErr)
			}
		}

		return nil
	})
	if txnErr != nil {
		return fmt.Errorf("error in updating depositRequest's & depositAccount's state, depositAccountId %s: %w",
			depositAccount.GetId(), txnErr)
	}

	return nil
}

// SendDepositFailureNotification sends a notification on deposit account failure
func (s *Service) SendDepositFailureNotification(ctx context.Context, depositRequest *depositPb.DepositRequest) {

	// TODO(mounish): remove this later once request updater is enabled on prod
	if s.config.RequestUpdater().Enable() {
		logger.Info(ctx, "request updater is enabled, skipping SendDepositFailureNotification")
		return
	}

	actor, err := s.getActorById(ctx, depositRequest.GetActorId())
	if err != nil {
		logger.Error(ctx, "failed to fetch actor by id to send notification",
			zap.String(logger.ACTOR_ID, depositRequest.GetActorId()),
			zap.Error(err))
		return
	}
	depositAccount, err := s.DepositAccountDao.GetById(ctx, depositRequest.GetDepositAccountId())
	if err != nil {
		logger.Error(ctx, "error in getting deposit account",
			zap.String(logger.DEPOSIT_ACCOUNT_ID, depositRequest.GetDepositAccountId()),
			zap.Error(err),
		)
		return
	}
	var notificationTemplate *config.NotificationTemplateParams
	if depositAccount.GetType() == accounts.Type_SMART_DEPOSIT {
		notificationTemplate = s.notificationParams.SDClosureFailed
	} else if depositAccount.GetType() == accounts.Type_FIXED_DEPOSIT {
		notificationTemplate = s.notificationParams.FDClosureFailed
	} else {
		logger.Error(ctx, "failed to get notification template for deposit type",
			zap.String(logger.ACCOUNT_TYPE, depositAccount.GetType().String()),
		)
		return
	}
	if notificationTemplate != nil {
		notificationBody := notificationTemplate.Body
		if err := s.sendDepositNotification(ctx, actor.GetEntityId(), depositAccount, notificationTemplate, notificationBody); err != nil {
			logger.Error(ctx, "failed to send deposit closure failure notification",
				zap.String(logger.ACTOR_ID, actor.GetId()),
				zap.String(logger.DEPOSIT_ACCOUNT_ID, depositAccount.GetId()),
				zap.Error(err))
		}
	}
}

func getInboundType(IsAutoClose bool) string {
	if IsAutoClose {
		return depositEvents.TypeDepAutoClosure
	}
	return depositEvents.TypeDepPreClosure
}
