package deposit_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/assert"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	pkgPagination "github.com/epifi/be-common/pkg/pagination"

	payPb "github.com/epifi/gamma/api/pay"
	payMock "github.com/epifi/gamma/api/pay/mocks"
	mockDao "github.com/epifi/gamma/deposit/dao/mocks"

	rpcPb "github.com/epifi/be-common/api/rpc"

	"github.com/epifi/gamma/api/pan"
	panMock "github.com/epifi/gamma/api/pan/mocks"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/deposit"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	mockAccountBalance "github.com/epifi/gamma/api/accounts/balance/mocks"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	brokerMocks "github.com/epifi/be-common/pkg/events/mocks"
	"github.com/epifi/be-common/pkg/money"
	mock_queue "github.com/epifi/be-common/pkg/queue/mocks"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"

	accountPb "github.com/epifi/gamma/api/accounts"
	accountsPb "github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	authPb "github.com/epifi/gamma/api/auth"
	authMocks "github.com/epifi/gamma/api/auth/mocks"
	"github.com/epifi/gamma/api/bankcust"
	bankCustMocks "github.com/epifi/gamma/api/bankcust/mocks"
	commsPb "github.com/epifi/gamma/api/comms"
	commsMocks "github.com/epifi/gamma/api/comms/mocks"
	depositPb "github.com/epifi/gamma/api/deposit"
	heMocks "github.com/epifi/gamma/api/health_engine/mocks"
	kycPb "github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/kyc/vkyc"
	vkycMock "github.com/epifi/gamma/api/kyc/vkyc/mocks"
	orderPb "github.com/epifi/gamma/api/order"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	paymentMocks "github.com/epifi/gamma/api/order/payment/mocks"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	accountPiMocks "github.com/epifi/gamma/api/paymentinstrument/account_pi/mocks"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	savingsPb "github.com/epifi/gamma/api/savings"
	savingsMocks "github.com/epifi/gamma/api/savings/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	groupMock "github.com/epifi/gamma/api/user/group/mocks"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	vgDepositPb "github.com/epifi/gamma/api/vendorgateway/openbanking/deposit"
	vgDepositMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/deposit/mocks"
	vgSavingsPb "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	vgSavingsMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/savings/mocks"
	accessorMocks "github.com/epifi/gamma/deposit/accessor/mocks"
	"github.com/epifi/gamma/deposit/config"
	"github.com/epifi/gamma/deposit/config/genconf"
	"github.com/epifi/gamma/deposit/dao"
	"github.com/epifi/gamma/deposit/dao/mocks"
	feedbackMocks "github.com/epifi/gamma/deposit/feedback_engine/mocks"
	preclosureEngineMocks "github.com/epifi/gamma/deposit/preclosureengine/mocks"
	"github.com/epifi/gamma/deposit/update_request"
)

var (
	db                *gorm.DB
	conf              *config.Config
	dynConf           *genconf.Config
	txnExecutor       storagev2.IdempotentTxnExecutor
	depositAccountDao *dao.DepositAccountDaoCrdb
	depositRequestDao *dao.DepositRequestDaoCrdb
	depositService    *deposit.Service

	inProgress rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatusWithoutDebug(uint32(vgDepositPb.CheckAccountStatusResponse_IN_PROGRESS), "Request is being processed")
	}

	statusFailed rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatusWithoutDebug(uint32(vgDepositPb.CheckAccountStatusResponse_FAILED), "Request failed")
	}

	fixtureActorId1             = "actor-user-1"
	fixtureActorId2             = "actor-2"
	fixtureSavingsAccountNumber = "**********"
	fixtureIfscCode             = "FDRL0001001"
	fixturePrincipleAmount1     = &moneyPb.Money{
		CurrencyCode: "INR",
		Units:        100,
	}
	fixtureDepositAccountSD1 = &depositPb.DepositAccount{
		Id:              "deposit-account-sd-1",
		ActorId:         "actor-user-1",
		AccountNumber:   "**********",
		Name:            "Bike",
		Type:            accountsPb.Type_SMART_DEPOSIT,
		SchemeCode:      depositPb.DepositScheme_SMART_DEPOSIT,
		State:           depositPb.DepositState_CREATED,
		PrincipalAmount: fixturePrincipleAmount1,
		RunningBalance:  fixturePrincipleAmount1,
		MaturityAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        110,
		},
		Term: &types.DepositTerm{
			Days:   7,
			Months: 0,
		},
		MaturityDate: &timestamp.Timestamp{
			Seconds: **********,
			Nanos:   *********,
		},
		InterestRate: "10",
		RenewInfo: &depositPb.RenewInfo{
			IsAutoRenewable: false,
		},
		OperativeAccountNumber: "**********",
		RepayAccountNumber:     "**********",
		PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
		IfscCode:               fixtureIfscCode,
		NomineeDetails:         &depositPb.DepositNomineeDetails{},
		IsAddFundsAllowed:      true,
		ClosureInfo:            &depositPb.ClosureInfo{},
		Provenance:             depositPb.DepositAccountProvenance_USER_APP,
		DepositIcon:            &commontypes.Image{ImageType: commontypes.ImageType_PNG, ImageUrl: "https://epifi-icons.pointz.in/deposit/SDSpendProof.png"},
	}
	fixtureDepositAccountSD2 = &depositPb.DepositAccount{
		Id:              "deposit-account-sd-2",
		ActorId:         "actor-user-1",
		AccountNumber:   "**********",
		Name:            "Phone",
		Type:            accountsPb.Type_SMART_DEPOSIT,
		SchemeCode:      depositPb.DepositScheme_SMART_DEPOSIT,
		State:           depositPb.DepositState_PRECLOSED,
		PrincipalAmount: fixturePrincipleAmount1,
		RunningBalance:  fixturePrincipleAmount1,
		MaturityAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        110,
		},
		Term: &types.DepositTerm{
			Days:   0,
			Months: 12,
		},
		MaturityDate: &timestamp.Timestamp{
			Seconds: **********,
			Nanos:   *********,
		},
		InterestRate: "10",
		RenewInfo: &depositPb.RenewInfo{
			IsAutoRenewable: false,
		},
		OperativeAccountNumber: "**********",
		RepayAccountNumber:     "**********",
		PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
		IfscCode:               fixtureIfscCode,
		NomineeDetails:         &depositPb.DepositNomineeDetails{},
		IsAddFundsAllowed:      true,
		ClosureInfo:            &depositPb.ClosureInfo{},
		Provenance:             depositPb.DepositAccountProvenance_USER_APP,
		DepositIcon:            &commontypes.Image{ImageType: commontypes.ImageType_PNG, ImageUrl: "https://epifi-icons.pointz.in/deposit/SDSpendProof.png"},
	}
	fixtureDepositAccountSD3 = &depositPb.DepositAccount{
		Id:              "deposit-account-sd-3",
		ActorId:         "actor-user-1",
		AccountNumber:   "**********",
		Name:            "New Car",
		Type:            accountsPb.Type_SMART_DEPOSIT,
		SchemeCode:      depositPb.DepositScheme_SMART_DEPOSIT,
		State:           depositPb.DepositState_CREATED,
		PrincipalAmount: fixturePrincipleAmount1,
		RunningBalance:  fixturePrincipleAmount1,
		MaturityAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        110,
		},
		Term: &types.DepositTerm{
			Days:   21,
			Months: 12,
		},
		MaturityDate: &timestamp.Timestamp{
			Seconds: **********,
			Nanos:   *********,
		},
		InterestRate: "10",
		RenewInfo: &depositPb.RenewInfo{
			IsAutoRenewable: true,
			Option:          depositPb.RenewOption_MATURITY_ONLY,
		},
		OperativeAccountNumber: "**********",
		RepayAccountNumber:     "**********",
		PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
		IfscCode:               fixtureIfscCode,
		NomineeDetails:         &depositPb.DepositNomineeDetails{},
		IsAddFundsAllowed:      true,
		ClosureInfo:            &depositPb.ClosureInfo{},
		Provenance:             depositPb.DepositAccountProvenance_REWARDS_APP,
		DepositIcon:            &commontypes.Image{ImageType: commontypes.ImageType_PNG, ImageUrl: "https://epifi-icons.pointz.in/deposit/SDSpendProof.png"},
	}
	fixtureDepositAccountFD1 = &depositPb.DepositAccount{
		Id:              "deposit-account-fd-1",
		ActorId:         "actor-user-1",
		AccountNumber:   "**********",
		Name:            "Car",
		Type:            accountsPb.Type_FIXED_DEPOSIT,
		SchemeCode:      depositPb.DepositScheme_FD_CASH_CERTIFICATE,
		State:           depositPb.DepositState_CREATED,
		PrincipalAmount: fixturePrincipleAmount1,
		RunningBalance:  fixturePrincipleAmount1,
		MaturityAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        107,
		},
		Term: &types.DepositTerm{
			Days:   21,
			Months: 12,
		},
		MaturityDate: &timestamp.Timestamp{
			Seconds: **********,
			Nanos:   *********,
		},
		InterestRate: "7",
		RenewInfo: &depositPb.RenewInfo{
			IsAutoRenewable: true,
			Option:          depositPb.RenewOption_MATURITY_ONLY,
		},
		OperativeAccountNumber: fixtureSavingsAccountNumber,
		RepayAccountNumber:     fixtureSavingsAccountNumber,
		PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
		IfscCode:               fixtureIfscCode,
		NomineeDetails:         &depositPb.DepositNomineeDetails{},
		IsAddFundsAllowed:      true,
		ClosureInfo:            &depositPb.ClosureInfo{},
		Provenance:             depositPb.DepositAccountProvenance_USER_APP,
		DepositIcon:            &commontypes.Image{ImageType: commontypes.ImageType_PNG, ImageUrl: "https://epifi-icons.pointz.in/deposit/default-FD.png"},
	}
	fixtureDepositRequestToAccountFD3 = &depositPb.DepositAccount{
		Id:              "deposit-request-create-fd-3",
		ActorId:         "actor-user-1",
		Name:            "Vacay",
		Type:            accountsPb.Type_FIXED_DEPOSIT,
		State:           depositPb.DepositState_IN_PROGRESS,
		PrincipalAmount: fixturePrincipleAmount1,
		Term: &types.DepositTerm{
			Days:   21,
			Months: 48,
		},
		RenewInfo: &depositPb.RenewInfo{
			IsAutoRenewable: false,
		},
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		SchemeCode:  depositPb.DepositScheme_FD_CASH_CERTIFICATE,
		Provenance:  depositPb.DepositAccountProvenance_USER_APP,
		DepositIcon: &commontypes.Image{ImageType: commontypes.ImageType_PNG, ImageUrl: "https://epifi-icons.pointz.in/deposit/default-FD.png"},
	}
	fixtureDepositRequestToAccountFD2 = &depositPb.DepositAccount{
		Id:              "deposit-request-create-fd-2-retry-1",
		ActorId:         "actor-user-1",
		Name:            "Car 2",
		Type:            accountsPb.Type_FIXED_DEPOSIT,
		State:           depositPb.DepositState_FAILED,
		PrincipalAmount: fixturePrincipleAmount1,
		Term: &types.DepositTerm{
			Days:   11,
			Months: 24,
		},
		RenewInfo: &depositPb.RenewInfo{
			IsAutoRenewable: true,
			Option:          depositPb.RenewOption_MATURITY_ONLY,
		},
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		SchemeCode:  depositPb.DepositScheme_FD_CASH_CERTIFICATE,
		Provenance:  depositPb.DepositAccountProvenance_USER_APP,
	}
	fixtureDepositRequestCreateSD2 = &depositPb.DepositRequest{
		Id:               "deposit-request-create-sd-2",
		RequestId:        "request-id-2",
		ClientRequestId:  "client-request-id-2",
		DepositAccountId: "deposit-account-sd-2",
		Type:             depositPb.RequestType_CREATE,
		State:            depositPb.RequestState_REQUEST_SUCCESS,
		DepositInfo: &depositPb.DepositInfo{
			ActorId: fixtureActorId1,
			Name:    "Phone",
			Type:    accountsPb.Type_SMART_DEPOSIT,
			Amount:  fixturePrincipleAmount1,
			Term: &types.DepositTerm{
				Days:   0,
				Months: 12,
			},
			RenewInfo: &depositPb.RenewInfo{
				IsAutoRenewable: false,
			},
			OperativeAccountNumber:   fixtureSavingsAccountNumber,
			RepayAccountNumber:       fixtureSavingsAccountNumber,
			Vendor:                   commonvgpb.Vendor_FEDERAL_BANK,
			InterestPayout:           depositPb.InterestPayout_INTEREST_PAYOUT_UNSPECIFIED,
			RequestType:              depositPb.RequestType_CREATE,
			DepositAccountProvenance: depositPb.DepositAccountProvenance_USER_APP,
		},
		PartnerBank:    commonvgpb.Vendor_FEDERAL_BANK,
		ActorId:        fixtureActorId1,
		LastAttempt:    false,
		DetailedStatus: &depositPb.DepositRequestDetailedStatus{},
	}

	depositAffectedTestTables = []string{"deposit_requests", "deposit_accounts"}
)

func TestService_GetById(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTest.PrepareScopedDatabase(t, conf.EpifiDb.GetName(), db, depositAffectedTestTables)

	ctr := gomock.NewController(t)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	invEventPublisher := mock_queue.NewMockPublisher(ctr)

	depositService = deposit.NewService(nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, depositAccountDao, depositRequestDao, nil, nil, nil, nil, nil, nil, brokerMock, conf, dynConf, nil, invEventPublisher, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	defer func() {
		ctr.Finish()
		depositService = nil
	}()

	type args struct {
		ctx context.Context
		req *depositPb.GetByIdRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *depositPb.GetByIdResponse
		wantErr bool
	}{
		{
			name: "should successfully fetch deposit account by id",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetByIdRequest{
					Id: fixtureDepositAccountSD1.Id,
				},
			},
			want: &depositPb.GetByIdResponse{
				Status:  rpc.StatusOk(),
				Account: fixtureDepositAccountSD1,
			},
			wantErr: false,
		},
		{
			name: "should fail as no deposit account exists",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetByIdRequest{
					Id: "random-deposit-account-id",
				},
			},
			want: &depositPb.GetByIdResponse{
				Status:  rpc.StatusRecordNotFound(),
				Account: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := depositService.GetById(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("depositService.GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.Status.Code != tt.want.Status.Code {
				t.Errorf("depositService.GetById() status: got = %v, want %v", got.Status, tt.want.Status)
			}
			if diff := isDepositAccountDeepEqual(got.Account, tt.want.Account); diff != "" {
				t.Errorf("depositService.GetById() error = %v, wantErr %v, diff %v", err, tt.wantErr, diff)
			}
		})
	}
}

func TestService_GetAccountDetailsById(t *testing.T) {
	var (
		testUpdatedBalance = &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        1000,
		}
		testFixtureDepositAccountSD1 = &depositPb.DepositAccount{
			Id:                     fixtureDepositAccountSD1.Id,
			ActorId:                fixtureDepositAccountSD1.ActorId,
			AccountNumber:          fixtureDepositAccountSD1.AccountNumber,
			Name:                   fixtureDepositAccountSD1.Name,
			Type:                   fixtureDepositAccountSD1.Type,
			SchemeCode:             fixtureDepositAccountSD1.SchemeCode,
			State:                  fixtureDepositAccountSD1.State,
			PrincipalAmount:        testUpdatedBalance,
			RunningBalance:         fixtureDepositAccountSD1.RunningBalance,
			MaturityAmount:         fixtureDepositAccountSD1.MaturityAmount,
			Term:                   fixtureDepositAccountSD1.Term,
			MaturityDate:           fixtureDepositAccountSD1.MaturityDate,
			InterestRate:           fixtureDepositAccountSD1.InterestRate,
			RenewInfo:              fixtureDepositAccountSD1.RenewInfo,
			OperativeAccountNumber: fixtureDepositAccountSD1.OperativeAccountNumber,
			RepayAccountNumber:     fixtureDepositAccountSD1.RepayAccountNumber,
			PartnerBank:            fixtureDepositAccountSD1.PartnerBank,
			IfscCode:               fixtureDepositAccountSD1.IfscCode,
			NomineeDetails:         fixtureDepositAccountSD1.NomineeDetails,
			IsAddFundsAllowed:      fixtureDepositAccountSD1.IsAddFundsAllowed,
			ClosureInfo:            fixtureDepositAccountSD1.ClosureInfo,
			Provenance:             fixtureDepositAccountSD1.Provenance,
			DepositIcon:            &commontypes.Image{ImageType: commontypes.ImageType_PNG, ImageUrl: "https://epifi-icons.pointz.in/deposit/SDSpendProof.png"},
		}
	)
	// Clean database, run migrations and load fixtures
	pkgTest.PrepareScopedDatabase(t, conf.EpifiDb.GetName(), db, depositAffectedTestTables)

	ctr := gomock.NewController(t)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	invEventPublisher := mock_queue.NewMockPublisher(ctr)

	vgSavingsMock := vgSavingsMocks.NewMockSavingsClient(ctr)

	authMock := authMocks.NewMockAuthClient(ctr)
	authMock.EXPECT().GetDeviceAuth(context.Background(), gomock.Any()).
		Return(&authPb.GetDeviceAuthResponse{
			Status:        rpc.StatusOk(),
			ActorId:       fixtureActorId2,
			UserProfileId: "random-user-profile-id",
			DeviceToken:   "random-device-token",
			Device: &commontypes.Device{
				DeviceId: "random-device-id",
			},
			DeviceRegStatus: authPb.DeviceRegistrationStatus_REGISTERED,
		}, nil).AnyTimes()

	actorMock := actorMocks.NewMockActorClient(ctr)
	actorMock.EXPECT().GetEntityDetailsByActorId(context.Background(), gomock.Any()).Return(&actorPb.GetEntityDetailsByActorIdResponse{
		Status:   rpc.StatusOk(),
		EntityId: "random-entity-id",
		Name: &commontypes.Name{
			FirstName: "random-first-name",
			LastName:  "random-last-name",
			Honorific: "Mr.",
		},
		MobileNumber: &commontypes.PhoneNumber{
			CountryCode:    91,
			NationalNumber: **********,
		},
	}, nil).AnyTimes()

	type vgMockGetBalance struct {
		enable bool
		res    *vgSavingsPb.GetBalanceResponse
		err    error
	}

	depositService = deposit.NewService(nil, nil, authMock, nil, nil, vgSavingsMock, actorMock, nil, nil, nil, nil, nil, nil, nil, nil, nil, depositAccountDao, depositRequestDao, nil, nil, nil, nil, nil, nil, brokerMock, conf, dynConf, nil, invEventPublisher, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	defer func() {
		ctr.Finish()
		depositService = nil
	}()

	type args struct {
		ctx context.Context
		req *depositPb.GetAccountDetailsByIdRequest
	}
	tests := []struct {
		name             string
		args             args
		want             *depositPb.GetAccountDetailsByIdResponse
		vgMockGetBalance vgMockGetBalance
		wantErr          bool
	}{
		{
			name: "Should successfully fetch deposit account details by id",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetAccountDetailsByIdRequest{Id: fixtureDepositAccountSD1.Id},
			},
			want: &depositPb.GetAccountDetailsByIdResponse{
				Status:  rpc.StatusOk(),
				Account: fixtureDepositAccountSD1,
			},
			vgMockGetBalance: vgMockGetBalance{
				enable: true,
				res: &vgSavingsPb.GetBalanceResponse{
					Status:           rpc.StatusOk(),
					AvailableBalance: fixtureDepositAccountSD1.PrincipalAmount,
				},
				err: nil,
			},
			wantErr: false,
		},
		{
			name: "should fail as no deposit account exists",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetAccountDetailsByIdRequest{
					Id: "random-deposit-account-id",
				},
			},
			want: &depositPb.GetAccountDetailsByIdResponse{
				Status:  rpc.StatusRecordNotFound(),
				Account: nil,
			},
			wantErr: false,
		},
		{
			name: "Should successfully fetch deposit details with updated balance",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetAccountDetailsByIdRequest{Id: fixtureDepositAccountSD1.Id},
			},
			want: &depositPb.GetAccountDetailsByIdResponse{
				Status:  rpc.StatusOk(),
				Account: testFixtureDepositAccountSD1,
			},
			vgMockGetBalance: vgMockGetBalance{
				enable: true,
				res: &vgSavingsPb.GetBalanceResponse{
					Status:           rpc.StatusOk(),
					AvailableBalance: testUpdatedBalance,
				},
				err: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.vgMockGetBalance.enable {
				vgSavingsMock.EXPECT().GetBalance(tt.args.ctx, gomock.Any()).
					Return(tt.vgMockGetBalance.res, tt.vgMockGetBalance.err)
			}
			got, err := depositService.GetAccountDetailsById(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAccountDetailsById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.Status.Code != tt.want.Status.Code {
				t.Errorf("depositService.GetAccountDetailsById() status: got = %v, want %v", got.Status, tt.want.Status)
			}
			if diff := isDepositAccountDeepEqual(got.Account, tt.want.Account); diff != "" {
				t.Errorf("depositService.GetAccountDetailsById() error = %v, wantErr %v, diff %v", err, tt.wantErr, diff)
			}
		})
	}
}

func TestService_GetByAccountNumberAndIfsc(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTest.PrepareScopedDatabase(t, conf.EpifiDb.GetName(), db, depositAffectedTestTables)

	ctr := gomock.NewController(t)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	invEventPublisher := mock_queue.NewMockPublisher(ctr)

	depositSvc := deposit.NewService(nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, depositAccountDao, depositRequestDao, nil, nil, nil, nil, nil, nil, brokerMock, conf, dynConf, nil, invEventPublisher, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	defer func() {
		ctr.Finish()
		depositSvc = nil
	}()

	type args struct {
		ctx context.Context
		req *depositPb.GetByAccountNumberAndIfscRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *depositPb.GetByAccountNumberAndIfscResponse
		wantErr bool
	}{
		{
			name: "Should successfully fetch deposit account by account number and ifsc code",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetByAccountNumberAndIfscRequest{
					AccountNumber: fixtureDepositAccountFD1.AccountNumber,
					IfscCode:      fixtureDepositAccountFD1.IfscCode,
				},
			},
			want: &depositPb.GetByAccountNumberAndIfscResponse{
				Status:  rpc.StatusOk(),
				Account: fixtureDepositAccountFD1,
			},
			wantErr: false,
		},
		{
			name: "Should return status record not found as no such deposit account exists",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetByAccountNumberAndIfscRequest{
					AccountNumber: "random-account-number",
					IfscCode:      "random-ifsc-code",
				},
			},
			want: &depositPb.GetByAccountNumberAndIfscResponse{
				Status:  rpc.StatusRecordNotFound(),
				Account: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := depositSvc.GetByAccountNumberAndIfsc(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByAccountNumberAndIfsc() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.Status.Code != tt.want.Status.Code {
				t.Errorf("GetByAccountNumberAndIfsc() status: got = %v, want %v", got.Status, tt.want.Status)
			}
			if diff := isDepositAccountDeepEqual(got.Account, tt.want.Account); diff != "" {
				t.Errorf("GetByAccountNumberAndIfsc() error = %v, wantErr %v, diff %v", err, tt.wantErr, diff)
			}
		})
	}
}

func TestService_GetDepositRequestByFilter(t *testing.T) {
	type args struct {
		ctx context.Context
		req *depositPb.GetDepositRequestByFilterRequest
	}
	tests := []struct {
		name      string
		args      args
		want      *depositPb.GetDepositRequestByFilterResponse
		mockSetup func(mockRequestDao *mockDao.MockDepositRequestDao, req *depositPb.GetDepositRequestByFilterRequest)
		wantErr   bool
	}{
		{
			name: "Should successfully fetch deposit request for given actor id",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetDepositRequestByFilterRequest{
					ActorId: fixtureActorId1,
					PageContextRequest: &rpc.PageContextRequest{
						PageSize: 1,
					},
				},
			},
			mockSetup: func(mockRequestDao *mocks.MockDepositRequestDao, req *depositPb.GetDepositRequestByFilterRequest) {
				pageToken, _ := pkgPagination.GetPageToken(req.GetPageContextRequest())
				mockRequestDao.EXPECT().GetRequestsByFilters(gomock.Any(), pageToken, req.PageContextRequest.GetPageSize(), gomock.Any()).Times(1).Return(
					[]*depositPb.DepositRequest{fixtureDepositRequestCreateSD2}, &rpcPb.PageContextResponse{}, nil)
			},
			want: &depositPb.GetDepositRequestByFilterResponse{
				Status:          rpc.StatusOk(),
				DepositRequests: []*depositPb.DepositRequest{fixtureDepositRequestCreateSD2},
			},
			wantErr: false,
		},
		{
			name: "Should give internal error as actor id is invalid",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetDepositRequestByFilterRequest{
					ActorId: "random-actor-id",
					PageContextRequest: &rpc.PageContextRequest{
						PageSize: 1,
					},
				},
			},
			mockSetup: func(mockRequestDao *mocks.MockDepositRequestDao, req *depositPb.GetDepositRequestByFilterRequest) {
				pageToken, _ := pkgPagination.GetPageToken(req.GetPageContextRequest())
				mockRequestDao.EXPECT().GetRequestsByFilters(gomock.Any(), pageToken, req.PageContextRequest.GetPageSize(), gomock.Any()).Times(1).Return(
					nil, nil, epifierrors.ErrInvalidArgument)
			},
			want: &depositPb.GetDepositRequestByFilterResponse{
				Status:          rpc.StatusInternal(),
				DepositRequests: nil,
			},
			wantErr: false,
		},
		{
			name: "Should give error as page size should be at least 1",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetDepositRequestByFilterRequest{
					ActorId: fixtureActorId1,
					PageContextRequest: &rpc.PageContextRequest{
						PageSize: 0,
					},
				},
			},
			mockSetup: func(mockRequestDao *mocks.MockDepositRequestDao, req *depositPb.GetDepositRequestByFilterRequest) {
				pageToken, _ := pkgPagination.GetPageToken(req.GetPageContextRequest())
				mockRequestDao.EXPECT().GetRequestsByFilters(gomock.Any(), pageToken, req.PageContextRequest.GetPageSize(), gomock.Any()).Times(1).Return(
					nil, nil, epifierrors.ErrInvalidArgument)
			},
			want: &depositPb.GetDepositRequestByFilterResponse{
				Status:          rpc.StatusInternal(),
				DepositRequests: nil,
			},
			wantErr: false,
		},
		{
			name: "Should give record not found status as no record found for given actor id",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetDepositRequestByFilterRequest{
					ActorId: fixtureActorId1,
					PageContextRequest: &rpc.PageContextRequest{
						PageSize: 1,
					},
				},
			},
			mockSetup: func(mockRequestDao *mocks.MockDepositRequestDao, req *depositPb.GetDepositRequestByFilterRequest) {
				pageToken, _ := pkgPagination.GetPageToken(req.GetPageContextRequest())
				mockRequestDao.EXPECT().GetRequestsByFilters(gomock.Any(), pageToken, req.PageContextRequest.GetPageSize(), gomock.Any()).Times(1).Return(
					[]*depositPb.DepositRequest{}, nil, epifierrors.ErrRecordNotFound)
			},
			want: &depositPb.GetDepositRequestByFilterResponse{
				Status:          rpc.StatusRecordNotFound(),
				DepositRequests: []*depositPb.DepositRequest{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockDepositRequestDao := mockDao.NewMockDepositRequestDao(ctr)
			tt.mockSetup(mockDepositRequestDao, tt.args.req)

			depositSvc := deposit.NewService(nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, mockDepositRequestDao, nil, nil, nil, nil, nil, nil, nil, conf, dynConf, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
			got, err := depositSvc.GetDepositRequestByFilter(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDepositRequestByFilter() error = %v, wantErr = %v", err, tt.wantErr)
				return
			}
			if got.Status.Code != tt.want.Status.Code {
				t.Errorf("GetDepositRequestByFilter() status: got = %v, want = %v", got.Status, tt.want.Status)
			}
			if len(tt.want.DepositRequests) != len(got.DepositRequests) {
				t.Errorf("Got deposit request of different length: got = %v, want = %v", len(got.DepositRequests), len(tt.want.DepositRequests))
			}

			for i := range tt.want.DepositRequests {
				if diff := isDepositRequestDeepEqual(got.DepositRequests[i], tt.want.DepositRequests[i]); diff != "" {
					t.Errorf("GetDepositRequestByFilter() got = %v, want %v, diff %s", got.DepositRequests[i], tt.want.DepositRequests[i], diff)
				}
			}
		})
	}
}

func TestService_GetDepositRequestsForActor(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTest.PrepareScopedDatabase(t, conf.EpifiDb.GetName(), db, depositAffectedTestTables)

	ctr := gomock.NewController(t)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()

	depositSvc := deposit.NewService(nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, depositAccountDao, depositRequestDao, nil, nil, nil, nil, nil, nil, brokerMock, conf, dynConf, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	defer func() {
		ctr.Finish()
		depositSvc = nil
	}()

	type args struct {
		ctx context.Context
		req *depositPb.GetDepositRequestsForActorRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *depositPb.GetDepositRequestsForActorResponse
		wantErr bool
	}{
		{
			name: "Should successfully fetch all deposit requests for an actor and type and account",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetDepositRequestsForActorRequest{
					ActorId:          fixtureActorId1,
					RequestType:      depositPb.RequestType_CREATE,
					DepositAccountId: fixtureDepositRequestCreateSD2.DepositAccountId,
					PageSize:         30,
				},
			},
			want: &depositPb.GetDepositRequestsForActorResponse{
				Status:          rpc.StatusOk(),
				DepositRequests: []*depositPb.DepositRequest{fixtureDepositRequestCreateSD2},
			},
			wantErr: false,
		},
		{
			name: "Should not fetch any deposit requests as no such actor exists",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetDepositRequestsForActorRequest{
					ActorId:     "random-actor-id",
					RequestType: 0,
					PageSize:    30,
				},
			},
			want: &depositPb.GetDepositRequestsForActorResponse{
				Status:          rpc.StatusOk(),
				DepositRequests: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := depositSvc.GetDepositRequestsForActor(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDepositRequestsForActor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.Status.Code != tt.want.Status.Code {
				t.Errorf("GetByAccountNumberAndIfsc() status: got = %v, want %v", got.Status, tt.want.Status)
			}

			for i := range tt.want.DepositRequests {
				var gotDR *depositPb.DepositRequest
				for j := range got.DepositRequests {
					if got.DepositRequests[j].Id == tt.want.DepositRequests[i].Id {
						gotDR = got.DepositRequests[j]
						break
					}
				}
				if diff := isDepositRequestDeepEqual(gotDR, tt.want.DepositRequests[i]); diff != "" {
					t.Errorf("GetByActorId() got = %v, want %v, diff %s", gotDR, tt.want.DepositRequests[i], diff)
				}
			}
		})
	}
}

func TestService_IsEligibleForCreateDeposit(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTest.PrepareScopedDatabase(t, conf.EpifiDb.GetName(), db, depositAffectedTestTables)

	ctr := gomock.NewController(t)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()

	mockSavingsClient := savingsMocks.NewMockSavingsClient(ctr)
	mockActorClient := actorMocks.NewMockActorClient(ctr)
	mockUsersClient := userMocks.NewMockUsersClient(ctr)
	onboardingAccessor := accessorMocks.NewMockIOnboardingAccessor(ctr)
	mockAccountBalanceBalanceClient := mockAccountBalance.NewMockBalanceClient(ctr)

	depositService = deposit.NewService(nil, nil, nil, mockUsersClient, mockSavingsClient, nil, mockActorClient, nil, nil, nil, nil, nil, nil, nil, nil, nil, depositAccountDao, depositRequestDao, nil, nil, nil, nil, nil, nil, brokerMock, conf, dynConf, nil, nil, nil, nil, nil, nil, onboardingAccessor, nil, nil, nil, nil, nil, nil, mockAccountBalanceBalanceClient, nil, nil)
	depositCreationAlreadyInProgressErrorStatus := func() *rpc.Status {
		return rpc.NewStatusWithoutDebug(uint32(depositPb.IsEligibleForCreateDepositResponse_DEPOSIT_CREATION_ALREADY_IN_PROGRESS),
			"Deposit Creation is already in progress. Please try again after few minutes.")
	}

	// depositCreationMinKycErrorStatus := func() *rpc.Status {
	// 	return rpc.NewStatusWithoutDebug(uint32(depositPb.IsEligibleForCreateDepositResponse_MIN_KYC_ACCOUNT_ERROR),
	// 		"Min kyc account holder can't open fixed deposits")
	// }
	mockSavingsClient.EXPECT().GetAccount(context.Background(), gomock.Any()).
		Return(&savingsPb.GetAccountResponse{Account: &savingsPb.Account{}}, nil).AnyTimes()

	mockAccountBalanceBalanceClient.EXPECT().GetAccountBalance(context.Background(),
		gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
		Status: rpc.StatusOk(),
		AvailableBalance: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        10000,
			Nanos:        0,
		},
		LedgerBalance: nil,
	}, nil).AnyTimes()
	mockActorClient.EXPECT().GetActorById(context.Background(), gomock.Any()).Return(&actorPb.GetActorByIdResponse{
		Status: rpc.StatusOk(),
		Actor: &types.Actor{
			Id:       fixtureActorId1,
			Type:     types.Actor_USER,
			EntityId: "Test-User-1",
			Name:     "test actor",
		},
	}, nil).AnyTimes()

	mockUsersClient.EXPECT().GetUser(context.Background(), gomock.Any()).Return(&userPb.GetUserResponse{
		Status: rpc.StatusOk(),
		User: &userPb.User{
			Id:      "Test-User-1",
			Profile: nil,
		},
	}, nil).AnyTimes()

	onboardingAccessor.EXPECT().IsFiLiteActor(context.Background(), gomock.Any()).Return(false, onboardingPb.FeatureStatus_FEATURE_STATUS_ACTIVE, nil).AnyTimes()

	defer func() {
		ctr.Finish()
		depositService = nil
	}()

	type args struct {
		ctx context.Context
		req *depositPb.IsEligibleForCreateDepositRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *depositPb.IsEligibleForCreateDepositResponse
		wantErr bool
	}{
		{
			name: "should return eligibility as true as user has no accounts till now",
			args: args{
				ctx: context.Background(),
				req: &depositPb.IsEligibleForCreateDepositRequest{ActorId: "random-actor",
					DepositAccountProvenance: depositPb.DepositAccountProvenance_USER_APP,
					DepositType:              accountsPb.Type_SMART_DEPOSIT,
				},
			},
			want: &depositPb.IsEligibleForCreateDepositResponse{
				Status:     rpc.StatusOk(),
				IsEligible: true,
			},
			wantErr: false,
		},
		{
			name: "should return eligibility as false as one deposit account is already in INITIATED state",
			args: args{
				ctx: context.Background(),
				req: &depositPb.IsEligibleForCreateDepositRequest{ActorId: fixtureActorId1,
					DepositAccountProvenance: depositPb.DepositAccountProvenance_USER_APP,
					DepositType:              accountsPb.Type_SMART_DEPOSIT,
				},
			},
			want: &depositPb.IsEligibleForCreateDepositResponse{
				Status:     depositCreationAlreadyInProgressErrorStatus(),
				IsEligible: false,
			},
			wantErr: false,
		},
		{
			name: "Should return eligibility as true in non prod env even if user account is of type MIN KYC",
			args: args{
				ctx: context.Background(),
				req: &depositPb.IsEligibleForCreateDepositRequest{
					ActorId:                  "fixtureActorId1",
					DepositType:              accountsPb.Type_FIXED_DEPOSIT,
					DepositAccountProvenance: depositPb.DepositAccountProvenance_USER_APP,
				},
			},
			want: &depositPb.IsEligibleForCreateDepositResponse{
				Status:     rpc.StatusOk(),
				IsEligible: true,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := depositService.IsEligibleForCreateDeposit(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsEligibleForCreateDeposit() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.Status.Code != tt.want.Status.Code {
				t.Errorf("IsEligibleForCreateDeposit() status: got = %v, want %v", got.Status, tt.want.Status)
			}
			if got.IsEligible != tt.want.IsEligible {
				t.Errorf("IsEligibleForCreateDeposit() isEligible: got = %v, want %v", got.IsEligible, tt.want.IsEligible)
			}
		})
	}
}

func TestService_IsEligibleForAddFunds(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTest.PrepareScopedDatabase(t, conf.EpifiDb.GetName(), db, depositAffectedTestTables)

	testTransaction1 := &paymentPb.Transaction{
		Id: "txn-1",
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        5000,
			Nanos:        0,
		},
	}
	testTransaction2 := &paymentPb.Transaction{
		Id: "txn-2",
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        194000,
			Nanos:        0,
		},
	}
	testTransaction3 := &paymentPb.Transaction{
		Id: "txn-3",
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        5000,
			Nanos:        0,
		},
		Status: paymentPb.TransactionStatus_MANUAL_INTERVENTION,
	}
	ctr := gomock.NewController(t)

	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()

	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockPaymentClient := paymentMocks.NewMockPaymentClient(ctr)
	mockActorClient := actorMocks.NewMockActorClient(ctr)
	mockUserClient := userMocks.NewMockUsersClient(ctr)
	mockUserGroupClient := groupMock.NewMockGroupClient(ctr)
	mockPanClient := panMock.NewMockPanClient(ctr)
	payMockClient := payMock.NewMockPayClient(ctr)

	mockUserGroupClient.EXPECT().GetGroupsMappedToEmail(gomock.Any(),
		gomock.Any()).Return(&userGroupPb.GetGroupsMappedToEmailResponse{
		Status: rpc.StatusOk(),
		Groups: []commontypes.UserGroup{commontypes.UserGroup_INTERNAL},
	}, nil).AnyTimes()

	mockPiClient.EXPECT().GetPi(gomock.Any(), gomock.Any()).
		Return(&piPb.GetPiResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: &piPb.PaymentInstrument{},
		}, nil).AnyTimes()

	mockPanClient.EXPECT().GetPANAadharLinkStatus(gomock.Any(), gomock.Any()).Return(&pan.GetPANAadharLinkStatusResponse{
		Status:              rpc.StatusOk(),
		PanAadharLinkStatus: pan.PANAadharLinkStatus_PAN_AADHAR_STATUS_LINKED,
	}, nil).AnyTimes()
	payMockClient.EXPECT().GetTransactionAggregates(gomock.Any(), gomock.Any()).Return(&payPb.GetTransactionAggregatesResponse{
		Status: rpc.StatusOk(),
		TransactionAggregates: &payPb.TransactionAggregates{
			Count: 10,
		},
	}, nil).AnyTimes()

	type mockGetTxnsByPi struct {
		enable bool
		res    *paymentPb.GetTxnsByPiResponse
		err    error
	}

	addFundsEligibilitySdMonthlyLimitExceededErrorStatus := func() *rpc.Status {
		return rpc.NewStatusWithoutDebug(uint32(depositPb.IsEligibleForAddFundsResponse_SD_MONTHLY_LIMIT_EXCEEDED),
			"Monthly limit exceeded for adding funds to this SD account.")
	}

	addFundsEligibilityPreclosureInProgressErrorStatus := func() *rpc.Status {
		return rpc.NewStatusWithoutDebug(uint32(depositPb.IsEligibleForAddFundsResponse_PRECLOSURE_IN_PROGRESS),
			"Pre closure for this deposit account is in progress.")
	}

	depositService = deposit.NewService(nil, nil, nil, mockUserClient, nil, nil, mockActorClient, mockPiClient, nil, nil, mockPaymentClient, nil, nil, nil, nil, nil, depositAccountDao, depositRequestDao, nil, nil, nil, nil, nil, nil, brokerMock, conf, dynConf, nil, nil, mockUserGroupClient, mockPanClient, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, payMockClient)

	defer func() {
		ctr.Finish()
		depositService = nil
	}()

	type args struct {
		ctx context.Context
		req *depositPb.IsEligibleForAddFundsRequest
	}
	tests := []struct {
		name                      string
		args                      args
		mockGetSuccessfulTxnsByPi mockGetTxnsByPi
		want                      *depositPb.IsEligibleForAddFundsResponse
		wantErr                   bool
	}{
		{
			name: "Should return eligibility as true",
			args: args{
				ctx: context.Background(),
				req: &depositPb.IsEligibleForAddFundsRequest{
					DepositAccountId: "deposit-account-sd-3",
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        200,
						Nanos:        0,
					},
				},
			},
			mockGetSuccessfulTxnsByPi: mockGetTxnsByPi{
				enable: true,
				res: &paymentPb.GetTxnsByPiResponse{
					Status:       rpc.StatusOk(),
					Transactions: []*paymentPb.Transaction{testTransaction1, testTransaction2},
				},
				err: nil,
			},
			want: &depositPb.IsEligibleForAddFundsResponse{
				Status:     rpc.StatusOk(),
				IsEligible: true,
			},
			wantErr: false,
		},
		{
			name: "Should return eligibility as false for SD account as preclosure is in progress",
			args: args{
				ctx: context.Background(),
				req: &depositPb.IsEligibleForAddFundsRequest{
					DepositAccountId: fixtureDepositAccountSD1.Id,
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        200,
						Nanos:        0,
					},
				},
			},
			want: &depositPb.IsEligibleForAddFundsResponse{
				Status:     addFundsEligibilityPreclosureInProgressErrorStatus(),
				IsEligible: false,
			},
			wantErr: false,
		},
		{
			name: "Should return eligibility as false for SD account as it'll exceed the monthly limit allowed",
			args: args{
				ctx: context.Background(),
				req: &depositPb.IsEligibleForAddFundsRequest{
					DepositAccountId: "deposit-account-sd-3",
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        11000,
						Nanos:        0,
					},
				},
			},
			mockGetSuccessfulTxnsByPi: mockGetTxnsByPi{
				enable: true,
				res: &paymentPb.GetTxnsByPiResponse{
					Status:       rpc.StatusOk(),
					Transactions: []*paymentPb.Transaction{testTransaction2, testTransaction1},
				},
				err: nil,
			},
			want: &depositPb.IsEligibleForAddFundsResponse{
				Status:     addFundsEligibilitySdMonthlyLimitExceededErrorStatus(),
				IsEligible: false,
			},
			wantErr: false,
		},
		{
			name: "should return eligibility as false as txns in manual intervention exceed the SD limit",
			args: args{
				ctx: context.Background(),
				req: &depositPb.IsEligibleForAddFundsRequest{
					DepositAccountId: fixtureDepositAccountSD3.GetId(),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        5100,
						Nanos:        0,
					},
				},
			},
			mockGetSuccessfulTxnsByPi: mockGetTxnsByPi{
				enable: true,
				res: &paymentPb.GetTxnsByPiResponse{
					Status:       rpc.StatusOk(),
					Transactions: []*paymentPb.Transaction{testTransaction2, testTransaction3},
				},
				err: nil,
			},
			want: &depositPb.IsEligibleForAddFundsResponse{
				Status:     addFundsEligibilitySdMonthlyLimitExceededErrorStatus(),
				IsEligible: false,
			},
			wantErr: false,
		},
		{
			name: "Should return internal server status",
			args: args{
				ctx: context.Background(),
				req: &depositPb.IsEligibleForAddFundsRequest{
					DepositAccountId: "deposit-account-sd-3",
					Amount:           fixtureDepositAccountSD1.PrincipalAmount,
				},
			},
			mockGetSuccessfulTxnsByPi: mockGetTxnsByPi{
				enable: true,
				res: &paymentPb.GetTxnsByPiResponse{
					Status:       rpc.StatusInternal(),
					Transactions: nil,
				},
				err: nil,
			},
			want: &depositPb.IsEligibleForAddFundsResponse{
				Status:     rpc.StatusInternal(),
				IsEligible: false,
			},
			wantErr: false,
		},
		// TODO(kunal): Add test cases for other status codes
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetSuccessfulTxnsByPi.enable {
				mockPaymentClient.EXPECT().GetTxnsByPi(tt.args.ctx, gomock.Any()).
					Return(tt.mockGetSuccessfulTxnsByPi.res, tt.mockGetSuccessfulTxnsByPi.err)
			}
			got, err := depositService.IsEligibleForAddFunds(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsEligibleForAddFunds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.Status.Code != tt.want.Status.Code {
				t.Errorf("IsEligibleForAddFunds() status got = %v, want = %v", got.Status, tt.want.Status)
			}
			if got.IsEligible != tt.want.IsEligible {
				t.Errorf("IsEligibleForAddFunds() isEligible got = %v want = %v", got.IsEligible, tt.want.IsEligible)
			}
		})
	}
}

func TestService_ProcessCreateDepositInboundTxn(t *testing.T) {
	partnerExecutedAt, err := datetime.ParseStringTimeStampProto("2006-01-02:15:04:05", "2021-01-05:15:04:05")
	assert.Nil(t, err)

	testTransactionDetails := &depositPb.InboundNotificationTxnDetails{
		AccountNumber: "**********",
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        1000,
		},
		Particular:      "Dr. Tran for funding A/c **********",
		ReferenceNumber: "1234123",
		Timestamp:       partnerExecutedAt,
	}
	testEntityId := "Test-User-1"

	ctrl := gomock.NewController(t)

	brokerMock := brokerMocks.NewMockBroker(ctrl)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	invEventPublisher := mock_queue.NewMockPublisher(ctrl)
	invEventPublisher.EXPECT().Publish(gomock.Any(), gomock.Any())

	savingsClient := savingsMocks.NewMockSavingsClient(ctrl)
	actorClient := actorMocks.NewMockActorClient(ctrl)
	piClient := piMocks.NewMockPiClient(ctrl)
	orderClient := orderMocks.NewMockOrderServiceClient(ctrl)
	commsClient := commsMocks.NewMockCommsClient(ctrl)

	actorClient.EXPECT().GetActorByEntityId(context.Background(), gomock.Any()).Return(&actorPb.GetActorByEntityIdResponse{
		Status: rpc.StatusOk(),
		Actor: &types.Actor{
			Id:       fixtureActorId1,
			Type:     types.Actor_USER,
			EntityId: "Test-User-1",
			Name:     "test actor",
		},
	}, nil).AnyTimes()

	piClient.EXPECT().
		GetPi(context.Background(), gomock.Any()).
		Return(&piPb.GetPiResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{}}, nil).AnyTimes()

	commsClient.EXPECT().
		SendMessage(context.Background(), gomock.Any()).
		Return(&commsPb.SendMessageResponse{Status: rpc.StatusOk()}, nil).AnyTimes()

	depositSvc := deposit.NewService(nil, nil, nil, nil, savingsClient, nil, actorClient, piClient, nil, orderClient, nil, commsClient, nil, nil, nil, nil, depositAccountDao, depositRequestDao, nil, nil, nil, nil, nil, nil, brokerMock, conf, dynConf, nil, invEventPublisher, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	defer func() {
		ctrl.Finish()
		depositSvc = nil
	}()

	type mockSavingsGetAccount struct {
		enable   bool
		response *savingsPb.GetAccountResponse
		err      error
	}

	type mockCreateOrderWithTxn struct {
		enable   bool
		response *orderPb.CreateOrderWithTransactionResponse
		err      error
	}

	type mockGetSavingsAccountEssentials struct {
		enable   bool
		request  *savingsPb.GetSavingsAccountEssentialsRequest
		response *savingsPb.GetSavingsAccountEssentialsResponse
		err      error
	}

	type args struct {
		ctx context.Context
		req *depositPb.ProcessCreateDepositInboundTxnRequest
	}
	var tests = []struct {
		name                            string
		args                            args
		shouldPrepareDB                 bool
		want                            *depositPb.ProcessCreateDepositInboundTxnResponse
		wantErr                         bool
		mockSavingsGetAccount           mockSavingsGetAccount
		mockCreateOrderWithTxn          mockCreateOrderWithTxn
		mockGetSavingsAccountEssentials mockGetSavingsAccountEssentials
	}{
		{
			name: "#1.1 Should result in waiting status response as deposit account is not created",
			args: args{
				ctx: context.Background(),
				req: &depositPb.ProcessCreateDepositInboundTxnRequest{
					PartnerBank:          commonvgpb.Vendor_FEDERAL_BANK,
					DepositAccountNumber: "random-account-number",
					TransactionDetails:   testTransactionDetails,
				},
			},
			shouldPrepareDB: true,
			want:            &depositPb.ProcessCreateDepositInboundTxnResponse{Status: deposit.CreateDepositInboundTxnWaitingStatus()},
			wantErr:         false,
		},
		{
			name: "#2.1 Should successfully process inbound txn",
			args: args{
				ctx: context.Background(),
				req: &depositPb.ProcessCreateDepositInboundTxnRequest{
					PartnerBank:          commonvgpb.Vendor_FEDERAL_BANK,
					DepositAccountNumber: fixtureDepositAccountFD1.AccountNumber,
					TransactionDetails:   testTransactionDetails,
				},
			},
			shouldPrepareDB: true,
			want:            &depositPb.ProcessCreateDepositInboundTxnResponse{Status: rpc.StatusOk()},
			wantErr:         false,
			mockSavingsGetAccount: mockSavingsGetAccount{
				enable: true,
				response: &savingsPb.GetAccountResponse{
					Account: &savingsPb.Account{AccountNo: testTransactionDetails.AccountNumber, PrimaryAccountHolder: testEntityId}},
				err: nil,
			},
			mockCreateOrderWithTxn: mockCreateOrderWithTxn{
				enable: true,
				response: &orderPb.CreateOrderWithTransactionResponse{
					Status:      rpc.StatusOk(),
					Order:       &orderPb.Order{},
					Transaction: &paymentPb.Transaction{},
				},
				err: nil,
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				response: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status:  rpc.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{AccountNo: testTransactionDetails.GetAccountNumber(), IfscCode: "FDRL0001001"},
				},
				request: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_AccountNumBankFilter{
						AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
							AccountNumber: testTransactionDetails.GetAccountNumber(),
							PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
						},
					}},
				err: nil,
			},
		},
		{
			name: "#2.2 Should successfully process the same inbound txn again and not create another order and txn",
			args: args{
				ctx: context.Background(),
				req: &depositPb.ProcessCreateDepositInboundTxnRequest{
					PartnerBank:          commonvgpb.Vendor_FEDERAL_BANK,
					DepositAccountNumber: fixtureDepositAccountFD1.AccountNumber,
					TransactionDetails:   testTransactionDetails,
				},
			},
			shouldPrepareDB: false,
			want:            &depositPb.ProcessCreateDepositInboundTxnResponse{Status: rpc.StatusOk()},
			wantErr:         false,
			mockSavingsGetAccount: mockSavingsGetAccount{
				enable: true,
				response: &savingsPb.GetAccountResponse{
					Account: &savingsPb.Account{AccountNo: testTransactionDetails.AccountNumber, PrimaryAccountHolder: testEntityId}},
				err: nil,
			},
			mockCreateOrderWithTxn: mockCreateOrderWithTxn{
				enable: true,
				response: &orderPb.CreateOrderWithTransactionResponse{
					Status:      rpc.StatusAlreadyExists(),
					Order:       &orderPb.Order{},
					Transaction: &paymentPb.Transaction{},
				},
				err: nil,
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				response: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status:  rpc.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{AccountNo: testTransactionDetails.GetAccountNumber(), IfscCode: "FDRL0001001"},
				},
				request: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_AccountNumBankFilter{
						AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
							AccountNumber: testTransactionDetails.GetAccountNumber(),
							PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
						},
					}},
				err: nil,
			},
		},
		{
			name: "#3.1 Should successfully process inbound txn for already closed account and not create new deposit account",
			args: args{
				ctx: context.Background(),
				req: &depositPb.ProcessCreateDepositInboundTxnRequest{
					PartnerBank:          commonvgpb.Vendor_FEDERAL_BANK,
					DepositAccountNumber: fixtureDepositAccountSD2.AccountNumber,
					TransactionDetails: &depositPb.InboundNotificationTxnDetails{
						AccountNumber: testTransactionDetails.AccountNumber,
						Amount:        testTransactionDetails.Amount,
						Date:          testTransactionDetails.Date,
						Timestamp:     partnerExecutedAt,
					},
				},
			},
			shouldPrepareDB: true,
			want:            &depositPb.ProcessCreateDepositInboundTxnResponse{Status: rpc.StatusOk()},
			wantErr:         false,
			mockSavingsGetAccount: mockSavingsGetAccount{
				enable: true,
				response: &savingsPb.GetAccountResponse{
					Account: &savingsPb.Account{AccountNo: testTransactionDetails.AccountNumber, PrimaryAccountHolder: testEntityId}},
				err: nil,
			},
			mockCreateOrderWithTxn: mockCreateOrderWithTxn{
				enable: true,
				response: &orderPb.CreateOrderWithTransactionResponse{
					Status:      rpc.StatusAlreadyExists(),
					Order:       &orderPb.Order{},
					Transaction: &paymentPb.Transaction{},
				},
				err: nil,
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				response: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status:  rpc.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{AccountNo: testTransactionDetails.GetAccountNumber(), IfscCode: "FDRL0001001"},
				},
				request: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_AccountNumBankFilter{
						AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
							AccountNumber: testTransactionDetails.GetAccountNumber(),
							PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
						},
					}},
				err: nil,
			},
		},
		{
			name: "#4.1 Should fail as associated savings account doesn't exists",
			args: args{
				ctx: context.Background(),
				req: &depositPb.ProcessCreateDepositInboundTxnRequest{
					PartnerBank:          commonvgpb.Vendor_FEDERAL_BANK,
					DepositAccountNumber: fixtureDepositAccountFD1.AccountNumber,
					TransactionDetails: &depositPb.InboundNotificationTxnDetails{
						AccountNumber: "random-account",
						Amount:        testTransactionDetails.Amount,
						Date:          testTransactionDetails.Date,
					},
				},
			},
			shouldPrepareDB: true,
			want:            &depositPb.ProcessCreateDepositInboundTxnResponse{Status: rpc.StatusInternal()},
			wantErr:         false,
			mockSavingsGetAccount: mockSavingsGetAccount{
				enable:   true,
				response: nil,
				err:      epifierrors.ErrRecordNotFound,
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				response: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status:  rpc.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{AccountNo: "random-account", IfscCode: "FDRL0001001"},
				},
				request: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_AccountNumBankFilter{
						AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
							AccountNumber: "random-account",
							PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
						},
					}},
				err: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clean database, run migrations and load fixtures
			if tt.shouldPrepareDB {
				pkgTest.PrepareScopedDatabase(t, conf.EpifiDb.GetName(), db, depositAffectedTestTables)
			}
			if tt.mockCreateOrderWithTxn.enable {
				orderClient.EXPECT().CreateOrderWithTransaction(gomock.Any(), gomock.Any()).Return(tt.mockCreateOrderWithTxn.response,
					tt.mockCreateOrderWithTxn.err)
			}
			if tt.mockSavingsGetAccount.enable {
				savingsClient.EXPECT().GetAccount(tt.args.ctx, gomock.Any()).Return(tt.mockSavingsGetAccount.response, tt.mockSavingsGetAccount.err)
			}
			if tt.mockGetSavingsAccountEssentials.enable {
				savingsClient.EXPECT().GetSavingsAccountEssentials(tt.args.ctx, tt.mockGetSavingsAccountEssentials.request).Times(1).Return(tt.mockGetSavingsAccountEssentials.response, nil)
			}

			got, err := depositSvc.ProcessCreateDepositInboundTxn(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessCreateDepositInboundTxn() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if got.Status.Code != tt.want.Status.Code {
				t.Errorf("ProcessCreateDepositInboundTxn() got status = %v, want status %v", got.Status, tt.want.Status)
			}
		})
	}
}

func TestService_ProcessPrecloseDepositInboundTxn(t *testing.T) {
	partnerExecutedAt, err := datetime.ParseStringTimeStampProto("2006-01-02:15:04:05", "2021-01-05:15:04:05")
	assert.Nil(t, err)

	testTransactionDetails := &depositPb.InboundNotificationTxnDetails{
		AccountNumber: "**********",
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        1000,
		},
		Particular:      "RD-************** Matured on 17-07-2023 TO Credit to Repayment Acct.",
		ReferenceNumber: "1234123",
		Timestamp:       partnerExecutedAt,
	}
	ctrl := gomock.NewController(t)

	brokerMock := brokerMocks.NewMockBroker(ctrl)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()

	savingsClient := savingsMocks.NewMockSavingsClient(ctrl)
	actorClient := actorMocks.NewMockActorClient(ctrl)
	userClient := userMocks.NewMockUsersClient(ctrl)
	piClient := piMocks.NewMockPiClient(ctrl)
	accountPiClient := accountPiMocks.NewMockAccountPIRelationClient(ctrl)
	orderClient := orderMocks.NewMockOrderServiceClient(ctrl)
	commsClient := commsMocks.NewMockCommsClient(ctrl)
	// currently we are not mocking dao functions and directly calling them using test db
	// so using updateRequest actual implementation instead of mock
	requestUpdateNotifier := update_request.NewNotifierImpl(depositRequestDao, nil, nil)
	updateRequest := update_request.NewUpdaterImpl(depositRequestDao, requestUpdateNotifier, dynConf)
	depositSvc := deposit.NewService(nil, nil, nil, userClient, savingsClient, nil, actorClient, piClient, accountPiClient, orderClient, nil, commsClient, nil, nil, nil, nil, depositAccountDao, depositRequestDao, nil, nil, nil, nil, nil, nil, brokerMock, conf, dynConf, txnExecutor, nil, nil, nil, nil, updateRequest, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	defer func() {
		ctrl.Finish()
		depositSvc = nil
	}()

	type mockGetActorById struct {
		enable   bool
		response *actorPb.GetActorByIdResponse
		err      error
	}

	type mockGetPi struct {
		enable   bool
		request  *piPb.GetPiRequest
		response *piPb.GetPiResponse
		err      error
	}

	type mockUpdatePiState struct {
		enable   bool
		response *piPb.UpdatePiResponse
		err      error
	}

	type mockCreateOrderWithTxn struct {
		enable   bool
		response *orderPb.CreateOrderWithTransactionResponse
		err      error
	}

	type mockSendNotification struct {
		enable   bool
		response *commsPb.SendMessageResponse
		err      error
	}

	type mockGetSavingsAccountEssentials struct {
		enable   bool
		request  *savingsPb.GetSavingsAccountEssentialsRequest
		response *savingsPb.GetSavingsAccountEssentialsResponse
		err      error
	}

	type args struct {
		ctx context.Context
		req *depositPb.ProcessPrecloseDepositInboundTxnRequest
	}
	tests := []struct {
		name                            string
		shouldPrepareDB                 bool
		args                            args
		want                            *depositPb.ProcessPrecloseDepositInboundTxnResponse
		wantDepositAccountState         depositPb.DepositState
		wantErr                         bool
		wantIsAddFundsAllowed           bool
		mockGetActorById                mockGetActorById
		mockGetPi                       []mockGetPi
		mockUpdatePiState               mockUpdatePiState
		mockCreateOrderWithTxn          mockCreateOrderWithTxn
		mockSendNotification            mockSendNotification
		mockGetSavingsAccountEssentials mockGetSavingsAccountEssentials
	}{
		{
			name:            "Should successfully process close deposit inbound txn",
			shouldPrepareDB: true,
			args: args{
				req: &depositPb.ProcessPrecloseDepositInboundTxnRequest{
					DepositAccountNumber: fixtureDepositAccountSD1.AccountNumber,
					TransactionDetails:   testTransactionDetails,
					PartnerBank:          commonvgpb.Vendor_FEDERAL_BANK,
					IsAutoClose:          true,
				},
				ctx: context.Background(),
			},
			want: &depositPb.ProcessPrecloseDepositInboundTxnResponse{
				Status: rpc.StatusOk(),
			},
			wantDepositAccountState: depositPb.DepositState_CLOSED,
			wantErr:                 false,
			wantIsAddFundsAllowed:   false,
			mockGetActorById: mockGetActorById{
				enable: true,
				response: &actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type:     types.Actor_USER,
						Id:       "actor-user-3",
						EntityId: "entity-id",
						Name:     "actor-name",
					},
				},
				err: nil,
			},
			mockGetPi: []mockGetPi{
				{
					enable: true,
					request: &piPb.GetPiRequest{
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.GetPiRequest_AccountRequestParams_{AccountRequestParams: &piPb.GetPiRequest_AccountRequestParams{
							ActualAccountNumber: fixtureDepositAccountSD1.AccountNumber,
							IfscCode:            fixtureIfscCode,
						}},
					},
					response: &piPb.GetPiResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							State: piPb.PaymentInstrumentState_CREATED,
							Id:    "piId-2",
							Type:  piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SMART_DEPOSIT,
									IfscCode:    fixtureIfscCode,
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					request: &piPb.GetPiRequest{
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.GetPiRequest_AccountRequestParams_{AccountRequestParams: &piPb.GetPiRequest_AccountRequestParams{
							ActualAccountNumber: fixtureDepositAccountSD1.RepayAccountNumber,
							IfscCode:            fixtureIfscCode,
						}},
					},
					response: &piPb.GetPiResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							State: piPb.PaymentInstrumentState_CREATED,
							Id:    "piId-1",
							Type:  piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SAVINGS,
									IfscCode:    fixtureIfscCode,
								},
							},
						},
					},
					err: nil,
				},
			},
			mockUpdatePiState: mockUpdatePiState{
				enable: true,
				response: &piPb.UpdatePiResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockCreateOrderWithTxn: mockCreateOrderWithTxn{
				enable: true,
				response: &orderPb.CreateOrderWithTransactionResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockSendNotification: mockSendNotification{
				enable: true,
				response: &commsPb.SendMessageResponse{
					Status:    rpc.StatusOk(),
					MessageId: "message-id-1",
				},
				err: nil,
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				response: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status:  rpc.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{AccountNo: testTransactionDetails.GetAccountNumber(), IfscCode: "FDRL0001001"},
				},
				request: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_AccountNumBankFilter{
						AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
							AccountNumber: testTransactionDetails.GetAccountNumber(),
							PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
						},
					}},
				err: nil,
			},
		},
		{
			name:            "Should return RNF if deposit account not found",
			shouldPrepareDB: false,
			args: args{
				ctx: context.Background(),
				req: &depositPb.ProcessPrecloseDepositInboundTxnRequest{
					DepositAccountNumber: "deposit-account-not-found",
					PartnerBank:          commonvgpb.Vendor_FEDERAL_BANK,
				},
			},
			want: &depositPb.ProcessPrecloseDepositInboundTxnResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr:               false,
			wantIsAddFundsAllowed: true,
		},
		{
			name:            "Should successfully process preclosed deposit inbound txn",
			shouldPrepareDB: true,
			args: args{
				ctx: context.Background(),
				req: &depositPb.ProcessPrecloseDepositInboundTxnRequest{
					DepositAccountNumber: fixtureDepositAccountSD2.AccountNumber,
					TransactionDetails:   testTransactionDetails,
					PartnerBank:          commonvgpb.Vendor_FEDERAL_BANK,
				},
			},
			want: &depositPb.ProcessPrecloseDepositInboundTxnResponse{
				Status: rpc.StatusOk(),
			},
			wantDepositAccountState: depositPb.DepositState_PRECLOSED,
			wantErr:                 false,
			wantIsAddFundsAllowed:   true,
			mockGetActorById: mockGetActorById{
				enable: true,
				response: &actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Type:     types.Actor_USER,
						Id:       fixtureActorId1,
						EntityId: "entity-id",
						Name:     "actor-name",
					},
				},
				err: nil,
			},
			mockGetPi: []mockGetPi{
				{
					enable: true,
					request: &piPb.GetPiRequest{
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.GetPiRequest_AccountRequestParams_{AccountRequestParams: &piPb.GetPiRequest_AccountRequestParams{
							ActualAccountNumber: fixtureDepositAccountSD2.AccountNumber,
							IfscCode:            fixtureIfscCode,
						}},
					},
					response: &piPb.GetPiResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							State: piPb.PaymentInstrumentState_CREATED,
							Id:    "piId-2",
							Type:  piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SMART_DEPOSIT,
									IfscCode:    fixtureIfscCode,
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					request: &piPb.GetPiRequest{
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.GetPiRequest_AccountRequestParams_{AccountRequestParams: &piPb.GetPiRequest_AccountRequestParams{
							ActualAccountNumber: fixtureDepositAccountSD2.RepayAccountNumber,
							IfscCode:            fixtureIfscCode,
						}},
					},
					response: &piPb.GetPiResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							State: piPb.PaymentInstrumentState_CREATED,
							Id:    "piId-1",
							Type:  piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									AccountType: accountsPb.Type_SAVINGS,
									IfscCode:    fixtureIfscCode,
								},
							},
						},
					},
					err: nil,
				},
			},
			mockUpdatePiState: mockUpdatePiState{
				enable: true,
				response: &piPb.UpdatePiResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockCreateOrderWithTxn: mockCreateOrderWithTxn{
				enable: true,
				response: &orderPb.CreateOrderWithTransactionResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			mockSendNotification: mockSendNotification{
				enable: true,
				response: &commsPb.SendMessageResponse{
					Status:    rpc.StatusOk(),
					MessageId: "message-id-1",
				},
				err: nil,
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				response: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status:  rpc.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{AccountNo: testTransactionDetails.GetAccountNumber(), IfscCode: "FDRL0001001"},
				},
				request: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_AccountNumBankFilter{
						AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
							AccountNumber: testTransactionDetails.GetAccountNumber(),
							PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
						},
					}},
				err: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.shouldPrepareDB {
				pkgTest.PrepareScopedDatabase(t, conf.EpifiDb.GetName(), db, depositAffectedTestTables)
			}
			for _, getPi := range tt.mockGetPi {
				if getPi.enable {
					piClient.EXPECT().GetPi(context.Background(), getPi.request).
						Return(getPi.response, getPi.err)
				}
			}
			if tt.mockGetActorById.enable {
				actorClient.EXPECT().GetActorById(context.Background(), gomock.Any()).
					Return(tt.mockGetActorById.response, nil)
			}
			if tt.mockCreateOrderWithTxn.enable {
				orderClient.EXPECT().CreateOrderWithTransaction(context.Background(), gomock.Any()).
					Return(tt.mockCreateOrderWithTxn.response, nil)
			}
			if tt.mockSendNotification.enable {
				commsClient.EXPECT().SendMessage(context.Background(), gomock.Any()).
					Return(tt.mockSendNotification.response, nil).Times(1)
			}
			if tt.mockUpdatePiState.enable {
				piClient.EXPECT().UpdatePi(context.Background(), gomock.Any()).
					Return(tt.mockUpdatePiState.response, nil)
			}
			if tt.mockGetSavingsAccountEssentials.enable {
				savingsClient.EXPECT().GetSavingsAccountEssentials(tt.args.ctx, tt.mockGetSavingsAccountEssentials.request).Times(1).Return(tt.mockGetSavingsAccountEssentials.response, nil)
			}
			got, err := depositSvc.ProcessPrecloseDepositInboundTxn(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessPrecloseDepositInboundTxn() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("ProcessPrecloseDepositInboundTxn() got = %v, want %v", got, tt.want)
			}
			if tt.want.Status.IsSuccess() {
				depositAccount, err := depositSvc.DepositAccountDao.GetByAccountNumberAndIfsc(context.Background(), tt.args.req.DepositAccountNumber, fixtureIfscCode)
				if err != nil {
					t.Errorf("error in getting deposit account %v", err)
					return
				}
				if depositAccount.State != tt.wantDepositAccountState {
					t.Errorf("deposit account state got= %v want=%v", depositAccount.State.String(), tt.wantDepositAccountState.String())
				}

				if depositAccount.IsAddFundsAllowed != tt.wantIsAddFundsAllowed {
					t.Errorf("deposit account isAddFundsAllowed got= %v want= %v", depositAccount.IsAddFundsAllowed, tt.wantIsAddFundsAllowed)
				}
			}
		})
	}
}

func TestService_IsDepositInProgress(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTest.PrepareScopedDatabase(t, conf.EpifiDb.GetName(), db, depositAffectedTestTables)

	ctr := gomock.NewController(t)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()

	depositSvc := deposit.NewService(nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, depositAccountDao, depositRequestDao, nil, nil, nil, nil, nil, nil, brokerMock, conf, dynConf, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	type args struct {
		ctx context.Context
		req *depositPb.IsDepositInProgressRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *depositPb.IsDepositInProgressResponse
		wantErr bool
	}{
		{
			name: "Should successfully check if deposit creation is in progress",
			args: args{
				ctx: context.Background(),
				req: &depositPb.IsDepositInProgressRequest{
					ActorId:     fixtureActorId1,
					RequestType: depositPb.RequestType_CREATE,
				},
			},
			want: &depositPb.IsDepositInProgressResponse{
				Status:              rpc.StatusOk(),
				IsDepositInProgress: true,
			},
			wantErr: false,
		},
		{
			name: "Should successfully check if deposit pre-closure is in progress",
			args: args{
				ctx: context.Background(),
				req: &depositPb.IsDepositInProgressRequest{
					ActorId:     fixtureActorId1,
					RequestType: depositPb.RequestType_PRECLOSE,
				},
			},
			want: &depositPb.IsDepositInProgressResponse{
				Status:              rpc.StatusOk(),
				IsDepositInProgress: true,
			},
			wantErr: false,
		},
		{
			name: "Should return deposit creation as false for random actor",
			args: args{
				ctx: context.Background(),
				req: &depositPb.IsDepositInProgressRequest{
					ActorId:     "random-actor-id",
					RequestType: depositPb.RequestType_CREATE,
				},
			},
			want: &depositPb.IsDepositInProgressResponse{
				Status:              rpc.StatusOk(),
				IsDepositInProgress: false,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := depositSvc.IsDepositInProgress(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsDepositInProgress() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.Status.Code != tt.want.Status.Code {
				t.Errorf("IsDepositInProgress() status: got = %v, want %v", got.Status, tt.want.Status)
			}
			if got.IsDepositInProgress != tt.want.IsDepositInProgress {
				t.Errorf("IsDepositInProgress() isDepositInProgress: got = %v, want %v", got.IsDepositInProgress, tt.want.IsDepositInProgress)
			}
		})
	}
}

func isDepositAccountDeepEqual(actual, expected *depositPb.DepositAccount) string {
	if actual != nil && expected != nil {
		expected.CreatedAt = actual.CreatedAt
		expected.UpdatedAt = actual.UpdatedAt
		expected.DeletedAt = actual.DeletedAt
		expected.MaturityDate = actual.MaturityDate
	}
	return cmp.Diff(actual, expected, protocmp.Transform())
}

func isDepositRequestDeepEqual(actual, expected *depositPb.DepositRequest) string {
	if actual != nil && expected != nil {
		expected.CreatedAt = actual.CreatedAt
		expected.UpdatedAt = actual.UpdatedAt
		expected.DeletedAt = actual.DeletedAt
	}
	return cmp.Diff(actual, expected, protocmp.Transform())
}

func TestService_GetInterestRates(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTest.PrepareScopedDatabase(t, conf.EpifiDb.GetName(), db, depositAffectedTestTables)

	ctr := gomock.NewController(t)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()

	depositService = deposit.NewService(nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, depositAccountDao, depositRequestDao, nil, nil, nil, nil, nil, nil, brokerMock, conf, dynConf, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	defer func() {
		ctr.Finish()
		depositService = nil
	}()

	type args struct {
		ctx context.Context
		req *depositPb.GetInterestRatesRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *depositPb.GetInterestRatesResponse
		wantErr bool
	}{
		{
			name: "Should return all interest rates",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetInterestRatesRequest{},
			},
			want:    &depositPb.GetInterestRatesResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := depositService.GetInterestRates(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetInterestRates() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if got.Status.Code != tt.want.Status.Code {
				t.Errorf("depositService.GetInterestRates() status: got = %v, want %v", got.Status, tt.want.Status)
			}
		})
	}
}

func TestService_GetDepositTemplates(t *testing.T) {
	type fields struct {
		depositAccountDao  *mocks.MockDepositAccountDao
		bcClient           *bankCustMocks.MockBankCustomerServiceClient
		onboardingAccessor *accessorMocks.MockIOnboardingAccessor
	}
	type args struct {
		ctx context.Context
		req *depositPb.GetDepositTemplatesRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *depositPb.GetDepositTemplatesResponse
		wantErr bool
		// change this in the future if templates Jar are added or removed
		totalFetchedTemplates int
		prepare               func(*args, *fields)
	}{
		{
			name: "Successfully fetch all templates for Smart Deposit",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetDepositTemplatesRequest{Type: accountsPb.Type_SMART_DEPOSIT},
			},
			want:                  &depositPb.GetDepositTemplatesResponse{Status: rpc.StatusOk()},
			wantErr:               false,
			totalFetchedTemplates: 8,
			prepare: func(a *args, f *fields) {

				f.depositAccountDao.EXPECT().GetByActorIdAndTemplateIDs(gomock.Any(), gomock.Any(),
					gomock.Any()).Return([]*depositPb.DepositAccount{}, nil).AnyTimes()
				f.depositAccountDao.EXPECT().GetCountByActorIdGroupByTemplateId(gomock.Any(),
					gomock.Any()).Return(map[string]int32{}, nil).AnyTimes()
				f.bcClient.EXPECT().GetBankCustomer(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&bankcust.GetBankCustomerResponse{
						BankCustomer: &bankcust.BankCustomer{
							KycInfo: &bankcust.KYCInfo{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
						Status: rpc.StatusOk(),
					}, nil)
				f.onboardingAccessor.EXPECT().IsFiLiteActor(gomock.Any(), gomock.Any()).Return(false, onboardingPb.FeatureStatus_FEATURE_STATUS_ACTIVE, nil)
			},
		},
		{
			name: "Successfully fetch all templates for Fixed Deposit",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetDepositTemplatesRequest{Type: accountsPb.Type_FIXED_DEPOSIT},
			},
			want:                  &depositPb.GetDepositTemplatesResponse{Status: rpc.StatusOk()},
			wantErr:               false,
			totalFetchedTemplates: 7,
			prepare: func(a *args, f *fields) {

				f.depositAccountDao.EXPECT().GetByActorIdAndTemplateIDs(gomock.Any(), gomock.Any(),
					gomock.Any()).Return([]*depositPb.DepositAccount{}, nil).AnyTimes()
				f.depositAccountDao.EXPECT().GetCountByActorIdGroupByTemplateId(gomock.Any(),
					gomock.Any()).Return(map[string]int32{}, nil).AnyTimes()
				f.bcClient.EXPECT().GetBankCustomer(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&bankcust.GetBankCustomerResponse{
						BankCustomer: &bankcust.BankCustomer{
							KycInfo: &bankcust.KYCInfo{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
						Status: rpc.StatusOk(),
					}, nil)
				f.onboardingAccessor.EXPECT().IsFiLiteActor(gomock.Any(), gomock.Any()).Return(false, onboardingPb.FeatureStatus_FEATURE_STATUS_ACTIVE, nil)
			},
		},
		{
			name: "Should fail to fetch template for Current account",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetDepositTemplatesRequest{Type: accountsPb.Type_CURRENT},
			},
			want:                  &depositPb.GetDepositTemplatesResponse{Status: rpc.StatusInternal()},
			wantErr:               false,
			totalFetchedTemplates: 0,
			prepare: func(a *args, f *fields) {
				f.depositAccountDao.EXPECT().GetByActorIdAndTemplateIDs(gomock.Any(), gomock.Any(),
					gomock.Any()).Return([]*depositPb.DepositAccount{}, nil).AnyTimes()
				f.depositAccountDao.EXPECT().GetCountByActorIdGroupByTemplateId(gomock.Any(),
					gomock.Any()).Return(map[string]int32{}, nil).AnyTimes()
			},
		},
		{
			name: "should hide template if its max no of accounts is reached",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetDepositTemplatesRequest{Type: accountsPb.Type_SMART_DEPOSIT},
			},
			want:                  &depositPb.GetDepositTemplatesResponse{Status: rpc.StatusOk()},
			wantErr:               false,
			totalFetchedTemplates: 7,
			prepare: func(a *args, f *fields) {
				f.depositAccountDao.EXPECT().GetByActorIdAndTemplateIDs(gomock.Any(), gomock.Any(),
					gomock.Any()).Return([]*depositPb.DepositAccount{}, nil).AnyTimes()
				f.depositAccountDao.EXPECT().GetCountByActorIdGroupByTemplateId(gomock.Any(),
					"").Return(map[string]int32{"SD-test-1": 3}, nil).AnyTimes()
				f.depositAccountDao.EXPECT().GetCountByActorIdGroupByTemplateId(gomock.Any(),
					"UNKNOWN_ID").Return(map[string]int32{"SD-test-1": 0}, nil).AnyTimes()
				f.bcClient.EXPECT().GetBankCustomer(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&bankcust.GetBankCustomerResponse{
						BankCustomer: &bankcust.BankCustomer{
							KycInfo: &bankcust.KYCInfo{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
						Status: rpc.StatusOk(),
					}, nil)
				f.onboardingAccessor.EXPECT().IsFiLiteActor(gomock.Any(), gomock.Any()).Return(false, onboardingPb.FeatureStatus_FEATURE_STATUS_ACTIVE, nil)
			},
		},
		{
			name: "should hide template if its max no of accounts per user is reached",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetDepositTemplatesRequest{Type: accountsPb.Type_SMART_DEPOSIT},
			},
			want:                  &depositPb.GetDepositTemplatesResponse{Status: rpc.StatusOk()},
			wantErr:               false,
			totalFetchedTemplates: 7,
			prepare: func(a *args, f *fields) {
				f.depositAccountDao.EXPECT().GetByActorIdAndTemplateIDs(gomock.Any(), gomock.Any(),
					gomock.Any()).Return([]*depositPb.DepositAccount{}, nil).Times(1)
				f.depositAccountDao.EXPECT().GetCountByActorIdGroupByTemplateId(gomock.Any(),
					"").Return(map[string]int32{"SD-test-1": 0}, nil).Times(1)
				f.depositAccountDao.EXPECT().GetCountByActorIdGroupByTemplateId(gomock.Any(),
					"UNKNOWN_ID").Return(map[string]int32{"SD-test-1": 1}, nil).Times(1)
				f.bcClient.EXPECT().GetBankCustomer(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&bankcust.GetBankCustomerResponse{
						BankCustomer: &bankcust.BankCustomer{
							KycInfo: &bankcust.KYCInfo{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
						Status: rpc.StatusOk(),
					}, nil)
				f.onboardingAccessor.EXPECT().IsFiLiteActor(gomock.Any(), gomock.Any()).Return(false, onboardingPb.FeatureStatus_FEATURE_STATUS_ACTIVE, nil)
			},
		},
		{
			name: "Don't fetch tax saving templates for Fixed Deposit",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetDepositTemplatesRequest{Type: accountsPb.Type_FIXED_DEPOSIT},
			},
			want:                  &depositPb.GetDepositTemplatesResponse{Status: rpc.StatusOk()},
			wantErr:               false,
			totalFetchedTemplates: 6,
			prepare: func(a *args, f *fields) {

				f.depositAccountDao.EXPECT().GetByActorIdAndTemplateIDs(gomock.Any(), gomock.Any(),
					gomock.Any()).Return([]*depositPb.DepositAccount{}, nil).AnyTimes()
				f.depositAccountDao.EXPECT().GetCountByActorIdGroupByTemplateId(gomock.Any(),
					gomock.Any()).Return(map[string]int32{}, nil).AnyTimes()
				f.bcClient.EXPECT().GetBankCustomer(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&bankcust.GetBankCustomerResponse{
						BankCustomer: &bankcust.BankCustomer{
							KycInfo: &bankcust.KYCInfo{
								KycLevel: kycPb.KYCLevel_MIN_KYC,
							},
						},
						Status: rpc.StatusOk(),
					}, nil)
				f.onboardingAccessor.EXPECT().IsFiLiteActor(gomock.Any(), gomock.Any()).Return(false, onboardingPb.FeatureStatus_FEATURE_STATUS_ACTIVE, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			depositAccountDao := mocks.NewMockDepositAccountDao(ctr)
			bcClient := bankCustMocks.NewMockBankCustomerServiceClient(ctr)
			onboardingAccessor := accessorMocks.NewMockIOnboardingAccessor(ctr)
			f := fields{
				depositAccountDao:  depositAccountDao,
				bcClient:           bcClient,
				onboardingAccessor: onboardingAccessor,
			}
			if tt.prepare != nil {
				tt.prepare(&tt.args, &f)
			}
			depositService = deposit.NewService(nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, depositAccountDao, depositRequestDao, nil, nil, nil, nil, nil, nil, nil, conf, dynConf, nil, nil, nil, nil, bcClient, nil, onboardingAccessor, nil, nil, nil, nil, nil, nil, nil, nil, nil)
			got, err := depositService.GetDepositTemplates(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDepositTemplates() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.Status.Code != tt.want.Status.Code {
				t.Errorf("depositService.GetDepositTemplates() status: got = %v, want %v", got.Status, tt.want.Status)
			}

			if tt.totalFetchedTemplates != len(got.DepositTemplates) {
				t.Errorf("GetDepositTemplates() status: Total fetched templates = %v, Expected fetched templates =  %v", len(got.DepositTemplates), tt.totalFetchedTemplates)
			}
		})
	}
}

func TestService_ForceProcessDeposit(t *testing.T) {
	ctr := gomock.NewController(t)
	testSoftDeletedCreateRequestId := "request-id-4"

	defer func() {
		ctr.Finish()
		depositService = nil
	}()

	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()

	mockAuthClient := authMocks.NewMockAuthClient(ctr)
	mockUserClient := userMocks.NewMockUsersClient(ctr)
	mockBCClient := bankCustMocks.NewMockBankCustomerServiceClient(ctr)
	mockActorClient := actorMocks.NewMockActorClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockAccountPiClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
	mockVgDepositClient := vgDepositMocks.NewMockDepositClient(ctr)
	mocksHealthEngineClient := heMocks.NewMockHealthEngineServiceClient(ctr)
	mockAuthClient.EXPECT().GetDeviceAuth(gomock.Any(), gomock.Any()).Return(&authPb.GetDeviceAuthResponse{
		Status: rpc.StatusOk(),
		Device: &commontypes.Device{},
	}, nil).AnyTimes()

	mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), gomock.Any()).Return(&actorPb.GetEntityDetailsByActorIdResponse{
		Status:       rpc.StatusOk(),
		MobileNumber: &commontypes.PhoneNumber{},
	}, nil).AnyTimes()

	mockActorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByIdResponse{
		Status: rpc.StatusOk(),
		Actor: &types.Actor{
			Id:        fixtureActorId1,
			Type:      0,
			EntityId:  "",
			Name:      "",
			Ownership: 0,
		},
	}, nil).AnyTimes()

	mockUserClient.EXPECT().GetUser(gomock.Any(), gomock.Any()).Return(&userPb.GetUserResponse{
		User:   &userPb.User{},
		Status: rpc.StatusOk(),
	}, nil).AnyTimes()

	mockBCClient.EXPECT().GetBankCustomer(gomock.Any(), gomock.Any()).Return(&bankcust.GetBankCustomerResponse{Status: rpc.StatusOk()}, nil).AnyTimes()

	mockPiClient.EXPECT().CreatePi(gomock.Any(), gomock.Any()).Return(&piPb.CreatePiResponse{
		Status:            rpc.StatusOk(),
		PaymentInstrument: &piPb.PaymentInstrument{},
	}, nil).AnyTimes()

	mockAccountPiClient.EXPECT().Create(gomock.Any(), gomock.Any()).Return(&accountPiPb.CreateAccountPIResponse{
		Status:    rpc.StatusOk(),
		AccountPi: &accountPiPb.AccountPI{},
	}, nil).AnyTimes()

	depositService = deposit.NewService(mocksHealthEngineClient, nil, mockAuthClient, mockUserClient, nil, nil, mockActorClient, mockPiClient, mockAccountPiClient, nil, nil, nil, nil, nil, nil, nil, depositAccountDao, depositRequestDao, nil, nil, nil, nil, nil, nil, brokerMock, conf, dynConf, nil, nil, nil, nil, mockBCClient, nil, nil, nil, mockVgDepositClient, nil, nil, nil, nil, nil, nil, nil)

	type mockVgDepositStatus struct {
		enable bool
		res    *vgDepositPb.CheckAccountStatusResponse
		err    error
	}

	type args struct {
		ctx context.Context
		req *depositPb.ForceProcessDepositRequest
	}
	tests := []struct {
		name                string
		args                args
		want                *depositPb.ForceProcessDepositResponse
		wantErr             bool
		mockVgDepositStatus mockVgDepositStatus
	}{
		{
			name: "Should successfully force process for soft deleted request",
			args: args{
				ctx: context.Background(),
				req: &depositPb.ForceProcessDepositRequest{RequestId: testSoftDeletedCreateRequestId},
			},
			want:    &depositPb.ForceProcessDepositResponse{Status: rpc.StatusOk()},
			wantErr: false,
			mockVgDepositStatus: mockVgDepositStatus{
				enable: true,
				res: &vgDepositPb.CheckAccountStatusResponse{
					Status:               rpc.StatusOk(),
					TransactionTimestamp: timestampPb.Now(),
					TransactionDetails: &vgDepositPb.CheckAccountStatusResponse_TransactionDetails{
						AccountNumber:  "**********",
						MaturityAmount: money.ZeroINR().GetPb(),
						MaturityDate:   timestampPb.Now(),
					},
				},
				err: nil,
			},
		},
		{
			name: "Should do nothing for non-success status from vendor",
			args: args{
				ctx: context.Background(),
				req: &depositPb.ForceProcessDepositRequest{RequestId: testSoftDeletedCreateRequestId},
			},
			want:    &depositPb.ForceProcessDepositResponse{Status: rpc.StatusInternal()},
			wantErr: false,
			mockVgDepositStatus: mockVgDepositStatus{
				enable: true,
				res: &vgDepositPb.CheckAccountStatusResponse{
					Status:               rpc.StatusRecordNotFound(),
					TransactionTimestamp: timestampPb.Now(),
				},
				err: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clean database, run migrations and load fixtures
			pkgTest.PrepareScopedDatabase(t, conf.EpifiDb.GetName(), db, depositAffectedTestTables)
			if tt.mockVgDepositStatus.enable {
				mockVgDepositClient.EXPECT().CheckAccountStatus(gomock.Any(), gomock.Any()).Return(tt.mockVgDepositStatus.res, tt.mockVgDepositStatus.err)
			}
			got, err := depositService.ForceProcessDeposit(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ForceProcessDeposit() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.Status.Code != tt.want.Status.Code {
				t.Errorf("depositService.ForceProcessDeposit() status: got = %v, want %v", got.Status, tt.want.Status)
			}
		})
	}
}

func TestService_ProcessDepositInterestCreditNotification(t *testing.T) {
	partnerExecutedAt, err := datetime.ParseStringTimeStampProto("2006-01-02:15:04:05", "2021-01-05:15:04:05")
	assert.Nil(t, err)
	transactionDetails := &depositPb.InboundNotificationTxnDetails{
		AccountNumber: "**********",
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        1000,
		},
		Particular:      "TDINT:**************(Tds: 0.00)",
		ReferenceNumber: "1234123",
		Timestamp:       partnerExecutedAt,
	}
	testUser := "Test-User-1"
	ctrl := gomock.NewController(t)

	brokerMock := brokerMocks.NewMockBroker(ctrl)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()

	savingsClient := savingsMocks.NewMockSavingsClient(ctrl)
	actorClient := actorMocks.NewMockActorClient(ctrl)
	piClient := piMocks.NewMockPiClient(ctrl)
	orderClient := orderMocks.NewMockOrderServiceClient(ctrl)

	actorClient.EXPECT().GetActorByEntityId(context.Background(), gomock.Any()).Return(&actorPb.GetActorByEntityIdResponse{
		Status: rpc.StatusOk(),
		Actor: &types.Actor{
			Id:       fixtureActorId1,
			Type:     types.Actor_USER,
			EntityId: "Test-User-1",
			Name:     "test actor",
		},
	}, nil).AnyTimes()

	actorClient.EXPECT().ResolveActorFrom(gomock.Any(), gomock.Any()).Return(&actorPb.ResolveActorFromResponse{
		Status:    rpc.StatusOk(),
		ActorFrom: "actor-user-1",
	}, nil).AnyTimes()

	piClient.EXPECT().
		GetPi(context.Background(), gomock.Any()).
		Return(&piPb.GetPiResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{}}, nil).AnyTimes()

	depositSvc := deposit.NewService(nil, nil, nil, nil, savingsClient, nil, actorClient, piClient, nil, orderClient, nil, nil, nil, nil, nil, nil, depositAccountDao, depositRequestDao, nil, nil, nil, nil, nil, nil, brokerMock, conf, dynConf, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	defer func() {
		ctrl.Finish()
		depositSvc = nil
	}()

	type mockSavingsGetAccount struct {
		enable   bool
		response *savingsPb.GetAccountResponse
		err      error
	}

	type mockCreateOrderWithTxn struct {
		enable   bool
		response *orderPb.CreateOrderWithTransactionResponse
		err      error
	}

	type mockGetSavingsAccountEssentials struct {
		enable   bool
		request  *savingsPb.GetSavingsAccountEssentialsRequest
		response *savingsPb.GetSavingsAccountEssentialsResponse
		err      error
	}

	type args struct {
		ctx context.Context
		req *depositPb.ProcessDepositInterestCreditNotificationRequest
	}

	tests := []struct {
		name                            string
		args                            args
		shouldPrepareDB                 bool
		want                            *depositPb.ProcessDepositInterestCreditNotificationResponse
		wantErr                         bool
		mockSavingsGetAccount           mockSavingsGetAccount
		mockCreateOrderWithTxn          mockCreateOrderWithTxn
		mockGetSavingsAccountEssentials mockGetSavingsAccountEssentials
	}{
		{
			name: "Successfully created order and transaction for deposit interest inbound notifications",
			args: args{
				ctx: context.Background(),
				req: &depositPb.ProcessDepositInterestCreditNotificationRequest{
					PartnerBank:        commonvgpb.Vendor_FEDERAL_BANK,
					TransactionDetails: transactionDetails,
				},
			},
			shouldPrepareDB: true,
			want:            &depositPb.ProcessDepositInterestCreditNotificationResponse{Status: rpc.StatusOk()},
			wantErr:         false,
			mockSavingsGetAccount: mockSavingsGetAccount{
				enable: true,
				response: &savingsPb.GetAccountResponse{
					Account: &savingsPb.Account{AccountNo: fixtureDepositAccountSD1.AccountNumber, PrimaryAccountHolder: testUser}},
				err: nil,
			},
			mockCreateOrderWithTxn: mockCreateOrderWithTxn{
				enable: true,
				response: &orderPb.CreateOrderWithTransactionResponse{
					Status:      rpc.StatusOk(),
					Order:       &orderPb.Order{},
					Transaction: &paymentPb.Transaction{},
				},
				err: nil,
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				response: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status:  rpc.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{AccountNo: transactionDetails.AccountNumber, IfscCode: "FDRL0001001"},
				},
				request: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_AccountNumBankFilter{
						AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
							AccountNumber: transactionDetails.AccountNumber,
							PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
						},
					}},
				err: nil,
			},
		},
		{
			name: "Should fail as associated account doesn't exists",
			args: args{
				ctx: context.Background(),
				req: &depositPb.ProcessDepositInterestCreditNotificationRequest{
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					TransactionDetails: &depositPb.InboundNotificationTxnDetails{
						AccountNumber: "random-account",
						Amount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        1000,
						},
					},
				},
			},
			shouldPrepareDB: true,
			want:            &depositPb.ProcessDepositInterestCreditNotificationResponse{Status: rpc.StatusInternal()},
			wantErr:         false,
			mockSavingsGetAccount: mockSavingsGetAccount{
				enable:   true,
				response: nil,
				err:      epifierrors.ErrRecordNotFound,
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				response: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status:  rpc.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{AccountNo: "random-account", IfscCode: "FDRL0001001"},
				},
				request: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_AccountNumBankFilter{
						AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
							AccountNumber: "random-account",
							PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
						},
					}},
				err: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.shouldPrepareDB {
				pkgTest.PrepareScopedDatabase(t, conf.EpifiDb.GetName(), db, depositAffectedTestTables)
			}
			if tt.mockCreateOrderWithTxn.enable {
				orderClient.EXPECT().CreateOrderWithTransaction(gomock.Any(), gomock.Any()).Return(tt.mockCreateOrderWithTxn.response,
					tt.mockCreateOrderWithTxn.err)
			}
			if tt.mockSavingsGetAccount.enable {
				savingsClient.EXPECT().GetAccount(tt.args.ctx, gomock.Any()).Return(tt.mockSavingsGetAccount.response, tt.mockSavingsGetAccount.err)
			}
			if tt.mockGetSavingsAccountEssentials.enable {
				savingsClient.EXPECT().GetSavingsAccountEssentials(tt.args.ctx, tt.mockGetSavingsAccountEssentials.request).Times(1).Return(tt.mockGetSavingsAccountEssentials.response, nil)
			}
			got, err := depositSvc.ProcessDepositInterestCreditNotification(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessDepositInterestCreditNotification() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.Status.Code != tt.want.Status.Code {
				t.Errorf("ProcessDepositInterestCreditNotification() got status = %v, want status %v", got.Status, tt.want.Status)
			}
		})
	}
}

func TestService_GetTotalDepositAmounts(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTest.PrepareScopedDatabase(t, conf.EpifiDb.GetName(), db, depositAffectedTestTables)

	depositService = deposit.NewService(nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, depositAccountDao, depositRequestDao, nil, nil, nil, nil, nil, nil, nil, conf, dynConf, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	type args struct {
		ctx context.Context
		req *depositPb.GetTotalDepositAmountsRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *depositPb.GetTotalDepositAmountsResponse
		wantErr bool
	}{
		{
			name: "Successfully fetch total deposited amount across all deposits for all provenance",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetTotalDepositAmountsRequest{
					ActorId:      fixtureActorId1,
					States:       []depositPb.DepositState{depositPb.DepositState_CREATED, depositPb.DepositState_PRECLOSED},
					AccountsType: []accountsPb.Type{accountsPb.Type_FIXED_DEPOSIT, accountsPb.Type_SMART_DEPOSIT},
				},
			},
			want: &depositPb.GetTotalDepositAmountsResponse{
				Status: rpc.StatusOk(),
				AggregatedSumMap: map[string]*depositPb.GetTotalDepositAmountsResponse_AggregatedSumForEachAccount{
					"SMART_DEPOSIT": {
						AggregatedSum: map[string]*depositPb.GetTotalDepositAmountsResponse_AggregatedSumForEachState{
							"CREATED": {
								TotalDepositAccounts: 2,
								TotalSum:             &moneyPb.Money{CurrencyCode: "INR", Units: 200},
							},
							"PRECLOSED": {
								TotalDepositAccounts: 1,
								TotalSum:             &moneyPb.Money{CurrencyCode: "INR", Units: 100},
							},
						},
					},
					"FIXED_DEPOSIT": {
						AggregatedSum: map[string]*depositPb.GetTotalDepositAmountsResponse_AggregatedSumForEachState{
							"CREATED": {
								TotalDepositAccounts: 1,
								TotalSum:             &moneyPb.Money{CurrencyCode: "INR", Units: 100},
							},
							"PRECLOSED": {
								TotalDepositAccounts: 0,
								TotalSum:             &moneyPb.Money{CurrencyCode: "INR", Units: 0},
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "Successfully fetch total deposited amount across all deposits for REWARDS provenance",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetTotalDepositAmountsRequest{
					ActorId:                  fixtureActorId1,
					States:                   []depositPb.DepositState{depositPb.DepositState_CLOSED, depositPb.DepositState_CREATED},
					DepositAccountProvenance: []depositPb.DepositAccountProvenance{depositPb.DepositAccountProvenance_REWARDS_APP},
					AccountsType:             []accountsPb.Type{accountsPb.Type_FIXED_DEPOSIT, accountsPb.Type_SMART_DEPOSIT},
				},
			},
			want: &depositPb.GetTotalDepositAmountsResponse{
				Status: rpc.StatusOk(),
				AggregatedSumMap: map[string]*depositPb.GetTotalDepositAmountsResponse_AggregatedSumForEachAccount{
					"SMART_DEPOSIT": {
						AggregatedSum: map[string]*depositPb.GetTotalDepositAmountsResponse_AggregatedSumForEachState{
							"CREATED": {
								TotalDepositAccounts: 1,
								TotalSum:             &moneyPb.Money{CurrencyCode: "INR", Units: 100},
							},
							"CLOSED": {
								TotalDepositAccounts: 0,
								TotalSum:             &moneyPb.Money{CurrencyCode: "INR", Units: 0},
							},
						},
					},
					"FIXED_DEPOSIT": {
						AggregatedSum: map[string]*depositPb.GetTotalDepositAmountsResponse_AggregatedSumForEachState{
							"CREATED": {
								TotalDepositAccounts: 0,
								TotalSum:             &moneyPb.Money{CurrencyCode: "INR", Units: 0},
							},
							"CLOSED": {
								TotalDepositAccounts: 0,
								TotalSum:             &moneyPb.Money{CurrencyCode: "INR", Units: 0},
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "Successfully fetch total deposited amount across all deposits for USER provenance",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetTotalDepositAmountsRequest{
					ActorId:                  fixtureActorId1,
					States:                   []depositPb.DepositState{depositPb.DepositState_CREATED, depositPb.DepositState_PRECLOSED},
					DepositAccountProvenance: []depositPb.DepositAccountProvenance{depositPb.DepositAccountProvenance_USER_APP},
					AccountsType:             []accountsPb.Type{accountsPb.Type_FIXED_DEPOSIT, accountsPb.Type_SMART_DEPOSIT},
				},
			},
			want: &depositPb.GetTotalDepositAmountsResponse{
				Status: rpc.StatusOk(),
				AggregatedSumMap: map[string]*depositPb.GetTotalDepositAmountsResponse_AggregatedSumForEachAccount{
					"SMART_DEPOSIT": {
						AggregatedSum: map[string]*depositPb.GetTotalDepositAmountsResponse_AggregatedSumForEachState{
							"CREATED": {
								TotalDepositAccounts: 1,
								TotalSum:             &moneyPb.Money{CurrencyCode: "INR", Units: 100},
							},
							"PRECLOSED": {
								TotalDepositAccounts: 1,
								TotalSum:             &moneyPb.Money{CurrencyCode: "INR", Units: 100},
							},
						},
					},
					"FIXED_DEPOSIT": {
						AggregatedSum: map[string]*depositPb.GetTotalDepositAmountsResponse_AggregatedSumForEachState{
							"CREATED": {
								TotalDepositAccounts: 1,
								TotalSum:             &moneyPb.Money{CurrencyCode: "INR", Units: 100},
							},
							"PRECLOSED": {
								TotalDepositAccounts: 0,
								TotalSum:             &moneyPb.Money{CurrencyCode: "INR", Units: 0},
							},
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := depositService.GetTotalDepositAmounts(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetTotalDepositAmountForDeposits() error = %v, wantErr = %v", err, tt.wantErr)
				return
			}
			if got.Status.Code != tt.want.Status.Code {
				t.Errorf("GetTotalDepositAmountForDeposits() got status = %v, want status = %v", got.Status, tt.want.Status)
			}
			if !checkAggregatedSumForEachState(got.AggregatedSumMap, tt.want.AggregatedSumMap) {
				t.Errorf("GetTotalDepositAmountForDeposits() got aggregatedSum = %v, want aggregatedSum = %v ", got.AggregatedSumMap, tt.want.AggregatedSumMap)
			}

		})
	}
}

func checkAggregatedSumForEachState(got map[string]*depositPb.GetTotalDepositAmountsResponse_AggregatedSumForEachAccount, want map[string]*depositPb.GetTotalDepositAmountsResponse_AggregatedSumForEachAccount) bool {
	for accountTypeGot, stateGot := range got {
		for accountTypeWant, statesWant := range want {
			if accountTypeWant == accountTypeGot && stateGot == statesWant {
				for state := range statesWant.AggregatedSum {
					if got[accountTypeGot].AggregatedSum[state].TotalSum != want[accountTypeWant].AggregatedSum[state].TotalSum {
						return false
					}
					if got[accountTypeGot].AggregatedSum[state].TotalDepositAccounts != want[accountTypeWant].AggregatedSum[state].TotalDepositAccounts {
						return false
					}
				}
			}
		}
	}
	return true
}

func TestService_IsEligibleForPreclosure(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTest.PrepareScopedDatabase(t, conf.EpifiDb.GetName(), db, depositAffectedTestTables)

	ctr := gomock.NewController(t)
	defer func() { ctr.Finish() }()

	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()

	savingsMock := savingsMocks.NewMockSavingsClient(ctr)
	actorMock := actorMocks.NewMockActorClient(ctr)
	vkycMocks := vkycMock.NewMockVKYCClient(ctr)
	actorMock.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actorPb.GetActorByIdResponse{
		Status: rpc.StatusOk(),
		Actor:  &types.Actor{Id: fixtureActorId1},
	}, nil).AnyTimes()

	savingsMock.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savingsPb.GetAccountResponse{Account: &savingsPb.Account{}}, nil).AnyTimes()

	depositService = deposit.NewService(nil, nil, nil, nil, savingsMock, nil, actorMock, nil, nil, nil, nil, nil, vkycMocks, nil, nil, nil, depositAccountDao, depositRequestDao, nil, nil, nil, nil, nil, nil, brokerMock, conf, dynConf, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	type mockIsTxnAllowed struct {
		enable bool
		res    *savingsPb.IsTxnAllowedResponse
		err    error
	}
	type mockGetVkycSummary struct {
		enable bool
		res    *vkyc.GetVKYCSummaryResponse
		err    error
	}
	type args struct {
		ctx context.Context
		req *depositPb.IsEligibleForPreclosureRequest
	}
	tests := []struct {
		name               string
		args               args
		want               *depositPb.IsEligibleForPreclosureResponse
		mockIsTxnAllowed   *mockIsTxnAllowed
		mockGetVkycSummary *mockGetVkycSummary
		wantErr            bool
	}{
		{
			name: "Should return eligibility as true as IsTxnAllowed returns okay status",
			args: args{
				ctx: context.Background(),
				req: &depositPb.IsEligibleForPreclosureRequest{ActorId: fixtureDepositAccountSD1.ActorId, DepositAccountId: fixtureDepositAccountSD1.Id},
			},
			want: &depositPb.IsEligibleForPreclosureResponse{Status: rpc.StatusOk()},
			mockIsTxnAllowed: &mockIsTxnAllowed{
				enable: true,
				res:    &savingsPb.IsTxnAllowedResponse{Status: rpc.StatusOk()},
				err:    nil,
			},
			wantErr: false,
		},
		{
			name: "Should fail user min-kyc and isTxnAllowed returned limit breach",
			args: args{
				ctx: context.Background(),
				req: &depositPb.IsEligibleForPreclosureRequest{ActorId: fixtureDepositAccountSD1.ActorId, DepositAccountId: fixtureDepositAccountSD1.Id},
			},
			want: &depositPb.IsEligibleForPreclosureResponse{Status: rpc.NewStatusWithoutDebug(uint32(depositPb.IsEligibleForPreclosureResponse_NOT_ELIGIBLE_MIN_KYC_CHECK_FAILURE), "")},
			mockIsTxnAllowed: &mockIsTxnAllowed{
				enable: true,
				res:    &savingsPb.IsTxnAllowedResponse{Status: rpc.NewStatusWithoutDebug(uint32(savingsPb.IsTxnAllowedResponse_NOT_ALLOWED_MIN_KYC_CHECK_FAILURE), "")},
				err:    nil,
			},
			mockGetVkycSummary: &mockGetVkycSummary{
				enable: true,
				res: &vkyc.GetVKYCSummaryResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			wantErr: false,
		},
		{
			name: "Should fail as user breached limit and VKYC in review",
			args: args{
				ctx: context.Background(),
				req: &depositPb.IsEligibleForPreclosureRequest{ActorId: fixtureDepositAccountSD1.ActorId, DepositAccountId: fixtureDepositAccountSD1.Id},
			},
			want: &depositPb.IsEligibleForPreclosureResponse{Status: rpc.NewStatusWithoutDebug(uint32(depositPb.IsEligibleForPreclosureResponse_NOT_ELIGIBLE_VKYC_PENDING), "")},
			mockIsTxnAllowed: &mockIsTxnAllowed{
				enable: true,
				res:    &savingsPb.IsTxnAllowedResponse{Status: rpc.NewStatusWithoutDebug(uint32(savingsPb.IsTxnAllowedResponse_NOT_ALLOWED_MIN_KYC_CHECK_FAILURE), "")},
				err:    nil,
			},
			mockGetVkycSummary: &mockGetVkycSummary{
				enable: true,
				res: &vkyc.GetVKYCSummaryResponse{
					Status:     rpc.StatusOk(),
					VkycRecord: &vkyc.VKYCRecord{VkycSummary: &vkyc.VKYCSummary{Status: vkyc.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_IN_REVIEW}},
				},
				err: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockIsTxnAllowed != nil && tt.mockIsTxnAllowed.enable {
				savingsMock.EXPECT().IsTxnAllowed(tt.args.ctx, gomock.Any()).Return(tt.mockIsTxnAllowed.res, tt.mockIsTxnAllowed.err)
			}
			if tt.mockGetVkycSummary != nil && tt.mockGetVkycSummary.enable {
				vkycMocks.EXPECT().GetVKYCSummary(tt.args.ctx, gomock.Any()).Return(tt.mockGetVkycSummary.res, tt.mockGetVkycSummary.err)
			}
			got, err := depositService.IsEligibleForPreclosure(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("depositService.IsEligibleForPreclosure() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.Status.Code != tt.want.Status.Code {
				t.Errorf("depositService.IsEligibleForPreclosure() status: got = %v, want %v", got.Status, tt.want.Status)
			}
		})
	}
}

func TestService_GetTransactionAggregates(t *testing.T) {
	ctr := gomock.NewController(t)
	defer func() { ctr.Finish() }()

	depositTransactionDao := mocks.NewMockDepositTransactionDao(ctr)
	depositAccountDao := mocks.NewMockDepositAccountDao(ctr)

	depositSvc := &deposit.Service{
		DepositTransactionDao: depositTransactionDao,
		DepositAccountDao:     depositAccountDao,
	}

	depositTransactionCreate1000 := &depositPb.DepositTransaction{
		TxnType: depositPb.DepositTxnType_CREATION,
		TxnAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        1000,
		},
	}

	depositTransactionCreate500 := &depositPb.DepositTransaction{
		TxnType: depositPb.DepositTxnType_CREATION,
		TxnAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        500,
		},
	}

	depositTransactionAdd120 := &depositPb.DepositTransaction{
		TxnType: depositPb.DepositTxnType_ADD_FUNDS,
		TxnAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        120,
		},
	}

	depositTransactionAdd200 := &depositPb.DepositTransaction{
		TxnType: depositPb.DepositTxnType_ADD_FUNDS,
		TxnAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        200,
		},
	}

	// money struct is not valid here
	depositTransactionAdd200Err := &depositPb.DepositTransaction{
		TxnType: depositPb.DepositTxnType_ADD_FUNDS,
		TxnAmount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        -200,
			Nanos:        20,
		},
	}

	type mockGetDepositAccountIdsByActorId struct {
		enable  bool
		actorId interface{}
		res     []string
		err     error
	}

	type mockGetByDepositAccountIdsWithinRange struct {
		enable            bool
		depositAccountIds interface{}
		txnTypes          interface{}
		res               []*depositPb.DepositTransaction
		err               error
	}

	type args struct {
		ctx context.Context
		req *depositPb.GetTransactionAggregatesRequest
	}
	tests := []struct {
		name                                  string
		args                                  args
		want                                  *depositPb.GetTransactionAggregatesResponse
		wantErr                               bool
		mockGetDepositAccountIdsByActorId     *mockGetDepositAccountIdsByActorId
		mockGetByDepositAccountIdsWithinRange *mockGetByDepositAccountIdsWithinRange
	}{
		{
			name: "successfully fetch transaction aggregates of actor user 1",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetTransactionAggregatesRequest{
					ActorId: "actor-user-1",
					TxnType: []depositPb.DepositTxnType{depositPb.DepositTxnType_CREATION, depositPb.DepositTxnType_ADD_FUNDS},
				},
			},
			want: &depositPb.GetTransactionAggregatesResponse{
				TotalAmount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        1620,
				},
				Status: rpc.StatusOk(),
			},
			wantErr: false,
			mockGetDepositAccountIdsByActorId: &mockGetDepositAccountIdsByActorId{
				enable:  true,
				actorId: "actor-user-1",
				res:     []string{"deposit-account-1", "deposit-account-2"},
			},
			mockGetByDepositAccountIdsWithinRange: &mockGetByDepositAccountIdsWithinRange{
				enable:            true,
				depositAccountIds: []string{"deposit-account-1", "deposit-account-2"},
				txnTypes:          []depositPb.DepositTxnType{depositPb.DepositTxnType_CREATION, depositPb.DepositTxnType_ADD_FUNDS},
				res: []*depositPb.DepositTransaction{
					depositTransactionCreate1000,
					depositTransactionCreate500,
					depositTransactionAdd120,
				},
			},
		},
		{
			name: "successfully fetch transaction aggregates of actor user 2",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetTransactionAggregatesRequest{
					ActorId: "actor-user-2",
					TxnType: []depositPb.DepositTxnType{depositPb.DepositTxnType_CREATION, depositPb.DepositTxnType_ADD_FUNDS},
				},
			},
			want: &depositPb.GetTransactionAggregatesResponse{
				TotalAmount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        820,
				},
				Status: rpc.StatusOk(),
			},
			wantErr: false,
			mockGetDepositAccountIdsByActorId: &mockGetDepositAccountIdsByActorId{
				enable:  true,
				actorId: "actor-user-2",
				res:     []string{"deposit-account-3"},
			},
			mockGetByDepositAccountIdsWithinRange: &mockGetByDepositAccountIdsWithinRange{
				enable:            true,
				depositAccountIds: []string{"deposit-account-3"},
				txnTypes:          []depositPb.DepositTxnType{depositPb.DepositTxnType_CREATION, depositPb.DepositTxnType_ADD_FUNDS},
				res: []*depositPb.DepositTransaction{
					depositTransactionCreate500,
					depositTransactionAdd120,
					depositTransactionAdd200,
				},
			},
		},
		{
			name: "fail to fetch for actor-user-random as it does not exists",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetTransactionAggregatesRequest{
					ActorId: "actor-user-random",
					TxnType: []depositPb.DepositTxnType{depositPb.DepositTxnType_CREATION, depositPb.DepositTxnType_ADD_FUNDS},
				},
			},
			want: &depositPb.GetTransactionAggregatesResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
			mockGetDepositAccountIdsByActorId: &mockGetDepositAccountIdsByActorId{
				enable:  true,
				actorId: "actor-user-random",
				res:     nil,
				err:     errors.New("actor not found"),
			},
			mockGetByDepositAccountIdsWithinRange: &mockGetByDepositAccountIdsWithinRange{
				enable: false,
			},
		},
		{
			name: "successfully fetch transaction aggregates of deposit accounts 1, 2 and 3",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetTransactionAggregatesRequest{
					DepositAccountIds: []string{"deposit-account-1", "deposit-account-2", "deposit-account-3"},
					TxnType:           []depositPb.DepositTxnType{depositPb.DepositTxnType_CREATION, depositPb.DepositTxnType_ADD_FUNDS},
				},
			},
			want: &depositPb.GetTransactionAggregatesResponse{
				TotalAmount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        2440,
				},
				Status: rpc.StatusOk(),
			},
			wantErr: false,
			mockGetDepositAccountIdsByActorId: &mockGetDepositAccountIdsByActorId{
				enable: false,
			},
			mockGetByDepositAccountIdsWithinRange: &mockGetByDepositAccountIdsWithinRange{
				enable:            true,
				depositAccountIds: []string{"deposit-account-1", "deposit-account-2", "deposit-account-3"},
				txnTypes:          []depositPb.DepositTxnType{depositPb.DepositTxnType_CREATION, depositPb.DepositTxnType_ADD_FUNDS},
				res: []*depositPb.DepositTransaction{
					depositTransactionCreate1000,
					depositTransactionCreate500,
					depositTransactionAdd120,
					depositTransactionCreate500,
					depositTransactionAdd120,
					depositTransactionAdd200,
				},
			},
		},
		{
			name: "successfully fetch transaction aggregates of deposit accounts 1, 2 and 3, only CREATION txn type",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetTransactionAggregatesRequest{
					DepositAccountIds: []string{"deposit-account-1", "deposit-account-2", "deposit-account-3"},
					TxnType:           []depositPb.DepositTxnType{depositPb.DepositTxnType_CREATION},
				},
			},
			want: &depositPb.GetTransactionAggregatesResponse{
				TotalAmount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        2000,
				},
				Status: rpc.StatusOk(),
			},
			wantErr: false,
			mockGetDepositAccountIdsByActorId: &mockGetDepositAccountIdsByActorId{
				enable: false,
			},
			mockGetByDepositAccountIdsWithinRange: &mockGetByDepositAccountIdsWithinRange{
				enable:            true,
				depositAccountIds: []string{"deposit-account-1", "deposit-account-2", "deposit-account-3"},
				txnTypes:          []depositPb.DepositTxnType{depositPb.DepositTxnType_CREATION},
				res: []*depositPb.DepositTransaction{
					depositTransactionCreate1000,
					depositTransactionCreate500,
					depositTransactionCreate500,
				},
			},
		},
		{
			name: "successfully return totalAmount as zero as we pass actor id 3",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetTransactionAggregatesRequest{
					ActorId: "actor-user-3",
					TxnType: []depositPb.DepositTxnType{depositPb.DepositTxnType_CREATION, depositPb.DepositTxnType_ADD_FUNDS},
				},
			},
			want: &depositPb.GetTransactionAggregatesResponse{
				TotalAmount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        0,
				},
				Status: rpc.StatusOk(),
			},
			wantErr: false,
			mockGetDepositAccountIdsByActorId: &mockGetDepositAccountIdsByActorId{
				enable:  true,
				actorId: "actor-user-3",
				res:     []string{},
			},
			mockGetByDepositAccountIdsWithinRange: &mockGetByDepositAccountIdsWithinRange{
				enable:            true,
				depositAccountIds: []string{},
				txnTypes:          []depositPb.DepositTxnType{depositPb.DepositTxnType_CREATION, depositPb.DepositTxnType_ADD_FUNDS},
				res:               []*depositPb.DepositTransaction{},
			},
		},
		{
			name: "fail to fetch for deposit-account-random as it does not exists",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetTransactionAggregatesRequest{
					DepositAccountIds: []string{"deposit-account-random"},
					TxnType:           []depositPb.DepositTxnType{depositPb.DepositTxnType_CREATION, depositPb.DepositTxnType_ADD_FUNDS},
				},
			},
			want: &depositPb.GetTransactionAggregatesResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
			mockGetDepositAccountIdsByActorId: &mockGetDepositAccountIdsByActorId{
				enable: false,
			},
			mockGetByDepositAccountIdsWithinRange: &mockGetByDepositAccountIdsWithinRange{
				enable:            true,
				depositAccountIds: []string{"deposit-account-random"},
				txnTypes:          []depositPb.DepositTxnType{depositPb.DepositTxnType_CREATION, depositPb.DepositTxnType_ADD_FUNDS},
				res:               nil,
				err:               errors.New("deposit account not found"),
			},
		},
		{
			name: "fail to fetch for deposit-account-4 as it have a deposit transaction with invalid money struct",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetTransactionAggregatesRequest{
					DepositAccountIds: []string{"deposit-account-4"},
					TxnType:           []depositPb.DepositTxnType{depositPb.DepositTxnType_CREATION, depositPb.DepositTxnType_ADD_FUNDS},
				},
			},
			want: &depositPb.GetTransactionAggregatesResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
			mockGetDepositAccountIdsByActorId: &mockGetDepositAccountIdsByActorId{
				enable: false,
			},
			mockGetByDepositAccountIdsWithinRange: &mockGetByDepositAccountIdsWithinRange{
				enable:            true,
				depositAccountIds: []string{"deposit-account-4"},
				txnTypes:          []depositPb.DepositTxnType{depositPb.DepositTxnType_CREATION, depositPb.DepositTxnType_ADD_FUNDS},
				res: []*depositPb.DepositTransaction{
					depositTransactionAdd200Err,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetDepositAccountIdsByActorId.enable {
				depositAccountDao.EXPECT().GetDepositAccountIdsByActorId(gomock.Any(), tt.mockGetDepositAccountIdsByActorId.actorId).
					Return(tt.mockGetDepositAccountIdsByActorId.res, tt.mockGetDepositAccountIdsByActorId.err).AnyTimes()
			}

			if tt.mockGetByDepositAccountIdsWithinRange.enable {
				depositTransactionDao.EXPECT().
					GetByDepositAccountIdsWithinRange(gomock.Any(), tt.mockGetByDepositAccountIdsWithinRange.depositAccountIds, gomock.Any(), gomock.Any(), tt.mockGetByDepositAccountIdsWithinRange.txnTypes, gomock.Any()).
					Return(tt.mockGetByDepositAccountIdsWithinRange.res, tt.mockGetByDepositAccountIdsWithinRange.err).AnyTimes()
			}

			got, err := depositSvc.GetTransactionAggregates(tt.args.ctx, tt.args.req)

			if (err != nil) != tt.wantErr {
				t.Errorf("GetTransactionAggregates() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetTransactionAggregates() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetPreclosureAttributes(t *testing.T) {
	preclosureScreen := "APP_SCREEN_SD_PRECLOSURE"
	depositAccountId := "deposit-account-sd-1"

	ctr := gomock.NewController(t)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	depositAccountDao := mocks.NewMockDepositAccountDao(ctr)
	depositFeedbackEngine := feedbackMocks.NewMockDepositFeedbackEngine(ctr)
	preclosureEngine := preclosureEngineMocks.NewMockPreclosureEngine(ctr)

	depositSvc := deposit.NewService(nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, depositAccountDao, nil, nil, nil, nil, nil, depositFeedbackEngine, preclosureEngine, brokerMock, conf, dynConf, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	defer func() {
		ctr.Finish()
		depositSvc = nil
	}()

	type mockDepositAccountDaoGetById struct {
		enable           bool
		depositAccountId string
		res              *depositPb.DepositAccount
		err              error
	}

	type mockDepositFeedbackEngine struct {
		enable     bool
		screen     string
		screenMeta map[string]string
		err        error
	}

	type mockGetPreclosureNudgeAttributes struct {
		enable bool
		res    []*depositPb.PreclosureAttribute
		err    error
	}

	type mockGetInterestForfeit struct {
		enable bool
		res    *depositPb.InterestForfeit
	}

	type args struct {
		ctx context.Context
		req *depositPb.GetPreclosureAttributesRequest
	}
	tests := []struct {
		name                             string
		args                             args
		want                             *depositPb.GetPreclosureAttributesResponse
		wantErr                          bool
		mockDepositAccountDaoGetById     *mockDepositAccountDaoGetById
		mockDepositFeedbackEngine        *mockDepositFeedbackEngine
		mockGetPreclosureNudgeAttributes *mockGetPreclosureNudgeAttributes
		mockGetInterestForfeit           *mockGetInterestForfeit
	}{
		{
			name: "successfully fetch feedback screen for given deposit account Id",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetPreclosureAttributesRequest{
					DepositAccountId: depositAccountId,
				},
			},
			want: &depositPb.GetPreclosureAttributesResponse{
				FeedbackDetails: &depositPb.FeedbackDetails{
					ScreenName: preclosureScreen,
					ScreenMeta: map[string]string{
						depositAccountId: "false",
					},
				},
				AccountNumber:      fixtureDepositAccountSD1.GetAccountNumber(),
				RepayAccountNumber: fixtureDepositAccountSD1.GetRepayAccountNumber(),
				DepositAccountType: accountsPb.Type_SMART_DEPOSIT,
				Status:             rpc.StatusOk(),
			},
			wantErr: false,
			mockDepositAccountDaoGetById: &mockDepositAccountDaoGetById{
				enable:           true,
				depositAccountId: depositAccountId,
				res: &depositPb.DepositAccount{
					Id:                 depositAccountId,
					AccountNumber:      fixtureDepositAccountSD1.GetAccountNumber(),
					ActorId:            fixtureDepositAccountSD1.GetActorId(),
					Type:               accountPb.Type_SMART_DEPOSIT,
					RepayAccountNumber: fixtureDepositAccountSD1.GetRepayAccountNumber(),
				},
				err: nil,
			},
			mockDepositFeedbackEngine: &mockDepositFeedbackEngine{
				enable: true,
				screen: preclosureScreen,
				screenMeta: map[string]string{
					depositAccountId: "false",
				},
				err: nil,
			},
			mockGetPreclosureNudgeAttributes: &mockGetPreclosureNudgeAttributes{
				enable: true,
				res:    nil,
				err:    nil,
			},
			mockGetInterestForfeit: &mockGetInterestForfeit{
				enable: true,
				res:    nil,
			},
		},
		{
			name: "feedback is already fethed for given deposit Account",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetPreclosureAttributesRequest{
					DepositAccountId: depositAccountId,
				},
			},
			want: &depositPb.GetPreclosureAttributesResponse{
				AccountNumber:      fixtureDepositAccountSD1.GetAccountNumber(),
				RepayAccountNumber: fixtureDepositAccountSD1.GetRepayAccountNumber(),
				DepositAccountType: accountsPb.Type_SMART_DEPOSIT,
				Status:             rpc.StatusOk(),
			},
			wantErr: false,
			mockDepositAccountDaoGetById: &mockDepositAccountDaoGetById{
				enable:           true,
				depositAccountId: depositAccountId,
				res: &depositPb.DepositAccount{
					Id:                 depositAccountId,
					ActorId:            fixtureDepositAccountSD1.GetActorId(),
					Type:               accountPb.Type_SMART_DEPOSIT,
					AccountNumber:      fixtureDepositAccountSD1.GetAccountNumber(),
					RepayAccountNumber: fixtureDepositAccountSD1.GetRepayAccountNumber(),
				},
				err: nil,
			},
			mockDepositFeedbackEngine: &mockDepositFeedbackEngine{
				enable:     true,
				screen:     "",
				screenMeta: nil,
				err:        nil,
			},
			mockGetPreclosureNudgeAttributes: &mockGetPreclosureNudgeAttributes{
				enable: true,
				res:    nil,
				err:    nil,
			},
			mockGetInterestForfeit: &mockGetInterestForfeit{
				enable: true,
				res:    nil,
			},
		},
		{
			name: "error from feedback engine",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetPreclosureAttributesRequest{
					DepositAccountId: depositAccountId,
				},
			},
			want: &depositPb.GetPreclosureAttributesResponse{
				FeedbackDetails:    nil,
				DepositAccountType: accountsPb.Type_SMART_DEPOSIT,
				Status:             rpc.StatusOk(),
			},
			wantErr: false,
			mockDepositAccountDaoGetById: &mockDepositAccountDaoGetById{
				enable:           true,
				depositAccountId: depositAccountId,
				res: &depositPb.DepositAccount{
					Id:      depositAccountId,
					ActorId: fixtureDepositAccountSD1.GetActorId(),
					Type:    accountPb.Type_SMART_DEPOSIT,
				},
				err: nil,
			},
			mockDepositFeedbackEngine: &mockDepositFeedbackEngine{
				enable:     true,
				screen:     "",
				screenMeta: nil,
				err:        epifierrors.ErrRecordNotFound,
			},
			mockGetPreclosureNudgeAttributes: &mockGetPreclosureNudgeAttributes{
				enable: true,
				res:    nil,
				err:    nil,
			},
			mockGetInterestForfeit: &mockGetInterestForfeit{
				enable: true,
				res:    nil,
			},
		},
		{
			name: "PreclosureDetails should be nil if attributes is empty",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetPreclosureAttributesRequest{
					DepositAccountId: depositAccountId,
				},
			},
			want: &depositPb.GetPreclosureAttributesResponse{
				PreclosureDetails:  nil, // should be nil
				AccountNumber:      fixtureDepositAccountSD1.GetAccountNumber(),
				RepayAccountNumber: fixtureDepositAccountSD1.GetRepayAccountNumber(),
				DepositAccountType: accountsPb.Type_SMART_DEPOSIT,
				Status:             rpc.StatusOk(),
			},
			wantErr: false,
			mockDepositAccountDaoGetById: &mockDepositAccountDaoGetById{
				enable:           true,
				depositAccountId: depositAccountId,
				res: &depositPb.DepositAccount{
					Id:                 depositAccountId,
					ActorId:            fixtureDepositAccountSD1.GetActorId(),
					Type:               accountPb.Type_SMART_DEPOSIT,
					AccountNumber:      fixtureDepositAccountSD1.GetAccountNumber(),
					RepayAccountNumber: fixtureDepositAccountSD1.GetRepayAccountNumber(),
				},
				err: nil,
			},
			mockDepositFeedbackEngine: &mockDepositFeedbackEngine{
				enable:     true,
				screen:     "",
				screenMeta: nil,
				err:        nil,
			},
			mockGetPreclosureNudgeAttributes: &mockGetPreclosureNudgeAttributes{
				enable: true,
				res:    []*depositPb.PreclosureAttribute{},
				err:    nil,
			},
			mockGetInterestForfeit: &mockGetInterestForfeit{
				enable: true,
				res:    nil,
			},
		},
		{
			name: "fetch preclosure nudge attributes successfully with fit rule count zero",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetPreclosureAttributesRequest{
					DepositAccountId: depositAccountId,
				},
			},
			want: &depositPb.GetPreclosureAttributesResponse{
				PreclosureDetails: &depositPb.PreclosureDetails{
					PreclosureAttributes: []*depositPb.PreclosureAttribute{
						{
							Name: depositPb.PreclosureAttribute_INTEREST_RETURNS,
						},
						{
							Name: depositPb.PreclosureAttribute_FIT_RULES_GENERIC,
						},
					},
				},
				AccountNumber:      fixtureDepositAccountSD1.GetAccountNumber(),
				RepayAccountNumber: fixtureDepositAccountSD1.GetRepayAccountNumber(),
				DepositAccountType: accountsPb.Type_SMART_DEPOSIT,
				Status:             rpc.StatusOk(),
			},
			wantErr: false,
			mockDepositAccountDaoGetById: &mockDepositAccountDaoGetById{
				enable:           true,
				depositAccountId: depositAccountId,
				res: &depositPb.DepositAccount{
					Id:                 depositAccountId,
					ActorId:            fixtureDepositAccountSD1.GetActorId(),
					Type:               accountPb.Type_SMART_DEPOSIT,
					AccountNumber:      fixtureDepositAccountSD1.GetAccountNumber(),
					RepayAccountNumber: fixtureDepositAccountSD1.GetRepayAccountNumber(),
				},
				err: nil,
			},
			mockDepositFeedbackEngine: &mockDepositFeedbackEngine{
				enable:     true,
				screen:     "",
				screenMeta: nil,
				err:        nil,
			},
			mockGetPreclosureNudgeAttributes: &mockGetPreclosureNudgeAttributes{
				enable: true,
				res: []*depositPb.PreclosureAttribute{
					{
						Name: depositPb.PreclosureAttribute_INTEREST_RETURNS,
					},
					{
						Name: depositPb.PreclosureAttribute_FIT_RULES_GENERIC,
					},
				},
				err: nil,
			},
			mockGetInterestForfeit: &mockGetInterestForfeit{
				enable: true,
				res:    nil,
			},
		},
		{
			name: "fetch preclosure nudge attributes successfully with fit rule count non-zero",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetPreclosureAttributesRequest{
					DepositAccountId: depositAccountId,
				},
			},
			want: &depositPb.GetPreclosureAttributesResponse{
				PreclosureDetails: &depositPb.PreclosureDetails{
					PreclosureAttributes: []*depositPb.PreclosureAttribute{
						{
							Name: depositPb.PreclosureAttribute_INTEREST_RETURNS,
						},
						{
							Name: depositPb.PreclosureAttribute_FIT_RULES_WITH_COUNT,
							Params: map[string]string{
								"fit_rules_count": "34",
							},
						},
					},
				},
				AccountNumber:      fixtureDepositAccountSD1.GetAccountNumber(),
				RepayAccountNumber: fixtureDepositAccountSD1.GetRepayAccountNumber(),
				DepositAccountType: accountsPb.Type_SMART_DEPOSIT,
				Status:             rpc.StatusOk(),
			},
			wantErr: false,
			mockDepositAccountDaoGetById: &mockDepositAccountDaoGetById{
				enable:           true,
				depositAccountId: depositAccountId,
				res: &depositPb.DepositAccount{
					Id:                 depositAccountId,
					ActorId:            fixtureDepositAccountSD1.GetActorId(),
					Type:               accountPb.Type_SMART_DEPOSIT,
					AccountNumber:      fixtureDepositAccountSD1.GetAccountNumber(),
					RepayAccountNumber: fixtureDepositAccountSD1.GetRepayAccountNumber(),
				},
				err: nil,
			},
			mockDepositFeedbackEngine: &mockDepositFeedbackEngine{
				enable:     true,
				screen:     "",
				screenMeta: nil,
				err:        nil,
			},
			mockGetPreclosureNudgeAttributes: &mockGetPreclosureNudgeAttributes{
				enable: true,
				res: []*depositPb.PreclosureAttribute{
					{
						Name: depositPb.PreclosureAttribute_INTEREST_RETURNS,
					},
					{

						Name: depositPb.PreclosureAttribute_FIT_RULES_WITH_COUNT,
						Params: map[string]string{
							"fit_rules_count": "34",
						},
					},
				},
				err: nil,
			},
			mockGetInterestForfeit: &mockGetInterestForfeit{
				enable: true,
				res:    nil,
			},
		},
		{
			name: "fetch interest forfeit successfully for 5 days",
			args: args{
				ctx: context.Background(),
				req: &depositPb.GetPreclosureAttributesRequest{
					DepositAccountId: depositAccountId,
				},
			},
			want: &depositPb.GetPreclosureAttributesResponse{
				ConfirmationDetails: &depositPb.ConfirmationDetails{
					InterestForfeit: &depositPb.InterestForfeit{
						Name:   depositPb.InterestForfeit_NO_INTEREST_LESS_THAN_7_DAYS,
						Params: nil,
					},
				},
				AccountNumber:      fixtureDepositAccountSD1.GetAccountNumber(),
				RepayAccountNumber: fixtureDepositAccountSD1.GetRepayAccountNumber(),
				DepositAccountType: accountsPb.Type_SMART_DEPOSIT,
				Status:             rpc.StatusOk(),
			},
			wantErr: false,
			mockDepositAccountDaoGetById: &mockDepositAccountDaoGetById{
				enable:           true,
				depositAccountId: depositAccountId,
				res: &depositPb.DepositAccount{
					Id:                 depositAccountId,
					ActorId:            fixtureDepositAccountSD1.GetActorId(),
					Type:               accountPb.Type_SMART_DEPOSIT,
					AccountNumber:      fixtureDepositAccountSD1.GetAccountNumber(),
					RepayAccountNumber: fixtureDepositAccountSD1.GetRepayAccountNumber(),
				},
				err: nil,
			},
			mockDepositFeedbackEngine: &mockDepositFeedbackEngine{
				enable:     true,
				screen:     "",
				screenMeta: nil,
				err:        nil,
			},
			mockGetPreclosureNudgeAttributes: &mockGetPreclosureNudgeAttributes{
				enable: true,
				res:    nil,
				err:    nil,
			},
			mockGetInterestForfeit: &mockGetInterestForfeit{
				enable: true,
				res: &depositPb.InterestForfeit{
					Name: depositPb.InterestForfeit_NO_INTEREST_LESS_THAN_7_DAYS,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockDepositAccountDaoGetById.enable {
				depositAccountDao.EXPECT().GetById(gomock.Any(), tt.mockDepositAccountDaoGetById.depositAccountId).
					Return(tt.mockDepositAccountDaoGetById.res, tt.mockDepositAccountDaoGetById.err).MaxTimes(1)
			}
			if tt.mockDepositFeedbackEngine.enable {
				depositFeedbackEngine.EXPECT().IsEligibleForPreclosureFeedback(gomock.Any(), gomock.Any()).
					Return(tt.mockDepositFeedbackEngine.screen, tt.mockDepositFeedbackEngine.screenMeta,
						tt.mockDepositFeedbackEngine.err).Times(1)
			}
			if tt.mockGetPreclosureNudgeAttributes.enable {
				preclosureEngine.EXPECT().GetPreclosureNudgeAttributes(gomock.Any(),
					tt.mockDepositAccountDaoGetById.res).Return(tt.mockGetPreclosureNudgeAttributes.
					res, tt.mockGetPreclosureNudgeAttributes.err).MaxTimes(1)
			}
			if tt.mockGetInterestForfeit.enable {
				preclosureEngine.EXPECT().GetInterestForfeit(gomock.Any(), tt.mockDepositAccountDaoGetById.res).Return(tt.mockGetInterestForfeit.
					res).MaxTimes(1)
			}

			got, err := depositSvc.GetPreclosureAttributes(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPreclosureAttributes() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetPreclosureAttributes() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func checkFeedbackDetails(got *depositPb.FeedbackDetails, want *depositPb.FeedbackDetails, depositAccountId string) bool {
	if (want == nil && got != nil) || (want != nil && got == nil) {
		return false
	}
	if got == nil && want == nil {
		return true
	}
	if got.GetScreenName() != want.GetScreenName() || got.GetScreenMeta()[depositAccountId] != want.GetScreenMeta()[depositAccountId] {
		return false
	}
	return true
}

func TestService_BatchGetByIds(t *testing.T) {
	type args struct {
		ctx context.Context
		req *depositPb.BatchGetByIdsRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(accountDao *mocks.MockDepositAccountDao)
		want       *depositPb.BatchGetByIdsResponse
		wantErr    assert.ErrorAssertionFunc
	}{
		{
			name: "no record from dao call",
			args: args{
				ctx: context.Background(),
				req: &depositPb.BatchGetByIdsRequest{
					AccountIds: []string{"acc-1", "acc-2"},
				},
			},
			setupMocks: func(accountDao *mocks.MockDepositAccountDao) {
				accountDao.EXPECT().GetByIds(gomock.Any(), []string{"acc-1", "acc-2"}, nil).Return([]*depositPb.DepositAccount{}, nil)
			},
			want: &depositPb.BatchGetByIdsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return err == nil
			},
		},
		{
			name: "failure from dao call",
			args: args{
				ctx: context.Background(),
				req: &depositPb.BatchGetByIdsRequest{
					AccountIds: []string{"acc-1", "acc-2"},
				},
			},
			setupMocks: func(accountDao *mocks.MockDepositAccountDao) {
				accountDao.EXPECT().GetByIds(gomock.Any(), []string{"acc-1", "acc-2"}, nil).Return([]*depositPb.DepositAccount{}, fmt.Errorf("err"))
			},
			want: &depositPb.BatchGetByIdsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return err == nil
			},
		},
		{
			name: "success fetch deposit accounts",
			args: args{
				ctx: context.Background(),
				req: &depositPb.BatchGetByIdsRequest{
					AccountIds: []string{"acc-1", "acc-2"},
				},
			},
			setupMocks: func(accountDao *mocks.MockDepositAccountDao) {
				accountDao.EXPECT().GetByIds(gomock.Any(), []string{"acc-1", "acc-2"}, nil).Return([]*depositPb.DepositAccount{
					{
						Id:   "acc-1",
						Type: accountsPb.Type_FIXED_DEPOSIT,
					},
					{
						Id:   "acc-2",
						Type: accountsPb.Type_SMART_DEPOSIT,
					},
				}, nil)
			},
			want: &depositPb.BatchGetByIdsResponse{
				Status: rpc.StatusOk(),
				AccountsMap: map[string]*depositPb.DepositAccount{
					"acc-1": {
						Id:   "acc-1",
						Type: accountsPb.Type_FIXED_DEPOSIT,
						DepositIcon: &commontypes.Image{
							ImageType: commontypes.ImageType_PNG,
							ImageUrl:  "https://epifi-icons.pointz.in/deposit/default-FD.png",
						},
					},
					"acc-2": {
						Id:   "acc-2",
						Type: accountsPb.Type_SMART_DEPOSIT,
						DepositIcon: &commontypes.Image{
							ImageType: commontypes.ImageType_PNG,
							ImageUrl:  "https://epifi-icons.pointz.in/deposit/SDSpendProof.png",
						},
					},
				},
			},
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return err == nil
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			depositAccDao := mocks.NewMockDepositAccountDao(ctrl)
			s := &deposit.Service{
				DepositAccountDao: depositAccDao,
			}
			tt.setupMocks(depositAccDao)
			got, err := s.BatchGetByIds(tt.args.ctx, tt.args.req)
			if !tt.wantErr(t, err, fmt.Sprintf("BatchGetByIds(%v, %v)", tt.args.ctx, tt.args.req)) {
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("BatchGetByIds value is mismatch (-got +want):%s\n", diff)
			}
		})
	}
}
