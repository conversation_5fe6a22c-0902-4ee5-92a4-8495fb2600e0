package main

import (
	"context"
	"encoding/json"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	operationalStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	bcPb "github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/product"
	savingsPb "github.com/epifi/gamma/api/savings"
	usersPb "github.com/epifi/gamma/api/user"
)

type JobGetUserDeletionChecks struct {
	bcClient         bcPb.BankCustomerServiceClient
	usersClient      usersPb.UsersClient
	saClient         savingsPb.SavingsClient
	operStatusClient operationalStatusPb.OperationalStatusServiceClient
	prodClient       product.ProductClient
}

func (g *JobGetUserDeletionChecks) DoJob(ctx context.Context, req *JobRequest) error {
	actorIds := splitCSV(req.Args1)

	for _, actorId := range actorIds {
		ctx = epificontext.CtxWithActorId(ctx, actorId)
		fmt.Printf("\nProcessing for actor : %v", actorId)

		// Get User
		getUserResp, getUserErr := g.usersClient.GetUser(ctx, &usersPb.GetUserRequest{
			Identifier: &usersPb.GetUserRequest_ActorId{
				ActorId: actorId,
			},
		})
		if rpcErr := epifigrpc.RPCError(getUserResp, getUserErr); rpcErr != nil {
			logger.Error(ctx, "Error in fetching detail for user", zap.Error(rpcErr))
		}

		// Get Savings Account
		savingsAccountRes, err := g.saClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
			Identifier: &savingsPb.GetAccountRequest_ActorId{
				ActorId: actorId,
			}},
		)
		if err != nil {
			logger.Error(ctx, "Error in getting accountDetails", zap.Error(err))
		}

		// Get operational status
		operResp, err := g.operStatusClient.GetOperationalStatus(ctx, &operationalStatusPb.GetOperationalStatusRequest{
			DataFreshness: operationalStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_LAST_KNOWN,
			AccountIdentifier: &operationalStatusPb.GetOperationalStatusRequest_SavingsAccountId{
				SavingsAccountId: savingsAccountRes.GetAccount().GetId(),
			},
		})
		if te := epifigrpc.RPCError(operResp, err); te != nil {
			logger.Error(ctx, "error in get operational status", zap.Error(te))
		}

		// Get prod client
		getProductsRes, err := g.prodClient.GetProductsStatus(ctx, &product.GetProductsStatusRequest{
			ActorId: actorId,
			ProductTypes: []product.ProductType{
				product.ProductType_PRODUCT_TYPE_PERSONAL_LOANS,
				product.ProductType_PRODUCT_TYPE_TPAP,
				product.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
				product.ProductType_PRODUCT_TYPE_USSTOCKS,
				product.ProductType_PRODUCT_TYPE_CREDIT_CARD,
			},
		})
		if rpcErr := epifigrpc.RPCError(getProductsRes, err); rpcErr != nil {
			logger.Error(ctx, "error in fetching products status details", zap.Error(rpcErr))
		}

		// Print all relevant details
		fmt.Printf("\n\n============== ACTOR %s ==============\n", actorId)

		if getProductsRes != nil {
			prettyPrint("Active Products", getProductsRes.GetProductInfoMap())
		}

		if getUserResp != nil && getUserResp.GetUser() != nil {
			prettyPrint("User Access Revoke Status", getUserResp.GetUser().GetAccessRevokeDetails())
		}

		if operResp != nil {
			prettyPrint("SA Closure Status", operResp.GetOperationalStatusInfo())
		}
	}
	return nil
}

// prettyPrint formats any data structure as pretty-printed JSON
func prettyPrint(title string, data interface{}) {
	fmt.Printf("\n%s:\n", title)

	// Try to marshal as JSON for better readability
	jsonBytes, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		// If JSON marshaling fails, use %+v for detailed output
		fmt.Printf("%+v\n", data)
	} else {
		fmt.Println(string(jsonBytes))
	}
	fmt.Println("==========================================")
}
