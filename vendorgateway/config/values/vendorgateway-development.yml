# On dev & test env, VNGW server listens on 9098 without any load balancer mapping 443 to 9098
# Hence, callback URL should be to 9098 port and not 443 port
# TLS is terminated at load balancer for VNGw.
# Load balancers are not present in dev localstack and hence VNGw will run on Http in dev & test envs

Application:
  Environment: "development"
  Name: "vendorgateway"
  SyncWrapperTimeout: 15
  VGAuthSvcSyncWrapperTimeout: 15
  IsStatementAPIEnabled: true
  IsListKeysSimulated: true
  #dmp dispute
  CreateDisputeURL: "https://simulator.demo.pointz.in:8080/test/DMP/v1.0.0/createDispute"
  DisputeStatusCheckUrl: "https://simulator.demo.pointz.in:8080/DMP/v1.0.0/disputeStatusCheck"
  SendCorrespondenceUrl: "https://simulator.demo.pointz.in:8080/DMP/v1.0.0/disputeCorrespondence"
  ChannelQuestionnaireUrl: "https://simulator.demo.pointz.in:8080/DMP/v1.0.0/channelQuestionnaire"
  UploadDocumentUrl: "https://simulator.demo.pointz.in:8080/DMP/v1.0.0/disputeDocument"
  CreateCustomerURL: "https://localhost:9091/createCustomerFederal"
  CreateLoanCustomerURL: "http://localhost:8079/createLoanCustomerFederal"
  LoanCustomerCreationStatusURL: "http://localhost:8079/loanCustomerCreationStatusFederal"
  AccountTransactionsUrl: "https://simulator.demo.pointz.in:8080/DMP/v1.0.0/transactions"
  CheckCustomerStatusURL: "https://localhost:8080/checkCustomerStatusFederal"
  DedupeCheckURL: "https://localhost:8080/dedupeCheck"
  FetchCustomerDetailsUrl: "https://localhost:9091/openbanking/accounts/federal/GetCustomerDetails"
  CreateAccountURL: "https://localhost:9091/createAccountFederal"
  CheckAccountStatusURL: "https://localhost:8080/checkAccountStatusFederal"
  EnquireVKYCStatusUrl: "https://localhost:9091/openbanking/enquirevkyc"
  EnquireBalanceURL: "https://localhost:9091/openbanking/accounts/federal/getBalance"
  CkycSearchURL: "https://localhost:9091/ckyc/search"
  GetKycDataURL: "https://localhost:9091/ckyc/download"
  CreateVirtualIdURL: "https://localhost:8080/createVirtualIdFederal"
  GetTokenURL: "https://localhost:8080/listKeys"
  DeviceRegistrationURL: "https://localhost:9091/registerDevice"
  SetPINURL: "https://localhost:8080/SetCredFederal"
  UPIBalanceEnquiryURL: "https://localhost:8080/BalEnq"
  ReqComplaintURL: "https://localhost:8080/reqComplaint"
  ReqCheckComplaintStatusUrl: "https://localhost:8080/checkComplaintStatus"
  ValidateAddressURL: "https://localhost:8080/ValAddFederal"
  GenerateUpiOtpURL: "https://localhost:8080/generateUpiOtp"
  RespAuthDetailsURL: "https://localhost:8080/RespAuthDetails"
  ReqPayURL: "https://localhost:8080/ReqPay"
  RegisterMobileURL: "https://localhost:8080/registerMobile"
  ListUpiKeyUrl: "https://localhost:8080/ListKeys"
  ListAccountURL: "https://localhost:8080/ListAccount"
  ListAccountProviderURL: "https://localhost:8080/ListAcctProvider"
  RespTxnConfirmationURL: "https://localhost:8080/RespTxnConfirmationFederal"
  RespValidateAddressURL: "https://localhost:8080/RespValAddFederal"
  ReqCheckTxnStatusURL: "https://localhost:8080/ReqCheckTxnStatusFederal"
  ListVaeURL: "https://localhost:8080/ListVaeFederal"
  ReqMandateURL: "https://localhost:8080/ReqMandate"
  RespAuthMandateURL: "https://localhost:8080/RespAuthMandate"
  RespMandateConfirmationURL: "https://localhost:8080/RespMandateConfirmation"
  RespAuthValCustURL: "https://localhost:8080/RespAuthValCust"
  ReqActivationUrl: "https://localhost:8080/ActivateInternationalPayments"
  ListPspURL: "https://localhost:8080/ListPsp"
  GetMapperInfoURL: "https://localhost:8080/GetMapperInfo"
  ReqValQRUrl: "https://localhost:8080/ValidateInternationalQR"
  GetUpiLiteURL: "https://localhost:8080/GetUpiLite"
  SyncUpiLiteInfoURL: "https://localhost:8080/SyncUpiLiteInfo"

  PanProfileURL: "https://localhost:9091/karza/panProfile"
  BureauIdUrl: "https://api.bureau.id/transactions"

  #Call back urls for account setup
  CreateCustomerCallBackUrl: "http://localhost:9098/openbanking/customer/create"
  CreateAccountCallBackUrl: "http://localhost:9098/openbanking/account/create"

  # EPan
  GetEPANKarzaStatusURL: "https://localhost:9091/karza/epan"
  InhouseGetAndValidateEPANURL: "https://localhost:9091/inhouse/epan"

  # send vkyc data to federal for inhouse vkyc service
  SendAgentDataURL: "https://localhost:9091/vkyc/federal/send-agent-data"
  SendAuditorDataURL: "https://localhost:9091/vkyc/federal/send-auditor-data"

  # ITR
  InhouseVerifyAndGetITRIntimationDetailsURL: "https://localhost:9091/inhouse/verify/itr-intimation"

  # Liveness Service
  Veri5CheckLivenessRequestURL: "https://localhost:9091/video-id-kyc/api/1.0/liveness"
  Veri5MatchFaceRequestURL: "https://localhost:9091/video-id-kyc/api/1.0/faceCompare"
  KarzaCheckLivenessRequestURL: "https://localhost:9091/v3/uat/video-liveness"
  KarzaLivenessCallbackURL: "http://localhost:9098/liveness/karza"
  KarzaMatchFaceRequestURL: "https://localhost:9091/v3/facesimilarity"
  KarzaCheckPassiveLivenessRequestURL: "https://localhost:9091/v3/image-liveness"
  KarzaCheckLivenessStatusURL: "https://localhost:9091/v3/video-liveness-status"
  InhouseCheckLivenessRequestURL: "https://localhost:9091/inhouse-liveness"
  InhouseMatchFaceRequestURL: "https://localhost:9091/inhouse-facematch"
  InhouseMatchFaceRequestURLV2: "https://localhost:9091/inhouse-facematch"
  UseFormMarshalForKarza: false
  UseFormMarshalForKarzaFM: false

  # Employment Service
  Employment:
    KarzaPFOTPURL: "https://testapi.karza.in/v2/epf-get-otp"
    KarzaPFPassbookURL: "https://testapi.karza.in/v3/epf-get-passbook"
    KarzaEmploymentVerificationURL: "https://testapi.karza.in/v2/employment-verification-advanced"
    KarzaSearchCompanyNameURL: "https://testapi.kscan.in/v3/employer-search-lite"
    KarzaUANLookupURL: "https://localhost:9091/v2/uan-lookup"
    KarzaEPFAuthURL: "https://testapi.karza.in/v2/epf-auth"
    KarzaEmployeeNameSearchURL: "https://testapi.karza.in/v2/employee-search"
    KarzaCompanyMasterLLPDataURL: "https://localhost:9091/v2/mca"
    KarzaSearchGSTINBasisPAN: "https://localhost:9091/uat/v1/search"
    KarzaGetForm16QuarterlyURL: "https://testapi.karza.in/v3/tdsq"
    KarzaGetEmployerDetailsByGstinURL: "https://localhost:9091/v2/gst-verification"
    KarzaGetUANFromPan: "https://localhost:9091/v2/uan-by-pan"
    SignzyLoginURL: "https://localhost:9091/v2/login"
    SignzyDomainNameVerificationURL: "https://localhost:9091/v2/domainverifications"
    KarzaFindUanByPan: "https://localhost:9091/v3/pan-uan"

  # Freshdesk service
  FreshdeskAgentURL: "https://ficaretesting.freshdesk.com/api/v2/agents"
  FreshdeskTicketURL: "https://ficaretesting.freshdesk.com/api/v2/tickets"
  FreshdeskFilterTicketURL: "https://ficaretesting.freshdesk.com/api/v2/search/tickets"
  FreshdeskSolutionsURL: "https://ficaretesting.freshdesk.com/api/v2/solutions"
  FreshdeskContactsURL: "https://ficaretesting.freshdesk.com/api/v2/contacts"
  FreshdeskTicketFieldURL: "https://ficaretesting.freshdesk.com/api/v2/admin/ticket_fields"
  CxFreshdeskTicketAttachmentsBucketName: "epifi-cx-ticket-attachments"

  Razorpay:
    BaseUrl: "https://api.razorpay.com"

  # CRM Service
  FreshdeskURI:
    Agent: "/agents"
    Ticket: "/tickets"
    FilterTicket: "/search/tickets"
    Solutions: "/solutions"
    Contacts: "/contacts"
    TicketField: "/admin/ticket_fields"
    Job: "/jobs"
    BulkUpdate: "/tickets/bulk_update"
  FreshDeskAccountConfig:
    EPIFI_TECH:
      USE_CASE_RISK_CASE_MANAGEMENT:
        ApiTokenSecretName: "EpifiTechRiskFreshdeskApiKey"
        BaseURL: "https://epifitechnologies.freshdesk.com/api/v2"
    FEDERAL_BANK:
      USE_CASE_RISK_CASE_MANAGEMENT:
        ApiTokenSecretName: "EpifiTechRiskFreshdeskApiKey"
        BaseURL: "https://epifitechnologies.freshdesk.com/api/v2"

  #Freshchat service
  FreshchatConversationURL: "https://epifi6.freshchat.com/v2/conversations"
  FreshchatUserURL: "https://epifi6.freshchat.com/v2/users"
  FreshchatAgentURL: "https://epifi6.freshchat.com/v2/agents"


  InhouseNameCheckUrl: "https://localhost:9091/namematch"
  InhouseEmployerNameMatchUrl: "https://entity-matcher.data-dev.pointz.in/api/v1/companymatch"
  InhouseBreForCCUrl: "http://iris.epifi.in/evaluator/flow/v1/flow-ccbrepolicy"
  InhouseEmployerNameCategoriserUrl: "http://text-semantics.data-dev.pointz.in/v1/name_categoriser"

  DrivingLicenseValidationUrl: "https://localhost:9091/v3/dl"

  VoterIdValidationUrl: "https://localhost:9091/v2/voter"

  BankAccountVerificationUrl: "https://localhost:9091/verify-bank-account"

  CAMS:
    # invoke simulator in development
    OrderFeedFileURL: "https://localhost:8080/cams/ProcessOrderFeedFile"
    FATCAFileURL: "https://localhost:8080/cams/ProcessFATCAFeedFile"
    ElogFileURL: "https://localhost:8080/cams/ProcessElogFile"
    OrderFeedFileStatusURL: "https://localhost:8080/cams/GetOrderFeedFileStatus"
    OrderFeedFileSyncURL: "https://localhost:8080/cams/ProcessOrderFeedFileSync"
    S3Bucket: "epifi-mutualfund-dev"
    NFTFileURL: "https://localhost:8080/cams/ProcessNFTFile"
    GetFolioDetailsURL: "https://localhost:8080/cams/GetFolioDetails"
    NomineeUpdateURL: "https://localhost:8080/cams/NomineeUpdateURL"
  Tiering:
    AddSchemeChangeURL: "https://localhost:9091/tiering/schemeChangeAdd"
    EnquireSchemeChangeURL: "https://localhost:9091/tiering/schemeChangeEnq"

  SmallCase:
    CreateTransactionURL: "https://localhost:8080/smallcase/CreateTransaction"
    InitiateHoldingsImportURL: "https://localhost:8080/smallcase/InitiateHoldingsImportURL"
    TriggerHoldingsImportFetchURL: "https://localhost:8080/smallcase/TriggerHoldingsImportFetchURL"
    MFAnalyticsURL: "https://localhost:8080/smallcase/MfAnalytics"
    SmallCaseGateway: "fimoney-stag"

  MFCentral:
    GenerateTokenURL: "https://localhost:8080/mfcentral/GenerateToken"
    EncryptAndSignURL: "https://nsdl-forwardsecrecy.deploy.pointz.in/mfcentral/v1/encryptAndSign"
    VerifyAndDecryptURL: "https://nsdl-forwardsecrecy.deploy.pointz.in/mfcentral/v1/verifyAndDecrypt"
    UpdateFolioEmailURL: "https://localhost:8080/mfcentral/api/client/v1/updateEmail"
    UpdateFolioMobileURL: "https://localhost:8080/mfcentral/api/client/v1/updateMobile"
    InvestorConsentUrl: "https://localhost:8080/mfcentral/api/client/v1/investorconsent"
    SubmitCasSummaryUrl: "https://localhost:8080/mfcentral/api/client/v1/submitcassummaryrequest"
    GetCasDocumentUrl: "https://localhost:8080/mfcentral/api/client/v1/getcasdocument"
    GetTransactionStatusUrl: "https://localhost:8080/mfcentral/api/client/v1/getTransactionStatus"

  ## aa smart parser
  InHouseAAParserURL: "https://smart-parser.data-dev.pointz.in/parse"
  InHouseAABulkParserURL: "https://smart-parser.data-dev.pointz.in/bulk_parse"

  #SMS Service
  Exotel:
    URL: "https://%s:%<EMAIL>/v1/Accounts/%s/SMS"
    AccountSid: "epifi2"
    SenderId: "***********"
  Twilio:
    URL: "https://api.twilio.com/2010-04-01/Accounts/%s/Messages"
    SenderId: "***********"
  AclEpifi:
    FallbackURL: "https://dmz.aclgateway.com/servlet/com.aclwireless.pushconnectivity.listeners.TextListener"
    URL: "https://push3.aclgateway.com/servlet/com.aclwireless.pushconnectivity.listeners.TextListener"
    AppId: "epiftalt"
    SenderId: "FiMony"
  AclFederal:
    FallbackURL: "https://dmz.aclgateway.com/servlet/com.aclwireless.pushconnectivity.listeners.TextListener"
    URL: "https://push3.aclgateway.com/servlet/com.aclwireless.pushconnectivity.listeners.TextListener"
    AppId: "epiftalt"
    SenderId: "FiMony"
  AclEpifiOtp:
    FallbackURL: "https://dmzotp.aclgateway.com/OTP_ACL_Web/OtpRequestListener"
    URL: "https://otp2.aclgateway.com/OTP_ACL_Web/OtpRequestListener"
    AppId: "epifiotp"
    SenderId: "FiMony"
  KaleyraFederal:
    URL: "https://api.in.kaleyra.io/alerts/api/v4"
    SenderId: "FedFiB"
  KaleyraEpifi:
    URL: "https://api.in.kaleyra.io/alerts/api/v4"
    SenderId: "FiMony"
  KaleyraEpifiNR:
    URL: "https://api.in.kaleyra.io/v1/HXIN1778099997IN/messages"
    SenderId: "FIMONY"
    CallbackProfileId: "IN_921541db-1b15-4571-b5db-3aa26bb7cbd1"
  KaleyraSmsCallbackURL: "https://localhost:9098/sms/callback/kaleyra/UrlListner/requestListener"

  AclWhatsapp:
    URL: "https://pushuat.aclwhatsapp.com/pull-platform-receiver/wa/messages"
    OptInURL: "http://***************:8085/api/v1/addoptinpost"
  KaleyraFederalCreditCard:
    URL: "https://api.in.kaleyra.io/alerts/api/v4"
    SenderId: "FedFiB"
  #Loylty Service
  Loylty:
    AuthTokenURL: "https://localhost:8080/offers/loylty/auth"
    GiftCardBookingURL: "https://localhost:8080/offers/loylty/egvbooking"
    CharityBookingURL: "https://localhost:8080/offers/loylty/charitybooking"
    GiftCardProductListURL: "https://localhost:8080/offers/loylty/giftcard/product"
    # todo (utkarsh) : add egv product detail rpc in simulator
    GiftCardProductDetailURL: ""
    CreateOrderURL: "https://localhost:8080/offers/loylty/create-order"
    ConfirmOrderURL: "https://localhost:8080/offers/loylty/confirm-order/%s"
    GetOrderDetailsURL: "https://localhost:8080/offers/loylty/order-details/%s"

  Qwikcilver:
    GetAuthorizationCodeBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/authorization-code"
    GetAccessTokenBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/access-token"
    CreateOrderBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/create-order"
    GetActivatedCardDetailsBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/activated-card-details/%s"
    GetCategoryDetailsBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/category-details"
    GetOrderStatusBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/order-status/%s"
    GetProductDetailsBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/product-details/%s"
    GetProductListBaseUrl: "https://simulator.qa.pointz.in:8080/offers/qwikcilver/product-list/%s"
    AccessTokenValidityDuration: "1h"
    MailOrderDetailsTo: "<EMAIL>"

  Thriwe:
    BaseUrl: "https://staging-india-api-gateway.thriwe.com"

  Riskcovry:
    BaseUrl: "https://api.uat-riskcovry.com/api/partner"

  Onsurity:
    BaseUrl: "https://partner.preprod.onsurity.com"

  Karvy:
    KarviAppId: "RIA"
    AgentCode: "INA200015185"
    BranchCode: "R999"
    BucketName: "epifi-mutualfund-dev-karvy"
    FATCAFileURL: "https://localhost:8080/karvy/ProcessFATCAFeedFile"
    OrderFeedFileSyncURL: "https://localhost:8080/karvy/ProcessOrderFeedFileSync"
    OrderFeedFileV2SyncURL: "https://localhost:8080/karvy/ProcessOrderFeedFileSync"
    NFTFileUploadURL: "https://localhost:8080/karvy/NFTFileUploadURL"
    GetFolioDetailsURL: "https://localhost:8080/karvy/GetFolioDetailsURL"

  #json file path
  PayFundTransferStatusCodeJson: "mappingJson/fundTransferStatusCodes.json"
  PayUpiStatusCodeJson: "mappingJson/upiStatusCodes.json"
  PayAckStatusCodeJson: "mappingJson/ackStatusCode.json"

  DepositAckStatusCodeFilePath: "mappingJson/depositAckStatusCode.json"
  DepositResponseStatusCodeFilePath: "mappingJson/depositResponseStatusCodes.json"

  CardResponseStatusCodeFilePath: "mappingJson/cardResponseStatusCodes.json"
  SIResponseStatusCodeFilePath: "mappingJson/siResponseStatusCodes.json"

  Federal:
    CheckCustomerStatusForNonResidentURL: "http://localhost:8079/checkCustomerStatusForNonResident"
    CreateCustomerForNonResidentURL: "https://localhost:8150/createCustomerForNonResident"
    CustomerDetailsInsertURL: "https://localhost:8080/upgradeKycLevel"
    PanValidationV2Url: "https://localhost:9091/fedbnk/pan/v2.0.0/validate"
    PayIntraBankURL: "https://localhost:9091/openbanking/fundtransfer/federal/intraBank"
    PayNEFTURL: "https://localhost:9091/openbanking/fundtransfer/federal/neft"
    PayIMPSURL: "https://localhost:9091/openbanking/fundtransfer/federal/imps"
    PayRTGSURL: "https://localhost:9091/openbanking/fundtransfer/federal/rtgs"
    PayStatusURL: "https://localhost:9091/openbanking/fundtransfer/federal/enquiry"
    PayIntraBankDepositURL: "https://localhost:9091/fedbnk/uat/neobanking/OwnDepositAccFT"
    RemitterDetailsFetchUrl: "https://localhost:9091/openbanking/fundtransfer/federal/GetRemitterDetails"
    RemitterDetailsV1FetchUrl: "https://localhost:9091/openbanking/fundtransfer/federal/GetRemitterDetailsV1"
    # TODO(Sundeep): Fill the correct URL
    BeneficiaryNameLookupUrl: ""
    GetCsisStatusUrl: "https://localhost:9091/openbanking/fundtransfer/federal/CSISStatusCheck"

    PayIntraBankCallbackURL: "http://localhost:9098/openbanking/payment/federal"
    PayNEFTCallbackURL: "http://localhost:9098/openbanking/payment/federal"
    PayIMPSCallbackURL: "http://localhost:9098/openbanking/payment/federal"
    PayRTGSCallbackURL: "http://localhost:9098/openbanking/payment/federal"

    # B2C Payments
    PayB2CIntraBankURL: "https://localhost:9091/openbanking/fundtransfer/B2C/federal/intraBank"
    PayB2CIntraBankCallbackURL: "http://localhost:9098/openbanking/payment/b2c/federal"
    PayB2CStatusURL: "https://simulator.staging.pointz.in:9091/openbanking/fundtransfer/B2C/federal/enquiry"
    PayB2CImpsURL: "https://localhost:9091/openbanking/fundtransfer/B2C/federal/imps"
    PayB2CImpsCallbackURL: "http://localhost:9098/openbanking/payment/b2c/federal"

    # Shipping Preference Service
    ShippingAddressUpdateURL: "https://localhost:9091/fedbnk/uat/neobanking-card/ShipAddrModification"
    ShippingAddressUpdateCallbackURL: "http://localhost:9098/openbanking/shipping_preference/federal"

    # Card Service
    DebitCardCreateURL: "https://localhost:9091/fedbnk/uat/neobanking-card/CardCreation"
    DebitCardCreateCallbackURL: "http://localhost:9098/openbanking/card/federal"
    DebitCardActivateURL: "https://localhost:9091/fedbnk/uat/neobanking-card/CardActivation"
    DebitCardEnquiryUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/CardEnqService"
    DebitCardPinSetUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/PinManagement"
    DebitCardPinChangeUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/PinManagement"
    DebitCardPinResetUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/PinManagement"
    DebitCardPinValidationUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/PinManagement"
    DebitCardBlockUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/CardControl"
    DebitCardSuspendOnOffUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/CardControl"
    DebitCardLocationOnOffUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/CardControl"
    DebitCardECommerceOnOffUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/CardControl"
    DebitCardLimitEnquiry: "https://localhost:9091/fedbnk/uat/neobanking-card/LimitEnquiry"
    DebitCardUpdateLimit: "https://localhost:9091/fedbnk/uat/neobanking-card/LimitUpdate"
    DebitCardDeliveryTracking: "https://localhost:9091/fedbnk/uat/neobanking-card/DeliveryTracking"
    DebitCardCVVEnquiryUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/CVVEnquiry"
    DebitCardConsolidatedCardControlUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/FHMCardONOFF"
    DebitCardPhysicalDispatchUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/cardDispatch"
    DebitCardPhysicalDispatchCallbackUrl: "https://localhost:9098/openbanking/cardPhysicalDispatch/federal"
    CheckDebitCardIssuanceFeeStatusUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/GSTbifurcationStatus"
    DebitCardCollectIssuanceFeeUrl: "https://localhost:9091/fedbnk/uat/neobanking-card/GSTbifurcation"

    # PAN Service
    PANValidationURL: "https://localhost:8080/panValidation"
    PANAadhaarValidationURL: "https://localhost:8080/panAadhaarValidation"

    EkycNameDobValidationURL: "https://localhost:8080/ekyc/namedob/validation"
    AadharMobileValidationURL: "https://localhost:9091/aadharmobilevalidate"
    ShareDocWithVendorURL: ""

    # UN Name Check Service
    UNNameCheckURL: "https://localhost:8080/UNNameCheck"

    # Device Re-registration / Profile Update API
    DeviceReRegURL: "https://localhost:9091/fedbnk/uat/neobanking/device/re-registration"
    DeviceReRegCallbackUrl: "http://localhost:9098/openbanking/auth/federal/user-device/re-register"

    # Device De-registration / Deactivate User
    DeviceDeRegURL: "https://localhost:9091/fedbnk/uat/neobanking/user-device/deactivation"

    # Generate OTP
    GenerateOTPURL: "https://localhost:9091/fedbnk/uat/neobanking/auth/generate-otp"

    DeviceReactivationURL: "https://localhost:9091/fedbnk/uat/neobanking/user-device/reactivate"
    # Deposit service
    CreateFDURL: "https://localhost:9091/fedbnk/uat/neobanking/CreateFD"
    CreateSDURL: "https://localhost:9091/fedbnk/uat/neobanking/CreateSD"
    CreateRDURL: "https://localhost:9091/fedbnk/uat/neobanking/CreateRD"
    AutoRenewFdURL: "https://localhost:9091/fedbnk/uat/neobanking/AutoRenewFd"
    CloseDepositAccountURL: "https://localhost:9091/fedbnk/uat/neobanking/ClosingDepositAcc"
    GetDepositAccountDetailURL: "https://localhost:9091/fedbnk/uat/neobanking/GetAccDetails"
    GetPreClosureDetailURL: "https://localhost:9091/fedbnk/uat/digitalCredit/v1.0.0/DepositAcctPreclosEnq"
    CheckDepositAccountStatusURL: "https://localhost:9091/fedbnk/uat/neobanking/DepositEnq"
    DepositListAccountURL: "https://localhost:9091/fedbnk/uat/neobanking/GetAccList"
    InterestRateInfoURL: "https://localhost:9091/fedbnk/uat/neobanking/interestRateInfo"
    CalculateInterestDetailsURL: "https://localhost:9091/fedbnk/uat/neobanking/CalculateInterestDetailsURL"
    CreateDepositCallbackUrl: "http://localhost:9098/openbanking/deposit/federal/create"
    PreCloseDepositCallbackUrl: "http://localhost:9098/openbanking/deposit/federal/preclose"
    AutoRenewFdCallbackUrl: "https://localhost:9098/openbanking/deposit/federal/AutoRenewFd"

    # Standing Instruction service
    CreateSIUrl: "https://localhost:9091/standinginstruction/federal/sicreate"
    ExecuteSIUrl: "https://localhost:9091/standinginstruction/federal/siexecute"
    SICallbackUrl: "http://localhost:9098/openbanking/payment/federal"
    ModifySIUrl: "https://localhost:9091/standinginstruction/federal/simodify"
    RevokeSIUrl: "https://localhost:9091/standinginstruction/federal/sirevoke"

    # csv file path
    CityCodesCsv: "mappingCsv/cityCodes.csv"
    StateCodesCsv: "mappingCsv/stateCodes.csv"
    CountryCodesCsv: "mappingCsv/countryCodes.csv"

    # Account
    OpeningBalanceURL: "https://localhost:9091/openbanking/accounts/federal/GetAccStatement"
    ClosingBalanceURL: "https://localhost:9091/openbanking/accounts/federal/GetClosingBalance"
    AccountStatementURL: "https://localhost:9091/openbanking/accounts/federal/GetAccStatement"
    AccountStatementByDRApiUrl: "https://localhost:9091/openbanking/accounts/federal/GetAccStatementByDrApi"
    EnquireBalanceV1URL: "https://localhost:9091/openbanking/accounts/federal/GetBalanceV1"
    MiniStatementURL: "https://localhost:9091/openbanking/accounts/federal/GetMiniStatement"
    AccountStatusURL: "https://localhost:9091/openbanking/accounts/federal/AccountStatusEnquiry"
    ThirdPartyAccountCollectionURL: "https://localhost:9091/openbanking/accounts/federal/tpAccountCollection"
    UpdateNomineeUrl: "https://localhost:9091/openbanking/accounts/federal/nomineeUpdate"

    # Partner SDK
    GetSessionParamsUrl: "https://localhost:9091/fedbnk/uat/partnersdk/GetSessionParams"

    # Enquiry Service Url
    CustomerCreationEnquiryStatusURL: "https://localhost:9091/CustomerCreationEnquiryStatus"
    AccountCreationEnquiryStatusURL: "https://localhost:9091/AccountCreationEnquiryStatus"
    CardCreationEnquiryStatusURL: "https://localhost:9091/CardCreationEnquiryStatus"
    DeviceReRegistrationDetailsEnquiryStatusURL: "https://localhost:9091/DeviceReRegistrationDetailsEnquiryStatus"
    MailingAddressModifyDetailsEnquiryStatusURL: "https://localhost:9091/MailingAddressModifyDetailsEnquiryStatus"
    ShippingAddressUpdateEnquiryStatusURL: "https://localhost:9091/ShippingAddressUpdateEnquiryStatus"
    DeviceRegistrationDetailsEnquiryStatusURL: "https://localhost:9091/DeviceRegistrationDetailsEnquiryStatus"
    PhysicalCardDispatchDetailsEnquiryStatus: "https://localhost:9091/PhysicalCardDispatchDetailsEnquiryStatus"

    # Chequebook Request and Track URLs
    OrderChequebookUrl: "https://localhost:9091/fedbnk/neobanking/v1.0.0/chequeBookOrder"
    TrackChequebookUrl: "https://localhost:9091/fedbnk/neobanking/v1.0.0/chequeBookTrack"
    IssueDigitalCancelledChequeUrl: "https://localhost:9091/fedbnk/account_utility/v1.0.0/digitalChequeLeafIssuance"

    # Profile Update and enquiry URL
    ProfileUpdateUrl: "http://localhost:9091/fedbnk/account_utility/v1.0.0/updateProfileAtBank"
    ProfileUpdateEnquiryUrl: "https://localhost:9091/fedbnk/account_utility/v1.0.0/profileUpdateStatus"

    # lien service url
    LienUrl: "https://localhost:9091/openbanking/lien/federal"

    TcsCalculationURL: "https://localhost:8080/test/federal"
    TcsCalculationChannelId: "EPI"

    # e-nach service url
    ListEnachUrl: "https://localhost:9091/NACHEnquiry_API/v1/enquiry"

    FetchEnachTransactionsUrl: ""

  LeadSquared:
    CreateOrUpdateLeadUrl: "https://localhost:8080/salaryprogram/leadsquared/CreateOrUpdateLead%s%s"

  # Video kyc karza service endpoints
  Karza:
    GenerateSessionTokenUrl: "https://localhost:8080/v3/get-jwt"
    AddNewCustomerUrl: "https://app.karza.in/test/videokyc/api/v2/customers"
    UpdateCustomerV3Url: "https://localhost:8080/test/videokyc/api/v3/customers"
    AddNewCustomerV3Url: "https://localhost:8080/test/videokyc/api/v3/customers"
    GenerateCustomerTokenUrl: "https://localhost:8080/test/videokyc/api/v2/generate-usertoken"
    GetSlotUrl: "https://app.karza.in/test/videokyc/api/v2/get-slot"
    BookSlotUrl: "https://app.karza.in/test/videokyc/api/v2/book-slot"
    GenerateWebLinkUrl: "https://localhost:8080/test/videokyc/api/v2/link"
    SlotAgentsUrl: "https://app.karza.in/test/videokyc/api/v2/slot-agents"
    TransactionStatusEnquiryUrl: "https://localhost:8080/test/videokyc/api/v2/transaction-events"
    ReScheduleSlotUrl: "https://app.karza.in/test/videokyc/api/v2/reschedule-customer"
    TriggerCallback: "https://app.karza.in/test/videokyc/api/v2/trigger-callback"
    AgentDashboardUrl: "https://localhost:8080/test/videokyc/api/agent-dashboard"
    AgentDashboardAuthUrl: "https://localhost:8080/test/videokyc/api/agent-dashboard-auth"
    EmploymentVerificationAdvancedUrl: "https://localhost:9091/karza/employmentVerificationAdvanced"
    KycOcrUrl: "https://localhost:9091/karza/v1/extract_passport"
    PassportVerificationURL: "https://localhost:9091/karza/v1/verify_passport"

  Roanuz:
    GenerateCricketAccessTokenUrl: "https://localhost:8080/test/rounaz/cricket"
    CricketURL: "https://localhost:8080/test/rounaz/cricket"
    GenerateFootballAccessTokenUrl: "https://localhost:8080/test/rounaz/football/auth/"
    FootballUrl: "https://localhost:8080/test/rounaz/football"
    FootballAppId: "com.epififootball.www"
    FootballDeviceId: "developer"

  IPStack:
    GetLocationDetailFromIpUrl: "https://localhost:8080/test/ipstack"

  # AA service vendor URLs
  AA:
    BaseURL: "http://localhost:8079"
    PostConsentURL: "/Consent"
    ConsentStatusURL: "/Consent/handle"
    ConsentArtefactURL: "/Consent"
    RequestDataURL: "/FI/request"
    FetchDataURL: "/FI/fetch"
    GetAccountLinkStatusURL: "/Account/link/status"
    GenerateAccessTokenURL: "https://api.uat.sahamati.org.in/iam/v1/entity/token/generate"
    FetchCrEntityDetailURL: "https://uatcr.sahamati.org.in/entityInfo/AA"
    ConsentUpdateURL: "/consent/update"
    AccountDeLinkURL: "/account/delink"
    UseSahamatiCrAndToken: false
    OneMoneyCrId: "onemoney-aa"
    FinvuCrId: "<EMAIL>"
    GetAccountLinkStatusBulkURL: "/Account/link/Status"
    GetHeartbeatStatusURL: "/Heartbeat"
    EpifiAaKid: "654024c8-29c8-11e8-8868-0289437bf331"
    AAClientApiKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************.A-VX3lgu6T_r2FWIp2bsDAQK9vll6p4uQC_D5LwXmdo"
    SahamatiClientId: "EPIFIUAT"
    FinvuFipMetricsURL: "/fip/latest-metrics-all"
    Ignosis:
      Url: "http://localhost:8079"
  # Bouncy castle library URLs
  BouncyCastle:
    GenerateKeyPairURL: "http://localhost:8080/ecc/v1/generateKey"
    GetSharedSecretURL: "http://localhost:8080/ecc/v1/getSharedKey"
    DecryptDataURL: "http://localhost:8080/ecc/v1/decrypt"
    CkycEncryptAndSignURL: "http://localhost:8080/ckyc/v1/encryptAndSign"
    CkycVerifyAndDecryptURL: "http://localhost:8080/ckyc/v1/verifyAndDecrypt"

  # Fennel Vendor URLs
  FennelFeatureStore:
    ExtractFeatureSetsURL: "https://main.epifi-staging.aws.fennel.ai/api/v1/extract_features"
    ExtractFeatureSetsURLV2: "https://epifi-staging.aws.fennel.ai/api/v1/query"
    ExtractFeatureSetsURLV3: "https://babel.data-dev.pointz.in/api/v1/query"
    LogDatasetsURL: "https://main.epifi-staging.aws.fennel.ai/api/v1/log"
    LogDatasetsURLV2: "https://epifi-staging.aws.fennel.ai/api/v1/log"
    LogDatasetsURLV3: "https://babel.data-dev.pointz.in/api/v1/log"
    PdScoreURL: "https://loan-default.data-dev.pointz.in/v2/loan_default"
    Simulator:
      Enable: true
      ExtractFeatureSetsURL: "https://localhost:8080/fennel/api/v1/extract_features"
      LogDatasetsURL: "https://localhost:8080/fennel/api/v1/log"
      AllowedWorkflowsForSimulation: [ "acquisition" ]

  Ckyc:
    SearchURL: "https://localhost:8080/Search/ckycverificationservice/verify"
    ApiVersion: "1.2"
    DownloadURL: "https://localhost:8080/Search/ckycverificationservice/download"
    EnableCryptor: false

  CvlKra:
    SoapHost: "https://krapancheck.cvlindia.com"
    PanEnquiryURL: "https://localhost:8080/CVLPanInquiry.svc"
    InsertUpdateKycURL: "https://localhost:8080/CVLPanInquiry.svc"
    # CvlKra sftp config
    Host: "sftp.deploy.pointz.in"
    Port: 22

  NsdlKra:
    PanInquiryURL: "https://localhost:8080/TIN/PanInquiryBackEnd"
    GenerateSignatureURL: "https://nsdl-forwardsecrecy.deploy.pointz.in/nsdl/v1/generateSignature"
    DisableSigning: false
    PerformPanInquiryV4URL: "https://localhost:8080/TIN/PanInquiryBackEnd"

  Manch:
    TransactionsURL: "https://localhost:8080/app/api/transactions"
    DocumentsURL: "https://localhost:8080/app/api/documents"
    OrgId: "TST00180"
    ReturnUrl: "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu"

  Digio:
    TransactionsURL: "https://ext.digio.in:444/v2/client/document"
    ExpiryInDays: 10
    ClientId: "AIJGENW6VPBUNQY37EZXO37HPG5SC2GW"
    SecretKey: "TQDHSJ74HZSHVB9TWIBBLPWL23YRSQVL"

  WealthKarza:
    OcrURL: "https://testapi.karza.in/v3/kycocr"
    MaskAadhaar: true
    HideAadhaar: true
    Confidence: true
    CheckBlur: true
    CheckBlackAndWhite: true
    CheckCutCard: true
    CheckBrightness: true

  Experian:
    CheckCreditReportPresenceURL: "https://localhost:8080/ECV-P2/content/enhancedMatch.action"
    FetchCreditReportURL: "https://localhost:8080/ECV-P2/content/enhancedMatch.action"
    FetchCreditReportForExistingUserURL: "https://localhost:8080/ECV-P2/content/onDemandRefresh.action"
    FetchExtendSubscriptionURL: "https://localhost:8080/ECV-P2/content/consumerConsentReRegistration.action"

  Cibil:
    PingUrl: "https://localhost:9091/cibil/consumer/dtc/ping"
    FulfillOfferUrl: "https://localhost:9091/cibil/consumer/dtc/fulfilloffer"
    GetAuthQuestionsUrl: "https://localhost:9091/cibil/consumer/dtc/GetAuthenticationQuestions"
    VerifyAuthAnswersUrl: "https://localhost:9091/cibil/consumer/dtc/VerifyAuthenticationAnswers"
    GetCustomerAssetsUrl: "https://localhost:9091/cibil/consumer/dtc/GetCustomerAssets"
    GetProductTokenUrl: "https://localhost:9091/cibil/consumer/dtc/GetProductWebToken"
    ProductUrlPrefix: "https://atlasls-in-live.sd.demo.truelink.com/CreditView"

  Shipway:
    BulkUploadShipmentDataUrl: "https://localhost:8080/shipway/BulkPushOrderData"
    GetShipmentDetailsUrl: "https://localhost:9091/shipway/GetOrderShipmentDetails"
    AddOrUpdateWebhookUrl: "https://localhost:9091/shipway/AddOrUpdateWebhook"
    UploadShipmentDataUrl: "https://localhost:9091/shipway/PushOrderData"

  Seon:
    GetUserSocialMediaInformationUrl: "https://localhost:9091/GetUserInformationByEmailId"

  InhouseOCR:
    MaskDocURL: "https://ocular.data-dev.pointz.in/v1/mask_doc"
    ExtractFieldsURL: "https://ocular.data-dev.pointz.in/v1/extract_fields"
    DetectDocumentURL: "https://ocular.data-dev.pointz.in/v1/detect_doc"
    ExtractFieldsURLV2: "https://ocular.data-dev.pointz.in/v1/extract_fields"

  InhousePopularFAQUrl: "http://popular-faqs.data-dev.pointz.in"

  Digilocker:
    GetAuthorizationCodeUrl: "https://localhost:8080/public/oauth2/1/authorize"
    GetAccessTokenUrl: "https://localhost:8080/public/oauth2/1/token"
    ClientId: "EBB0DE86"
    RedirectUri: "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu"
    GetListOfIssuedDocumentsUrl: "https://localhost:8080/public/oauth2/2/files/issued"
    GetRefreshTokenUrl: "https://localhost:8080/public/oauth2/1/token"
    GetFileFromUriUrl: "https://localhost:8080/public/oauth2/1/file/"
    GetAadhaarInXmlUrl: "https://localhost:8080/public/oauth2/3/xml/eaadhaar"
    TokenCacheTtl: 10m

  Liquiloans:
    Host: "https://localhost:9091"
    SupplyIntegrationHost: "https://localhost:9091"
    SftpHost: "sftp.liquiloans.com"
    SftpPort: 22
    S3BucketP2PInvestmentLedger: "epifi-p2p-investment-ledger"

  # TODO(@prasoon): update URL once available
  Lending:
    PreApprovedLoan:
      Federal:
        UrlLentra: "https://localhost:8080/lending/federal/v1/enquiry"
        Url: "https://localhost:9091"
        HttpURL: "http://localhost:8079"
        FetchDetailsUrl: "https://localhost:9091/account_utility/v1.0.0/fetchLoanDetails"
        RespUrl: ""
        SftpHost: "sftp.deploy.pointz.in"
        SftpPort: 22
        PlAcntCrnNtbHttpURL: "http://localhost:8079/loan/account/v2.0.0/creation"
        PlAcntCrnEnqNtbHttpURL: "http://localhost:8079/loan/v1.0.0/enquiry"
      Liquiloans:
        Url: "https://localhost:9091"
        # This URL points to HTTP port of simulator as few Liquiloans API needs request body  in HTTP GET request, those are
        # mocked using Router and not by gRPC method.
        HttpUrl: "http://localhost:8079"
      Idfc:
        Url: "https://localhost:9091"
        # URL to fetch the access token for IDFC APIs
        GetAccessTokenUrl: "https://app.uat-opt.idfcfirstbank.com/platform/oauth/oauth2/token"
        MandatePageUrl: "https://uat.fmreporting.idfcfirstbank.com/IDFCEMandate/EMandateB2BPaynimmo.aspx"
        EnableEncryption: false
        Source: "FIMONEY"
        SimulatorHttpURL: "http://localhost:8079"
      Abfl:
        Url: "https://localhost:9091"
        BreUrl: "https://localhost:9091/v2/decisionEngineConfig"
        TxnDetailsUrl: "https://localhost:9091"
        PwaJourneyUrl: "https://localhost:9091/abfl/pwa"
      Moneyview:
        BaseUrl: "https://localhost:9091/moneyview"
        # This URL points to HTTP port of simulator as few MV API needs request body  in HTTP GET request, those are
        # mocked using Router and not by gRPC method.
        HttpUrl: "http://localhost:8079/moneyview"
      Setu:
        BaseUrl: "https://localhost:9091"
      Digitap:
        UanAdvancedUrl: "https://localhost:9091/v3/uan_advanced/sync"
      Finflux:
        BaseUrl: "https://epifi.lms-uat.pointz.in"
        Auth:
          Username: "post"
          Password: "Test@123"
          IsPasswordEncrypted: false
          TokenValidityDuration: "14m"
        Charges:
          ProcessingFeeChargeId: 1
      Lenden:
        BaseUrl: "https://localhost:9091/lenden/common/v1/EPF"
        ProductId: "EcoX-Loan-102"
        EnableCryptor: false
    CreditCard:
      M2P:
        RegisterCustomerHost: "https://localhost:9091/"
        M2PHost: "https://localhost:9091/"
        CreditCardRepaymentHost: "https://localhost:9091/"
        M2PFallbackHost: "https://localhost:9091/"
        M2PLMSHost: "https://localhost:9091/"
        M2PPartnerSdkUrl: "https://localhost:9091/gateway"
        M2PSetPinUrl: "https://localhost:9091/"
        EnableEncryption: false
        M2PFederalHost: "https://localhost:9091/"
      Federal:
        Url: "https://uatgateway.federalbank.co.in/fedbnk/uat/CreditCard/v1.0.0/"
        UpdateCreditCardLimitDetailsUnsecuredChannel: "M2P-FDEPIFICR"
        UpdateCreditCardLimitDetailsMassUnsecuredChannel: "M2P-FDEPIFIMASSCR"
    CreditLine:
      Federal:
        Url: "https://localhost:9091/limitFetch"
      M2P:
        Url: "https://localhost:9091"
    Collateral:
      LendingMFCentralConfig:
        HttpUrl:
          AuthToken: "https://localhost:8080/mfcentral/GenerateToken"
          EncryptAndSign: "https://nsdl-forwardsecrecy.deploy.pointz.in/mfcentral/v1/encryptAndSign"
          DecryptAndVerify: "https://nsdl-forwardsecrecy.deploy.pointz.in/mfcentral/v1/verifyAndDecrypt"
          SubmitCasSummary: "https://localhost:9091/mfcentral/submitcassummary"
          InvestorConsent: "https://localhost:9091/mfcentral/investorconsent"
          GetCasDocument: "https://localhost:9091/mfcentral/getcasdocument"
          ValidateLien: "https://localhost:9091/mfcentral/validatelien"
          SubmitLien: "https://localhost:9091/mfcentral/submitlien"
          InvokeRevokeLien: "https://localhost:9091/mfcentral/validateLienInvokeRevoke"
          CheckStatus: "https://localhost:9091/mfcentral/lienCheckStatus"
          GetTransactionStatus: "https://localhost:9091/mfcentral/getTransactionStatus"
    SecuredLoans:
      Url: "http://localhost:8079/fiftyfin"

  Alpaca:
    BrokerApiHost: "https://localhost:8080/test/alpaca"
    BrokerApiVersion: "v1"
    MarketApiHost: "https://data.sandbox.alpaca.markets"
    MarketApiVersion: "v2"
    OrderEventsApiPath: "/test/alpaca/v1/trade/events"
    AccountEventsApiPath: "/test/alpaca/v1/account/events"
    FundTransferEventsPath: "/test/alpaca/v1/events/transfers/status"
    JournalEventsPath: "/test/alpaca/v1/events/journals/status"
    BrokerEventsApiHost: "localhost:8080"
    BrokerEventsApiScheme: "https"
    ShouldUseSimulatedEnvForEvents: true
    MarketDataBetaAPIPrefix: "v1beta1"

  MorningStar:
    TokenCacheTtl: 12h
    EquityAPIURL: "https://equityapi.morningstar.com"
    ApiUrl: "https://api.morningstar.com"
    ApiAccessTokenExpiryInDays: 90
    MorningStarObsoleteFundAPIUrl: "https://intools.morningstar.com/identifier/api/data"

  FederalInternationalFundTransfer:
    URL: "https://localhost:8080/test/federal"
    CheckLRSEligibilityPrecision: 9

  Esign:
    Leegality:
      Url: "https://localhost:9091/"

  ProfileValidation:
    Federal:
      Url: "https://localhost:9091"

  #GPlace Vendor URLs
  GPlace:
    FindPlaceReqURL: "https://maps.googleapis.com/maps/api/place/findplacefromtext/json"
    GetPlaceDetailsReqURL: "https://maps.googleapis.com/maps/api/place/details/json"

  GoogleReverseGeocodingUrl: "https://localhost:9091/get-address-coordinate"
  GoogleGeocodingUrl: "https://localhost:9091/get-coordinate-for-address"
  InhouseLocationServiceUrl: "https://geo.data-dev.pointz.in"
  MaxmindIp2CityUrlPrefix: "https://localhost:9091/get-address-ip/"

  BureauPhoneNumberDetailsUrl: "https://localhost:9091/v1/phone-network"

  #DronaPay
  DronapayHostURL: "https://riskuat.dronapay.pointz.in/springapi"

  InhouseRiskServiceURL: "https://localhost:9091/risk"
  InhouseRiskServiceURLV1: "https://localhost:9091/risk"

  "InhouseMerchantResolutionServiceUrl": "https://merchant.data-dev.pointz.in/resolution"

  Aml:
    Tss:
      Epifi:
        ScreeningUrl: "https://localhost:8080/crmapi/a44twcustomerscreeningalertcreationcontroller/GenerateScreeningAlert"
        SystemName: "Epifi_tech"
        ParentCompany: "Epifi"
      StockGuardian:
        ScreeningUrl: "https://localhost:8080/crmapi/a44twcustomerscreeningalertcreationcontroller/GenerateScreeningAlert"
        SystemName: "LMS"
        ParentCompany: "SGIPL"

  LocationModel:
    InHouseUrl: "http://onboarding-risk-detection.data-dev.pointz.in/v1/geo_score"
    RiskSeverityValToEnumMapping:
      "LOW": "RISK_SEVERITY_LOW"
      "MEDIUM": "RISK_SEVERITY_MEDIUM"
      "HIGH": "RISK_SEVERITY_HIGH"
      "CRITICAL": "RISK_SEVERITY_CRITICAL"

  IncomeEstimatorConf:
    InhouseIncomeEstimatorURL: "https://localhost:9091/get-income-estimate"

  EpanConfig:
    FederalConfig:
      SftpConn:
        Host: "sftp.deploy.pointz.in"
        Port: 22

  EnachConfig:
    FederalConfig:
      SftpConn:
        Host: "sftp.deploy.pointz.in"
        Port: 22
      FederalSFTPUploadPath: "/data/"
      FederalSFTPDownloadPath: "/data/"
      FileTypeToSFTPPathMap:
        FILE_TYPE_PRESENTATION_FILE: "/data/"
        FILE_TYPE_PRESENTATION_ACK_FILE: "/data/"
        FILE_TYPE_PRESENTATION_RESPONSE_FILE: "/data/"

  Dreamfolks:
    BaseUrl: "https://localhost:9091/dreamfolks"
    ProgramId: "1000001632"
    ServiceId: "11"

  MFCentralConfig:
    GenerateTokenURL: "https://localhost:8080/mfcentral/GenerateToken"
    EncryptAndSignURL: "http://localhost:8080/mfcentral/v1/encryptAndSign"
    VerifyAndDecryptURL: "http://localhost:8080/mfcentral/v1/verifyAndDecrypt"

  Visa:
    FindNearbyAtmTotalsUrl: "https://sandbox.api.visa.com/globalatmlocator/v3/localatms/totalsinquiry"
    FindNearbyAtmsUrl: "https://sandbox.api.visa.com/globalatmlocator/v3/localatms/atmsinquiry"
    FindGeocodesUrl: "https://sandbox.api.visa.com/globalatmlocator/v3/localatms/geocodesinquiry"
    GetEnhancedForeignExchangeRatesUrl: "https://sandbox.api.visa.com/fx/rates"
    GetEnhancedMarkupForeignExchangeRatesUrl: "https://sandbox.api.visa.com/fx/rates/markup"

  SetU:
    PartnerId: "1510324753200579781"
    BaseURL: "https://sandbox-coudc.setu.co"
    MobileRechargeProductInstanceId: "8eea3ab4-09c4-4d97-824c-7f93aa902c08"
    MobileRechargeLoginBaseUrl: "https://accountservice.setu.co" # todo: update to localhost url
    MobileRechargeBaseUrl: "https://prepaid-uat.setu.co" # todo: update to localhost url

  Saven:
    CreditCardBaseUrl: "https://localhost:8080/cc-saven"
    JwtExpiry: "3s"

  FederalEscalation:
    BaseURL: "https://uatgateway.federalbank.co.in"
    CreateEscalationURL: "fedbnk/uat/CRM/v1.0.0/cxiosrcreation"
    BulkFetchURL: "fedbnk/uat/CRM/v1.0.0/cxsrbulkretrieval"
    S3BucketName: "epifi-cx-ticket-attachments"

  Nugget:
    BaseURL: "https://api.nugget.com"
    AccessTokenEndpoint: "/unified-support/auth/users/getAccessToken"

Server:
  Ports:
    GrpcPort: 8081
    GrpcSecurePort: 9522
    HttpPort: 9885

Aws:
  Region: "ap-south-1"

# TODO: enable this if localstack needs to be setup
#Secrets:
#  Ids:
#    EpifiPgpPrivateKeyUat: "epifi-priv-key"
#    FederalPgpPublicKeyUat: "fed-pub-key-v2"
#    EpifiPgpPassphraseUat: "test-passphrase"

Secrets:
  Ids:
    #M2P
    M2PSecrets: '{"partnerId" : "FDEPIFICR", "partnerToken" : "Basic Q1JFRElUREVNTw==", "Authorization" : "Basic YWRtaW46YWRtaW4=", "TENANT" : "FDEPIFICR", "Source": "EPIFI", "AgencyId": "EPIFI", "MerchantId": "EPIFI", "AggregatorId": "f9f9df6deb5c69768d47a39be9d84114146e1f31"}'
    M2PSecuredCardSecrets: '{"partnerId" : "FDEPIFISECCR", "partnerToken" : "Basic RkRFUElGSVNFQ0NS", "Authorization" : "Basic YWRtaW46YWRtaW4=", "TENANT" : "FDEPIFISECCR", "Source": "EPIFI", "AgencyId": "EPIFI", "MerchantId": "EPIFI", "AggregatorId": "f9f9df6deb5c69768d47a39be9d84114146e1f31"}'
    M2PMassUnsecuredCardSecrets: '{"partnerId" : "FDEPIFIMASSCR", "partnerToken" : "Basic Q1JFRElUREVNTw==", "Authorization" : "Basic YWRtaW46YWRtaW4=", "TENANT" : "FDEPIFIMASSCR", "Source": "EPIFI", "AgencyId": "EPIFI", "MerchantId": "EPIFI", "AggregatorId": "f9f9df6deb5c69768d47a39be9d84114146e1f31"}'
    EpifiM2pRsaPrivateKey: |
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    EpifiM2pRsaPublicKey: |
      -----BEGIN RSA PUBLIC KEY-----
      MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhitxUhyoSd7GDHLTnlFi
      lzgrMx9294X5vQESdqI+IS45yebNzhkureNsw6CajFaVEkhterIGFgkNIZOmcxW7
      D7alhFEIVdxoFjGkyvC/5KEvi/8tM6N3jHt9Ss3+b54WyLu63vHQa+KXa2DMZEue
      N1AsLqXAuHgQfNWdeFOPCIAzaopLkohaOISae2xO/Yr/uDo5EA7dO+AWfII5lQn0
      XYr3HqnEQvyjYVjzaBi/K4cngpFwnr6JngM+RmPAa8E0XByZNrK4WkfOAy2741JI
      fkjreN0LwDnAB8VBXN1BjMSkx60xXbBYgXh69reMi0VhKqMrSOYQ8u691JthaxHl
      FQIDAQAB
      -----END RSA PUBLIC KEY-----

    # In-house BRE
    InHouseBreBearer: "Bearer _Wj_tbmPiUD27JE_Sp8_zIaTE517HCuAnRX6CzfjvXZ-90lZdS9UghdqrXqGNopjK0KHfHdXMBL0yRbrt8gLWg=="

    #Federal
    EpifiFederalPgpPassphrase: "qwerty1234"
    EpifiFederalPgpPrivateKey: |
      -----BEGIN PGP PRIVATE KEY BLOCK-----

      lQdGBF5qBvgBEADGijefHuDTXqiReXdZcxbknDmueW5XGWzR0EHec9kRYkxcQy+H
      gY/q7RJ+5VNNM8KfWtdM+K2zoTdkv6uIp6eYEcC6YcRV1MeX6KbxGGpMzRmb5f0c
      d+gePS1WOeOWjjS+3AXmH0DO+v8bXDxAullL8L0jDYK1sRxWlu9tdwglyJx7IXQi
      VibCaSfrNDrTI7HmrLw8PZ/rfHq5prhEQ9cCeI+kHKz2Amiuts8PGl5M1MEx2oC8
      nSMtb3YXg32FiYkyOi/vXZ5jmltcR9PyhJnMkNFrZRcBxU5dWhB/CpD/IebaKbir
      KuH2Ig/0d8PvvLlyILMyto5C2hfho2QWbFisMuSF23x798CYrFqLBDjFersDDfIW
      4yKsMD+2J+9T/7nBUYdIUmCEmg+khtIVanpJnQQP+iqb8NB7oTevBXKaIST95NhK
      b7B6J9/IDy0xFyPFPtXQyHSoufAjJPfNIgFb8cMTIuQEfN402Fp6eH5NwlvECFWy
      y8S00VFxr8mHXZ1zmvYK6Kp3OLR+q0tpUKmWu6KR4UdKY2Lce7ciuFVGlw1ufBgE
      nxlJNguLDCD23aod78QTWI6ubnCRKTdDLoR7H3CRkIQYzQme3xKJRHdEBfEOioTd
      6+rc1VHp1iLlo7uAlk01wGVdwRmO3deSi1y367TWXy1tyFscOvlGuF6r9QARAQAB
      /gcDAo8iYHB6tWca6/vYjx8IhXmfges2Lti8I805xB7+Ooilxat4ZtBUQHjMGm36
      CPTHwxpca4HfrGIYkCPQ7RmIQTYJBir1NWciD/Dvk0F3TeBicXz9Qzg8nsIsmNug
      SUgHFmbUshT9hMYXAp6no4a+0vJhOH+Wg5CbXV++fl6VhxOoesftfoenJ1JJ2G4Y
      HWElJWuaY4ozf5APC9JrC4KKyjJpZFWg7GoHjyPi6DYNOP1UhXghB0r2biokoJJt
      HErCBXpH+yA2/8L/j0tfDT31IiP/Ge0m2mFIKt6SpBzkbyeY5Pr/ZfMIt4rPwsf8
      2O2B1aL5JF358cKsv4DGGjIv1G3joqyYbrub8j8zL0shYmiBfPeLXdW39NQxplxy
      BDiSCSMRa3IV3J7YztR87GEeKbcqS6gY0sVQB9HrkNTxqh9DG0kNwovZRhGAbK1+
      +1ctahA+XEWghkXF2U2VBJjT42OgzYtgAkyD1+vPeThEKfzOxMARGcavuURX+kiI
      e19MLPuFdJhcZ8pOUv3Y3Otpa5BgsjBUWObGVwUFe1XJ9Zhe+JOIg2QUj35kVReR
      NiyvYrh6QHxnlG15HAgpica3a1gEnxf6M5nXP2XOsmGX+SKzlnBtvkxCy9+3zopJ
      S3NReIK52YWX/NPTK4rKahbv+17xFyRk5msmxsxVO1X3nZbGRgy+49Uv3D8fc7JD
      u3hnpj0DgIaQXKGv/vlEqFtHUsonABXG9ylPvsTJ4I7gbUbmpLV1t8LmF5x4xFfw
      n0O++iBRUqzkLi2+6e5ZPTna24F8hWkAq6k++nVKFCSz2NoDcTwrjLRM/fkkqJe3
      g2X1tug7x0B+/MwbY0IJwhEaHbZpeGVuXr+XIEs2uleWZ3kiJJYHSyRHHpcc/IxQ
      6N4gflA/tZKwFuBBBnIRXt97m2WseTGcxF+lAuVbCS7mB2PFscYfkQVEaq7enHrN
      Cx62RlIf8ZnmeeIVmU9VylFOy4BlFcg4lwjuY2cgoX+q/zNwkhaoGRXlp039CjFW
      0R6OMV2iY3tUDpQyTki9lc4seSIKyN+LOEd74R6mHZEKNpuDJFmGDla5GDPW9ZP3
      s57zvRXDOjgrxsQNcZUcF/cmaIc+pdVzgs32P/ojaCW5h7ncr66hxms4VBvi1JVD
      N3Q74BAcHbyJ/FYVW1lgHSzDVXJL8EqctV6/HGg9e+IRRgYBIf0q4fUWURrIes6d
      /TQ2S1O/u536ncDTzhuh7MoC5UUv97Cmjovk5iLbdsYXUapJKMZX+duDw2LhOUzy
      kukrLShRR8BQ4aQndStLBGzorVjMOrPMFZQivwmwFEB3n4rXBeG4YtMAUvOZeZvM
      tMvD3gKh+0hkRgYRuQi0ZKBb3OSaw7O/Dvb+68wUMlPfoK+i1x5TIx4wY5ws3A37
      XM3i10aFbWouk84RZ5ntF4aDcFZ+r/T1BkzHc7e4HrcuYz3nAudxx+zqkjCM/MZv
      4dmTnw5ODcN97tXhnxaLcRVT9M+2inMOtuSVYa48y7AbyR/cSk9N46CVpTwwioiI
      5fpvHCTGy52m5HxXVwwNXqliWESGc5PefoN5IoDUzX5J6RVFcDw7n0bTNBbvxFHT
      mbsu2w+biEmQXqovAIW0YZK0Xnnu4J0DwKCb+8loxnNOTzANyfkUa2eskjZ7H2Q6
      J4aq7sqH/lAQU439uHeuCPmzwK2jlDmrUhO1SgJyHglP/tnSPkk+rCEivqIawqgr
      ATFhWgNUXP9zPBHejhiNFOO65AXVTivAmlFkZm6+di6XBD9lCF5dtP+0I0F1dG9n
      ZW5lcmF0ZWQgS2V5IDxhbmFuZEBlcGlmaS5jb20+iQJOBBMBCAA4FiEEhUtW2MRo
      EDU7awr4z4B1fb7ViawFAl5qBvgCGy8FCwkIBwIGFQoJCAsCBBYCAwECHgECF4AA
      CgkQz4B1fb7Viaw0RBAAtLprII+/f3u53627dzrbyIE/z19ih34v/WVsDiMpyUYK
      qgXvL9a0P8dP+EO/rERxH9jbt5KtJ1zE4bGoT7zEQr/a36gwAu5DwaFInaPTrqPZ
      5FL/xytReUcjuPb5W6qWF2mO6iSd3ZwQbo/ZNjqgGIem+ouPfPtyyz7mdsM42FRF
      ANZ6JWSpqpuEl1l1GNFfVEmJMM0SgfsSm2XClGgTHQ2hyRyH0zcBoojjOigqkEHd
      ytM3MVovn2z2+HjqSU1GMCT21dRKZnV/FkN8GHW4ITOUd2X1Z4eBt7y8/n/WIFbx
      uwbSRtYKULe5LjgMA+Cv6bUBRqruUOdW52iFpxU0Eyr6+T2kEJeXtyWkGA297B5W
      LtCWRPvQAdoj8Qxky2ch5zyrKfSPwpPF3o/HcPvodk5hYXkhE/AHNqqu0ORmtevj
      20pbH84yUfjJEKJbLKu8MEuKysxSgNN00gHmjVEJa98Cl+qMAdzNBpDiJTlriM6y
      uwychPPrgZoe98YKy2/xRsZ2s/BPRazqllwZ8i+x/Ks8iYC+lSrmGlAtZulwOS8g
      +Flz7yPuYZB9IXNRyd7dZNRlxrGlcD1VHUkgyoZvL3EgpKtUofaNJ1WYtXigEqe8
      aLci0kOkZjjhA9FhikU3VUf+jlLKR5sGx2xWPZAnASrZU8/pIGI6sUsxWmSBHvg=
      =c5Qs
      -----END PGP PRIVATE KEY BLOCK-----

    FederalPgpPublicKey: |
      -----BEGIN PGP PUBLIC KEY BLOCK-----

      mQINBF5or0QBEADLY8Gg8s3RoRjxtX3fyHvgpAl/jdE1v3QETt6mBBWK2jrxgDYv
      uGrE2dqHr6/XETpFzgsmGpXILijryvqu+6QL5/RQTXfSUyK+l14unUEWwAtIEPt9
      kbxxXs8QvTw4+PnskVCOOcmwPnamU7jHYmx3bR82KBW0lroSroAq+U0bQGWuDeei
      Ry5uhN6xzQHjEXJEEwLMn0UBJI/vf4B6jJ3Z3Se9yCcuUqEPCAId3Vz2nNFwEAQA
      kC/WeuaVnxqL/evRlNeEzPVKmbwzLhc9e2p4acPRdMSeu9lvmHuS7VTR0iAomjL2
      KCXCJWQ+LhkusKDTjh5U+/cZtRF3JWRzdjjIGhxjz87kzjQXup+0KETF06Uzm4BB
      dVodXV2YJKn3WsC9xolAOjZ3sFPHReOxzS2MVtzAp7hFUBagZ/PmokbG/lEkPqWT
      1YBGCrIZ1CRFWpQCZeA4gbVny/RAeMuaDJp+Sr6QVi/NPAGtMXregGxxuHYPRZmb
      6xNPxsTW0Ak31YzFYCj/1hMMtGLp81cc3FVGGZIZWHajm0LBBOHE6sjCaKdyl6Bo
      BHxhaAMRHg+H123JOCz1WdkyhE+5R+0sQiEZLNK7/hciVaLESrfblNCc5RhgQ3mm
      qLYydkZMqhi1zwDIKg7YueJoQM+gd6bTzNfqLGW2UPOQojJZ9SosxcHthwARAQAB
      tCNBdXRvZ2VuZXJhdGVkIEtleSA8YW5hbmRAZXBpZmkuY29tPokCTgQTAQgAOBYh
      BL2/CLI39EtWfkngfSioaI1AylhdBQJeaK9EAhsvBQsJCAcCBhUKCQgLAgQWAgMB
      Ah4BAheAAAoJECioaI1Aylhd1vYQAKRmtMDPSgmiAN5+eVDA75PPdmjemzkHyM2S
      lEOljzUeCZKOIY3JZCWwQEFdoru9HHmMqFPrHUSJMGqN19xc/JYeEMxv/hkZoeEv
      DPG70cvnJotDYqm++EFRH8u0VbvEWDJKMCBAKQ2AYcWsrq5EsA3FP/a+FvxriBgj
      tfA5pMSrcxCsLfqkfgrA9k2JQekJ5hTIi4p9Dcm3eaKr6fqmNW3FRTQ/Pg8M04vA
      WuwRwOqccUKauNAT+UrqOdJZWOJswlC5OumjHRXILoyBps8ALLuVMEkFyLEBDPLR
      D79bgxjIzHmOxjIJPv8NypFqr7Z/KNIknsGQb0+d0Nb9RgOIWDpqeiKHLI3ejsqJ
      S4+q4G0oSta5CKfg5lMN80pmFqeuSQ9ZU2PfQL9JadTerzWs+6ob+HLAAnXelcOJ
      aRO2y7jUa86cgkxxy64wiiZUup0d9rDN8o/ek0EUnzROXteiSvX2Rx8a8yd9k/FI
      j3iXEY5M0Mqo6/HapgNttRrCK9ku1mrJ5YnnkSS+DlBVYtXM1kL2pKSHVtdcxVxi
      yKNBHzAyG0cjHMatW3RBGSReYZbe5+g+QaxdqzVLK6DEFqp1n2kAM+h9aCdBrqwC
      TtIUbUa7OIiSXWCau5c5NgxESfwVDS6tBNA8BN/MxjcvpNsJ0xG/YmfCAy3eYFXF
      xiTYY1ba
      =bnCx
      -----END PGP PUBLIC KEY BLOCK-----

    EpifiFederalUPIPrivateKey: |
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    EpifiFederalUPIFallbackPrivateKey: |
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    EpifiFederalCardDataPrivateKeyFallBack: |
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    EpifiFederalCardDataPrivateKey: |
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    SenderCode: "EpiFi_Test_Cd"
    SenderCodeLSO: "EpiFi_Test_Cdd"
    ServiceAccessId: "EpiFi_Fed_Test"
    ServiceAccessCode: "EFed@123"
    ClientId: "3086b14c-6922-42a4-972b-ba4560de24c8"
    ClientSecretKey: "rR2jG6aF0yH8fJ3wB2eW6rS0gV4vD6iR0uC7iW4qX6qW4uI2dX"

    #Closing Balance params
    ClosingBalanceCredentials: "{\"ClosingBalanceSenderId\": \"Epifi\",\"ClosingBalanceSenderCode\":\"Epifi\",\"ClosingBalanceServiceAccessCode\":\"Epifi\"}"

    GetBalanceCredentialsV1: "{\"GetBalanceSenderIdV1\": \"Epifi\",\"GetBalanceSenderCodeV1\":\"Epifi\",\"GetBalanceSenderAccessCodeV1\":\"Epifi\"}"

    GetRemitterDetailsCredentials: "{\"GetRemitterDetailsSenderId\": \"EPIFI\",\"GetRemitterDetailsSenderCode\":\"EPIFI_CD\",\"GetRemitterDetailsSenderAccessCode\":\"epifi@123\"}"
    GetRemitterDetailsV1Credentials: "{\"SenderCode\":\"EPIFI\"}"
    GetCsisStatusCredentials: "{\"SenderId\": \"EPIFI\",\"SenderCode\":\"EPIFI_CD\",\"ServiceAccessCode\":\"epifi@123\"}"

    #FCM
    FCMServiceAccountCredJson: |
      ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    #Sendgrid
    SendGridAPIKey: "*********************************************************************"
    #Freshdesk
    FreshdeskApiKey: "vvUqQKHsOKC25MWKuh"
    EpifiTechRiskFreshdeskApiKey: "Uld4v4dsEwNIr45BY6aU"
    #Ozonetel
    OzonetelApiKey: "KK6fa7a56fc377dee9b2b140ae27a4c1f3"
    #Freshchat
    FreshchatApiKey: "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJsY1pWMF82VTNza2RKU19GOV9qdGNMNXotNlZPc05EaGNuZ1lLNHFHaVJvIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YVHAXiuf_8Avo_GZDqioVewtNXzRVst00n0fKLaQtjehvzRGuRQOyV-VepNzTTN0obov4c58ve6tVz2O0rAsSSC3_ml5kB90_DtCOPj1kdbSaQIc5zwQOMh792plEe_ReSkkSGyaGwqFXfi3td28w9EtMDXpXewLb_PkJ0bOzFzlmGWhMf9M5l1XnlYTTbdnBdiGF6yebgKIYecXX4C1d2Ik8rWe57b48zcjfWbQJk9OsL_i583al7Waszg7i770MhUzCj9ALfrdDJ3q5xgnA7UhVqMmgaXhjf1V4HkP2bnN9DLIGMk4YZSCnPYe1LpCezCng75rNwICbKPuwKdgTw"
    #TLS certs
    SimulatorCert: |
      -----BEGIN CERTIFICATE-----
      MIID4jCCAsqgAwIBAgIJAPHySk9LXHBYMA0GCSqGSIb3DQEBCwUAMIGJMQswCQYD
      VQQGEwJJTjESMBAGA1UECAwJS2FybmF0YWthMRIwEAYDVQQHDAlCYW5nYWxvcmUx
      KTAnBgNVBAoMIGVwaUZpIFRlY2hub2xvZ3kgUHJpdmF0ZSBMaW1pdGVkMRMwEQYD
      VQQLDApUZWNobm9sb2d5MRIwEAYDVQQDDAlsb2NhbGhvc3QwHhcNMjIwMTI1MTEz
      OTQ0WhcNMzExMDI1MTEzOTQ0WjCBiTELMAkGA1UEBhMCSU4xEjAQBgNVBAgMCUth
      cm5hdGFrYTESMBAGA1UEBwwJQmFuZ2Fsb3JlMSkwJwYDVQQKDCBlcGlGaSBUZWNo
      bm9sb2d5IFByaXZhdGUgTGltaXRlZDETMBEGA1UECwwKVGVjaG5vbG9neTESMBAG
      A1UEAwwJbG9jYWxob3N0MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA
      qEGrrXTlxnNe77CjSHKN1zLdURnicArDB4Jm0pTygLw7jSpLzl1ucJoJL+mdE3GB
      Pvm/mw8wwORWQ5pjCkIz+Wyyv9PbIMHkY0wK1TUFZ1pCzlWPPKoYzHrAp0cue5ab
      J1iSrsQivZGFw8HFYxCKFQ3aQYQF4MKwZLnRQK5PdWrIxCkcAE0MD+GbKUJOUhVh
      gFp6bOlsVRxzC9kiPN7kcNhCyomEvFN4SktRGRTrf6hMsk50qfY8oNHZIjYM4sYi
      O4uNbS4pEc7JL7a5mjIz1571jEuv+RQYgKjZep/2h1mifXTnKa8uZxBt7IkfYBaZ
      ie264TpFswJuCHj+PnXbMQIDAQABo0swSTALBgNVHQ8EBAMCBDAwEwYDVR0lBAww
      CgYIKwYBBQUHAwEwJQYDVR0RBB4wHIIJbG9jYWxob3N0gg8qLmxvY2FsaG9zdC5j
      b20wDQYJKoZIhvcNAQELBQADggEBABqPwuCGr5eIOY5fCHD7AUTgTOjiqvckZ1uo
      +2h96pd/IlLUUkWa90dvX4ig6Hp5zJdsinjTmPkkEqm64S4Ae1i1aKIfNrwl1uh3
      icgKw4RBTL+xk9qSAiUpX2Bd3JtvDhsfSJh5WPjnaIqGFeuH2Iihqq8LfUdRKKy/
      s6/RCpG3pZHc06DG8LbI7UGL6+q9YKyBC9rGWDPejVlKF+GydGMb70SZC1MbKLwv
      xyCiszQJgWxEIIVyLRKXBgWE0BUErzjCGHiIDXLmNRG+m7FRiY9Arawq5D4Hjqs9
      UkBbV8UGKWx35T62IRsTciClmd6hwRi9a2Z0tRIYj077wFEBYeM=
      -----END CERTIFICATE-----
    EpiFiFederalClientSslCert: |
      -----BEGIN CERTIFICATE-----
      MIIDtzCCAp8CBhJhjgiy2TANBgkqhkiG9w0BAQsFADCBnjELMAkGA1UEBhMCSU4x
      CzAJBgNVBAgMAktBMRIwEAYDVQQHDAlCZW5nYWx1cnUxIzAhBgNVBAoMGmVwaUZp
      IFRlY2hub2xvZ2llcyBQdnQgTHRkMSMwIQYDVQQLDBplcGlGaSBUZWNobm9sb2dp
      ZXMgUHZ0IEx0ZDEkMCIGA1UEAwwbdWF0LXZlbmRvcmdhdGV3YXkucG9pbnR6Lmlu
      MB4XDTIxMDIwNDA2MjQyNVoXDTIzMDIwNDA2MjQyNVowgZ4xCzAJBgNVBAYTAklO
      MQswCQYDVQQIDAJLQTESMBAGA1UEBwwJQmVuZ2FsdXJ1MSMwIQYDVQQKDBplcGlG
      aSBUZWNobm9sb2dpZXMgUHZ0IEx0ZDEjMCEGA1UECwwaZXBpRmkgVGVjaG5vbG9n
      aWVzIFB2dCBMdGQxJDAiBgNVBAMMG3VhdC12ZW5kb3JnYXRld2F5LnBvaW50ei5p
      bjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAMmXeCDFGEh+PvHbhCLR
      dE3F/Wqrb9myT+i2CqGyg1Ww2sy1S1lnS2h9MkqejdCMAWDNxOsarkM6CK3tZTGy
      sEsQQSezug/Rp7PxkkX10/dVkt2+lx9tS0tOZisROVCLwGt8GfbGYGT5lHD5rwMR
      F7X0YgDAEkjxbYdV0p76rXTQjKpMGWF+B/2Lkpapr8PVihIPX4w+K58Cn3fVVhrt
      USof1Ooi9gr8cF+AOAfGAYHvkyac4PqK45Jwh95fodgMLm9UQIY++491Qn5ZlNNS
      4WnVTGlU8Rca5OOhTh4DRpLiAMo6t9qqempaFm65666H4xfAYWyRHjuWekNcuBW6
      UNUCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEATzDAgUDFX0U3SvrLlwUQRaB0okmD
      6QSjTgfOi0mNF+h9wwf4olxNf6gIMVai6dFoks10yKDaIl+rMgCDCxqlIBuTU1Rr
      oURqwH2Ac8Aov4T0pcJr6a9Wtrd1O7C1dlEaZRfpvZr6ehpXFHjTNOOTZvrH85Dl
      M2VMoS6rKWNH30VPixwXWqNmFYjfEumUk5FSpooUMyGWQ6ykrwShPbhDk2qG2wPi
      aoEFc8ciopq3JRW6eDNfE8jjGA22noJJBP6JabTV4UeJDx8VBF8EQQx5R63Yy4P3
      Ew8qnFIUG8FcdzAIEGAcy9JIPjZKN0abMBZbAEYY+YkQLHxe876rsXQm+A==
      -----END CERTIFICATE-----
    EpiFiFederalClientSslKey: |
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************


    # Using the same value as epifi federal client ssl cert
    RemittanceFederalRootCA: |
      -----BEGIN CERTIFICATE-----
      MIIDiDCCAnACCQCEu/A4YFWX5zANBgkqhkiG9w0BAQsFADCBhTELMAkGA1UEBhMC
      SU4xEjAQBgNVBAgMCUthcm5hdGFrYTESMBAGA1UEBwwJQmFuZ2Fsb3JlMQ4wDAYD
      VQQKDAVlcGlGaTELMAkGA1UECwwCSVQxEjAQBgNVBAMMCWVwaWZpLmNvbTEdMBsG
      CSqGSIb3DQEJARYOdGVhbUBlcGlmaS5jb20wHhcNMjAwMjA0MTMwMTA1WhcNMjEw
      MjAzMTMwMTA1WjCBhTELMAkGA1UEBhMCSU4xEjAQBgNVBAgMCUthcm5hdGFrYTES
      MBAGA1UEBwwJQmFuZ2Fsb3JlMQ4wDAYDVQQKDAVlcGlGaTELMAkGA1UECwwCSVQx
      EjAQBgNVBAMMCWVwaWZpLmNvbTEdMBsGCSqGSIb3DQEJARYOdGVhbUBlcGlmaS5j
      b20wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCXVsjAUyQQO4CX66Sn
      NDDFljkhR0P5xQ7M1QFobfCIoj1zFb2DYgJtUzPFkhoFS4BRFxo6/iaEDmMUflql
      izZAAMOi+li+RhXiAl6Czns9PGgNmX6FSLP3dFFahjbNQn3aCNMKaalJ699+G8cF
      vi/jOcmb/ieYxQTGdYjmijRrV2MEc/0rmH6AhZFAxpFf8JpRGTKVCDxsAEn6vH0K
      /1RQF9xpFGfw5QTCOvGDdJj3XSYA02zEnY32WAKyFP/SMqYbg6KTXRdjEZr3fJHt
      oJps+bfiIfhgxvazUdW67nr1fO3Mhta1onSWnLTArmbJBAx0tv68DlCSanNgfBm0
      4uGlAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAHXRh59vyYkT5B6x4ecmrVPMhHUo
      bSQjWGgRXTSWXsClb+o4QwGfLqr+eUbpfUBd8qeK59nxs71/Ytnloc2sA/8cpgsw
      HQVAPJCGR/J2YvD7NlNgYVnyxF/efYStQCMER+duw2RLTZ4S7yuhwBfWxsvQnjTF
      J9BhAbZgOy8QWeGB8MPDMPWzE8KcaSuOy6F/qH3T3QnV1rWi/Jo2d4UTQyhRqqTa
      sC5w1S9fo/lfqt/c7UuKaC8d4SFU2F3Q9NnL4Cp59QGAmC1VSqQbEqfkpE7ZLRbf
      6e88i5/faH2vKBIrh5KZxp2LjZygaF3AhNgundhq/X9RfKu/qFf93t+Kric=
      -----END CERTIFICATE-----

    # Using the same value as epifi federal client ssl cert
    RemittanceFederalIntermediateCA: |
      -----BEGIN CERTIFICATE-----
      MIIDiDCCAnACCQCEu/A4YFWX5zANBgkqhkiG9w0BAQsFADCBhTELMAkGA1UEBhMC
      SU4xEjAQBgNVBAgMCUthcm5hdGFrYTESMBAGA1UEBwwJQmFuZ2Fsb3JlMQ4wDAYD
      VQQKDAVlcGlGaTELMAkGA1UECwwCSVQxEjAQBgNVBAMMCWVwaWZpLmNvbTEdMBsG
      CSqGSIb3DQEJARYOdGVhbUBlcGlmaS5jb20wHhcNMjAwMjA0MTMwMTA1WhcNMjEw
      MjAzMTMwMTA1WjCBhTELMAkGA1UEBhMCSU4xEjAQBgNVBAgMCUthcm5hdGFrYTES
      MBAGA1UEBwwJQmFuZ2Fsb3JlMQ4wDAYDVQQKDAVlcGlGaTELMAkGA1UECwwCSVQx
      EjAQBgNVBAMMCWVwaWZpLmNvbTEdMBsGCSqGSIb3DQEJARYOdGVhbUBlcGlmaS5j
      b20wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCXVsjAUyQQO4CX66Sn
      NDDFljkhR0P5xQ7M1QFobfCIoj1zFb2DYgJtUzPFkhoFS4BRFxo6/iaEDmMUflql
      izZAAMOi+li+RhXiAl6Czns9PGgNmX6FSLP3dFFahjbNQn3aCNMKaalJ699+G8cF
      vi/jOcmb/ieYxQTGdYjmijRrV2MEc/0rmH6AhZFAxpFf8JpRGTKVCDxsAEn6vH0K
      /1RQF9xpFGfw5QTCOvGDdJj3XSYA02zEnY32WAKyFP/SMqYbg6KTXRdjEZr3fJHt
      oJps+bfiIfhgxvazUdW67nr1fO3Mhta1onSWnLTArmbJBAx0tv68DlCSanNgfBm0
      4uGlAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAHXRh59vyYkT5B6x4ecmrVPMhHUo
      bSQjWGgRXTSWXsClb+o4QwGfLqr+eUbpfUBd8qeK59nxs71/Ytnloc2sA/8cpgsw
      HQVAPJCGR/J2YvD7NlNgYVnyxF/efYStQCMER+duw2RLTZ4S7yuhwBfWxsvQnjTF
      J9BhAbZgOy8QWeGB8MPDMPWzE8KcaSuOy6F/qH3T3QnV1rWi/Jo2d4UTQyhRqqTa
      sC5w1S9fo/lfqt/c7UuKaC8d4SFU2F3Q9NnL4Cp59QGAmC1VSqQbEqfkpE7ZLRbf
      6e88i5/faH2vKBIrh5KZxp2LjZygaF3AhNgundhq/X9RfKu/qFf93t+Kric=
      -----END CERTIFICATE-----

    # IDFC Preapproved loans secrets
    FiIdfcPreApprovedLoanPrivateKey: |
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

    FederalEscalationClientSslCert: |
      -----BEGIN CERTIFICATE-----
      MIIDtzCCAp8CBhJhjgiy2TANBgkqhkiG9w0BAQsFADCBnjELMAkGA1UEBhMCSU4x
      CzAJBgNVBAgMAktBMRIwEAYDVQQHDAlCZW5nYWx1cnUxIzAhBgNVBAoMGmVwaUZp
      IFRlY2hub2xvZ2llcyBQdnQgTHRkMSMwIQYDVQQLDBplcGlGaSBUZWNobm9sb2dp
      ZXMgUHZ0IEx0ZDEkMCIGA1UEAwwbdWF0LXZlbmRvcmdhdGV3YXkucG9pbnR6Lmlu
      MB4XDTIxMDIwNDA2MjQyNVoXDTIzMDIwNDA2MjQyNVowgZ4xCzAJBgNVBAYTAklO
      MQswCQYDVQQIDAJLQTESMBAGA1UEBwwJQmVuZ2FsdXJ1MSMwIQYDVQQKDBplcGlG
      aSBUZWNobm9sb2dpZXMgUHZ0IEx0ZDEjMCEGA1UECwwaZXBpRmkgVGVjaG5vbG9n
      aWVzIFB2dCBMdGQxJDAiBgNVBAMMG3VhdC12ZW5kb3JnYXRld2F5LnBvaW50ei5p
      bjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAMmXeCDFGEh+PvHbhCLR
      dE3F/Wqrb9myT+i2CqGyg1Ww2sy1S1lnS2h9MkqejdCMAWDNxOsarkM6CK3tZTGy
      sEsQQSezug/Rp7PxkkX10/dVkt2+lx9tS0tOZisROVCLwGt8GfbGYGT5lHD5rwMR
      F7X0YgDAEkjxbYdV0p76rXTQjKpMGWF+B/2Lkpapr8PVihIPX4w+K58Cn3fVVhrt
      USof1Ooi9gr8cF+AOAfGAYHvkyac4PqK45Jwh95fodgMLm9UQIY++491Qn5ZlNNS
      4WnVTGlU8Rca5OOhTh4DRpLiAMo6t9qqempaFm65666H4xfAYWyRHjuWekNcuBW6
      UNUCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEATzDAgUDFX0U3SvrLlwUQRaB0okmD
      6QSjTgfOi0mNF+h9wwf4olxNf6gIMVai6dFoks10yKDaIl+rMgCDCxqlIBuTU1Rr
      oURqwH2Ac8Aov4T0pcJr6a9Wtrd1O7C1dlEaZRfpvZr6ehpXFHjTNOOTZvrH85Dl
      M2VMoS6rKWNH30VPixwXWqNmFYjfEumUk5FSpooUMyGWQ6ykrwShPbhDk2qG2wPi
      aoEFc8ciopq3JRW6eDNfE8jjGA22noJJBP6JabTV4UeJDx8VBF8EQQx5R63Yy4P3
      Ew8qnFIUG8FcdzAIEGAcy9JIPjZKN0abMBZbAEYY+YkQLHxe876rsXQm+A==
      -----END CERTIFICATE-----
    FederalEscalationClientSslKey: |
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

    # Pan Validation
    PanValidationAccessId: "FEDAPI"
    PanValidationAccessCode: "federal@123"

    #Loylty
    LoyltyClientId: "194"
    LoyltyClientKey: "123e2265-eccd-4a32-b584-b0f4b5a9fd92"
    LoyltyClientSecret: "3286C29DDDD6497398D9E3FA0FEAC129"
    LoyltyClientEncryptionKey: "E7009092646F4B078E2DC05E7815D431"
    LoyltyEGVModuleId: "3a0d3ee2-e422-11e8-9b38-00155dc9974a"
    LoyltyCharityModuleId: "6b321e5e-24d0-11eb-8040-0050569332bd"
    LoyltyApplicationId: "7aa7f694-da83-11e7-960e-00155dc90735"
    LoyltyProgramId: "d93afa51-e07c-11ea-8b0a-00155da44a11"

    #Qwikcilver
    QwikcilverSecrets: "{\"ClientId\": \"88d7346c8674587bc95f8fbde2e33acd\", \"ClientSecret\": \"1a33c6118d1e0c74f7a012991d0e4e39\" , \"Username\": \"<EMAIL>\", \"Password\": \"ePiFi@123\"}"

    #Thriwe
    ThriweSecrets: "{\"ClientApiKey\": \"CLIENT_API_KEY\", \"ClientApplicationId\": \"CLIENT_APPLICATION_ID\", \"ProjectCode\": \"EPIFI_V1\"}"

    # Riskcovry
    RiskcovrySecrets: "{\"PartnerCode\": \"FIMONEY\", \"PartnerKey\": \"oKpxo7dUe6iQCQdyzbJe\"}"

    #Onsurity Secrets
    OnsuritySecrets: "{\"ApiSecretKey\": \"kJ1j/O9BZjokTCsKE5H/rB4wUBjlDj/y9VLSExtEnDY=\", \"AccountId\": \"OSHE-*************\"}"

    # UPI API
    UPISenderUserId: "EPIFI"
    UPISenderPassword: "epifi@111"
    UPISenderCode: "EPIFI"
    UPISenderUserIdfede: "EPIFI"
    UPISenderPasswordfede: "epifi@111"
    UPISenderCodefede: "EPIFI"
    UPISenderUserIdIosAddFunds: "EPIFIIOS"
    UPISenderPasswordIosAddFunds: "EPIIOS@123"
    UPISenderCodeIosAddFunds: "EPIFIIOS"
    UPISenderUserIdfifederal: "EPIFI"
    UPISenderPasswordfifederal: "epifi@111"
    UPISenderCodefifederal: "EPIFI"

    # SMS API keys
    TwilioAccountSid: "**********************************"
    TwilioApiKey: "45733540c35a8cc8edc038b0908633b6"
    ExotelApiKey: "43443c5ea5ffd57e8e4ebaf44f930c24d09901a2ea257324"
    ExotelApiToken: "6e8ddb2181b6e25b87ef76a5a3010cf00f1a71765a186472"
    AclEpifiUserId: "epiftalt"
    AclEpifiPassword: "epiftalt09"
    AclFederalUserId: "epiftalt"
    AclFederalPassword: "epiftalt09"
    AclEpifiOtpUserId: "epifiotp"
    AclEpifiOtpPassword: "epifiotp19"
    KaleyraFederalApiKey: "A7d60eb7691c476eab3a71e40ea69fc15"
    KaleyraFederalCreditCardApiKey: "A5848a57a6376178ff30761971f37f06f"
    KaleyraEpifiApiKey: "A2e7e0dc85bcb95361e6d90e4f4b5f5b8"
    KaleyraEpifiNRApiKey: ""
    AclWhatsappUserId: "epifiuat"
    AclWhatsappPassword: "epifiuat23"
    WhatsappEnterpriseId: "epifiuat"
    WhatsappEnterpriseToken: "7a48d49b-111c-42c2-8a50-c72adb857b2c"
    # Video kyc KARZA api key
    KarzaVkycApiKey: "enaI2iQG0fylESWB0v6K"
    KarzaVkycPriorityApiKey: "kwE6KLGjdG7CjKT7"
    KarzaReVkycPriorityApiKey: "enaI2iQG0fylESWB0v6K"

    # Rounza cricket api's project and api key
    RounazCricketProjectKey: "RS_P_1377206605560549389"
    RounazCricketApiKey: "RS5:83d0670d6aab0919d3669844437be507"

    # Rounza football api's access and secret key
    RounazFootballAccessKey: "TBD"
    RounazFootballSecretKey: "TBD"

    # B2C payments keys
    B2cUserId: "federal"
    B2cPassword: "federal@123"
    B2cSenderCodeKey: "BNKCD"

    # ipstack access key
    IpstackAccessKey: "accesskey1234"

    # client api key for aa
    AaVgSecretsV1: '{"sahamati_client_secret":"11b692cd-6444-4b40-80e3-b842c969daeb","finvu_entity_key":"********************************","ignosis_api_key":"a8f68276c20147c0baa30607bca4d0a8"}'
    AaVgSecretsV2: '{"sahamati_client_secret":"11b692cd-6444-4b40-80e3-b842c969daeb","finvu_entity_key":"********************************"}'
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    # Shipway username and password
    ShipwayUsername: "shipway_username"
    ShipwayPassword: "shipway_password"

    NsdlUserId: "********"

    # Cvlkra
    CvlKraPassKey: "epifi"
    CvlKraPosCode: "epifi"
    CvlKraUserName: "webepifi" # aka, Login ID on CVL's UAT website: https://test.cvlkra.com
    CvlKraPassWord: "Epifi@123"
    CvlSftpUser: "epifi"
    CvlSftpPass: "epifi"
    CvlSftpUploadUser: "epifi"
    CvlSftpUploadPass: "epifi"
    CvlSftpSshKey: "sftp.deploy.pointz.in ecdsa-sha2-nistp521 AAAAE2VjZHNhLXNoYTItbmlzdHA1MjEAAAAIbmlzdHA1MjEAAACFBADQ11ICNYbGYdI2Xe/If/84L/ei9z2fPMJP/wI/wTwZ505Y7IvhKE45Tq6vooq4s5FBRCbJkYPNC/75NsqPMBJfpAGXou71spDNRgFS4m/t/eQE+4OYSfgXOg06QwqSfPh4ttvM2o9ePaxf6pHn7Z5dg5K0yRV+p8UiAvZ+paxHqTJzIw=="

    # Experian Credit Report
    ExperianCreditReportPresenceClientName: "EPIFI_CC_EM"
    ExperianCreditReportFetchClientName: "EPIFI_EM"
    ExperianCreditReportForExistingUserClientName: "EPIFIMONEY_EM"
    ExperianExtendSubscriptionClientName: "EPIFIMONEY_EM"
    ExperianVoucherCode: "EPIFIqOjhZ"

    # Manch secure key and template key
    ManchSecureKey: "1WkpYr93DhiKSFIo"
    ManchTemplateKey: "TMPTS01578"

    WealthKarzaApiKey: "AwAPBOYS29jaz2gh5fed"

    # Credentials for cams
    CAMSKey: '{"UserName":"ABHISH","Password":"EpifiDevCams@123"}'

    SmallCaseKey: '{"GatewayAuthToken":"EPIFI123","GatewaySecret":"EPIFI456"}'

    MFCentralKey: '{"UserName":"EPIFI123","Password":"EPIFI124", "ClientID":"EPIFI222","ClientSecret":"EPIFI126"}'

    SeonClientApiKey: "923de0bc-77c5-44ad-930b-548c16d38b2b"

    #ckyc
    CkycFiCode: "IN9630"

    FederalDepositSecrets: "staging/vendorgateway/federal-deposit-secrets"

    #digilocker
    DigilockerClientSecret: "e950dd382aee10208a9e"

    # Lending keys
    PreApprovedLoanFederalSecrets: '{"UserName":"EPIFI","Password":"EPIFI#432", "UserNameSftp":"epifi_test", "PasswordSftp":"epifi", "SenderCode":"EPIFI_CD", "SenderId":"EPIFI", "SenderAccessCode":"EPIFI", "PartnerName":"EPIFI", "PartnerCode":"EPIFI@123#", "DisbursementApiSenderCode":"EPIFICD", "UserAccessCode":"EFed@123", "UserAccessId":"EPIFI_TEST", "SchemeCode":"79425"}'
    FederalSftpSshKey: "sftp.deploy.pointz.in ecdsa-sha2-nistp521 AAAAE2VjZHNhLXNoYTItbmlzdHA1MjEAAAAIbmlzdHA1MjEAAACFBADQ11ICNYbGYdI2Xe/If/84L/ei9z2fPMJP/wI/wTwZ505Y7IvhKE45Tq6vooq4s5FBRCbJkYPNC/75NsqPMBJfpAGXou71spDNRgFS4m/t/eQE+4OYSfgXOg06QwqSfPh4ttvM2o9ePaxf6pHn7Z5dg5K0yRV+p8UiAvZ+paxHqTJzIw=="
    PreApprovedLoanSecrets: '{ "UserName": "EPIFI", "Password": "EPIFI#432", "UserNameSftp": "epifi_test", "PasswordSftp": "epifi", "SenderCode": "EPIFI_CD", "SenderId": "EPIFI", "SenderAccessCode": "EPIFI","FederalNTBLoanCredentials":{"UserAccessCode":"exampleAccessCode123","UserAccessId":"exampleUserId456","SenderCode":"exampleSenderCode789","ClientSecretKey":"exampleClientSecretKeyABC","ClientId":"exampleClientIdDEF"}, "Liquiloans": { "SID": "S003082", "Key": "84A39534BB3EF", "EarlySalary": { "SID": "EsSimSID", "Key": "84A39534BB3EF", "SchemeCode": "44720" }, "FLDG": { "SID": "S003341", "Key": "4D87667B61AFD" }, "STPL": { "SID": "S003341", "Key": "4D87667B61AFD" } }, "Idfc": { "SecretKey": "********************************", "Iv": "[$\\7F*+8n9@7^6!~", "ClientId": "378ce59c-fed0-4bb5-800e-4324022211a1", "KId": "529ba75c-de15-4885-9edd-0b9289e9e26c", "CorrelationId": "SJBDJKSABDJKASARDBNASKJJJ", "Source": "FIMONEY", "MerchantCode": "FIMONY", "MandateRegistrationIv": "VEN@#FIMNYAIV@05", "MandateRegistrationSecretKey": "VEN@#FIMNYAES@08", "BankCode": "5430" }, "Abfl": { "Username": "fimoney", "Password": "pqjskieH6ss9sQwe", "PwaAuthorizationKey": "YVYyLwsevOdBDxFWTUFNtTulaPaTEitHxaCxoGyw" }, "Setu":{ "PartnerId":"fi", "PartnerSchemeId":"fa169394-b88d-485c-a701-84728a12eef3", "PartnerSecret":"c64187e2-5d12-4081-a2c2-0d91a0739696" }, "Lenden" : {"SecretKey" : "********************************","Iv" : "DummyIv12b564546", "Authorization" : "random"}, "Digitap":{"DigitapSecrets":{"Authorization":"********************************************************"}}}'
    LendingMFCentralSecrets: '{"ClientId" : "fi", "ClientSecret" : "secret", "LenderCode" : "001", "Password" : "wordpass", "UserName" : "conan"}'


    # Leegality
    LeegalitySecret: '{"X-Auth-Token":"********************************","FederalLeegalityProfileId":"6btsM0U","PreApprovedLoansFederalProfileId":"6btsM0U","SavingsAccountFederalProfileId":"oyLVQSr"}'

    #Liquiloans
    LiquiloansMid: "M001110"
    LiquiloansKey: "8F43559AF4F26"

    # Credentials for karvy
    KarvyKey: '{"UserName":"Hs7ViMqZCxouBxoPbFD/EA==","Password":"SPrwFkufU2hkxt+oxQh46x7O1YjKmQsaLgcaD2xQ/xA="}'

    CvlSecrets: '{"sftp-upload-pass": "epifi", "sftp-upload-user": "epifi", "api-password": "TBKPqRNcj7uNBOyzSjXW5A!3d!3d"}'
    LiquiloansSecrets: '{"MID": "M001110", "KEY": "8F43559AF4F26"}'

    #GPlace api key
    GPlaceApiKey: "AIzaSyBzvLUojiKWSFOntSykMwTxhJsa7ohLXCg"

    # karza api keys
    KarzaKey: "AwAPBOYS29jaz2gh5fed"
    TartanKey: "RVBMFCXB"

    KarzaPanProfileKey: "OuFL0E8ovTQJjqkv"

    GeolocationKey: "geolocationkey"
    VKYCAgentDashboardSecrets: '{"Password":"XXXXXX","Username":"XXXXXX"}'

    # DronaPay
    DronaPayKey: "a6329869-9acf-40af-bc87-cdb1daf4db68"

    # Secrets of payu
    payuToken: ""
    payuApiKey: ""

    BureauSecrets: '{"Id":"Id", "Password":"Password"}'

    SignzySecrets: '{"Username":"username", "Password":"password", "UserId":"userid"}'

    AlpacaSecrets: '{"BROKER_API_KEY":"api_key","BROKER_API_SECRET":"secret"}'

    FederalInternationalFundTransferSecrets: '{"Channel":"ChannelTest","ClientId":"TestId","RateCode":"TestRateCode"}'

    FederalProfileValidationSecrets: '{"TenantId":"IN34FKS3", "SenderAccessCode": "EPIFI", "SenderId": "EPIFI", "SenderCode": "EPIFI"}'

    MorningStarSecrets: '{"UserName":"<EMAIL>","Password":"password"}'

    MorningStarAccountSecrets: '{"CompanyUserName":"DummyUserName","CompanyPassword":"DummyPassword"}'

    MaxmindSecrets: '{"UserId":"username","LicenseKey":"LicenseKey"}'

    TssApiToken: "tss-api-token"
    TSSAPITokenForSG: "tss-api-token-sg"

    TSSCloudCredentials: "{'PublicCertPEMBase64':'dummy-cert-pem-base64', 'Tenants':{'EpifiTech': {'APIToken': 'dummy-api-token', 'PrivateKeyBase64': 'dummy-private-key-base64'}, 'LMS': {'APIToken': 'dummy-api-token', 'PrivateKeyBase64': 'dummy-private-key-base64'}}}"

    # p2p Investment secrets
    p2pInvestmentLiquiloansSftpUser: "Uat-epifi"
    p2pInvestmentLiquiloansSftpPassword: "B5n0$53w"

    VistaraSecrets: '{"Sftp":{"User":"epifi_staging_cams","Password":"********************************","Host":"sftp.deploy.pointz.in","Port":22,"SshKey":""},"Http":{"BaseUrl":"https://cv-uat-*********.ap-southeast-1.elb.clubvistara.com/partnersApi","Username":"Test","Password":"Test@123"}}'

    SlackSecrets: '{"apptoken": "xapp-1-A04UWC2C50R-*************-8738f923a1cf4c72cfa77d810b377fcdfd9d44ea15fc73d772fdf550d43a5a9d", "bottoken":"*********************************************************"}'

    # Fennel Secrets
    FennelFeatureStoreSecrets: '{"bearer_token":"test"}'

    DreamfolksSecrets: '{"ApiKey":"KWtMtaVGar4AcCkP", "ApiSecret":"5Tp2BHWW6jHmpubm"}'

    CommonSftpUser: "dummy-user"
    CommonSftpPass: "dummy-pass"
    # Lentra secrets
    LentraSecrets: '{"AggregatorId":"f9f9df6deb5c69768d47a39be9d84114146e1f31", "ClientId":"9c1a5048-dd20-3e5c-a424-a87a0f5b58a1", "ClientSecret":"ac189a25-0111-35e9-9d19-8e8c3dc85c6c", "InstituteName":"XXXX", "sInstitutionName":"XXXX", "sClientSecret":"XXXX", "sClientIdentifier":"XXXX"}'

    EpifiFederalEpanSftpSecrets: '{"User":"epifi_test", "Password":"epifi", "SshKey":"dummy-ssh-key"}'

    EpifiFederalEnachSftpSecrets: '{"User":"dummy-user", "Password":"dummy-password", "SshKey":"dummy-ssh-key"}'

    CredgenicsAuthToken: "3d31c4d3-e0af-4e3f-9616-112340aafd2e"

    CredgenicsAuthenticationKeyV2: '{"EPIFI_AUTH_SECRETS":{"CLIENT_ID":"dummy", "CLIENT_SECRET":"dummy"},"SG_AUTH_SECRETS":{"CLIENT_ID":"dummy", "CLIENT_SECRET":"dummy"}}'

    LendingFiftyFinLamfSecrets: '{"ClientId" : "fi", "ClientSecret" : "secret"}'

    PoshvineSecrets: '{"key":"********************************", "iv":"41d0a599ea8c4217", "mode": "AES-256-CBC", "UpdateApiSecretKey": "OKTyMzk9MTZlMzk1YmE0MTAzZGNiMzJhMzP2MmM9OKI=", "UpdateApiIv": "PKJkMGE1OWllYTdjNzKxKg==", "UpdateApiClientApiKey": "3c60c09a3a84edf7"}'

    CibilSecrets: '{
      "SiteName": "site name",
      "AccountName": "account name",
      "AccountCode": "account code",
      "ApiKey": "api key",
      "ClientSecret":"client secret",
      "ApiKeyV2": "api key v2",
      "ClientSecretV2":"client secret v2",
      "MemberRefId": "member ref id",
      "ProductConfigurationId": "product conf id",
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      "Certificate": "-----BEGIN CERTIFICATE-----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\nJrjiJ7DIy+uVfrg8DdZdQFWQzRgu/PYHLvXvAGqFPvSvqE47L+xJSWA=\n-----END CERTIFICATE-----"
    }'
    # Payment Gateway secrets
    RazorpaySecrets: '{ "KeyId": "rzp_test_J8bVSzs01Aaq7V", "KeySecret": "fjdeWi73ZX71TyZOjYzeB0jh" }'
    PanValidationSecretskey: '{"SenderCode": "TEST1","SenderId": "TEST1","SenderKey": "1234"}'
    BureauIdSecrets: '{"ApiKey": "1a0b7868-75b4-4713-ac54-c98624d63d4a","Password": "2e9f8abf-89d7-4543-88f4-869c91e27f4a","WorkflowId": "f9c8bf64-0278-4726-8b27-a4db3c4e8180"}'

    VisaSecrets: '{
        "key": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "cert": "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUR4akNDQXE2Z0F3SUJBZ0lJUmFieEJVVEZQWGd3RFFZSktvWklodmNOQVFFTEJRQXdNVEVPTUF3R0ExVUUKQXd3RlZrUlFRMEV4RWpBUUJnTlZCQW9NQ1ZaRVVGWkpVMEZEUVRFTE1Ba0dBMVVFQmhNQ1ZWTXdIaGNOTWpRdwpPVEV3TVRFME5URTJXaGNOTWpVd056SXhNRFF5TnpNM1dqQ0JrekVkTUJzR0NTcUdTSWIzRFFFSkFSWU9jbUZwCmJrQmxjR2xtYVM1amIyMHhHakFZQmdOVkJBTU1FVVZ3YVdacFZHVmphRzV2Ykc5bmFXVnpNUk13RVFZRFZRUUwKREFwRVpYQmhjblJ0Wlc1ME1SVXdFd1lEVlFRS0RBeFBjbWRoYm1sNllYUnBiMjR4RFRBTEJnTlZCQWNNQkVOcApkSGt4RGpBTUJnTlZCQWdNQlZOMFlYUmxNUXN3Q1FZRFZRUUdFd0pWVXpDQ0FTSXdEUVlKS29aSWh2Y05BUUVCCkJRQURnZ0VQQURDQ0FRb0NnZ0VCQUpzbThNQWluNWYvVGRlQlZ2WFRZNkNrR2FIRjRXSXd2SVNxTlJpSUJKeWcKUk1LQzhvWGJ0TmxxOTh0eEM4Si9zSitLT0VEZlk4UVRlMDYyYjN3THdDcDBJRk1ZaGRibTZnSW1mcng0cTRVeAo5NnZXQ0N6eHdadVk2YmhwalZoMkFLM2REcUJEMUg2QjNWdGJGaGEwbmZKUUNTNy9UTUEvOXo3MGtkQ2dlV3o4Clo3bXhDd3NvQllpSmd2VGxGVkJ3cFpCK3VRVEJXNU5KUm9rbFFmRUszWStnVG5CRmVscnd3dTR4VVdhdW9DOTAKd3lSMGplMUlWNmd4UVR5S0FFMEo3aUZLWldjcndhT1krcUFWUjljUGh3eExZbnhqaStYQVdVd3h2YTIvaXVIQgp4U1ZJUEp5NkFsWE9zYlNRTC9qSVA1YjBDdlBHSk5jSTEweit0b1JVL2xFQ0F3RUFBYU4vTUgwd0RBWURWUjBUCkFRSC9CQUl3QURBZkJnTlZIU01FR0RBV2dCU3YzVzYyb0V1Y2Via1dDR0xtSXpFUXA0THJvVEFkQmdOVkhTVUUKRmpBVUJnZ3JCZ0VGQlFjREFnWUlLd1lCQlFVSEF3UXdIUVlEVlIwT0JCWUVGR1JYRTIrWXA2VFVyNGpWeWxKeQpMblZFVEY0L01BNEdBMVVkRHdFQi93UUVBd0lGNERBTkJna3Foa2lHOXcwQkFRc0ZBQU9DQVFFQWp0YXQyTjBDCjVvdWt5Y2dvK3hnV29KckgzVEo3RXIyMUNNTkVYOHZ3Q0Zhak1GYXcweEdNNWlDUHZVNHVBcDBXd1QvUDRwbTAKRWpLdEM0SkxHdzJUQzc0YXp0ZDdITlZsemlXcGlQWmFoMWM0UlpaUHZRQ3FCMitNYkp0citCTlhhbkhPR0RjbApKMG4zNVJPQkdZLzhoN2FzbGlieEx2SXQxdkVsQjdzRVZRdXROWGtEUzBCY0RCSEdPQndBTFF5SHVMV09qOWQvCm5Rak9PdlI5OUFJT2tjT1B1czRaNSsvTzQwZDFCZlMyUzRibldteUhKN0dGMTJ1dm13cDR2NEtBMTd3dEJ2ZXIKcVNqQ21iRnIvb0NkcnpZZnE4VE9lZ2hOZ1o3dmNaaWVBWWdYVW9UcjN0WEQrdnNhVWswdW1WdmNqMnhzL2t5Ywp2RnE1czdCb0J1QXFEdz09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K",
        "UserId": "NA0ZMWBCEWYIJPLGF43621wcFA1iu518-0L36eEeRFqlt5Ia0",
        "Password": "YK5T6YNKezx6f9GOkKcq6z70QrwSYY6pOGCErW6"
    }'

    SavenSecrets: '{ "ApiKey": "saven_test_J8bVSzs01Aaq7V", "PayloadSigningKeyBase64": "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}'

    FederalEscalationSecrets: '{ "ClientSecret": "sample_client_secret","ClientId": "sample_client_id","Username":"username","Password":"password"}'

    SetuBillPaySecrets: '{ "ClientId": "dummy-client-id","Secret": "dummy-secret"}'

    NuggetSecrets: '{ "Username": "dev_user","Password": "dev_password","ClientID":12345}'

SyncWrapperSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "vn-sync-wrapper-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 2
      MaxAttempts: 6
      TimeUnit: "Second"

DisputeSFTP:
  user: ""
  password: ""
  host: ""
  port: 80
  S3BucketName: "epifi-federal-disputes"

Flags:
  TrimDebugMessageFromStatus: false
  TokenValidation: true
  AllowSpecialCharactersInAddress: true
  EnableFennelClusterV3: true
  EnableInstrumentBillingInterceptor: true
  EnableCibilV2Secrets: true

SecureLogging:
  EnableSecureLog: true
  SecureLogPath: "/tmp/secure.log"
  MaxSizeInMBs: 5
  MaxBackups: 20

RedisOptions:
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 0

FcmAnalyticsLabel: "push-notification"

AwsSes:
  FiMoneyArn: "arn:aws:ses:ap-south-1:632884248997:identity/fi.money"
  FiCareArn: "arn:aws:ses:ap-south-1:632884248997:identity/fi.care"
  StockGuardianArn: "arn:aws:ses:ap-south-1:632884248997:identity/stockguardian.in"

# generic downtime handler for vg
# the vendor name is the enum value with config values like IsEnabled, StartTime, EndTime
DowntimeConfig:
  NSDL:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2022-03-15 15:04:05"
        EndTimestamp: "2022-03-16 15:04:05"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
  CVLKRA:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2021-11-01 15:04:05"
        EndTimestamp: "2021-11-02 15:04:05"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
  CKYC:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: true
        StartTimestamp: "2020-11-01 15:04:05"
        EndTimestamp: "2021-04-09 00:00:00"
        Msg: "This is due to a downtime at our vendor partner. This should be up by %s. We will notify you once its up."
  DIGILOCKER:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2021-11-01 15:04:05"
        EndTimestamp: "2021-11-02 15:04:05"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
  MANCH:
    Downtime:
      # Time should be in HH:MM 24-hour format IST
      DailyDownTime:
        IsEnable: false
        StartTime: "00:30"
        EndTime: "04:30"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."
      # Timestamp should be in YYYY-MM-DD HH:MM:SS format IST
      TimestampBasedDownTime:
        IsEnable: false
        StartTimestamp: "2022-08-26 23:00:00"
        EndTimestamp: "2022-08-27 09:00:00"
        Msg: "Our systems are under maintenance from %s to %s. Please retry after sometime."

Profiling:
  StackDriverProfiling:
    ProjectId: "development"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true
