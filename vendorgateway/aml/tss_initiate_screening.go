package aml

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"
	"github.com/epifi/be-common/pkg/vendorapi"
	amlVgPb "github.com/epifi/gamma/api/vendorgateway/aml"
	"github.com/epifi/gamma/api/vendors/tss"
	"github.com/epifi/gamma/vendorgateway/config"
)

type initiateScreeningReq struct {
	req        *amlVgPb.InitiateScreeningRequest
	tenantConf *config.TSSCloudTenant
	*SecureExchange
}

func (s *initiateScreeningReq) URL() string {
	return s.tenantConf.URL + "/customerinfo/as501"
}

func (s *initiateScreeningReq) HTTPMethod() string {
	return http.MethodPost
}

func (s *initiateScreeningReq) Add(req *http.Request) *http.Request {
	req.Header.Add("Cluster", "CL1_User")
	req.Header.Add("Domain", s.URL())
	req.Header.Add("ApiToken", s.tenantConf.APIToken)
	return req
}

func (s *initiateScreeningReq) GetResponse() vendorapi.Response {
	return &initiateScreeningRes{}
}

func (s *initiateScreeningReq) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}

func (s *initiateScreeningReq) Marshal() ([]byte, error) {
	purposeCode, err := getPurposeCode(s.req.GetPurpose())
	if err != nil {
		return nil, errors.Wrap(err, "error getting purpose code")
	}
	customerData, err := s.convertCustomerData()
	if err != nil {
		return nil, errors.Wrap(err, "error converting customer data")
	}
	req := &tss.AS501Request{
		RequestId:        s.req.GetVendorRequestId(),
		SourceSystemName: s.tenantConf.Name,
		Purpose:          purposeCode,
		CustomerList:     []*tss.CustomerData{customerData},
	}
	reqJson, err := protojson.Marshal(req)
	if err != nil {
		return nil, errors.Wrap(err, "error marshalling request to JSON")
	}
	return reqJson, nil
}

func (s *initiateScreeningReq) RedactRequestBody(ctx context.Context, requestBody []byte, contentType string) ([]byte, error) {
	return httpcontentredactor.GetInstance().Redact(ctx, requestBody, contentType, map[string]mask.MaskingStrategy{
		// Personal Information
		"firstName":            mask.DontMaskFirstTwoAndLastTwoChars,
		"middleName":           mask.DontMaskFirstTwoAndLastTwoChars,
		"lastName":             mask.DontMaskFirstTwoAndLastTwoChars,
		"fatherFirstName":      mask.DontMaskFirstTwoAndLastTwoChars,
		"fatherMiddleName":     mask.DontMaskFirstTwoAndLastTwoChars,
		"fatherLastName":       mask.DontMaskFirstTwoAndLastTwoChars,
		"motherFirstName":      mask.DontMaskFirstTwoAndLastTwoChars,
		"motherMiddleName":     mask.DontMaskFirstTwoAndLastTwoChars,
		"motherLastName":       mask.DontMaskFirstTwoAndLastTwoChars,
		"dateOfBirth":          mask.MaskToStaticValue,
		"personalEmail":        mask.MaskCharTillAtSign,
		"personalMobileNumber": mask.DontMaskLastFourChars,
		"personalMobileIsd":    mask.MaskToStaticValue,

		// Identity Documents
		"pan":                         mask.DontMaskLastFourChars,
		"passportNumber":              mask.DontMaskLastFourChars,
		"voterIdNumber":               mask.DontMaskLastFourChars,
		"drivingLicenseNumber":        mask.DontMaskLastFourChars,
		"aadhaarNumber":               mask.DontMaskLastFourChars,
		"aadhaarVaultReferenceNumber": mask.MaskAllChars,

		// Address Information
		"permanentAddressLine1":        mask.DontMaskFirstTwoAndLastTwoChars,
		"permanentAddressLine2":        mask.DontMaskFirstTwoAndLastTwoChars,
		"permanentAddressLine3":        mask.DontMaskFirstTwoAndLastTwoChars,
		"permanentAddressZipCode":      mask.MaskToStaticValue,
		"correspondenceAddressLine1":   mask.DontMaskFirstTwoAndLastTwoChars,
		"correspondenceAddressLine2":   mask.DontMaskFirstTwoAndLastTwoChars,
		"correspondenceAddressLine3":   mask.DontMaskFirstTwoAndLastTwoChars,
		"correspondenceAddressZipCode": mask.MaskToStaticValue,

		// Financial Information
		"exactIncome":   mask.MaskToStaticValue,
		"exactNetworth": mask.MaskToStaticValue,
	})
}

func getPurposeCode(purpose amlVgPb.Purpose) (string, error) {
	switch purpose {
	case amlVgPb.Purpose_PURPOSE_INITIAL_SCREENING:
		return "03", nil
	case amlVgPb.Purpose_PURPOSE_CONTINUOUS_SCREENING:
		return "04", nil
	default:
		return "", errors.Errorf("unsupported purpose type: %s", purpose.String())
	}
}

func (s *initiateScreeningReq) convertCustomerData() (*tss.CustomerData, error) {
	if strings.TrimSpace(s.req.GetUserId()) == "" {
		return nil, errors.New("user id is mandatory")
	}
	if s.req.GetUserDetails().GetName() == nil &&
		s.req.GetUserDetails().GetPhoneNumber().GetNationalNumber() == 0 &&
		s.req.GetUserDetails().GetEmail() == "" &&
		s.req.GetUserDetails().GetPanNumber() == "" &&
		s.req.GetUserDetails().GetPassportNumber() == "" &&
		s.req.GetUserDetails().GetDrivingLicenseNumber() == "" {
		return nil, errors.New("at least one of name, phone number, email, pan, passport or driving license is mandatory")
	}

	gender, err := getGenderCode(s.req.GetUserDetails().GetGender())
	if err != nil {
		return nil, errors.Wrap(err, "error converting gender to code")
	}
	product, err := getProductCode(s.req.GetProduct())
	if err != nil {
		return nil, errors.Wrap(err, "error converting product to code")
	}
	nationality, err := getNationalityCode(s.req.GetUserDetails().GetNationality())
	if err != nil {
		return nil, errors.Wrap(err, "error converting nationality to code")
	}
	dateOfBirth := ""
	if s.req.GetUserDetails().GetDateOfBirth() != nil {
		if !datetime.IsDateBeforeTodayInLoc(s.req.GetUserDetails().GetDateOfBirth(), datetime.IST) {
			return nil, errors.New("date of birth is not before today")
		}
		dateOfBirth = datetime.DateToString(s.req.GetUserDetails().GetDateOfBirth(), "02-Jan-2006", datetime.IST)
	}

	// Convert address lines
	permanentAddress := s.req.GetUserDetails().GetPermanentAddress()
	correspondenceAddress := s.req.GetUserDetails().GetCorrespondenceAddress()
	permanentAddressLines := permanentAddress.GetAddressLines()
	correspondenceAddressLines := correspondenceAddress.GetAddressLines()
	permanentAddressLine1 := ""
	permanentAddressLine2 := ""
	permanentAddressLine3 := ""
	if len(permanentAddressLines) > 0 {
		permanentAddressLine1 = permanentAddressLines[0]
		if len(permanentAddressLines) > 1 {
			permanentAddressLine2 = permanentAddressLines[1]
			if len(permanentAddressLines) > 2 {
				permanentAddressLine3 = permanentAddressLines[2]
			}
		}
	}
	correspondenceAddressLine1 := ""
	correspondenceAddressLine2 := ""
	correspondenceAddressLine3 := ""
	if len(correspondenceAddressLines) > 0 {
		correspondenceAddressLine1 = correspondenceAddressLines[0]
		if len(correspondenceAddressLines) > 1 {
			correspondenceAddressLine2 = correspondenceAddressLines[1]
			if len(correspondenceAddressLines) > 2 {
				correspondenceAddressLine3 = correspondenceAddressLines[2]
			}
		}
	}
	return &tss.CustomerData{
		SourceSystemName:         s.tenantConf.Name,
		SourceSystemCustomerCode: s.req.GetUserId(),
		UniqueIdentifier:         s.req.GetVendorRequestId(),
		Products:                 product,
		ConstitutionType:         "1", // Individual
		FirstName:                s.req.GetUserDetails().GetName().GetFirstName(),
		MiddleName:               s.req.GetUserDetails().GetName().GetMiddleName(),
		LastName:                 s.req.GetUserDetails().GetName().GetLastName(),
		FatherFirstName:          s.req.GetUserDetails().GetFatherName().GetFirstName(),
		FatherMiddleName:         s.req.GetUserDetails().GetFatherName().GetMiddleName(),
		FatherLastName:           s.req.GetUserDetails().GetFatherName().GetLastName(),
		MotherFirstName:          s.req.GetUserDetails().GetMotherName().GetFirstName(),
		MotherMiddleName:         s.req.GetUserDetails().GetMotherName().GetMiddleName(),
		MotherLastName:           s.req.GetUserDetails().GetMotherName().GetLastName(),
		Gender:                   gender,
		DateOfBirth:              dateOfBirth,
		PersonalEmail:            s.req.GetUserDetails().GetEmail(),
		PersonalMobileIsd:        strconv.Itoa(int(s.req.GetUserDetails().GetPhoneNumber().GetCountryCode())),
		PersonalMobileNumber:     strconv.FormatUint(s.req.GetUserDetails().GetPhoneNumber().GetNationalNumber(), 10),

		// Permanent address
		PermanentAddressZipCode: permanentAddress.GetPostalCode(),
		PermanentAddressLine1:   permanentAddressLine1,
		PermanentAddressLine2:   permanentAddressLine2,
		PermanentAddressLine3:   permanentAddressLine3,
		PermanentAddressCity:    permanentAddress.GetLocality(),
		PermanentAddressState:   permanentAddress.GetAdministrativeArea(),

		// Correspondence address
		CorrespondenceAddressZipCode: correspondenceAddress.GetPostalCode(),
		CorrespondenceAddressLine1:   correspondenceAddressLine1,
		CorrespondenceAddressLine2:   correspondenceAddressLine2,
		CorrespondenceAddressLine3:   correspondenceAddressLine3,
		CorrespondenceAddressCity:    correspondenceAddress.GetLocality(),
		CorrespondenceAddressState:   correspondenceAddress.GetAdministrativeArea(),

		PassportNumber:       s.req.GetUserDetails().GetPassportNumber(),
		VoterIdNumber:        s.req.GetUserDetails().GetVoterId(),
		DrivingLicenseNumber: s.req.GetUserDetails().GetDrivingLicenseNumber(),
		Pan:                  s.req.GetUserDetails().GetPanNumber(),
		Nationalities:        strings.Join([]string{nationality}, ","),
	}, nil
}

func getGenderCode(gender common.Gender) (string, error) {
	switch gender {
	case common.Gender_MALE:
		return "01", nil
	case common.Gender_FEMALE:
		return "02", nil
	case common.Gender_TRANSGENDER:
		return "03", nil
	default:
		return "", errors.New(fmt.Sprintf("unsupported gender type: %s", gender.String()))
	}
}

func getProductCode(product amlVgPb.Product) (string, error) {
	switch product {
	case amlVgPb.Product_PRODUCT_MUTUAL_FUND:
		return "MF", nil
	case amlVgPb.Product_PRODUCT_LOAN:
		return "Loan", nil
	default:
		return "", errors.Errorf("unsupported product type: %s", product.String())
	}
}

func getNationalityCode(nationality common.Nationality) (string, error) {
	switch nationality {
	case common.Nationality_NATIONALITY_INDIAN:
		return "IND", nil
	default:
		return "", errors.Errorf("unsupported nationality type: %s", nationality.String())
	}
}

type initiateScreeningRes struct{}

func (s *initiateScreeningRes) UnmarshalV2(ctx context.Context, b []byte) (proto.Message, error) {
	res := &tss.AS501Response{}
	err := (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(b, res)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling response to TSS proto")
	}
	initScreeningRes, err := s.convertTSSResToVGRes(res)
	if err != nil {
		return nil, errors.Wrap(err, "error converting TSS response to VG proto")
	}
	return initScreeningRes, nil
}

func (s *initiateScreeningRes) Unmarshal(b []byte) (proto.Message, error) {
	return s.UnmarshalV2(context.Background(), b)
}

func (s *initiateScreeningRes) convertTSSResToVGRes(res *tss.AS501Response) (*amlVgPb.InitiateScreeningResponse, error) {
	if res.GetValidationCode() != "" {
		return &amlVgPb.InitiateScreeningResponse{
			Status:                rpc.StatusInvalidArgumentWithDebugMsg(fmt.Sprintf("validation error: %s", res.GetValidationDescription())),
			RequestId:             res.GetRequestId(),
			ValidationCode:        res.GetValidationCode(),
			ValidationDescription: res.GetValidationDescription(),
		}, nil
	}
	overallStatus, err := convertOverallStatus(res.GetOverallStatus())
	if err != nil {
		return nil, errors.Wrap(err, "error converting overall status")
	}
	if len(res.GetCustomerResponse()) > 1 {
		return nil, errors.New("more than one customer response found")
	}
	userScreeningResult, err := s.convertUserScreeningResult(res.GetCustomerResponse()[0])
	if err != nil {
		return nil, errors.Wrap(err, "error converting customer response")
	}
	return &amlVgPb.InitiateScreeningResponse{
		Status:                rpc.StatusOk(),
		RequestId:             res.GetRequestId(),
		OverallStatus:         overallStatus,
		ValidationCode:        res.GetValidationCode(),
		ValidationDescription: res.GetValidationDescription(),
		UserScreeningResult:   userScreeningResult,
	}, nil
}

func convertOverallStatus(status string) (amlVgPb.OverallStatus, error) {
	switch status {
	case "AcceptedByTW":
		return amlVgPb.OverallStatus_OVERALL_STATUS_ACCEPTED, nil
	case "RejectedByTW":
		return amlVgPb.OverallStatus_OVERALL_STATUS_REJECTED, nil
	default:
		return 0, errors.Errorf("unsupported overall status: %s", status)
	}
}

func (s *initiateScreeningRes) convertUserScreeningResult(tssCustomerResponse *tss.CustomerResponse) (*amlVgPb.UserScreeningResult, error) {
	validationOutcome, err := convertValidationOutcome(tssCustomerResponse.GetValidationOutcome())
	if err != nil {
		return nil, errors.Wrap(err, "error converting validation outcome")
	}
	suggestedAction, err := convertSuggestedAction(tssCustomerResponse.GetSuggestedAction())
	if err != nil {
		return nil, errors.Wrap(err, "error converting suggested action")
	}
	if len(tssCustomerResponse.GetPurposeResponse()) > 1 {
		return nil, errors.New("more than one purpose response found")
	}
	purposeScreeningResult, err := s.convertPurposeScreeningResult(tssCustomerResponse.GetPurposeResponse()[0])
	if err != nil {
		return nil, errors.Wrap(err, "error converting purpose response")
	}
	return &amlVgPb.UserScreeningResult{
		UserId:                 tssCustomerResponse.GetSourceSystemCustomerCode(),
		ValidationOutcome:      validationOutcome,
		SuggestedAction:        suggestedAction,
		PurposeScreeningResult: purposeScreeningResult,
		ValidationCode:         tssCustomerResponse.GetValidationCode(),
		ValidationDescription:  tssCustomerResponse.GetValidationDescription(),
		ValidationFailureCount: uint32(tssCustomerResponse.GetValidationFailureCount()),
	}, nil
}

func convertValidationOutcome(outcome string) (amlVgPb.ValidationOutcome, error) {
	switch outcome {
	case "Success":
		return amlVgPb.ValidationOutcome_VALIDATION_OUTCOME_SUCCESS, nil
	case "Failure":
		return amlVgPb.ValidationOutcome_VALIDATION_OUTCOME_FAILURE, nil
	default:
		return 0, errors.Errorf("unsupported validation outcome: %s", outcome)
	}
}

func convertSuggestedAction(action string) (amlVgPb.SuggestedAction, error) {
	switch action {
	case "Proceed":
		return amlVgPb.SuggestedAction_SUGGESTED_ACTION_PROCEED, nil
	case "Review":
		return amlVgPb.SuggestedAction_SUGGESTED_ACTION_REVIEW, nil
	case "Stop":
		return amlVgPb.SuggestedAction_SUGGESTED_ACTION_STOP, nil
	case "":
		return amlVgPb.SuggestedAction_SUGGESTED_ACTION_UNSPECIFIED, nil
	default:
		return 0, errors.Errorf("unsupported suggested action: %s", action)
	}
}

func (s *initiateScreeningRes) convertPurposeScreeningResult(tssPurposeResponse *tss.PurposeResponse) (*amlVgPb.PurposeScreeningResult, error) {
	var screeningResult *amlVgPb.ScreeningResult
	if tssPurposeResponse.GetData() != nil {
		var hits []*amlVgPb.Hit
		for _, rawHit := range tssPurposeResponse.GetData().GetHitResponse() {
			matchType, err := convertMatchType(rawHit.MatchType)
			if err != nil {
				return nil, errors.Wrap(err, "error converting match type")
			}
			hit := &amlVgPb.Hit{
				Source:                      rawHit.Source,
				WatchlistSourceId:           rawHit.WatchlistSourceId,
				MatchType:                   matchType,
				Score:                       rawHit.Score,
				ConfirmedMatchingAttributes: rawHit.ConfirmedMatchingAttributes,
			}
			hits = append(hits, hit)
		}
		screeningResult = &amlVgPb.ScreeningResult{
			HitsDetected: tssPurposeResponse.GetData().GetHitsDetected() == "Yes",
			HitsCount:    uint32(tssPurposeResponse.GetData().GetHitsCount()),
			ConfirmedHit: tssPurposeResponse.GetData().GetConfirmedHit() == "Yes",
			ReportData:   tssPurposeResponse.GetData().GetReportData(),
			CaseId:       tssPurposeResponse.GetData().GetCaseId(),
			CaseUrl:      tssPurposeResponse.GetData().GetCaseUrl(),
			Hits:         hits,
		}
	}
	return &amlVgPb.PurposeScreeningResult{
		Purpose:                tssPurposeResponse.GetPurpose(),
		PurposeCode:            tssPurposeResponse.GetPurposeCode(),
		ValidationCode:         tssPurposeResponse.GetValidationCode(),
		ValidationDescription:  tssPurposeResponse.GetValidationDescription(),
		ValidationFailureCount: uint32(tssPurposeResponse.GetValidationFailureCount()),
		ScreeningResult:        screeningResult,
	}, nil
}

func convertMatchType(matchType string) (amlVgPb.MatchType, error) {
	switch matchType {
	case "Confirm Hit", "Confirmed":
		return amlVgPb.MatchType_MATCH_TYPE_CONFIRMED, nil
	case "Probable":
		return amlVgPb.MatchType_MATCH_TYPE_PROBABLE, nil
	default:
		return 0, errors.Errorf("unsupported match type: %s", matchType)
	}
}

func (s *initiateScreeningRes) RedactResponseBody(ctx context.Context, responseBody []byte, contentType string) ([]byte, error) {
	return httpcontentredactor.GetInstance().Redact(ctx, responseBody, contentType, map[string]mask.MaskingStrategy{
		// Report data is a huge payload
		"ReportData": mask.MaskAllChars,
	})
}

func (s *initiateScreeningRes) HandleHttpError(_ context.Context, httpStatus int, b []byte) (proto.Message, error) {
	return nil, errors.New(fmt.Sprintf("http error, http_status = %v, response = %v", httpStatus, string(b)))
}
