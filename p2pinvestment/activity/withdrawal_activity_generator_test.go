package activity

import (
	"context"
	"testing"

	p2pPb "github.com/epifi/gamma/api/p2pinvestment"

	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"
)

func GetBaseReq() *GeneratorReq {
	return &GeneratorReq{
		Transaction: &p2pPb.InvestmentTransaction{
			Id:               "id",
			InvestorId:       "InvestorId",
			SchemeId:         "SchemeId",
			OrderClientReqId: "OrderClientReqId",
			PaymentRefId:     "PaymentRefId",
			VendorRefId:      "VendorRefId",
			Type:             p2pPb.InvestmentTransactionType_INVESTMENT_TRANSACTION_TYPE_WITHDRAWAL,
			Vendor:           p2pPb.Vendor_VENDOR_LIQUILOANS,
			Status:           p2pPb.InvestmentTransactionStatus_CREATED,
			SubStatus:        p2pPb.InvestmentTransactionSubStatus_INVESTMENT_TRANSACTION_SUB_STATUS_UNSPECIFIED,
			Details: &p2pPb.InvestmentTransactionDetails{
				Amount: &money.Money{
					CurrencyCode: "INR",
					Units:        10000,
				},
				Timeline: []*p2pPb.InvestmentTransactionDetails_Timeline{
					{
						Status: p2pPb.InvestmentTransactionStatus_CREATED,
						CreatedAt: &timestamp.Timestamp{
							Seconds: 1755699400,
						},
					},
				},
				OrderExternalId: "OrderExternalId",
			},
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
			UpdatedAt: &timestamp.Timestamp{
				Seconds: 1655710200,
			},
		},
	}
}

func GetBaseRes() *GeneratorResponse {
	return &GeneratorResponse{
		Activity: &p2pPb.Activity{
			Type:   p2pPb.ActivityType_ACTIVITY_TYPE_WITHDRAWAL,
			Status: p2pPb.ActivityStatus_ACTIVITY_STATUS_PROCESSING,
			CompletionEta: &timestamp.Timestamp{
				Seconds: 1755973740,
			},
			Amount: &money.Money{
				CurrencyCode: "INR",
				Units:        10000,
			},
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
			UpdatedAt: &timestamp.Timestamp{
				Seconds: 1655710200,
			},
			Timeline: []*p2pPb.ActivityStageDetails{
				{
					Stage:       p2pPb.ActivityStage_ACTIVITY_STAGE_WITHDRAW,
					StageStatus: p2pPb.ActivityStageStatus_ACTIVITY_STAGE_STATUS_PROCESSING,
					CreatedAt: &timestamp.Timestamp{
						Seconds: 1755699400,
					},
				},
			},
			Details: &p2pPb.Activity_WithdrawalActivityDetails{
				WithdrawalActivityDetails: &p2pPb.WithdrawalActivityDetails{
					OrderId: "OrderExternalId",
					Utr:     "PaymentRefId",
				},
			},
		},
	}
}

func GetReq1() *GeneratorReq {
	req := GetBaseReq()
	req.GetTransaction().GetDetails().Timeline = []*p2pPb.InvestmentTransactionDetails_Timeline{
		{
			Status: p2pPb.InvestmentTransactionStatus_CREATED,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
		{
			Status: p2pPb.InvestmentTransactionStatus_INITIATED,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
		{
			Status: p2pPb.InvestmentTransactionStatus_IN_PROGRESS,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
		{
			Status: p2pPb.InvestmentTransactionStatus_APPROVED,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
		{
			Status: p2pPb.InvestmentTransactionStatus_SETTLEMENT,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
		{
			Status: p2pPb.InvestmentTransactionStatus_SUCCESS,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
	}
	return req
}

func GetReq2() *GeneratorReq {
	req := GetBaseReq()
	req.GetTransaction().GetDetails().Timeline = []*p2pPb.InvestmentTransactionDetails_Timeline{
		{
			Status: p2pPb.InvestmentTransactionStatus_CREATED,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
		{
			Status: p2pPb.InvestmentTransactionStatus_INITIATED,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
		{
			Status: p2pPb.InvestmentTransactionStatus_IN_PROGRESS,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
		{
			Status: p2pPb.InvestmentTransactionStatus_APPROVED,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
		{
			Status: p2pPb.InvestmentTransactionStatus_SETTLEMENT,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
	}
	return req
}

func GetReq3() *GeneratorReq {
	req := GetBaseReq()
	req.GetTransaction().GetDetails().Timeline = []*p2pPb.InvestmentTransactionDetails_Timeline{
		{
			Status: p2pPb.InvestmentTransactionStatus_CREATED,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
		{
			Status: p2pPb.InvestmentTransactionStatus_INITIATED,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
		{
			Status: p2pPb.InvestmentTransactionStatus_IN_PROGRESS,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
		{
			Status: p2pPb.InvestmentTransactionStatus_APPROVED,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
	}
	return req
}

func GetReq4() *GeneratorReq {
	req := GetBaseReq()
	req.GetTransaction().GetDetails().Timeline = []*p2pPb.InvestmentTransactionDetails_Timeline{
		{
			Status: p2pPb.InvestmentTransactionStatus_CREATED,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
		{
			Status: p2pPb.InvestmentTransactionStatus_INITIATED,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
		{
			Status: p2pPb.InvestmentTransactionStatus_IN_PROGRESS,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
	}
	return req
}

func GetReq5() *GeneratorReq {
	req := GetBaseReq()
	req.GetTransaction().GetDetails().Timeline = []*p2pPb.InvestmentTransactionDetails_Timeline{
		{
			Status: p2pPb.InvestmentTransactionStatus_CREATED,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
		{
			Status: p2pPb.InvestmentTransactionStatus_INITIATED,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
	}
	return req
}

func GetReq6() *GeneratorReq {
	req := GetBaseReq()
	req.GetTransaction().GetDetails().Timeline = []*p2pPb.InvestmentTransactionDetails_Timeline{
		{
			Status: p2pPb.InvestmentTransactionStatus_CREATED,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
	}
	return req
}

func GetRes1() *GeneratorResponse {
	res := GetBaseRes()
	res.Activity.Timeline = []*p2pPb.ActivityStageDetails{
		{
			Stage:       p2pPb.ActivityStage_ACTIVITY_STAGE_WITHDRAW,
			StageStatus: p2pPb.ActivityStageStatus_ACTIVITY_STAGE_STATUS_SUCCESS,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
		{
			Stage:       p2pPb.ActivityStage_ACTIVITY_STAGE_WITHDRAW_CONFIRMATION,
			StageStatus: p2pPb.ActivityStageStatus_ACTIVITY_STAGE_STATUS_SUCCESS,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
		{
			Stage:       p2pPb.ActivityStage_ACTIVITY_STAGE_WITHDRAW_PAYMENT,
			StageStatus: p2pPb.ActivityStageStatus_ACTIVITY_STAGE_STATUS_SUCCESS,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
			CompletionEta: &timestamp.Timestamp{
				Seconds: 1755973740,
			},
		},
	}
	return res
}

func GetRes2() *GeneratorResponse {
	res := GetBaseRes()
	res.Activity.Timeline = []*p2pPb.ActivityStageDetails{
		{
			Stage:       p2pPb.ActivityStage_ACTIVITY_STAGE_WITHDRAW,
			StageStatus: p2pPb.ActivityStageStatus_ACTIVITY_STAGE_STATUS_SUCCESS,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
		{
			Stage:       p2pPb.ActivityStage_ACTIVITY_STAGE_WITHDRAW_CONFIRMATION,
			StageStatus: p2pPb.ActivityStageStatus_ACTIVITY_STAGE_STATUS_SUCCESS,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
		{
			Stage:       p2pPb.ActivityStage_ACTIVITY_STAGE_WITHDRAW_PAYMENT,
			StageStatus: p2pPb.ActivityStageStatus_ACTIVITY_STAGE_STATUS_PROCESSING,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
			CompletionEta: &timestamp.Timestamp{
				Seconds: 1755973740,
			},
		},
	}
	return res
}

func GetRes3() *GeneratorResponse {
	res := GetBaseRes()
	res.Activity.Timeline = []*p2pPb.ActivityStageDetails{
		{
			Stage:       p2pPb.ActivityStage_ACTIVITY_STAGE_WITHDRAW,
			StageStatus: p2pPb.ActivityStageStatus_ACTIVITY_STAGE_STATUS_SUCCESS,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
		},
		{
			Stage:       p2pPb.ActivityStage_ACTIVITY_STAGE_WITHDRAW_CONFIRMATION,
			StageStatus: p2pPb.ActivityStageStatus_ACTIVITY_STAGE_STATUS_PROCESSING,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
			CompletionEta: &timestamp.Timestamp{
				Seconds: 1755973740,
			},
		},
	}
	return res
}

func GetRes4() *GeneratorResponse {
	res := GetBaseRes()
	res.Activity.Timeline = []*p2pPb.ActivityStageDetails{
		{
			Stage:       p2pPb.ActivityStage_ACTIVITY_STAGE_WITHDRAW,
			StageStatus: p2pPb.ActivityStageStatus_ACTIVITY_STAGE_STATUS_PROCESSING,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
			CompletionEta: &timestamp.Timestamp{
				Seconds: 1755973740,
			},
		},
	}
	return res
}

func GetRes5() *GeneratorResponse {
	res := GetBaseRes()
	res.Activity.Timeline = []*p2pPb.ActivityStageDetails{
		{
			Stage:       p2pPb.ActivityStage_ACTIVITY_STAGE_WITHDRAW,
			StageStatus: p2pPb.ActivityStageStatus_ACTIVITY_STAGE_STATUS_PENDING,
			CreatedAt: &timestamp.Timestamp{
				Seconds: 1755699400,
			},
			CompletionEta: &timestamp.Timestamp{
				Seconds: 1755973740,
			},
		},
	}
	return res
}

func TestWithdrawalActivityGenerator_GenerateActivity(t *testing.T) {
	t.Skip()
	type args struct {
		ctx context.Context
		req *GeneratorReq
	}
	tests := []struct {
		name    string
		args    args
		want    *GeneratorResponse
		wantErr bool
	}{
		{
			name: "tc1",
			args: args{
				ctx: nil,
				req: GetReq1(),
			},
			want:    GetRes1(),
			wantErr: false,
		},
		{
			name: "tc2",
			args: args{
				ctx: nil,
				req: GetReq2(),
			},
			want:    GetRes2(),
			wantErr: false,
		},
		{
			name: "tc3",
			args: args{
				ctx: nil,
				req: GetReq3(),
			},
			want:    GetRes2(),
			wantErr: false,
		},
		{
			name: "tc4",
			args: args{
				ctx: nil,
				req: GetReq4(),
			},
			want:    GetRes3(),
			wantErr: false,
		},
		{
			name: "tc5",
			args: args{
				ctx: nil,
				req: GetReq5(),
			},
			want:    GetRes4(),
			wantErr: false,
		},
		{
			name: "tc6",
			args: args{
				ctx: nil,
				req: GetReq6(),
			},
			want:    GetRes5(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &WithdrawalActivityGenerator{}
			got, err := w.GenerateActivity(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateActivity() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got.Activity, tt.want.Activity) {
				t.Errorf("GenerateActivity() got = %v, want %v", got, tt.want)
			}
		})
	}
}
