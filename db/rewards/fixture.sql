set timezone to 'UTC';

INSERT INTO public.reward_offers (id, status, active_since, active_till, display_since, display_till, created_at, updated_at, created_by, action_type, constraints_meta, reward_meta, display_meta, is_visible) VALUES ('5e5b62d8-a2f0-44cc-82de-7bc6284a3402', 'CREATED', '2021-01-01 00:00:00.000000', '2030-01-01 00:00:00.000000', '2020-01-01 00:00:00.000000', '2030-01-01 00:00:00.000000', '2020-09-09 11:01:20.902310', '2020-09-09 11:01:20.902310', 'CreatedBy-1', 'TXN', '{"expression":"TXN_AMOUNT\u003e100"}', '{"rewardAggregates":{"userAggregate":2,"actionAggregate":100},"defaultDecideTimeInSecs":10,"probability":1,"rewardConfigOptions":[{"rewardType":"FI_COINS","expressionProbabilityConfig":{"expression":"TXN_AMOUNT*0.1","probability":0.5}}]}', '{"displaySince":"2020-05-10T10:04:05+05:30","displayTill":"2030-05-10T10:04:05+05:30","displayType":"FRINGE","title":"title","steps":["steps"],"tncs":["tnc"],"icon":"icon_url"}', false);
INSERT INTO public.reward_offers (id, status, active_since, active_till, display_since, display_till, created_at, updated_at, created_by, action_type, constraints_meta, reward_meta, display_meta, is_visible) VALUES ('19eac8b3-0fda-4f4d-95ae-7ae563e8ca69', 'CREATED', '2021-01-01 00:00:00.000000', '2030-01-01 00:00:00.000000', '2020-01-01 00:00:00.000000', '2030-01-01 00:00:00.000000', '2020-09-09 11:01:20.905600', '2020-09-09 11:01:20.905600', 'CreatedBy-1', 'SIGN_UP', '{"expression":"ACCOUNT_BALANCE\u003e100"}', '{"rewardAggregates":{"userAggregate":2,"actionAggregate":100},"defaultDecideTimeInSecs":10,"probability":1,"rewardConfigOptions":[{"rewardType":"FI_COINS","fixedProbabilityConfig":{"value":10,"probability":0.5}}]}', '{"displaySince":"2020-05-10T10:04:05+05:30","displayTill":"2030-05-10T10:04:05+05:30","displayType":"FRINGE","title":"title","steps":["steps"],"tncs":["tnc"],"icon":"icon_url"}', true);
INSERT INTO public.reward_offers (id, status, active_since, active_till, display_since, display_till, created_at, updated_at, created_by, action_type, constraints_meta, reward_meta, display_meta, is_visible) VALUES ('18eac8b3-0fda-4f4d-95ae-7ae563e8ca69', 'CREATED', '2021-01-01 00:00:00.000000', '2030-01-01 00:00:00.000000', '2020-01-01 00:00:00.000000', '2030-01-01 00:00:00.000000', '2020-09-09 11:01:20.905600', '2020-09-09 11:01:20.905600', 'CreatedBy-1', 'SIGN_UP','{}','{"rewardAggregates" : {"userAggregate" : 2, "actionAggregate" : 10}}', '{"displaySince":"2020-05-10T10:04:05+05:30","displayTill":"2030-05-10T10:04:05+05:30","displayType":"FRINGE","title":"title","steps":["steps"],"tncs":["tnc"],"icon":"icon_url"}', true);
INSERT INTO public.reward_offers (id, status, active_since, active_till, display_since, display_till, created_at, updated_at, created_by, action_type, constraints_meta, reward_meta, display_meta, is_visible) VALUES ('18eac8b3-0fda-4f4d-95ae-7ae563e8ca79', 'ACTIVE', '2021-01-01 00:00:00.000000', '2030-01-01 00:00:00.000000', '2020-01-01 00:00:00.000000', '2030-01-01 00:00:00.000000', '2020-09-09 11:01:20.905600', '2020-09-09 11:01:20.905600', 'CreatedBy-1', 'SIGN_UP','{}','{"rewardAggregates" : {"userAggregate" : 5, "actionAggregate" : 25}}', '{"displaySince":"2020-05-10T10:04:05+05:30","displayTill":"2030-05-10T10:04:05+05:30","displayType":"FRINGE","title":"title","steps":["steps"],"tncs":["tnc"],"icon":"icon_url"}', true);
INSERT INTO public.reward_offers (id, status, active_since, active_till, display_since, display_till, created_at, updated_at, created_by, action_type, constraints_meta, reward_meta, display_meta, is_visible) VALUES ('18eac8b3-0fda-4f4d-95ae-7ae563e8ca89', 'ACTIVE', '2021-01-01 00:00:00.000000', '2030-01-01 00:00:00.000000', '2020-01-01 00:00:00.000000', '2030-01-01 00:00:00.000000', '2020-09-09 11:01:20.905600', '2020-09-09 11:01:20.905600', 'CreatedBy-1', 'SIGN_UP','{}','{}', '{"displaySince":"2020-05-10T10:04:05+05:30","displayTill":"2030-05-10T10:04:05+05:30","displayType":"FRINGE","title":"title","steps":["steps"],"tncs":["tnc"],"icon":"icon_url"}', true);
INSERT INTO public.reward_offers (id, status, active_since, active_till, display_since, display_till, created_at, updated_at, created_by, action_type, constraints_meta, reward_meta, display_meta, is_visible) VALUES ('5e5b62d8-a2f0-44cc-82de-7bc6284a3412', 'ACTIVE', '2021-01-01 00:00:00.000000', '2030-01-01 00:00:00.000000', '2020-01-01 00:00:00.000000', '2030-01-01 00:00:00.000000', '2020-09-09 11:01:20.902310', '2020-09-09 11:01:20.902310', 'CreatedBy-1', 'ORDER', '{"expression":"TXN_AMOUNT\u003e100"}', '{"rewardAggregates":{"userAggregate":2,"actionAggregate":100},"defaultDecideTimeInSecs":10,"probability":1,"rewardConfigOptions":[{"rewardType":"FI_COINS","expressionProbabilityConfig":{"expression":"TXN_AMOUNT*0.1","probability":0.5}}]}', '{"displaySince":"2020-05-10T10:04:05+05:30","displayTill":"2030-05-10T10:04:05+05:30","displayType":"FRINGE","title":"title","steps":["steps"],"tncs":["tnc"],"icon":"icon_url"}', false);
INSERT INTO public.reward_offers (id, status, active_since, active_till, display_since, display_till, created_at, updated_at, created_by, action_type, constraints_meta, reward_meta, display_meta, is_visible, group_id, supported_platform) VALUES ('5e5b62d8-a2f0-44cc-82de-7bc6284a3413', 'ACTIVE', '2021-01-01 00:00:00.000000', '2030-01-01 00:00:00.000000', '2020-01-01 00:00:00.000000', '2030-01-01 00:00:00.000000', '2020-09-09 11:01:20.902310', '2020-09-09 11:01:20.902310', 'CreatedBy-1', 'ORDER', '{"expression":"TXN_AMOUNT\u003e100"}', '{"rewardAggregates":{"userAggregate":2,"actionAggregate":100},"defaultDecideTimeInSecs":10,"probability":1,"rewardConfigOptions":[{"rewardType":"FI_COINS","expressionProbabilityConfig":{"expression":"TXN_AMOUNT*0.1","probability":0.5}}]}', '{"displaySince":"2020-05-10T10:04:05+05:30","displayTill":"2030-05-10T10:04:05+05:30","displayType":"FRINGE","title":"title","steps":["steps"],"tncs":["tnc"],"icon":"icon_url"}', false, '18b949a4-d0be-40cf-bc49-1f65916840fc', 'ANDROID');
INSERT INTO public.reward_offers (id, status, active_since, active_till, display_since, display_till, created_at, updated_at, created_by, action_type, constraints_meta, reward_meta, display_meta, is_visible, group_id, supported_platform) VALUES ('5e5b62d8-a2f0-44cc-82de-7bc6284a3414', 'ACTIVE', '2021-01-01 00:00:00.000000', '2030-01-01 00:00:00.000000', '2020-01-01 00:00:00.000000', '2030-01-01 00:00:00.000000', '2020-09-09 11:01:20.902310', '2020-09-09 11:01:20.902310', 'CreatedBy-1', 'ORDER', '{"expression":"TXN_AMOUNT\u003e100"}', '{"rewardAggregates":{"userAggregate":2,"actionAggregate":100},"defaultDecideTimeInSecs":10,"probability":1,"rewardConfigOptions":[{"rewardType":"FI_COINS","expressionProbabilityConfig":{"expression":"TXN_AMOUNT*0.1","probability":0.5}}]}', '{"displaySince":"2020-05-10T10:04:05+05:30","displayTill":"2030-05-10T10:04:05+05:30","displayType":"FRINGE","title":"title","steps":["steps"],"tncs":["tnc"],"icon":"icon_url"}', false, '18b949a4-d0be-40cf-bc49-1f65916840fc', 'ANDROID');
INSERT INTO public.reward_offers (id, status, active_since, active_till, display_since, display_till, created_at, updated_at, created_by, action_type, constraints_meta, reward_meta, display_meta, is_visible, group_id, offer_type, supported_platform) VALUES ('6e5b62d8-a2f0-44cc-82de-7bc6284a3415', 'ACTIVE', '2021-01-01 00:00:00.000000', '2030-01-01 00:00:00.000000', '2020-01-01 00:00:00.000000', '2030-01-01 00:00:00.000000', '2020-09-09 11:01:20.902310', '2020-09-09 11:01:20.902310', 'CreatedBy-1', 'ORDER', '{"expression":"TXN_AMOUNT\u003e100"}', '{"rewardAggregates":{"userAggregate":2,"actionAggregate":100},"defaultDecideTimeInSecs":10,"probability":1,"rewardConfigOptions":[{"rewardType":"FI_COINS","expressionProbabilityConfig":{"expression":"TXN_AMOUNT*0.1","probability":0.5}}]}', '{"displaySince":"2020-05-10T10:04:05+05:30","displayTill":"2030-05-10T10:04:05+05:30","displayType":"FRINGE","title":"title","steps":["steps"],"tncs":["tnc"],"icon":"icon_url"}', false, '18b949a4-d0be-40cf-bc49-1f65916840fc', 'SALARY_PROGRAM_OFFER', 'IOS');
INSERT INTO public.reward_offers (id, status, active_since, active_till, display_since, display_till, created_at, updated_at, created_by, action_type, constraints_meta, reward_meta, display_meta, is_visible, group_id, display_segment_id, is_display_segment_excluded) VALUES ('5e5b62d8-a2f0-44cc-82de-7bc6284a3568', 'ACTIVE', '2030-01-01 00:00:00.000000', '2031-01-01 00:00:00.000000', '2030-01-01 00:00:00.000000', '2031-01-01 00:00:00.000000', '2030-09-09 11:01:20.902310', '2030-09-09 11:01:20.902310', 'CreatedBy-1', 'ORDER', '{"expression":"TXN_AMOUNT\u003e100"}', '{"rewardAggregates":{"userAggregate":2,"actionAggregate":100},"defaultDecideTimeInSecs":10,"probability":1,"rewardConfigOptions":[{"rewardType":"FI_COINS","expressionProbabilityConfig":{"expression":"TXN_AMOUNT*0.1","probability":0.5}}]}', '{"displaySince":"2020-05-10T10:04:05+05:30","displayTill":"2030-05-10T10:04:05+05:30","displayType":"FRINGE","title":"title","steps":["steps"],"tncs":["tnc"],"icon":"icon_url"}', true, '18b949a4-d0be-40cf-bc49-1f65916840fc', 'AWS_segment-id-1', true);
INSERT INTO public.reward_offers (id, status, active_since, active_till, display_since, display_till, created_at, updated_at, created_by, action_type, constraints_meta, reward_meta, display_meta, is_visible, offer_type, tags, group_id, ref_reward_offer_id, display_segment_id, is_display_segment_excluded, supported_platform, display_segment_expression) VALUES ('8738757f-3fc0-453d-a1ee-40f8b918e4ff', 'ACTIVE', '2022-07-15 06:15:01.000000 +00:00', '2024-01-15 06:15:01.000000 +00:00', '2022-07-15 06:15:01.000000 +00:00', '2024-01-15 18:29:59.000000 +00:00', '2022-12-26 11:16:36.478008 +00:00', '2022-12-26 11:16:36.478008 +00:00', 'Divyadeep', 'CREDIT_CARD_TRANSACTION', '{"expression":"TXN_TYPE == ''DEBIT'' && TXN_AMOUNT >= 100"}', '{  "rewardAggregates": {    "userAggregate": 100  },  "rewardLockTimeConfig": {    "relativeTimeInMinutes": 5  },  "autoClaimTimeConfig": {    "relativeTimeInMinutes": 5  },  "defaultDecideTimeInSecs": 45,  "probability": 1,  "rewardConfigOptions": [    {      "rewardType": "FI_COINS",      "displayConfig": {        "beforeClaimTextExpression": "Fi-Coins",        "afterClaimTextExpression": "Fi-Coins",        "icon": "https://epifi-icons.pointz.in/rewards/ficoin-2.svg",        "bgColor": "#00B899"      },      "rewardProcessingTimeConfig": {        "relativeTimeInMinutes": 0      },      "expressionProbabilityConfig": {        "expression": "GetRewardAmount()",        "probability": 1      }    }  ],  "rewardNotificationConfig": {    "earnedRewardNotificationConfig": {      "notificationType": "SYSTEM_TRAY"    }  }}', '{"displaySince":"2022-07-15T11:45:01+05:30","displayTill":"2024-01-15T23:59:59+05:30","displayType":"HEADLINE","title":"Credit card 1x reward","steps":["Do a CC txn."],"tncs":["Valid from 09th December 2021, 6 pm IST to 31st December 2022","Reward would be given only once during the reward duration","Reward programs can be changed or terminated without prior notice at the Company''s discretion","User agrees that their participation in the rewards programme of the Company constitutes their understanding of and agreement to reward specific terms and conditions","Please also note that users of our Platform residing in the State of Tamil Nadu are not eligible to participate in specific rewards/offers as per the applicable law in Tamil Nadu. Thus, users residing in Tamil Nadu are requested not to participate in offers relating to cash backs etc"],"icon":"https://epifi-icons.pointz.in/rewards/Add+funds.svg","bgColor":"#00B899","actionDesc":"NEW Credit card 1x reward NEW","cta":{"name":"Add Funds","deeplink":{"screen":"TRANSFER_IN"}},"shortDesc":"CC txn"}', true, 'CREDIT_CARD_SPENDS_1X_OFFER', '{"Tags": null}', null, null, null, null, 'PLATFORM_UNSPECIFIED', null);
INSERT INTO public.reward_offers (id, status, active_since, active_till, display_since, display_till, created_at, updated_at, created_by, action_type, constraints_meta, reward_meta, display_meta, is_visible) VALUES ('2b99dedf-63ae-4670-a68b-bad4b54da6c4', 'ACTIVE', '2021-01-01 00:00:00.000000', '2030-01-01 00:00:00.000000', '2020-01-01 00:00:00.000000', '2030-01-01 00:00:00.000000', '2020-09-09 11:01:20.902310', '2020-09-09 11:01:20.902310', 'CreatedBy-1', 'CREDIT_CARD_TRANSACTION', '{"expression":"true"}', '{"rewardAggregates":{"userAggregate":2,"actionAggregate":100,"cc1xRewardUnitsAccountIdLevelMonthlyCapAggregate":{"unitsCaps":[{"rewardType":"FI_COINS","units":510}]}},"defaultDecideTimeInSecs":10,"probability":1,"rewardConfigOptions":[{"rewardType":"FI_COINS","expressionProbabilityConfig":{"expression":"TXN_AMOUNT*0.1","probability":0.5}}]}', '{"displaySince":"2020-05-10T10:04:05+05:30","displayTill":"2030-05-10T10:04:05+05:30","displayType":"FRINGE","title":"title","steps":["steps"],"tncs":["tnc"],"icon":"icon_url"}', false);
INSERT INTO public.reward_offers (id, status, active_since, active_till, display_since, display_till, created_at, updated_at, created_by, action_type, constraints_meta, reward_meta, display_meta, is_visible) VALUES ('75021f37-aa95-43c5-9292-74826b333f97', 'ACTIVE', '2021-01-01 00:00:00.000000', '2050-01-01 00:00:00.000000', '2020-01-01 00:00:00.000000', '2050-01-01 00:00:00.000000', '2020-09-09 11:01:20.905600', '2020-09-09 11:01:20.905600', 'CreatedBy-1', 'ORDER','{}','{"rewardAggregates" : {"userAggregate" : 5, "actionAggregate" : 25, "rewardUnitsCapMonthlyUserAggregate":{"unitsCaps":[{"rewardType":"FI_COINS","units":1000}, {"rewardType":"CREDIT_CARD_BILL_ERASER","units":150}]}}}', '{"displaySince":"2020-05-10T10:04:05+05:30","displayTill":"2030-05-10T10:04:05+05:30","displayType":"FRINGE","title":"title","steps":["steps"],"tncs":["tnc"],"icon":"icon_url"}', true);
INSERT INTO public.reward_offers (id, status, active_since, active_till, display_since, display_till, created_at, updated_at, created_by, action_type, constraints_meta, reward_meta, display_meta, is_visible) VALUES ('5afc94c1-4f3a-492a-8527-a4364bc2509e', 'ACTIVE', '2021-01-01 00:00:00.000000', '2050-01-01 00:00:00.000000', '2020-01-01 00:00:00.000000', '2050-01-01 00:00:00.000000', '2020-09-09 11:01:20.905600', '2020-09-09 11:01:20.905600', 'CreatedBy-1', 'ORDER','{}','{"rewardAggregates" : {"userAggregate" : 5, "actionAggregate" : 25, "rewardUnitsCapMonthlyUserAggregate":{"unitsCaps":[{"rewardType":"FI_COINS","units":1000}]}}}', '{"displaySince":"2020-05-10T10:04:05+05:30","displayTill":"2030-05-10T10:04:05+05:30","displayType":"FRINGE","title":"title","steps":["steps"],"tncs":["tnc"],"icon":"icon_url"}', true);
INSERT INTO public.reward_offers (id, status, active_since, active_till, display_since, display_till, created_at, updated_at, created_by, action_type, constraints_meta, reward_meta, display_meta, is_visible) VALUES ('5afc94c1-4f3a-492a-8527-a4364bc1235a', 'ACTIVE', '2021-01-01 00:00:00.000000', '2050-01-01 00:00:00.000000', '2020-01-01 00:00:00.000000', '2050-01-01 00:00:00.000000', '2020-09-09 11:01:20.905600', '2020-09-09 11:01:20.905600', 'CreatedBy-1', 'ORDER','{}','{"rewardAggregates" : {"userAggregate" : 5, "actionAggregate" : 25, "rewardUnitsCapUserAggregate": {"unitsCaps":[{"rewardType":"CREDIT_CARD_BILL_ERASER","units":100}]}}}', '{"displaySince":"2020-05-10T10:04:05+05:30","displayTill":"2030-05-10T10:04:05+05:30","displayType":"FRINGE","title":"title","steps":["steps"],"tncs":["tnc"],"icon":"icon_url"}', true);
INSERT INTO public.reward_offers (id, status, active_since, active_till, display_since, display_till, created_at, updated_at, created_by, action_type, constraints_meta, reward_meta, display_meta, is_visible, group_id) VALUES ('75f0b4b9-ae8b-4d35-9659-3d1124a782ca', 'ACTIVE', '2021-01-01 00:00:00.000000', '2050-01-01 00:00:00.000000', '2020-01-01 00:00:00.000000', '2050-01-01 00:00:00.000000', '2020-09-09 11:01:20.905600', '2020-09-09 11:01:20.905600', 'CreatedBy-1', 'ORDER','{}','{"rewardAggregates" : {"userAggregate" : 5, "actionAggregate" : 25, "rewardUnitsCapMonthlyUserAggregate":{"unitsCaps":[{"rewardType":"FI_COINS","units":1000}]}}}', '{"displaySince":"2020-05-10T10:04:05+05:30","displayTill":"2030-05-10T10:04:05+05:30","displayType":"FRINGE","title":"title","steps":["steps"],"tncs":["tnc"],"icon":"icon_url"}', true, 'f68b8299-d038-485e-a59c-606f53bd7f9e');
-- generates locked rewards
INSERT INTO public.reward_offers (id, status, active_since, active_till, display_since, display_till, created_at, updated_at, created_by, action_type, constraints_meta, reward_meta, display_meta, is_visible, group_id, unlock_event, unlock_meta) VALUES ('dabfc41f-69a4-44f1-a2b2-332101f7405a', 'ACTIVE', '2021-01-01 00:00:00.000000', '2050-01-01 00:00:00.000000', '2020-01-01 00:00:00.000000', '2050-01-01 00:00:00.000000', '2020-09-09 11:01:20.905600', '2020-09-09 11:01:20.905600', 'CreatedBy-1', 'ORDER','{}','{"rewardAggregates" : {}}', '{}', false, '', 'ORDER', '{"constraint": {"expression": "true"}}');
INSERT INTO public.reward_offers (id, status, active_since, active_till, display_since, display_till, created_at, updated_at, created_by, action_type, constraints_meta, reward_meta, display_meta, is_visible, group_id, unlock_event, unlock_meta) VALUES ('dabfc41f-69a4-44f1-a2b2-332101f7405b', 'TERMINATED', '2021-01-01 00:00:00.000000', '2050-01-01 00:00:00.000000', '2020-01-01 00:00:00.000000', '2050-01-01 00:00:00.000000', '2020-09-09 11:01:20.905600', '2020-09-09 11:01:20.905600', 'CreatedBy-1', 'ORDER','{}','{"rewardAggregates" : {}}', '{}', false, '', 'ORDER', '{"constraint": {"expression": "true"}}');
-- generates reward projections
INSERT INTO public.reward_offers (id, status, active_since, active_till, display_since, display_till, created_at, updated_at, created_by, action_type, constraints_meta, reward_meta, display_meta, is_visible, group_id, generation_type) VALUES ('afa0ba3f-2941-430b-85ba-24eac8a59970', 'ACTIVE', '2021-01-01 00:00:00.000000', '2050-01-01 00:00:00.000000', '2020-01-01 00:00:00.000000', '2050-01-01 00:00:00.000000', '2020-09-09 11:01:20.905600', '2020-09-09 11:01:20.905600', 'CreatedBy-1', 'ORDER', '{"expression":"true"}','{"rewardAggregates" : {"userAggregate" : 5, "actionAggregate" : 25, "rewardConfigOptions":[{"rewardType":"FI_COINS","expressionProbabilityConfig":{"expression":"TXN_AMOUNT*1","probability":1}}]}}', '{}', true, '', 'GENERATION_TYPE_PROJECTION');


INSERT INTO public.rewards (id, type, actor_id, ref_id, created_at, updated_at, status, sub_status, offer_id, reward_options, chosen_reward, is_visible) VALUES
('RW200911WjgI+xGYRY+0PhNl96WmYA==', 'CASH', 'act-5', 'txn-1', '2020-09-11 09:13:11.061754', '2020-09-11 09:13:11.061754', 'CREATED', 'SUB_STATUS_UNSPECIFIED', 'offer-1', '{"options":[{"cash":{"amount":{"currencyCode":"INR","units":"10"}}},{"fiCoins":{"expiresAt":"1970-01-02T10:12:03Z"}}]}', '{}', false),
('RW200911WjgI+xGYRY+0PhNl96WmWA==', 'CASH', 'act-6', 'txn-1', '2020-09-11 09:13:11.061754', '2020-09-11 09:13:11.061754', 'CREATED', 'SUB_STATUS_UNSPECIFIED', 'offer-1', '{"options":[{"cash":{"amount":{"currencyCode":"INR","units":"10"}}},{"fiCoins":{"expiresAt":"1970-01-02T10:12:03Z"}}]}', '{}', false),
('RW200911WjgI+xGYRY+0PhNl96WmYB==', 'FI_COINS', 'act-5', 'txn-2', '2020-09-12 09:13:11.061754', '2020-09-11 09:13:11.061754', 'PROCESSING_PENDING', 'SUB_STATUS_UNSPECIFIED', 'offer-1', '{"defaultDecideTimeInSecs":45,"unlockDate":"2021-06-29T13:44:52.490136776Z","options":[{"id":"f81c541f-7649-49fd-af67-d9be21fc0ed1","display":{"title":"Fi-Coins 26 hours later","icon":"https://epifi-icons.pointz.in/rewards/ficoin-2.svg","bgColor":"#00B899","beforeClaimTitle":"Fi-Coins 26 hours later","afterClaimTitle":"Fi-Coins"},"rewardProcessingTimeConfig":{"relativeTimeInMinutes":1560},"rewardType":"FI_COINS","fiCoins":{"units":527,"expiresAt":"2023-06-29T13:44:52.490100276Z"}},{"id":"9879a5a0-ce93-4af3-94a8-c74169789455","display":{"title":"Fi-Coins 15 days later","icon":"https://epifi-icons.pointz.in/rewards/ficoin-2.svg","bgColor":"#00B899","beforeClaimTitle":"Fi-Coins 15 days later","afterClaimTitle":"Fi-Coins"},"rewardProcessingTimeConfig":{"relativeTimeInMinutes":21600},"rewardType":"FI_COINS","fiCoins":{"units":556,"expiresAt":"2023-06-29T13:44:52.490131086Z"}}],"actionDetails":"Earned for saving money on Fi"}', '{"id":"f81c541f-7649-49fd-af67-d9be21fc0ed1","display":{"title":"Fi-Coins 26 hours later","icon":"https://epifi-icons.pointz.in/rewards/ficoin-2.svg","bgColor":"#00B899","beforeClaimTitle":"Fi-Coins 26 hours later","afterClaimTitle":"Fi-Coins"},"processingDate":"2021-06-30T16:34:08.355116408Z","rewardProcessingTimeConfig":{"relativeTimeInMinutes":1560},"rewardType":"FI_COINS","fiCoins":{"units":527,"expiresAt":"2023-06-29T13:44:52.490100276Z"}}', true),
('RW200911WjgI+xGYRY+0PhNl96WmYC==', 'CASH', 'act-5', 'txn-3', '2020-09-13 09:13:11.061754', '2020-09-11 09:13:11.061754', 'CREATED', 'SUB_STATUS_UNSPECIFIED', 'offer-1', '{"options":[{"cash":{"amount":{"currencyCode":"INR","units":"10"}}},{"fiCoins":{"expiresAt":"1970-01-02T10:12:03Z"}}]}', '{}', true),
('RW200911WjgI+xGYRY+0PhNl96WmYD==', 'CASH', 'act-5', 'txn-4', '2020-09-14 09:13:11.061754', '2020-09-11 09:13:11.061754', 'CREATED', 'SUB_STATUS_UNSPECIFIED', 'offer-1', '{"options":[{"cash":{"amount":{"currencyCode":"INR","units":"10"}}},{"fiCoins":{"expiresAt":"1970-01-02T10:12:03Z"}}]}', '{}', true),
('RW200911WjgI+xGYRY+0PhNl96WmYY==', 'CASH', 'act-5', 'txn-7', '2020-09-14 09:13:11.061754', '2020-09-11 09:13:11.061754', 'CREATED', 'SUB_STATUS_UNSPECIFIED', 'offer-2', '{"options":[{"cash":{"amount":{"currencyCode":"INR","units":"10"}}},{"fiCoins":{"expiresAt":"1970-01-02T10:12:03Z"}}]}', '{}', true),
('RW200911WjgI+xGYRY+0PhNl96WmYX==', 'CASH', 'act-1-1', 'txn-8', '2020-09-14 09:13:11.061754', '2020-09-11 09:13:11.061754', 'CREATED', 'SUB_STATUS_UNSPECIFIED', '5e5b62d8-a2f0-44cc-82de-7bc6284a3413', '{"options":[{"cash":{"amount":{"currencyCode":"INR","units":"10"}}},{"fiCoins":{"expiresAt":"1970-01-02T10:12:03Z"}}]}', '{}', true),
('RW200923WjgI+xGYRY+0PhNl96WmYX==', 'REWARD_TYPE_UNSPECIFIED', 'act-10', 'txn-10', '2023-09-20 09:13:11.061754', '2023-09-20 09:13:11.061754', 'LOCKED', 'SUB_STATUS_EXPLICITLY_LOCKED', 'dabfc41f-69a4-44f1-a2b2-332101f7405a', '{"options":[{"fiCoins":{"expiresAt":"1970-01-02T10:12:03Z"}}]}', '{}', true),
('RW200923Wijk+xGYRY+0PhNl96WmYX==', 'REWARD_TYPE_UNSPECIFIED', 'act-11', 'txn-11', '2023-09-20 09:13:11.061754', '2023-09-20 09:13:11.061754', 'LOCKED', 'SUB_STATUS_IMPLICITLY_LOCKED', 'dabfc41f-69a4-44f1-a2b2-332101f7405a', '{"options":[{"fiCoins":{"expiresAt":"1970-01-02T10:12:03Z"}}]}', '{}', true);

INSERT INTO public.rewards (id, type, actor_id, ref_id, created_at, updated_at, status, sub_status, offer_id, reward_options, chosen_reward, is_visible, offer_type, external_ref, global_dedupe_id, external_id) VALUES ('RW200911WjgI+xGYRY+0PhNl96WmYE==', 'REWARD_TYPE_UNSPECIFIED', 'act-6', 'txn-5', '2020-09-14 09:13:11.061754', '2020-09-11 09:13:11.061754', 'CREATED', 'SUB_STATUS_UNSPECIFIED', 'offer-2', '{"options":[{"cash":{"amount":{"currencyCode":"INR","units":"10"}}},{"fiCoins":{"expiresAt":"1970-01-02T10:12:03Z"}}]}', '{}', true,'REFERRAL_REFEREE_OFFER','external-ref-1', 'global-dedupe-id-1', 'external-id-1');
INSERT INTO public.rewards (id, type, actor_id, ref_id, created_at, updated_at, status, sub_status, offer_id, reward_options, chosen_reward, is_visible, offer_type, external_ref, global_dedupe_id) VALUES ('RW200912WjgI+xGYRY+0PhNl96WmYE==', 'REWARD_TYPE_UNSPECIFIED', 'act-6', 'txn-6', '2020-09-14 09:13:11.061754', '2020-09-11 09:13:11.061754', 'CREATED', 'SUB_STATUS_UNSPECIFIED', 'offer-3', '{"options":[{"cash":{"amount":{"currencyCode":"INR","units":"10"}}},{"fiCoins":{"expiresAt":"1970-01-02T10:12:03Z"}}]}', '{}', true,'REFERRAL_REFERRER_OFFER','external-ref-2', 'global-dedupe-id-2');
INSERT INTO public.rewards (id, type, actor_id, ref_id, created_at, updated_at, status, sub_status, offer_id, reward_options, chosen_reward, is_visible, action_type) VALUES ('RW200911WjgI+xGYRY+0PhNl96WmYZ==', 'CASH', 'act-1-1', 'txn-9', '2020-09-14 09:13:11.061754', '2020-09-11 09:13:11.061754', 'CREATED', 'SUB_STATUS_UNSPECIFIED', '5e5b62d8-a2f0-44cc-82de-7bc6284a3414', '{"options":[{"cash":{"amount":{"currencyCode":"INR","units":"10"}}},{"fiCoins":{"expiresAt":"1970-01-02T10:12:03Z"}}]}', '{}', true, 'ORDER');
INSERT INTO public.rewards (id, type, actor_id, ref_id, created_at, updated_at, status, sub_status, offer_id, reward_options, chosen_reward, is_visible, offer_type, action_type) VALUES ('RW200912WkgI+xGYRY+0PhNl96WmAZ==', 'CASH', 'act-1-2', 'txn-10', '2122-06-08 00:00:00.000000', '2122-06-08 00:00:00.000000', 'CREATED', 'SUB_STATUS_UNSPECIFIED', 'referral-referrer-offer-1', '{"options":[{"cash":{"amount":{"currencyCode":"INR","units":"10"}}},{"fiCoins":{"expiresAt":"1970-01-02T10:12:03Z"}}]}', '{}', true, 'REFERRAL_REFERRER_OFFER', 'ORDER');
INSERT INTO public.rewards (id, type, actor_id, ref_id, created_at, updated_at, status, sub_status, offer_id, reward_options, chosen_reward, is_visible, offer_type, action_type) VALUES ('RW200912WkgI+xGYRY+0PhNl96WmBZ==', 'FI_COINS', 'act-1-2', 'txn-11', '2122-06-08 00:00:00.000000', '2122-06-08 00:00:00.000000', 'CREATED', 'SUB_STATUS_UNSPECIFIED', 'referral-referrer-offer-1', '{"options":[{"cash":{"amount":{"currencyCode":"INR","units":"10"}}},{"fiCoins":{"expiresAt":"1970-01-02T10:12:03Z"}}]}', '{}', true, 'REFERRAL_REFEREE_OFFER', 'ORDER');

INSERT INTO public.rewards (id, type, actor_id, ref_id, created_at, updated_at, status, sub_status, offer_id, reward_options, chosen_reward, is_visible, processing_ref, external_id, reward_display, offer_type, external_ref, global_dedupe_id, deleted_at, clawback_ref_id, clawback_reason, tags, action_type, reward_metadata, claim_type, action_time) VALUES
('RW230201VsQE+Y0bSGum/Zk4LacC+w==', 'FI_COINS', 'AC220704UJONP+gHTx2AURVKqgX4Jw==', 'cc-txn-1', '2023-02-01 19:48:54.850579 +00:00', '2023-02-01 19:53:55.148009 +00:00', 'CREATED', 'credited fi coins', '8738757f-3fc0-453d-a1ee-40f8b918e4ff', '{"defaultDecideTimeInSecs":45,"unlockDate":"2023-02-01T19:53:54.841406807Z","options":[{"id":"761c9483-67d2-4d24-aef0-ffa12e9882c4","display":{"title":"Fi-Coins","icon":"https://epifi-icons.pointz.in/rewards/ficoin-2.svg","bgColor":"#00B899","beforeClaimTitle":"Fi-Coins","afterClaimTitle":"Fi-Coins"},"rewardProcessingTimeConfig":{"relativeTimeInMinutes":0},"rewardType":"FI_COINS","rewardUnitsCalculationInfo":{"rewardUnitsCalculationEntries":[{"rewardValue":100}]},"fiCoins":{"units":100,"expiresAt":"2030-02-01T19:48:54.841395627Z"}}],"actionDetails":"NEW Credit card 1x reward NEW","autoClaimTime":"2023-02-01T19:53:54.841407397Z"}', '{"id":"761c9483-67d2-4d24-aef0-ffa12e9882c4","display":{"title":"Fi-Coins","icon":"https://epifi-icons.pointz.in/rewards/ficoin-2.svg","bgColor":"#00B899","beforeClaimTitle":"Fi-Coins","afterClaimTitle":"Fi-Coins"},"processingDate":"2023-02-01T19:53:54.981737178Z","rewardProcessingTimeConfig":{"relativeTimeInMinutes":0},"rewardType":"FI_COINS","rewardUnitsCalculationInfo":{"rewardUnitsCalculationEntries":[{"rewardValue":100}]},"fiCoins":{"units":100,"expiresAt":"2030-02-01T19:48:54.841395627Z"}}', false, '2029f427-f2a6-41cb-b0ca-4f94faf538c0', 'external-id-14', '{}', 'CREDIT_CARD_SPENDS_1X_OFFER', null, null, null, null, 'CLAWBACK_REASON_UNSPECIFIED', null, 'CREDIT_CARD_TRANSACTION', '{}', 'CLAIM_TYPE_AUTOMATIC','2023-02-01 19:48:54.850579 +00:00'),
('RW231201VsQE+Y0bSGum/Zk4LacC+w==', 'FI_COINS', 'AC220704UJONP+gHTx2AURVKqgX4Jw==', 'cc-txn-2', '2023-02-01 19:49:54.850579 +00:00', '2023-02-01 19:53:55.148009 +00:00', 'CREATED', 'credited fi coins', '8738757f-3fc0-453d-a1ee-40f8b918e4ff', '{"defaultDecideTimeInSecs":45,"unlockDate":"2023-02-01T19:53:54.841406807Z","options":[{"id":"761c9483-67d2-4d24-aef0-ffa12e9882c5","display":{"title":"Fi-Coins","icon":"https://epifi-icons.pointz.in/rewards/ficoin-2.svg","bgColor":"#00B899","beforeClaimTitle":"Fi-Coins","afterClaimTitle":"Fi-Coins"},"rewardProcessingTimeConfig":{"relativeTimeInMinutes":0},"rewardType":"FI_COINS","rewardUnitsCalculationInfo":{"rewardUnitsCalculationEntries":[{"rewardValue":200}]},"fiCoins":{"units":200,"expiresAt":"2030-02-01T19:48:54.841395627Z"}}],"actionDetails":"NEW Credit card 1x reward NEW","autoClaimTime":"2023-02-01T19:53:54.841407397Z"}', '{"id":"761c9483-67d2-4d24-aef0-ffa12e9882c4","display":{"title":"Fi-Coins","icon":"https://epifi-icons.pointz.in/rewards/ficoin-2.svg","bgColor":"#00B899","beforeClaimTitle":"Fi-Coins","afterClaimTitle":"Fi-Coins"},"processingDate":"2023-02-01T19:53:54.981737178Z","rewardProcessingTimeConfig":{"relativeTimeInMinutes":0},"rewardType":"FI_COINS","rewardUnitsCalculationInfo":{"rewardUnitsCalculationEntries":[{"rewardValue":200}]},"fiCoins":{"units":200,"expiresAt":"2030-02-01T19:48:54.841395627Z"}}', false, '2029f427-f2a6-41cb-b0ca-4f94faf538c1', 'external-id-15', '{}', 'CREDIT_CARD_SPENDS_1X_OFFER', null, null, null, null, 'CLAWBACK_REASON_UNSPECIFIED', null, 'CREDIT_CARD_TRANSACTION', '{}', 'CLAIM_TYPE_AUTOMATIC','2023-02-01 19:48:54.850579 +00:00'),
('RW232201VsQE+Y0bSGum/Zk4LacC+w==', 'FI_COINS', 'AC220704UJONP+gHTx2AURVKqgX4Jw==', 'cc-txn-3', '2023-02-01 19:45:54.850579 +00:00', '2023-02-01 19:53:55.148009 +00:00', 'PROCESSED', 'credited fi coins', '8738757f-3fc0-453d-a1ee-40f8b918e4ff', '{"defaultDecideTimeInSecs":45,"unlockDate":"2023-02-01T19:53:54.841406807Z","options":[{"id":"761c9483-67d2-4d24-aef0-ffa12e9882c5","display":{"title":"Fi-Coins","icon":"https://epifi-icons.pointz.in/rewards/ficoin-2.svg","bgColor":"#00B899","beforeClaimTitle":"Fi-Coins","afterClaimTitle":"Fi-Coins"},"rewardProcessingTimeConfig":{"relativeTimeInMinutes":0},"rewardType":"FI_COINS","rewardUnitsCalculationInfo":{"rewardUnitsCalculationEntries":[{"rewardValue":300}]},"fiCoins":{"units":200,"expiresAt":"2030-02-01T19:48:54.841395627Z"}}],"actionDetails":"NEW Credit card 1x reward NEW","autoClaimTime":"2023-02-01T19:53:54.841407397Z"}', '{"id":"761c9483-67d2-4d24-aef0-ffa12e9882c4","display":{"title":"Fi-Coins","icon":"https://epifi-icons.pointz.in/rewards/ficoin-2.svg","bgColor":"#00B899","beforeClaimTitle":"Fi-Coins","afterClaimTitle":"Fi-Coins"},"processingDate":"2023-02-01T19:53:54.981737178Z","rewardProcessingTimeConfig":{"relativeTimeInMinutes":0},"rewardType":"FI_COINS","rewardUnitsCalculationInfo":{"rewardUnitsCalculationEntries":[{"rewardValue":250}]},"fiCoins":{"units":250,"expiresAt":"2030-02-01T19:48:54.841395627Z"}}', false, '2029f427-f2a6-41cb-b0ca-4f94faf538c1', 'external-id-16', '{}', 'CREDIT_CARD_SPENDS_1X_OFFER', null, null, null, null, 'CLAWBACK_REASON_UNSPECIFIED', null, 'CREDIT_CARD_TRANSACTION', '{}', 'CLAIM_TYPE_MANUAL','2023-02-01 19:48:54.850579 +00:00'),
('RW232211VsQE+Y0bSGum/Zk4LacC+w==', 'FI_COINS', 'AC220704UJONP+gHTx2AURVKqgX4Jw==', 'cc-txn-4', '2023-02-01 19:45:54.850579 +00:00', '2023-02-01 19:53:55.148009 +00:00', 'PROCESSED', 'credited fi coins', '8738757f-3fc0-453d-a1ee-40f8b918e4ff', '{"defaultDecideTimeInSecs":45,"unlockDate":"2023-02-01T19:53:54.841406807Z","options":[{"id":"761c9483-67d2-4d24-aef0-ffa12e9882c5","display":{"title":"Fi-Coins","icon":"https://epifi-icons.pointz.in/rewards/ficoin-2.svg","bgColor":"#00B899","beforeClaimTitle":"Fi-Coins","afterClaimTitle":"Fi-Coins"},"rewardProcessingTimeConfig":{"relativeTimeInMinutes":0},"rewardType":"FI_COINS","rewardUnitsCalculationInfo":{"rewardUnitsCalculationEntries":[{"rewardValue":300}]},"fiCoins":{"units":200,"expiresAt":"2030-02-01T19:48:54.841395627Z"}}],"actionDetails":"NEW Credit card 1x reward NEW","autoClaimTime":"2023-02-01T19:53:54.841407397Z"}', '{"id":"761c9483-67d2-4d24-aef0-ffa12e9882c4","display":{"title":"Fi-Coins","icon":"https://epifi-icons.pointz.in/rewards/ficoin-2.svg","bgColor":"#00B899","beforeClaimTitle":"Fi-Coins","afterClaimTitle":"Fi-Coins"},"processingDate":"2023-02-01T19:53:54.981737178Z","rewardProcessingTimeConfig":{"relativeTimeInMinutes":0},"rewardType":"FI_COINS","rewardUnitsCalculationInfo":{"rewardUnitsCalculationEntries":[{"rewardValue":250}]},"fiCoins":{"units":250,"expiresAt":"2030-02-01T19:48:54.841395627Z"}}', false, '2029f427-f2a6-41cb-b0ca-4f94faf538c1', 'external-id-17', '{}', 'CREDIT_CARD_SPENDS_1X_OFFER', null, null, null, null, 'CLAWBACK_REASON_UNSPECIFIED', null, 'CREDIT_CARD_TRANSACTION', '{}', 'CLAIM_TYPE_MANUAL','2023-02-01 19:48:54.850579 +00:00');


--reward_offer_aggregates sql
INSERT INTO public.reward_offer_aggregates (id, offer_id, count, created_at, updated_at) VALUES
('fb068b70-d351-44a3-81e3-4ed19b3271aa', '18eac8b3-0fda-4f4d-95ae-7ae563e8ca69', 8, '2020-12-19 11:24:16.373457', '2020-12-23 17:26:09.198942');

--reward_offer_actor_aggregates sql
INSERT INTO public.reward_offer_actor_aggregates (id, offer_id, actor_id, count, created_at, updated_at) VALUES
('875cbee3-213b-4bc6-9a41-c565f35f44fd', '18eac8b3-0fda-4f4d-95ae-7ae563e8ca69', 'AC2011105fgWvFh2QbWmt+Zux/SVpA==', 1, '2020-11-11 09:49:36.343137', '2020-11-11 09:49:36.346361'),
('875cbee3-213b-4bc6-9a41-c565f35f44f6', '18eac8b3-0fda-4f4d-95ae-7ae563e8ca69', 'AC2011105fgWvFh2QbWmt+Zux/SVpB==', 0, '2020-11-11 09:49:36.343137', '2020-11-11 09:49:36.346361');

--reward_offer_group_actor_aggregates sql
INSERT INTO public.reward_offer_group_actor_aggregates (id, offer_group_id, actor_id, count, created_at, updated_at) VALUES
('a2bd5bf8-95d4-4a33-9d2a-7bd22eb33b00', 'a6298923-8c49-446b-911d-f9c781699d12', 'actor1', 1, '2020-11-11 09:49:36.343137', '2020-11-11 09:49:36.346361');

--lucky_draw_campaigns sql
INSERT INTO lucky_draw_campaigns (id, description, created_at, updated_at) VALUES
('0d6ae1f0-4016-4230-978a-5387de7d37c4', 'first lucky draw campaign', '2020-10-22 09:02:44.701237', '2020-10-22 09:02:44.701237');


--lucky_draws sql
INSERT INTO lucky_draws (id, campaign_id, registration_from, registration_till, lucky_draw_reveal_time, lucky_draw_config, lucky_draw_status, created_at, updated_at) VALUES
('bf7eb8dd-955c-488e-894f-376f4f3996a1', '0d6ae1f0-4016-4230-978a-5387de7d37c4', '2020-10-22 09:04:49.000000', '2020-10-23 12:51:29.000000', '2020-10-24 16:38:09.000000', '{"tieredRewardDistributionConfig": {"rewardDistributionUnits": [{"reward": {"cash": {"amount": {"units": "20000", "currencyCode": "INR"}}, "rewardType": "CASH", "display": {"icon": "https://icon-1-url.png", "bgColor": "#00B899", "afterClaimTitle": "Cash from lucky draw", "beforeClaimTitle": "Cash from lucky draw"}}, "distributionOrder": 1, "distributionQuantity": 1}, {"reward": {"fiCoins": {"units": "10000"}, "rewardType": "FI_COINS", "display": {"icon": "https://icon-2-url.png", "bgColor": "#00B899", "afterClaimTitle": "FiCoins from lucky draw", "beforeClaimTitle": "FiCoins from lucky draw"}}, "distributionOrder": 2, "distributionQuantity": 10}]}}', 'LUCKY_DRAW_STATUS_CREATED', '2020-10-22 09:05:48.045813', '2020-10-22 09:05:48.045813'),
('bf7eb8dd-955c-488e-894f-376f4f3996a2', '0d6ae1f0-4016-4230-978a-5387de7d37c4', '2020-10-22 09:04:49.000000', '2020-10-23 12:51:29.000000', '2020-10-25 16:38:09.000000', '{"tieredRewardDistributionConfig": {"rewardDistributionUnits": [{"reward": {"cash": {"amount": {"units": "20000", "currencyCode": "INR"}}, "rewardType": "CASH"}, "distributionOrder": 1, "distributionQuantity": 1}, {"reward": {"cash": {"amount": {"units": "1000", "currencyCode": "INR"}}, "rewardType": "CASH"}, "distributionOrder": 2, "distributionQuantity": 10}]}}', 'LUCKY_DRAW_STATUS_COMPLETED', '2020-10-22 09:05:48.045813', '2020-10-22 09:05:48.045813');

--lucky_draw_registrations sql
INSERT INTO lucky_draw_registrations (id, lucky_draw_id, actor_id, created_at, updated_at) VALUES
('58869436-4657-465d-a06d-61ad8d02c37d', 'bf7eb8dd-955c-488e-894f-376f4f3996a1', 'actor-1', '2020-10-22 09:06:15.121302', '2020-10-22 09:06:15.121302'),
('58869436-4657-465d-a06d-61ad8d02c37a', 'bf7eb8dd-955c-488e-894f-376f4f3996a1', 'actor-2', '2020-10-22 09:06:15.121302', '2020-10-22 09:06:15.121302');

--lucky_draw_winnings sql
insert into public.lucky_draw_winnings (id, lucky_draw_registration_id, reward_type, reward, created_at, updated_at, processing_request_id, reward_status) values
('d66fd746-4b1e-4705-81ac-35f9fabe9d4a', '58869436-4657-465d-a06d-61ad8d02c37d', 'CASH', '{"cash": {"amount": {"units": "20000", "currencyCode": "INR"}}, "display": {"icon": "https://icon-1-url.png", "bgColor": "#00B899", "afterClaimTitle": "Cash from lucky draw", "beforeClaimTitle": "Cash from lucky draw"}, "rewardType": "CASH"}', '2021-10-08 11:47:45.894906', '2021-10-08 12:13:56.166202', '', 'CREATED'),
('d66fd746-4b1e-4705-81ac-35f9fabe9d4b', '58869436-4657-465d-a06d-61ad8d02c37a', 'FI_COINS', '{"fiCoins": {"units": "10000"}, "rewardType": "FI_COINS", "display": {"icon": "https://icon-2-url.png", "bgColor": "#00B899", "afterClaimTitle": "FiCoins from lucky draw", "beforeClaimTitle": "FiCoins from lucky draw"}}', '2021-10-08 11:47:45.894906', '2021-10-08 12:13:56.166202', 'cbaf896f-9b8a-4b4a-9850-60e6e7ea8c22', 'PROCESSED');

--processing_requests sql
INSERT INTO processing_requests (id, processing_ref, created_at, updated_at, additional_info) VALUES
('52ff40f1-3f42-4fd3-b59f-d31082b2e031', 'processing-ref-1', '2020-10-29 12:37:13.129951', '2020-10-29 12:37:13.129951', '{"egv_basket_processing_request_info":{}}');


--gift_hamper_requests sql
INSERT INTO public.gift_hamper_requests (id, ref_id, actor_id, vendor_name, vendor_product_id, shipping_address, created_at, updated_at) VALUES
('42ebd934-2993-405f-b53e-fea9674148c6', 'ref-id-1', 'actor-1', 'WHOLE_TRUTH', 'M11082265', '{}', '2020-01-20 10:12:05.458938', '2020-02-05 10:12:05.458938'),
('42ebd934-2993-405f-b53e-fea9674158c6', 'ref-id-2', 'actor-1', 'WHOLE_TRUTH', 'M11082266', '{}', '2020-02-20 10:12:05.458938', '2021-01-20 10:12:05.458938'),
('42ebd934-2993-405f-b53e-fea9674168c6', 'ref-id-3', 'actor-2', 'WHOLE_TRUTH', 'M11082265', '{}', '2020-02-21 10:12:05.458938', '2021-01-20 10:12:05.458938'),
('42ebd934-2993-405f-b53e-fea9674178c6', 'ref-id-4', 'actor-2', 'WHOLE_TRUTH', 'M11082268', '{}', '2020-05-20 10:12:05.458938', '2021-01-20 10:12:05.458938');

--reward_offer_groups sql
INSERT INTO public.reward_offer_groups (id, user_reward_aggregate, created_at, updated_at, description) VALUES
('a6298923-8c49-446b-911d-f9c781699d12', 10, '2021-07-16 07:09:32.529026', '2021-07-16 07:09:32.529026', 'first fund addition offer group'),
('18b949a4-d0be-40cf-bc49-1f65916840fb', 10, '2021-07-16 07:09:32.529026', '2021-07-16 07:09:32.529026', 'first fund addition offer group'),
('18b949a4-d0be-40cf-bc49-1f65916840fc', 0, '2021-07-16 07:09:32.529026', '2021-07-16 07:09:32.529026', 'group with multiple offers'),
('18b949a4-d0be-40cf-bc49-1f65916840fd', 0, '2021-07-16 07:09:32.529026', '2021-07-16 07:09:32.529026', 'group with multiple offers');

--reward_offer_reward_units_actor_utilisation sql
INSERT INTO public.reward_offer_reward_units_actor_utilisation (offer_id, actor_id, fi_coin_units, cash_units, sd_cash_units) VALUES
('randomOfferIdUpsert', 'randomActorIdUpsert', 200, 150, NULL);

--reward_offer_reward_units_actor_utilisation sql
INSERT INTO public.reward_offer_reward_units_actor_utilisation (id, offer_id, actor_id, fi_coin_units, cash_units, sd_cash_units, cc_bill_eraser_units, created_at, updated_at) VALUES
('3c8bbec0-a611-4a9d-85b6-aaf123663041', 'offerId1', 'actorId1', 100, 150, 200, NULL, '2020-01-20 10:12:05.458938', '2020-01-20 10:12:05.458938'),
('1f7e4f92-a7f9-4a73-b46c-670b2e53f144', 'offerId2', 'actorId1', NULL, 150, NULL, NULL, '2020-01-20 10:12:05.458938', '2020-01-20 10:12:05.458938'),
('2d87f10e-e81c-4a8d-b803-1e639f872581', '5afc94c1-4f3a-492a-8527-a4364bc1235a', 'actor-1', NULL, 150, NULL, 100, '2020-01-20 10:12:05.458938', '2020-01-20 10:12:05.458938');

--reward_offer_reward_units_actor_utilisation_in_time_period sql
INSERT INTO public.reward_offer_reward_units_actor_utilisation_in_time_period (id, offer_id, actor_id, fi_coin_units, cash_units, sd_cash_units, cc_bill_eraser_units, created_at, updated_at,from_time,till_time) VALUES
('3c8bbec0-a611-4a9d-85b6-aaf123663441', 'offerId1', 'actorId1', 100, 150, 200, NULL, '2020-01-20 10:12:05.458938', '2020-01-20 10:12:05.458938','2024-01-01 00:00:00.000000 +05:30', '2024-02-01 00:00:00.000000 +05:30'),
('1f7e4f92-a7f9-4a73-b46c-670b2e53f244', 'offerId2', 'actorId1', NULL, 150, NULL, NULL, '2020-01-20 10:12:05.458938', '2020-01-20 10:12:05.458938','2024-01-01 00:00:00.000000 +05:30', '2024-02-01 00:00:00.000000 +05:30');

--reward_offer_group_reward_units_actor_utilisation sql
INSERT INTO public.reward_offer_group_reward_units_actor_utilisation (offer_group_id, actor_id, fi_coin_units, cash_units, sd_cash_units, cc_bill_eraser_units) VALUES
('randomOfferGroupIdUpsert', 'randomActorIdUpsert', 200, 150, NULL, NULL),
('18b949a4-d0be-40cf-bc49-1f65916840fc', 'actor-1', 200, 150, NULL, 100);

--reward_offer_group_reward_units_actor_utilisation sql
INSERT INTO public.reward_offer_group_reward_units_actor_utilisation (id, offer_group_id, actor_id, fi_coin_units, cash_units, sd_cash_units, created_at, updated_at) VALUES
('1ea598e9-3038-495c-ac07-36faca303306', 'offerGroupId1', 'actorId1', 100, 150, 200, '2020-01-20 10:12:05.458938', '2020-01-20 10:12:05.458938'),
('3af2c514-11eb-4cb9-9ce3-d21e7f249808', 'offerGroupId2', 'actorId1', NULL, 150, NULL, '2020-01-20 10:12:05.458938', '2020-01-20 10:12:05.458938');

--ro_group_reward_units_actor_utilisation_in_time_period sql
INSERT INTO public.ro_group_reward_units_actor_utilisation_in_time_period (id, group_id, actor_id, fi_coin_units, cash_units, sd_cash_units, created_at, updated_at, from_time, till_time) VALUES
('1ea598e9-3038-495c-ac07-36faca303106', 'offerGroupId1', 'actorId1', 100, 150, 200, '2020-01-20 10:12:05.458938', '2020-01-20 10:12:05.458938','2024-01-01 00:00:00.000000 +05:30', '2024-02-01 00:00:00.000000 +05:30'),
('3af2c514-11eb-4cb9-9ce3-d21e7f249208', 'offerGroupId2', 'actorId1', NULL, 150, NULL, '2020-01-20 10:12:05.458938', '2020-01-20 10:12:05.458938','2024-01-01 00:00:00.000000 +05:30', '2024-02-01 00:00:00.000000 +05:30');

INSERT INTO public.credit_card_rewards_info (id, reward_id, reward_type, initial_reward_units, reward_offer_type, txn_merchant_id, credit_card_account_id, credit_card_id, credit_card_scheme_code, action_timestamp, created_at, updated_at, deleted_at) VALUES
('12d83cbc-f882-47fd-9537-c1a538cd6243', 'RW230201VsQE+Y0bSGum/Zk4LacC+w==', 'FI_COINS', 100, 'CREDIT_CARD_SPENDS_1X_OFFER', 'merchant-1', 'cc-account-1', 'credit-card-1', 'CARD_SKU_TYPE_UNSPECIFIED', '2022-12-27 07:56:54.722304 +00:00', '2023-02-01 19:48:54.855976 +00:00', '2023-02-01 19:48:54.855976 +00:00', null),
('12d83cbc-f882-47fd-9537-c1a538cd6244', 'RW231201VsQE+Y0bSGum/Zk4LacC+w==', 'FI_COINS', 501, 'CREDIT_CARD_SPENDS_1X_OFFER', 'merchant-1', 'cc-account-1', 'credit-card-1', 'CARD_SKU_TYPE_UNSPECIFIED', '2023-03-05 07:56:54.722304 +00:00', '2023-02-01 19:48:54.855976 +00:00', '2023-02-01 19:48:54.855976 +00:00', null),
('12d83cbc-f882-47fd-9537-c1a538cd6245', 'RW232201VsQE+Y0bSGum/Zk4LacC+w==', 'FI_COINS', 250, 'CREDIT_CARD_SPENDS_1X_OFFER', 'merchant-2', 'cc-account-1', 'credit-card-1', 'CARD_SKU_TYPE_UNSPECIFIED', '2023-01-27 07:56:54.722304 +00:00', '2023-02-01 19:48:54.855976 +00:00', '2023-02-01 19:48:54.855976 +00:00', null),
('12d83cbc-f882-47fd-9537-c1a538cd6246', 'RW232211VsQE+Y0bSGum/Zk4LacC+w==', 'FI_COINS', 250, 'CREDIT_CARD_SPENDS_1X_OFFER', 'merchant-3', 'cc-account-1', 'credit-card-1', 'CARD_SKU_TYPE_UNSPECIFIED', '2023-01-27 07:56:54.722304 +00:00', '2023-02-01 19:48:54.855976 +00:00', '2023-02-01 19:48:54.855976 +00:00', '2023-02-01 19:48:54.855976 +00:00');


--reward_clawbacks sql
INSERT INTO public.reward_clawbacks(id, reward_id, reward_type, clawed_back_reward_units, action_ref_id, action, status, processing_ref, action_timestamp, created_at, updated_at) VALUES
('f0a4f0f7-1b2b-4cbe-a95a-cdcd13c99055', 'RW200911WjgI+xGYRY+0PhNl96WmYB==', 'FI_COINS', 100, 'action-ref-1', 'CREDIT_CARD_TXN_REVERSAL', 'CLAWBACK_STATUS_PROCESSING_PENDING', 'processing-ref', '2022-01-20 10:12:05.458938', '2022-01-20 10:12:05.458938', '2022-01-20 10:12:05.458938'),
('f0a4f0f7-1b2b-4cbe-a95a-cdcd13c99056', 'RW230201VsQE+Y0bSGum/Zk4LacC+w==', 'FI_COINS', 10, 'cc-refund-txn-1', 'CREDIT_CARD_TXN_REVERSAL', 'CLAWBACK_STATUS_PROCESSING_PENDING', 'processing-ref-1', '2023-02-06 21:50:00.000000', '2023-02-06 21:50:00.000000', '2023-02-06 21:50:00.000000'),
('f0a4f0f7-1b2b-4cbe-a95a-cdcd13c99057', 'RW230201VsQE+Y0bSGum/Zk4LacC+w==', 'FI_COINS', 20, 'cc-refund-txn-2', 'CREDIT_CARD_TXN_REVERSAL', 'CLAWBACK_STATUS_PROCESSED', 'processing-ref-2', '2023-02-06 21:50:00.000000', '2023-02-06 21:50:00.000000', '2023-02-06 21:50:00.000000'),
('f0a4f0f7-1b2b-4cbe-a95a-cdcd13c99058', 'RW231201VsQE+Y0bSGum/Zk4LacC+w==', 'FI_COINS', 25, 'cc-refund-txn-3', 'CREDIT_CARD_TXN_REVERSAL', 'CLAWBACK_STATUS_PROCESSING_PENDING', 'processing-ref-3', '2023-02-06 21:50:00.000000', '2023-02-06 21:50:00.000000', '2023-02-06 21:50:00.000000'),
('f0a4f0f7-1b2b-4cbe-a95a-cdcd13c99059', 'RW232201VsQE+Y0bSGum/Zk4LacC+w==', 'FI_COINS', 30, 'cc-refund-txn-4', 'CREDIT_CARD_TXN_REVERSAL', 'CLAWBACK_STATUS_PROCESSING_PENDING', 'processing-ref-4', '2023-02-06 21:50:00.000000', '2023-02-06 21:50:00.000000', '2023-02-06 21:50:00.000000');

-- cc_rewards_cc_account_level_monthly_utilisation
INSERT INTO public.cc_rewards_cc_account_level_monthly_utilisation(id, credit_card_account_id, fi_coin_units, reward_offer_type, from_time, till_time, created_at, updated_at, deleted_at) VALUES
('2fd7f031-45b3-4751-a5e5-3d7a9ff25606', 'cc-account-1', 500, 'CREDIT_CARD_SPENDS_1X_OFFER', '2023-03-01 00:00:00.000000 +05:30', '2023-04-01 00:00:00.000000 +05:30', '2023-03-04 00:00:00.000000 +05:30', '2023-03-04 00:00:00.000000 +05:30', null);

--reward_offer_reward_units_actor_utilisation_in_time_period
INSERT INTO public.reward_offer_reward_units_actor_utilisation_in_time_period(id, actor_id, offer_id, fi_coin_units, cash_units, sd_cash_units, cc_bill_eraser_units, from_time, till_time, created_at, updated_at, deleted_at,generated_rewards_count) VALUES
('1b951656-600d-42d4-b584-06b6e596fb92', 'actor-1', '75021f37-aa95-43c5-9292-74826b333f97', 1100, 0, 0, 100, '2023-03-01 00:00:00.000000 +05:30', '2023-04-01 00:00:00.000000 +05:30', '2023-03-04 00:00:00.000000 +05:30', '2023-03-04 00:00:00.000000 +05:30', null,0),
('1b951656-600d-42d4-b584-06b6e596fb93', 'actor-1', '75f0b4b9-ae8b-4d35-9659-3d1124a782ca', 1000, 0, 0, 100, '2023-03-01 00:00:00.000000 +05:30', '2023-04-01 00:00:00.000000 +05:30', '2023-03-04 00:00:00.000000 +05:30', '2023-03-04 00:00:00.000000 +05:30', null,1),
('1b951656-600d-42d4-b584-06b6e596fb94', 'actor-1', '75f0b4b9-ae8b-4d35-9659-3d1124a782ca', 1100, 0, 0, 100, '2023-01-01 00:00:00.000000 +05:30', '2023-04-01 00:00:00.000000 +05:30', '2023-01-01 00:00:00.000000 +05:30', '2023-03-04 00:00:00.000000 +05:30', null,3);

--ro_group_reward_units_actor_utilisation_in_time_period
INSERT INTO public.ro_group_reward_units_actor_utilisation_in_time_period(id, actor_id, group_id, fi_coin_units, cash_units, sd_cash_units, cc_bill_eraser_units, from_time, till_time, created_at, updated_at, deleted_at,generated_rewards_count) VALUES
	('d9b7e66d-f782-4556-9347-8729104fcf58', 'actor-1', 'f68b8299-d038-485e-a59c-606f53bd7f9e', 1100, 0, 0, 100, '2023-03-01 00:00:00.000000 +05:30', '2023-04-01 00:00:00.000000 +05:30', '2023-03-04 00:00:00.000000 +05:30', '2023-03-04 00:00:00.000000 +05:30', null,1),
	('d9b7e66d-f782-4556-9347-8729104fcf59', 'actor-1', 'f68b8299-d038-485e-a59c-606f53bd7f9e', 1100, 0, 0, 100, '2023-01-01 00:00:00.000000 +05:30', '2023-04-01 00:00:00.000000 +05:30', '2023-01-01 00:00:00.000000 +05:30', '2023-03-04 00:00:00.000000 +05:30', null,3);

INSERT INTO public.reward_projections("actor_id","account_id","reward_id","offer_id","ref_id","action_type","offer_type","projected_options","reward_contributions","action_time","created_at","updated_at","deleted_at","id") VALUES ('temp-actor-id','temp-acc-id','reward-id','fd1f671d-d0f3-4a69-bb19-c6daa5e066e4','ref-id','CREDIT_CARD_BILLING','SECURED_CREDIT_CARD_WEEKEND_OFFER','{}','{}','2023-10-16 12:42:52.959','2023-10-16 18:12:53.01','2023-10-16 18:12:53.01',NULL,'fd1f671d-d0f3-4a69-bb19-c6daa5e066e4');
INSERT INTO public.reward_projections("actor_id","account_id","reward_id","offer_id","ref_id","action_type","offer_type","projected_options","reward_contributions","action_time","created_at","updated_at","deleted_at","id") VALUES ('temp-actor-id-1','temp-acc-id-1','reward-id-1','fd1f671d-e0f3-4a69-bb19-c6daa5e066e4','ref-id-1','CREDIT_CARD_BILLING','SECURED_CREDIT_CARD_WEEKEND_OFFER','{}','{}','2023-10-17 12:42:52.959','2023-10-17 18:12:53.01','2023-10-17 18:12:53.01',NULL,'fd1f671d-e0f3-4a69-bb19-c6daa5e066e4');
INSERT INTO public.reward_projections("actor_id","account_id","reward_id","offer_id","ref_id","action_type","offer_type","projected_options","reward_contributions","action_time","created_at","updated_at","deleted_at","id") VALUES ('temp-actor-id-1','temp-acc-id-1','reward-id-2','fd1f671d-f0f3-4a69-bb19-c6daa5e066e4','ref-id-2','CREDIT_CARD_BILLING','SECURED_CREDIT_CARD_WEEKDAY_OFFER','{}','{}','2023-10-18 12:42:52.959','2023-10-18 18:12:53.01','2023-10-18 18:12:53.01',NULL,'fd1f671d-10f3-4a69-bb19-c6daa5e066e4');
INSERT INTO public.reward_projections("actor_id","account_id","reward_id","offer_id","ref_id","action_type","offer_type","projected_options","reward_contributions","action_time","created_at","updated_at","deleted_at","id") VALUES ('temp-actor-id-1','temp-acc-id-1','reward-id-3','fd1f671d-10f3-4a69-bb19-c6daa5e066e4','ref-id-3','CREDIT_CARD_BILLING','SECURED_CREDIT_CARD_WEEKDAY_OFFER','{}','{}','2023-10-19 12:42:52.959','2023-10-19 18:12:53.01','2023-10-19 18:12:53.01',NULL,'fd1f671d-20f3-4a69-bb19-c6daa5e066e4');
INSERT INTO public.reward_projections("actor_id","account_id","reward_id","offer_id","ref_id","action_type","offer_type","projected_options","reward_contributions","action_time","created_at","updated_at","deleted_at","id") VALUES ('temp-actor-id-1','temp-acc-id-1','reward-id-4','fd1f671d-20f3-4a69-bb19-c6daa5e066e4','ref-id-4','CREDIT_CARD_BILLING','SECURED_CREDIT_CARD_WEEKEND_OFFER','{}','{}','2023-10-20 12:42:52.959','2023-10-20 18:12:53.01','2023-10-20 18:12:53.01',NULL,'fd1f671d-30f3-4a69-bb19-c6daa5e066e4');
INSERT INTO public.reward_projections("actor_id","account_id","reward_id","offer_id","ref_id","action_type","offer_type","projected_options","reward_contributions","action_time","created_at","updated_at","deleted_at","id") VALUES ('temp-actor-id-2','temp-acc-id-2','reward-id-5','fd1f671d-30f3-4a69-bb19-c6daa5e066e4','ref-id-5','CREDIT_CARD_BILLING','SECURED_CREDIT_CARD_WEEKDAY_OFFER','{}','{}','2023-10-21 12:42:52.959','2023-10-21 18:12:53.01','2023-10-21 18:12:53.01',NULL,'fd1f671d-40f3-4a69-bb19-c6daa5e066e4');
INSERT INTO public.reward_projections("actor_id","account_id","reward_id","offer_id","ref_id","action_type","offer_type","projected_options","reward_contributions","action_time","created_at","updated_at","deleted_at","id") VALUES ('temp-actor-id-2','temp-acc-id-2','reward-id-6','fd1f671d-40f3-4a69-bb19-c6daa5e066e4','ref-id-6','CREDIT_CARD_BILLING','SECURED_CREDIT_CARD_WEEKDAY_OFFER','{}','{}','2023-10-22 12:42:52.959','2023-10-22 18:12:53.01','2023-10-22 18:12:53.01',NULL,'fd1f671d-50f3-4a69-bb19-c6daa5e066e4');
INSERT INTO public.reward_projections("actor_id","account_id","reward_id","offer_id","ref_id","action_type","offer_type","projected_options","reward_contributions","action_time","created_at","updated_at","deleted_at","id") VALUES ('temp-actor-id-2','temp-acc-id-2','reward-id-7','fd1f671d-50f3-4a69-bb19-c6daa5e066e4','ref-id-7','CREDIT_CARD_BILLING','SECURED_CREDIT_CARD_WEEKEND_OFFER','{}','{}','2023-10-23 12:42:52.959','2023-10-23 18:12:53.01','2023-10-23 18:12:53.01',NULL,'fd1f671d-60f3-4a69-bb19-c6daa5e066e4');
INSERT INTO public.reward_projections("actor_id","account_id","reward_id","offer_id","ref_id","action_type","offer_type","projected_options","reward_contributions","action_time","created_at","updated_at","deleted_at","id") VALUES ('temp-actor-id-3','temp-acc-id-3','','fd1f671d-50f3-4a69-bb19-c6daa5e066e4','ref-id-8','CREDIT_CARD_BILLING','SECURED_CREDIT_CARD_WEEKEND_OFFER','{}','{}','2023-10-23 12:42:52.959','2023-10-23 18:12:53.01','2023-10-23 18:12:53.01',NULL,'fd1f671d-70f3-4a69-bb19-c6daa5e066e4');
INSERT INTO public.reward_projections("actor_id","account_id","reward_id","offer_id","ref_id","action_type","offer_type","projected_options","reward_contributions","action_time","created_at","updated_at","deleted_at","id") VALUES ('temp-actor-id-3','temp-acc-id-3','reward-id-9','fd1f671d-50f3-4a69-bb19-c6daa5e066e4','ref-id-9','CREDIT_CARD_BILLING','SECURED_CREDIT_CARD_WEEKEND_OFFER','{}','{}','2023-10-23 12:42:52.959','2023-10-23 18:12:53.01','2023-10-23 18:12:53.01',NULL,'fd1f671d-80f3-4a69-bb19-c6daa5e066e4');
INSERT INTO public.reward_projections("actor_id","account_id","reward_id","offer_id","ref_id","action_type","offer_type","projected_options","reward_contributions","action_time","created_at","updated_at","deleted_at","id") VALUES ('temp-actor-id-2','temp-acc-id-2','','fd1f671d-50f3-4a69-bb19-c6daa5e066e4','ref-id-10','CREDIT_CARD_BILLING','SECURED_CREDIT_CARD_WEEKEND_OFFER','{}','{}','2023-10-23 12:42:52.959','2023-10-23 18:12:53.01','2023-10-23 18:12:53.01',NULL,'ea838103-af38-47ed-8dfc-630d5f13b285');

INSERT INTO public.reward_offers (id, status, active_since, active_till, display_since, display_till, created_at, updated_at, created_by, action_type, constraints_meta, reward_meta, display_meta, is_visible, offer_type, tags, group_id, ref_reward_offer_id, display_segment_id, is_display_segment_excluded, display_segment_expression, analytics_data, unlock_event, unlock_meta, supported_platform, generation_type) VALUES ('e49f980a-a1a7-4cc3-bd24-7d7b9f038220', 'ACTIVE', '2023-07-04 18:30:01.000000 +00:00', '2031-01-31 18:29:59.000000 +00:00', '2023-07-04 18:30:01.000000 +00:00', '2031-01-31 18:29:59.000000 +00:00', '2023-07-06 12:44:56.929845 +00:00', '2023-10-25 11:28:27.092615 +00:00', 'Ravi', 'CREDIT_CARD_REQUEST_STAGE_UPDATE', '{"expression":"CARD_REQUEST_WORKFLOW == ''CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING'' && CARD_REQUEST_STAGE == ''CARD_REQUEST_STAGE_NAME_CARD_CREATION'' && STAGE_STATUS == ''CARD_REQUEST_STAGE_STATUS_SUCCESS'' && CARD_REQUEST_STATUS == ''CARD_REQUEST_STATUS_IN_PROGRESS''"}', '{"rewardAggregates":{"userAggregate":1}, "rewardLockTimeConfig":{"relativeTimeInMinutes":0}, "defaultDecideTimeInSecs":45, "probability":1, "rewardConfigOptions":[{"rewardType":"FI_COINS", "displayConfig":{"beforeClaimTextExpression":"Fi-Coins", "afterClaimTextExpression":"Fi-Coins", "icon":"https://epifi-icons.pointz.in/rewards/ficoin-2.png", "bgColor":"#00B899"}, "rewardProcessingTimeConfig":{"relativeTimeInMinutes":0}, "rangeProbabilityConfig":{"configUnits":[{"start":50000, "end":50000, "percentage":100}], "probability":1}}, {"rewardType":"THRIWE_BENEFITS_PACKAGE", "displayConfig":{"beforeClaimTextExpression":"Vouchers worth ₹5000", "afterClaimTextExpression":"Vouchers worth ₹5000", "icon":"https://epifi-icons.pointz.in/rewards/My_Rewards/dc_international.png", "bgColor":"#00B899"}, "rewardProcessingTimeConfig":{"relativeTimeInMinutes":0}, "expressionProbabilityConfig":{"expression":"1", "probability":1}, "thriweBenefitsPackageConfig":{"offerId":"6339f0a9-34d0-43a1-b798-47c13da3a1e4"}, "productSku":"THRIWE_BENEFITS_WORTH_5K"}], "rewardNotificationConfig":{"earnedRewardNotificationConfig":{}},"isImplicitLockingDisabled":true}', '{"displaySince":"2023-07-05T00:00:01+05:30", "displayTill":"2031-01-31T23:59:59+05:30", "displayType":"HEADLINE", "title":"Credit Card Welcome Offer", "steps":["CC Welcome Offer Bundle - to be shown on Thriwe"], "tncs":["CC Welcome Offer Bundle - to be shown on Thriwe"], "icon":"https://epifi-icons.pointz.in/salaryprogram/amazon_voucher.png", "bgColor":"#00B899", "actionDesc":"Fi Federal Credit Card: Welcome benefit", "cta":{"name":"Ok, got it", "deeplink":{"screen":"SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN"}}, "displayRank":2, "shortDesc":"Credit Card Welcome Offer", "tileBgColor":"#87BA6B", "imageUrl":"https://epifi-icons.pointz.in/salaryprogram/amazon_voucher.png", "secondaryTileBgColor":"#87BA6B"}', false, 'CREDIT_CARD_WELCOME_OFFER', '{"Tags": null}', '', null, null, null, null, null, 'UNSPECIFIED_COLLECTED_DATA_TYPE', '{}', 'PLATFORM_UNSPECIFIED', 'GENERATION_TYPE_REWARD');
