CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;
COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';
CREATE TABLE public.cc_rewards_cc_account_level_monthly_utilisation (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    credit_card_account_id character varying NOT NULL,
    fi_coin_units integer DEFAULT 0 NOT NULL,
    reward_offer_type character varying NOT NULL,
    from_time timestamp with time zone NOT NULL,
    till_time timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.cc_rewards_cc_account_level_monthly_utilisation IS 'stores utilisation of rewards units on credit card account id level for time periods';
COMMENT ON COLUMN public.cc_rewards_cc_account_level_monthly_utilisation.fi_coin_units IS 'stores the net utilisation of fi coins for given credit card account id and offer type, in a given time frame';
CREATE TABLE public.credit_card_rewards_info (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    reward_id character varying NOT NULL,
    reward_type character varying NOT NULL,
    initial_reward_units integer NOT NULL,
    reward_offer_type character varying NOT NULL,
    txn_merchant_id character varying,
    credit_card_account_id character varying NOT NULL,
    credit_card_id character varying,
    credit_card_scheme_code character varying,
    action_timestamp timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.credit_card_rewards_info IS 'table for storing additional information related to credit card rewards.';
CREATE TABLE public.gift_hamper_requests (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    ref_id character varying NOT NULL,
    actor_id character varying NOT NULL,
    vendor_name character varying NOT NULL,
    vendor_product_id character varying NOT NULL,
    shipping_address jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);
CREATE TABLE public.lucky_draw_campaigns (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    description character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);
CREATE TABLE public.lucky_draw_registrations (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    lucky_draw_id character varying NOT NULL,
    actor_id character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);
CREATE TABLE public.lucky_draw_winnings (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    lucky_draw_registration_id character varying NOT NULL,
    reward_type character varying NOT NULL,
    reward jsonb NOT NULL,
    reward_processing_status character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    processing_request_id character varying,
    reward_status character varying NOT NULL
);
COMMENT ON COLUMN public.lucky_draw_winnings.reward_status IS 'stores the status of reward received as a part of lucky draw winning';
CREATE TABLE public.lucky_draws (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    campaign_id character varying NOT NULL,
    registration_from timestamp with time zone NOT NULL,
    registration_till timestamp with time zone NOT NULL,
    lucky_draw_reveal_time timestamp with time zone NOT NULL,
    lucky_draw_config jsonb NOT NULL,
    lucky_draw_status character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);
CREATE TABLE public.processing_requests (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    processing_ref character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    entity_type character varying,
    entity_id character varying,
    processing_status character varying,
    is_deleted boolean DEFAULT false,
    additional_info jsonb DEFAULT '{}'::jsonb
);
COMMENT ON COLUMN public.processing_requests.additional_info IS 'stores any additional info related to the processing request';
CREATE TABLE public.reward_clawbacks (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    reward_id character varying NOT NULL,
    reward_type character varying NOT NULL,
    clawed_back_reward_units double precision NOT NULL,
    action_ref_id character varying NOT NULL,
    action character varying NOT NULL,
    status character varying NOT NULL,
    processing_ref character varying,
    action_timestamp timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.reward_clawbacks IS 'reward_clawback stores all info related to clawback of rewards';
COMMENT ON COLUMN public.reward_clawbacks.reward_id IS 'reward ID which is clawed-back';
COMMENT ON COLUMN public.reward_clawbacks.reward_type IS 'reward type of reward that was given and is clawed back';
COMMENT ON COLUMN public.reward_clawbacks.clawed_back_reward_units IS 'number of units that have been clawed back';
COMMENT ON COLUMN public.reward_clawbacks.action_ref_id IS 'reference ID of the action that led to clawback';
COMMENT ON COLUMN public.reward_clawbacks.action IS 'enum of action that led to clawback';
COMMENT ON COLUMN public.reward_clawbacks.status IS 'status of clawback';
COMMENT ON COLUMN public.reward_clawbacks.processing_ref IS 'reference ID of the action that led to clawback';
COMMENT ON COLUMN public.reward_clawbacks.action_timestamp IS 'timestamp of action that led to creation of clawback';
CREATE TABLE public.reward_offer_actor_aggregates (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    offer_id character varying(50) NOT NULL,
    actor_id character varying(50) NOT NULL,
    count integer NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);
CREATE TABLE public.reward_offer_aggregates (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    offer_id character varying(50) NOT NULL,
    count integer NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);
CREATE TABLE public.reward_offer_group_actor_aggregates (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    offer_group_id character varying NOT NULL,
    actor_id character varying NOT NULL,
    count integer NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);
COMMENT ON TABLE public.reward_offer_group_actor_aggregates IS 'stores information of reward offer group inventory for an actor';
CREATE TABLE public.reward_offer_group_reward_units_actor_utilisation (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    offer_group_id character varying NOT NULL,
    actor_id character varying NOT NULL,
    fi_coin_units integer,
    cash_units integer,
    sd_cash_units integer,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    usstock_cash_units integer,
    cc_bill_eraser_units integer
);
COMMENT ON TABLE public.reward_offer_group_reward_units_actor_utilisation IS 'stores the reward units which are given to the actor for that group (of reward offers)';
CREATE TABLE public.reward_offer_groups (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_reward_aggregate integer NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    description character varying,
    reward_units_cap_user_aggregate jsonb DEFAULT '{}'::jsonb,
    reward_units_cap_user_monthly_aggregate jsonb DEFAULT '{}'::jsonb,
    user_reward_aggregate_in_time_period jsonb DEFAULT '{}'::jsonb
);
COMMENT ON TABLE public.reward_offer_groups IS 'entity created for grouping multiple reward offers under same group for creating across reward offers inventory';
COMMENT ON COLUMN public.reward_offer_groups.description IS 'stores the description of the group';
COMMENT ON COLUMN public.reward_offer_groups.reward_units_cap_user_aggregate IS '{"proto_type":"rewardOffersPb.RewardUnitsCapAggregate", "comment": "field stores the cap config set on reward-units at group level"}';
CREATE TABLE public.reward_offer_reward_units_actor_utilisation (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    offer_id character varying NOT NULL,
    actor_id character varying NOT NULL,
    fi_coin_units integer,
    cash_units integer,
    sd_cash_units integer,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    usstock_cash_units integer,
    cc_bill_eraser_units integer
);
COMMENT ON TABLE public.reward_offer_reward_units_actor_utilisation IS 'stores the reward units which are given to the actor for that offer';
CREATE TABLE public.reward_offer_reward_units_actor_utilisation_in_time_period (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    offer_id character varying NOT NULL,
    fi_coin_units integer,
    cash_units integer,
    sd_cash_units integer,
    from_time timestamp with time zone NOT NULL,
    till_time timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    usstock_cash_units integer,
    cc_bill_eraser_units integer,
    generated_rewards_count integer
);
COMMENT ON TABLE public.reward_offer_reward_units_actor_utilisation_in_time_period IS 'stores utilisation of rewards units on offerId-actorId level in time periods';
CREATE TABLE public.reward_offers (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    status character varying(50) NOT NULL,
    active_since timestamp with time zone NOT NULL,
    active_till timestamp with time zone NOT NULL,
    display_since timestamp with time zone NOT NULL,
    display_till timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    created_by character varying(50) NOT NULL,
    action_type character varying(50) NOT NULL,
    constraints_meta text,
    reward_meta text,
    display_meta text,
    is_visible boolean DEFAULT true,
    offer_type character varying DEFAULT 'UNSPECIFIED_REWARD_OFFER_TYPE'::character varying,
    tags jsonb DEFAULT '{}'::jsonb,
    group_id character varying,
    ref_reward_offer_id character varying,
    display_segment_id character varying,
    is_display_segment_excluded boolean,
    supported_platform character varying DEFAULT 'PLATFORM_UNSPECIFIED'::character varying NOT NULL,
    display_segment_expression character varying,
    analytics_data jsonb,
    unlock_event character varying DEFAULT 'UNSPECIFIED_COLLECTED_DATA_TYPE'::character varying NOT NULL,
    unlock_meta jsonb DEFAULT '{}'::jsonb NOT NULL,
    generation_type character varying DEFAULT 'GENERATION_TYPE_UNSPECIFIED'::character varying NOT NULL,
    additional_details jsonb DEFAULT '{}'::jsonb NOT NULL
);
COMMENT ON COLUMN public.reward_offers.offer_type IS 'denotes the type of offer e.g REFERRAL_REFEREE_OFFER denotes referral reward offer for referee';
COMMENT ON COLUMN public.reward_offers.tags IS 'stores a list of tags associated with the reward offer';
COMMENT ON COLUMN public.reward_offers.group_id IS 'denotes the id of group to which this offer belongs';
COMMENT ON COLUMN public.reward_offers.ref_reward_offer_id IS 'stores ref to some other reward offer to which current offer is linked';
COMMENT ON COLUMN public.reward_offers.display_segment_id IS 'segment id for which reward offer is displayed';
COMMENT ON COLUMN public.reward_offers.is_display_segment_excluded IS 'boolean to indicate if reward offer is excluded for segment';
COMMENT ON COLUMN public.reward_offers.supported_platform IS 'platform for which reward offer is created, a null value means that the offer is platform independent';
COMMENT ON COLUMN public.reward_offers.display_segment_expression IS 'segment id expression for which reward offer is displayed';
COMMENT ON COLUMN public.reward_offers.analytics_data IS '{"proto_type" : rewards.rewardoffers.RewardOfferAnalyticsData, "comment": "stores info related to reward offer that should be used for analytics purpose"}';
COMMENT ON COLUMN public.reward_offers.unlock_event IS 'stores the event which would unlock the reward. would be UNSPECIFIED if reward is not to be locked';
COMMENT ON COLUMN public.reward_offers.unlock_meta IS 'stores any config related to unlocking of reward like CTA to be shown, constraint to be evaluated to unlock reward, etc.';
COMMENT ON COLUMN public.reward_offers.generation_type IS 'denotes what the reward offer will generate - actual reward or a projection of reward';
COMMENT ON COLUMN public.reward_offers.additional_details IS 'stores additional details related to the reward offer';
CREATE TABLE public.reward_projections (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    account_id character varying,
    action_type character varying,
    reward_id character varying,
    action_time timestamp with time zone,
    ref_id character varying,
    offer_id uuid,
    offer_type character varying,
    projected_options jsonb DEFAULT '{}'::jsonb,
    reward_contributions jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON COLUMN public.reward_projections.account_id IS 'further level of differentiation between different accounts of same actor';
COMMENT ON COLUMN public.reward_projections.ref_id IS 'an external identifier of the action that generated the projection';
COMMENT ON COLUMN public.reward_projections.projected_options IS 'projections of options that are generated for the offer';
COMMENT ON COLUMN public.reward_projections.reward_contributions IS 'actual contribution of the projection to generated reward';
CREATE TABLE public.reward_unlock_details (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    reward_id character varying NOT NULL,
    unlock_event character varying DEFAULT 'UNSPECIFIED_COLLECTED_DATA_TYPE'::character varying NOT NULL,
    unlock_event_ref character varying,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.reward_unlock_details IS 'reward_unlock_details table will store the details of the event that unlocked a reward';
COMMENT ON COLUMN public.reward_unlock_details.unlock_event IS 'type of event that unlocked the reward';
COMMENT ON COLUMN public.reward_unlock_details.unlock_event_ref IS 'reference ID of the event that unlocked the reward';
CREATE TABLE public.rewards (
    id character varying(255) NOT NULL,
    type character varying(50) DEFAULT NULL::character varying,
    actor_id character varying(50) NOT NULL,
    ref_id character varying(50) NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    status character varying(50) NOT NULL,
    sub_status character varying(255) DEFAULT 'SUB_STATUS_UNSPECIFIED'::character varying NOT NULL,
    offer_id character varying(50) NOT NULL,
    reward_options text NOT NULL,
    chosen_reward text,
    is_visible boolean DEFAULT true,
    processing_ref character varying(50) DEFAULT NULL::character varying,
    external_id character varying DEFAULT public.uuid_generate_v4() NOT NULL,
    reward_display jsonb DEFAULT '{}'::jsonb,
    offer_type character varying DEFAULT 'UNSPECIFIED_REWARD_OFFER_TYPE'::character varying,
    external_ref character varying,
    global_dedupe_id character varying,
    deleted_at timestamp with time zone,
    clawback_ref_id character varying,
    clawback_reason character varying DEFAULT 'CLAWBACK_REASON_UNSPECIFIED'::character varying,
    tags character varying[],
    action_type character varying DEFAULT 'UNSPECIFIED_COLLECTED_DATA_TYPE'::character varying,
    reward_metadata jsonb DEFAULT '{}'::jsonb,
    claim_type character varying DEFAULT 'CLAIM_TYPE_UNSPECIFIED'::character varying,
    action_time timestamp with time zone,
    expires_at timestamp with time zone,
    secondary_ref_id character varying DEFAULT 'NA'::character varying NOT NULL
);
COMMENT ON COLUMN public.rewards.reward_display IS '{"proto_type":"rewards.RewardDisplay", "comment": "Stores the display properties of a reward like tile bg image, tile image etc"}';
COMMENT ON COLUMN public.rewards.offer_type IS 'denotes the type of offer responsible for this reward e.g REFERRAL_REFEREE_OFFER denotes referral reward offer for referee';
COMMENT ON COLUMN public.rewards.external_ref IS 'used to link reward with an external ref/entity e.g. linking finiteCode to a referral rewards';
COMMENT ON COLUMN public.rewards.global_dedupe_id IS 'used for special deduping cases for deduping rewards for an actor';
COMMENT ON COLUMN public.rewards.clawback_ref_id IS 'reference ID of event which lead to clawback of reward. it will be empty unless reward is clawed-back';
COMMENT ON COLUMN public.rewards.clawback_reason IS 'reason because of which the reward was clawed back. it will be CLAW_BACK_REASON_UNSPECIFIED unless reward has been moved to CLAWED_BACK state';
COMMENT ON COLUMN public.rewards.tags IS '{"proto_type":"[]api.rewards.RewardTag", "comment": "stores tags associated with rewards for multi-purpose usage"';
COMMENT ON COLUMN public.rewards.action_type IS 'type of event that was collected which lead to the generation of the reward, like ORDER, FITTT,  etc.';
COMMENT ON COLUMN public.rewards.claim_type IS 'stores how the reward will be claimed';
COMMENT ON COLUMN public.rewards.secondary_ref_id IS 'denotes the secondary identifier of the action that generated the reward. This allows us to generate multiple rewards for the same (actor_id, offer_id, ref_id) combination';
CREATE TABLE public.ro_group_reward_units_actor_utilisation_in_time_period (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    actor_id character varying NOT NULL,
    group_id character varying NOT NULL,
    fi_coin_units integer,
    cash_units integer,
    sd_cash_units integer,
    from_time timestamp with time zone NOT NULL,
    till_time timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    usstock_cash_units integer,
    cc_bill_eraser_units integer,
    generated_rewards_count integer
);
COMMENT ON TABLE public.ro_group_reward_units_actor_utilisation_in_time_period IS 'stores utilisation of rewards units on groupId-actorId level for time periods for a group';
CREATE TABLE public.schema_migrations (
    version bigint NOT NULL,
    dirty boolean NOT NULL
);
ALTER TABLE ONLY public.reward_offer_aggregates
    ADD CONSTRAINT actor_id_index UNIQUE (offer_id);
ALTER TABLE ONLY public.reward_offer_actor_aggregates
    ADD CONSTRAINT actor_id_offer_id_index UNIQUE (actor_id, offer_id);
ALTER TABLE ONLY public.cc_rewards_cc_account_level_monthly_utilisation
    ADD CONSTRAINT cc_rewards_cc_account_level_monthly_utilisation_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.credit_card_rewards_info
    ADD CONSTRAINT cc_rewards_info_primary_key PRIMARY KEY (id);
ALTER TABLE ONLY public.gift_hamper_requests
    ADD CONSTRAINT gift_hamper_requests_pk_id PRIMARY KEY (id);
ALTER TABLE ONLY public.gift_hamper_requests
    ADD CONSTRAINT gift_hamper_requests_unique_ref_id UNIQUE (ref_id);
ALTER TABLE ONLY public.lucky_draw_registrations
    ADD CONSTRAINT lucky_draw_actor_unique UNIQUE (lucky_draw_id, actor_id);
ALTER TABLE ONLY public.lucky_draw_campaigns
    ADD CONSTRAINT lucky_draw_campaign_pk PRIMARY KEY (id);
ALTER TABLE ONLY public.lucky_draws
    ADD CONSTRAINT lucky_draw_pk PRIMARY KEY (id);
ALTER TABLE ONLY public.lucky_draw_registrations
    ADD CONSTRAINT lucky_draw_registration_pk PRIMARY KEY (id);
ALTER TABLE ONLY public.lucky_draw_winnings
    ADD CONSTRAINT lucky_draw_winning_pk PRIMARY KEY (id);
ALTER TABLE ONLY public.lucky_draw_winnings
    ADD CONSTRAINT lucky_draw_winning_registration_id_unique UNIQUE (lucky_draw_registration_id);
ALTER TABLE ONLY public.processing_requests
    ADD CONSTRAINT processing_requests_pk PRIMARY KEY (id);
ALTER TABLE ONLY public.reward_clawbacks
    ADD CONSTRAINT reward_clawbacks_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.reward_offer_actor_aggregates
    ADD CONSTRAINT reward_offer_actor_aggregates_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.reward_offer_aggregates
    ADD CONSTRAINT reward_offer_aggregates_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.reward_offer_group_actor_aggregates
    ADD CONSTRAINT reward_offer_group_actor_aggr_unique_actor_id_offer_group_id UNIQUE (actor_id, offer_group_id);
ALTER TABLE ONLY public.reward_offer_group_actor_aggregates
    ADD CONSTRAINT reward_offer_group_actor_aggregates_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.reward_offer_group_reward_units_actor_utilisation
    ADD CONSTRAINT reward_offer_group_reward_units_actor_utilisation_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.reward_offer_groups
    ADD CONSTRAINT reward_offer_groups_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.reward_offers
    ADD CONSTRAINT reward_offer_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.reward_offer_reward_units_actor_utilisation_in_time_period
    ADD CONSTRAINT reward_offer_reward_units_actor_utilisation_in_time_period_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.reward_offer_reward_units_actor_utilisation
    ADD CONSTRAINT reward_offer_reward_units_actor_utilisation_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.rewards
    ADD CONSTRAINT reward_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.reward_projections
    ADD CONSTRAINT reward_projections_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.reward_unlock_details
    ADD CONSTRAINT reward_unlock_details_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.rewards
    ADD CONSTRAINT rewards_external_id_key UNIQUE (external_id);
ALTER TABLE ONLY public.ro_group_reward_units_actor_utilisation_in_time_period
    ADD CONSTRAINT ro_group_reward_units_actor_utilisation_in_time_period_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.reward_offer_group_reward_units_actor_utilisation
    ADD CONSTRAINT rogruau_actor_id_offer_group_id_uniq_idx UNIQUE (actor_id, offer_group_id);
ALTER TABLE ONLY public.reward_offer_reward_units_actor_utilisation
    ADD CONSTRAINT roruau_actor_id_offer_id_uniq_idx UNIQUE (actor_id, offer_id);
ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);
CREATE INDEX cc_rewards_info_cc_account_id_offer_type_action_time_idx ON public.credit_card_rewards_info USING btree (credit_card_account_id, reward_offer_type, action_timestamp);
CREATE UNIQUE INDEX cc_rewards_info_reward_id_unq_idx ON public.credit_card_rewards_info USING btree (reward_id) INCLUDE (reward_offer_type, credit_card_account_id);
CREATE INDEX cc_rewards_info_updated_at_idx ON public.credit_card_rewards_info USING btree (updated_at);
CREATE UNIQUE INDEX ccrcalmu_cc_account_id_offer_type_from_time_till_time_uniq_idx ON public.cc_rewards_cc_account_level_monthly_utilisation USING btree (credit_card_account_id, reward_offer_type, from_time, till_time);
CREATE INDEX lucky_draws_status_reveal_time_idx ON public.lucky_draws USING btree (lucky_draw_status, lucky_draw_reveal_time);
CREATE INDEX projections_actor_action_offer_type_action_time_idx ON public.reward_projections USING btree (actor_id, action_type, offer_type, action_time);
CREATE INDEX projections_actor_action_offer_type_ref_id_idx ON public.reward_projections USING btree (actor_id, action_type, offer_type, ref_id);
CREATE UNIQUE INDEX projections_actor_offer_ref_id_uniq_idx ON public.reward_projections USING btree (actor_id, offer_id, ref_id);
CREATE INDEX reward_clawbacks_action_ref_id_idx ON public.reward_clawbacks USING btree (action_ref_id);
CREATE UNIQUE INDEX reward_clawbacks_reward_id_action_ref_action_unique_idx ON public.reward_clawbacks USING btree (reward_id, action_ref_id, action);
CREATE INDEX reward_clawbacks_updated_at_idx ON public.reward_clawbacks USING btree (updated_at);
CREATE INDEX reward_offer_action_offer_generation_type_idx ON public.reward_offers USING btree (action_type, offer_type, generation_type);
CREATE INDEX reward_offers_active_since_till_idx ON public.reward_offers USING btree (active_since, active_till);
CREATE INDEX reward_offers_display_segment_id_idx ON public.reward_offers USING btree (display_segment_id);
CREATE INDEX reward_offers_display_till_since_idx ON public.reward_offers USING btree (display_till, display_since);
CREATE INDEX reward_projections_ref_id_idx ON public.reward_projections USING btree (ref_id);
CREATE INDEX rewards_actor_id_action_type_created_at_idx ON public.rewards USING btree (actor_id, action_type, created_at);
CREATE INDEX rewards_actor_id_claim_type_status_idx ON public.rewards USING btree (actor_id, claim_type, status);
CREATE INDEX rewards_actor_id_clawback_ref_id_idx ON public.rewards USING btree (actor_id, clawback_ref_id);
CREATE UNIQUE INDEX rewards_actor_id_global_dedupe_id_unique_idx ON public.rewards USING btree (actor_id, global_dedupe_id);
CREATE INDEX rewards_actor_id_offer_type_reward_type_created_at_idx ON public.rewards USING btree (actor_id, offer_type, type, created_at);
CREATE INDEX rewards_actor_id_sub_status_idx ON public.rewards USING btree (actor_id, sub_status, offer_id);
CREATE UNIQUE INDEX rewards_actor_offer_ref_secondary_ref_id_uniq_idx ON public.rewards USING btree (actor_id, offer_id, ref_id, secondary_ref_id);
CREATE INDEX rewards_external_ref_idx ON public.rewards USING btree (external_ref);
CREATE INDEX rewards_refid_idx ON public.rewards USING btree (ref_id) INCLUDE (id, status);
CREATE INDEX rewards_status_created_at_idx ON public.rewards USING btree (status, created_at);
CREATE INDEX rogruau_updated_at_index ON public.reward_offer_group_reward_units_actor_utilisation USING btree (updated_at);
CREATE UNIQUE INDEX rogruauitp_actor_group_reward_type_from_and_till_time_uniq_idx ON public.ro_group_reward_units_actor_utilisation_in_time_period USING btree (actor_id, group_id, from_time, till_time);
CREATE INDEX roruau_updated_at_index ON public.reward_offer_reward_units_actor_utilisation USING btree (updated_at);
CREATE UNIQUE INDEX roruauitp_actor_offer_reward_type_from_and_till_time_uniq_idx ON public.reward_offer_reward_units_actor_utilisation_in_time_period USING btree (actor_id, offer_id, from_time, till_time);
CREATE INDEX rw_ghr_updated_at_index ON public.gift_hamper_requests USING btree (updated_at);
CREATE INDEX rw_ld_updated_at_index ON public.lucky_draws USING btree (updated_at);
CREATE INDEX rw_ldc_updated_at_index ON public.lucky_draw_campaigns USING btree (updated_at);
CREATE INDEX rw_ldr_updated_at_index ON public.lucky_draw_registrations USING btree (updated_at);
CREATE INDEX rw_ldw_updated_at_index ON public.lucky_draw_winnings USING btree (updated_at);
CREATE INDEX rw_pr_updated_at_index ON public.processing_requests USING btree (updated_at);
CREATE INDEX rw_projections_updated_at_index ON public.reward_projections USING btree (updated_at);
CREATE INDEX rw_ro_group_id_index ON public.reward_offers USING btree (group_id);
CREATE INDEX rw_ro_updated_at_index ON public.reward_offers USING btree (updated_at);
CREATE INDEX rw_roa_updated_at_index ON public.reward_offer_aggregates USING btree (updated_at);
CREATE INDEX rw_roag_updated_at_index ON public.reward_offer_actor_aggregates USING btree (updated_at);
CREATE INDEX rw_rw_actor_id_offer_id_index ON public.rewards USING btree (actor_id, offer_id);
CREATE INDEX rw_rw_actor_id_offer_type_created_at_index ON public.rewards USING btree (actor_id, offer_type, created_at);
CREATE INDEX rw_rw_updated_at_index ON public.rewards USING btree (updated_at);
ALTER TABLE ONLY public.reward_unlock_details
    ADD CONSTRAINT reward_unlock_details_reward_id_fkey FOREIGN KEY (reward_id) REFERENCES public.rewards(id);
