package preapprovedloan

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"
	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palEvents "github.com/epifi/gamma/frontend/preapprovedloan/events"
	loansPkg "github.com/epifi/gamma/pkg/loans"
)

func (s *Service) sendATLJourneyStartedEventIfApplicable(
	ctx context.Context,
	loanHeader *palFeEnumsPb.LoanHeader,
	actorId string,
) {
	if loanHeader.GetLoanProgram() != palFeEnumsPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND {
		logger.Info(ctx, "skip sending AcquireToLendJourneyStarted event", zap.String(logger.ACTOR_ID_V2, actorId))
		return
	}

	deviceId := loansPkg.GetEventsDeviceIDByBestEffort(ctx, actorId, s.usersClient, s.authClient)

	logger.Info(ctx, "sending AcquireToLendJourneyStarted event", zap.String(logger.ACTOR_ID_V2, actorId))
	s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), palEvents.NewAcquireToLendJourneyStarted(
		actorId,
		deviceId,
		loanHeader.GetLoanProgram().String(),
		loanHeader.GetVendor().String(),
	))
	logger.Info(ctx, "sent AcquireToLendJourneyStarted event", zap.String(logger.ACTOR_ID_V2, actorId))
	return
}
