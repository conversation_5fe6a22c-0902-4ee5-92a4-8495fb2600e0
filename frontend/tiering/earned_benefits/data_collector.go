package earned_benefits

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"time"

	rewardsPb "github.com/epifi/gamma/api/rewards"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/frontend/pkg/common"
	"github.com/epifi/gamma/pkg/accrual"

	"github.com/epifi/gamma/frontend/pkg/featureflags"

	"github.com/google/wire"
	errors2 "github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	gmoney "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/frontend/pkg/tiering/amb"

	fireflyAccPb "github.com/epifi/gamma/api/firefly/accounting"
	fireflyV2Pb "github.com/epifi/gamma/api/firefly/v2"
	headerPb "github.com/epifi/gamma/api/frontend/header"
	rewardsFePb "github.com/epifi/gamma/api/frontend/rewards"
	feTieringPb "github.com/epifi/gamma/api/frontend/tiering"
	segmentPb "github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/api/tiering"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/frontend/config/genconf"
	dataCollector "github.com/epifi/gamma/frontend/tiering/data_collector"
	"github.com/epifi/gamma/frontend/tiering/errors"
	"github.com/epifi/gamma/frontend/tiering/hierarchy"
	"github.com/epifi/gamma/pkg/feature/release"
	rewardsPkg "github.com/epifi/gamma/pkg/rewards"
)

var EarnedBenefitsWireSet = wire.NewSet(
	wire.NewSet(NewEarnedBenefitsDataCollectorService, wire.Bind(new(EarnedBenefitsDataCollector), new(*EarnedBenefitsDataCollectorService))),
	wire.NewSet(NewComponentBuilder, wire.Bind(new(IComponentBuilder), new(*ComponentBuilder))),
)

type EarnedBenefitsDataCollector interface {
	GatherData(ctx context.Context, actorId string, componentsToLoad map[feTieringPb.BenefitsType]bool, req *headerPb.RequestHeader) (*EarnedBenefitData, error)

	GatherDataForHistories(ctx context.Context, actorId string, monthsToFetch []*dataCollector.MonthYear) (*EarnedBenefitHistoriesData, error)
}

type EarnedBenefitsDataCollectorService struct {
	gconf              *genconf.Config
	dataCollector      dataCollector.DataCollector
	segmentClient      segmentPb.SegmentationServiceClient
	tieringClient      tiering.TieringClient
	rewardsFeClient    rewardsFePb.RewardsClient
	releaseEvaluator   release.IEvaluator
	fireFlyAccClientV2 fireflyV2Pb.FireflyV2Client
	fireFlyAccClient   fireflyAccPb.AccountingClient
}

func NewEarnedBenefitsDataCollectorService(gconf *genconf.Config, dataCollector dataCollector.DataCollector,
	segmentClient segmentPb.SegmentationServiceClient, tieringClient tiering.TieringClient, rewardsFeClient rewardsFePb.RewardsClient, releaseEvaluator release.IEvaluator,
	fireFlyAccClientV2 fireflyV2Pb.FireflyV2Client, fireFlyAccClient fireflyAccPb.AccountingClient) *EarnedBenefitsDataCollectorService {
	return &EarnedBenefitsDataCollectorService{
		gconf: gconf, dataCollector: dataCollector,
		segmentClient:      segmentClient,
		tieringClient:      tieringClient,
		rewardsFeClient:    rewardsFeClient,
		releaseEvaluator:   releaseEvaluator,
		fireFlyAccClientV2: fireFlyAccClientV2,
		fireFlyAccClient:   fireFlyAccClient,
	}
}

func (e *EarnedBenefitsDataCollectorService) GatherData(ctx context.Context, actorId string, componentsToLoad map[feTieringPb.BenefitsType]bool, req *headerPb.RequestHeader) (*EarnedBenefitData, error) {
	data := &EarnedBenefitData{
		actorId:              actorId,
		fetchFailedMap:       make(map[feTieringPb.BenefitsType]bool),
		segmentMembershipMap: make(map[string]*segmentPb.SegmentMembership),
	}

	firstIterErr := e.gatherDataFirstIteration(ctx, actorId, componentsToLoad, data)
	if firstIterErr != nil {
		return nil, fmt.Errorf("failed to gather data in first iteration, %w", firstIterErr)
	}

	if !IsTierAllowedForEarnedBenefitsScreen(data.GetTierToLoad()) {
		// this should not happen since we redirect to different deeplink when user is in lower tier
		return nil, fmt.Errorf("tiering benefits page not applicable for %s tier, %w", data.GetTierToLoad().String(), errors.ErrEarnedBenefitsScreenNotApplicable)
	}

	secondIterErr := e.gatherDataSecondIteration(ctx, actorId, componentsToLoad, data)
	if secondIterErr != nil {
		return nil, fmt.Errorf("failed to gather data in second iteration, %w", secondIterErr)
	}

	data.tierToPitch = e.getTierToPitch(ctx, data.GetTierToLoad())

	thirdIterErr := e.gatherDataThirdIteration(ctx, actorId, componentsToLoad, data, req)
	if thirdIterErr != nil {
		return nil, fmt.Errorf("failed to gather data in third iteration, %w", thirdIterErr)
	}

	var totalBenefitsErr error
	data.totalBenefitsEarned, totalBenefitsErr = computeTotalBenefitsEarned(data)
	if totalBenefitsErr != nil {
		return nil, fmt.Errorf("failed to compute total benefits earned, %w", totalBenefitsErr)
	}

	return data, nil
}

func (e *EarnedBenefitsDataCollectorService) GatherDataForHistories(ctx context.Context, actorId string, monthsToFetch []*dataCollector.MonthYear) (*EarnedBenefitHistoriesData, error) {
	data := &EarnedBenefitHistoriesData{}

	var gatherErr error
	data.earnedBenefitsData, gatherErr = e.GatherData(ctx, actorId, GetComponentsToLoadForEarnedBenefitsScreen([]string{}), nil)
	if gatherErr != nil {
		return nil, fmt.Errorf("failed to gather earned benefits data, %w", gatherErr)
	}

	if len(data.GetEarnedBenefitsData().GetFetchFailedMap()) != 0 {
		var failedComponents string
		for failedComponent := range data.GetEarnedBenefitsData().GetFetchFailedMap() {
			failedComponents += failedComponent.String()
		}
		return nil, fmt.Errorf("fetch failed for components: %s", failedComponents)
	}

	monthsToFetch = e.filterMonthsToFetch(data.GetEarnedBenefitsData().GetTierTimeRanges(), monthsToFetch)

	gatherMonthlyDataErr := e.gatherMonthlyDataForHistories(ctx, actorId, data, monthsToFetch)
	if gatherMonthlyDataErr != nil {
		return nil, fmt.Errorf("failed to gather monthly data for histories, %w", gatherMonthlyDataErr)
	}

	monthlyBenefitsDispDataErr := e.populateMonthlyBenefitsDisplayData(monthsToFetch, data)
	if monthlyBenefitsDispDataErr != nil {
		return nil, fmt.Errorf("failed to get display data for monthly benefits, %w", monthlyBenefitsDispDataErr)
	}

	return data, nil
}

func (e *EarnedBenefitsDataCollectorService) filterMonthsToFetch(tierTimeRanges []*tiering.TimeRange, monthsToFetch []*dataCollector.MonthYear) []*dataCollector.MonthYear {
	if len(tierTimeRanges) == 0 {
		return nil
	}

	var filteredMonthsToFetch []*dataCollector.MonthYear
	firstMonthInTier := dataCollector.GetMonthYear(tierTimeRanges[0].GetFromTime().AsTime())
	for _, monthToFetch := range monthsToFetch {
		if monthToFetch.After(firstMonthInTier) || dataCollector.IsMonthYearEqual(monthToFetch, firstMonthInTier) {
			filteredMonthsToFetch = append(filteredMonthsToFetch, monthToFetch)
		}
	}

	return filteredMonthsToFetch
}

func (e *EarnedBenefitsDataCollectorService) gatherDataFirstIteration(ctx context.Context, actorId string, componentsToLoad map[feTieringPb.BenefitsType]bool, data *EarnedBenefitData) error {
	g, gCtx := errgroup.WithContext(ctx)

	g.Go(func() error {
		var tieringEssentialsErr error
		data.tieringEssentials, tieringEssentialsErr = e.dataCollector.GetTieringEssentials(ctx, actorId)
		if tieringEssentialsErr != nil {
			return fmt.Errorf("failed to get tiering essentials data, %w", tieringEssentialsErr)
		}

		data.tierToLoad = GetTierToLoad(data.GetTieringEssentials().GetCurrentTier(), data.GetTieringEssentials().GetPreviousTier(), data.GetTieringEssentials().GetIsUserInDowngradedWindow())

		return nil
	})

	g.Go(func() error {
		externalDeps := &common.ExternalDependencies{
			Evaluator: e.releaseEvaluator,
		}
		FiCoinsToFiPointsPreMigrationPhaseFlagReq := &featureflags.IsFeatureFiCoinsToFiPointsPreMigrationPhaseEnabledRequest{
			ActorId:      actorId,
			ExternalDeps: externalDeps,
		}

		data.isFiCoinsPreMigrationEnabled = featureflags.IsFeatureFiCoinsToFiPointsPreMigrationPhaseEnabled(ctx, FiCoinsToFiPointsPreMigrationPhaseFlagReq)

		// Generate deeplink only after we know the feature flag status
		if data.isFiCoinsPreMigrationEnabled {
			data.fiCoinsFiPointsDeeplink = rewardsPkg.GetFiCoinsFiPointsKnowMoreButtonDeeplink(gCtx, actorId, e.fireFlyAccClient, e.fireFlyAccClientV2, e.tieringClient, true)
		}
		return nil
	})

	g.Go(func() error {
		externalDeps := &common.ExternalDependencies{
			Evaluator: e.releaseEvaluator,
		}
		FiCoinsToFiPointsPostMigrationPhaseFlagReq := &featureflags.IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabledRequest{
			ActorId:      actorId,
			ExternalDeps: externalDeps,
		}

		data.isFiCoinsPostMigrationEnabled = featureflags.IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabled(ctx, FiCoinsToFiPointsPostMigrationPhaseFlagReq)

		// Fetch the post-migration deeplink only if the post-migration feature flag is enabled
		if data.isFiCoinsPostMigrationEnabled {
			data.fiCoinsFiPointsDeeplink = rewardsPkg.GetFiCoinsFiPointsKnowMoreButtonDeeplink(gCtx, actorId, e.fireFlyAccClient, e.fireFlyAccClientV2, e.tieringClient, false)
		}
		return nil
	})

	g.Go(func() error {
		if !componentsToLoad[feTieringPb.BenefitsType_BENEFITS_TYPE_DEBIT_CARD] {
			return nil
		}

		var dcOrderChargesErr error
		data.debitCardOrderChargesData, dcOrderChargesErr = e.dataCollector.GetDebitCardOrderCharges(ctx, actorId)
		if dcOrderChargesErr != nil || e.shouldFailComponentForActor(actorId) {
			logger.Error(ctx, "failed to get dc order charges data", zap.Error(dcOrderChargesErr))
			data.GetFetchFailedMap()[feTieringPb.BenefitsType_BENEFITS_TYPE_DEBIT_CARD] = true
		}

		return nil
	})

	g.Go(func() error {
		var getEmployerErr error
		data.employerData, getEmployerErr = e.dataCollector.GetEmploymentInfo(ctx, actorId)
		if getEmployerErr != nil {
			if errors2.Is(getEmployerErr, epifierrors.ErrRecordNotFound) {
				return nil
			}
			return fmt.Errorf("failed to get employer data, %w", getEmployerErr)
		}
		return nil
	})

	g.Go(func() error {
		var getHealthInsuranceDetailsErr error
		data.healthInsuranceDetails, getHealthInsuranceDetailsErr = e.dataCollector.GetHealthInsuranceDetails(ctx, actorId)
		if getHealthInsuranceDetailsErr != nil {
			return fmt.Errorf("failed to get health insurance details, %w", getHealthInsuranceDetailsErr)
		}

		return nil
	})

	g.Go(func() error {
		var trialErr error
		data.trialsResponse, trialErr = e.tieringClient.GetTrialDetails(ctx, &tiering.GetTrialDetailsRequest{ActorId: actorId})
		if rpcErr := epifigrpc.RPCError(data.trialsResponse, trialErr); rpcErr != nil {
			// gracefully handle failure
			logger.Error(ctx, "error in GetTrialDetails rpc", zap.Error(rpcErr))
		}

		return nil
	})

	gErr := g.Wait()
	if gErr != nil {
		return fmt.Errorf("failed to fetch data in err group, %w", gErr)
	}

	return nil
}

// nolint:funlen
func (e *EarnedBenefitsDataCollectorService) gatherDataSecondIteration(ctx context.Context, actorId string, componentsToLoad map[feTieringPb.BenefitsType]bool, data *EarnedBenefitData) error {
	g, _ := errgroup.WithContext(ctx)

	g.Go(func() error {
		var getBalErr error
		data.userBalance, getBalErr = e.dataCollector.GetBalance(ctx, actorId)
		if getBalErr != nil {
			return fmt.Errorf("failed to get balance data, %w", getBalErr)
		}

		return nil
	})

	g.Go(func() error {
		if data.GetTierToLoad() == tieringExtPb.Tier_TIER_UNSPECIFIED {
			return fmt.Errorf("cannot fetch tier time range for %s tier", data.GetTierToLoad().String())
		}

		var getTimeRangeErr error
		data.tierTimeRanges, getTimeRangeErr = e.dataCollector.GetTierTimeRange(ctx, actorId, data.GetTierToLoad())
		if getTimeRangeErr != nil {
			return fmt.Errorf("failed to get tier time range for the user for tier %s, %w", data.GetTierToLoad().String(), getTimeRangeErr)
		}

		return nil
	})

	g.Go(func() error {
		// skip fetching forex refund since fetching dc charges order charges has failed in prev iteration - as we do not load the dc component
		if data.GetFetchFailedMap()[feTieringPb.BenefitsType_BENEFITS_TYPE_DEBIT_CARD] {
			return nil
		}

		if data.GetTieringEssentials() == nil {
			logger.Error(ctx, "cannot load forex refund data without tiering essentials")
			data.GetFetchFailedMap()[feTieringPb.BenefitsType_BENEFITS_TYPE_DEBIT_CARD] = true
			return nil
		}

		var getForexDataErr error
		data.forexRefundAtTier, getForexDataErr = e.dataCollector.GetForexRefundAtTier(ctx, actorId, data.GetTierToLoad(), time.Now().Add(-e.gconf.Tiering().ForexRefundFilterDepth()), time.Now())
		if getForexDataErr != nil {
			logger.Error(ctx, "failed to get forex refund data", zap.Error(getForexDataErr))
			data.GetFetchFailedMap()[feTieringPb.BenefitsType_BENEFITS_TYPE_DEBIT_CARD] = true
			return nil
		}

		return nil
	})

	g.Go(func() error {
		var getAaSalaryErr error
		data.aaSalaryData, getAaSalaryErr = e.dataCollector.GetAaSalaryData(ctx, actorId)
		if getAaSalaryErr != nil {
			logger.Error(ctx, "failed to get prime data", zap.Error(getAaSalaryErr))
			data.GetFetchFailedMap()[feTieringPb.BenefitsType_BENEFITS_TYPE_MONTHLY_TRANSFER_VIEW] = true
		}

		return nil
	})

	g.Go(func() error {
		if !componentsToLoad[feTieringPb.BenefitsType_BENEFITS_TYPE_OTHER] {
			return nil
		}

		if data.GetTierToLoad().IsAaSalaryTier() {
			return nil
		}

		var chequeBkChargesErr error
		data.chequeBookOrderData, chequeBkChargesErr = e.dataCollector.GetChequeBookRefundDetails(ctx, actorId)
		if chequeBkChargesErr != nil || e.shouldFailComponentForActor(actorId) {
			logger.Error(ctx, "failed to get chequebook refund details", zap.Error(chequeBkChargesErr))
			data.GetFetchFailedMap()[feTieringPb.BenefitsType_BENEFITS_TYPE_OTHER] = true
		}

		return nil
	})

	g.Go(func() error {
		evaluatedTierResp, evaluatedTierErr := e.tieringClient.EvaluateTierForActor(ctx, &tiering.EvaluateTierForActorRequest{
			ActorId: actorId,
			Options: &tiering.EvaluateTierForActorRequest_Options{
				ToEvalForMultipleWays: data.GetTieringEssentials().GetIsMultipleWaysToEnterTieringEnabledForActor(),
				ToSkipAppAccessCheck:  true,
			},
		})
		if rpcErr := epifigrpc.RPCError(evaluatedTierResp, evaluatedTierErr); rpcErr != nil {
			return fmt.Errorf("evaluate tier for actor %s: %w", actorId, rpcErr)
		}

		data.evaluatedTier = evaluatedTierResp.GetEvaluatedTier()
		return nil
	})

	g.Go(func() error {
		segments := data.GetTieringEssentials().GetGetConfigParamsResp().GetCriteriaSegmentExclusionConfigs().GetAaSalaryExcludedSegments()
		if len(segments) == 0 {
			return nil
		}

		isMemberResp, isMemberErr := e.segmentClient.IsMember(ctx, &segmentPb.IsMemberRequest{
			ActorId:    actorId,
			SegmentIds: segments,
			LatestBy:   timestamppb.Now(),
		})
		if rpcErr := epifigrpc.RPCError(isMemberResp, isMemberErr); rpcErr != nil {
			return fmt.Errorf("segmentClient.IsMember rpc failed, %w", rpcErr)
		}
		data.segmentMembershipMap = isMemberResp.GetSegmentMembershipMap()
		return nil
	})

	// Add AMB fetching logic
	g.Go(func() error {
		// Skip regular tier scenarios as instructed
		if data.GetTierToLoad() == tieringExtPb.Tier_TIER_FI_REGULAR {
			return nil
		}

		// Check if AMB is enabled for this actor
		data.isAmbEnabledForActor = amb.IsAmbEnabledForActor(ctx, actorId, e.gconf.Tiering(), e.tieringClient, e.segmentClient, e.releaseEvaluator)
		if !data.isAmbEnabledForActor {
			return nil
		}

		// Fetch AMB info
		ambRes, err := e.tieringClient.GetAMBInfo(ctx, &tiering.GetAMBInfoRequest{ActorId: actorId})
		if te := epifigrpc.RPCError(ambRes, err); te != nil {
			logger.Error(ctx, "error fetching amb info", zap.Error(te))
			return nil // Don't fail the entire request if AMB info fails
		}

		data.ambInfo = ambRes

		// Check if shortfall exists
		doesShortfallExist, err := money.IsGreaterThan(ambRes.GetShortfallAmount(), money.ZeroINR().GetPb())
		if err != nil {
			logger.Error(ctx, "error checking if shortfall exists", zap.Error(err))
			return nil
		}

		data.ambShortfallExists = doesShortfallExist
		return nil
	})

	gErr := g.Wait()
	if gErr != nil {
		return fmt.Errorf("failed to fetch data in err group, %w", gErr)
	}

	return nil
}

func (e *EarnedBenefitsDataCollectorService) gatherDataThirdIteration(ctx context.Context, actorId string, _ map[feTieringPb.BenefitsType]bool, data *EarnedBenefitData, req *headerPb.RequestHeader) error {
	g, gCtx := errgroup.WithContext(ctx)

	g.Go(func() error {
		if data.GetTierToLoad() == tieringExtPb.Tier_TIER_UNSPECIFIED || data.GetTierTimeRanges() == nil {
			return fmt.Errorf("cannot load rewards data without data from prev iterations, "+
				"tier_to_load: %s, len(tier_time_ranges): %d", data.GetTierToLoad().String(), len(data.GetTierTimeRanges()))
		}

		var getRewardsDataErr error
		data.rewardsData, getRewardsDataErr = e.dataCollector.GetRewardsDataForEarnedBenefits(gCtx, actorId, data.GetTierToLoad(), data.GetTierToPitch(), data.GetTierTimeRanges())
		if getRewardsDataErr != nil {
			return fmt.Errorf("failed to get rewards data, %w", getRewardsDataErr)
		}
		return nil
	})

	g.Go(func() error {
		if req == nil {
			return nil
		}
		var getCatalogOffersErr error
		data.catalogOffers, getCatalogOffersErr = e.rewardsFeClient.GetCatalogOffersAndFilters(gCtx, &rewardsFePb.GetCatalogOffersAndFiltersRequest{
			Req: req,
		})
		if getCatalogOffersErr != nil {
			return fmt.Errorf("failed to get catalog offers and filters, %w", getCatalogOffersErr)
		}
		return nil
	})

	var rewardsResponse *rewardsPb.RewardsResponse
	g.Go(func() error {
		rewardResp, err := e.dataCollector.GetRewardsByActorId(gCtx, actorId, data.GetTieringEssentials().GetCurrentTier())
		if rpcErr := epifigrpc.RPCError(rewardResp, err); rpcErr != nil {
			logger.Error(ctx, "error in GetRewardsByActorId rpc", zap.Error(rpcErr))
			return fmt.Errorf("error in GetRewardsByActorId rpc, err: %w", rpcErr)
		}
		rewardsResponse = rewardResp
		return nil
	})

	g.Go(func() error {
		var err error
		data.isMoneyPlantEnabled, err = e.releaseEvaluator.Evaluate(gCtx, release.NewCommonConstraintData(typesv2.Feature_FEATURE_MONEY_PLANT_EARNED_BENEFITS).WithActorId(actorId))
		if err != nil {
			logger.Error(gCtx, "failed to evaluate money plant feature", zap.Error(err))
			return errors2.Wrap(err, "failed to evaluate money plant feature")
		}
		return nil
	})

	gErr := g.Wait()
	if gErr != nil {
		return fmt.Errorf("failed to fetch data in err group, %w", gErr)
	}
	data.RewardsResponse = rewardsResponse
	return nil
}
func (e *EarnedBenefitsDataCollectorService) gatherMonthlyDataForHistories(ctx context.Context, actorId string, data *EarnedBenefitHistoriesData, monthsToFetch []*dataCollector.MonthYear) error {
	errGrp, gCtx := errgroup.WithContext(ctx)

	errGrp.Go(func() error {
		var getRewardsDataErr error
		data.monthlyRewardsData, getRewardsDataErr = e.dataCollector.GetRewardHistoryForEarnedBenefits(gCtx, actorId, data.GetEarnedBenefitsData().GetTierToLoad(), monthsToFetch)
		if getRewardsDataErr != nil {
			return fmt.Errorf("failed to get reward history data, %w", getRewardsDataErr)
		}

		return nil
	})

	errGrp.Go(func() error {
		var getForexDataErr error
		data.monthlyForexData, getForexDataErr = e.dataCollector.GetMonthlyForexRefundAtTier(ctx, actorId, data.GetEarnedBenefitsData().GetTierToLoad(), monthsToFetch)
		if getForexDataErr != nil {
			return fmt.Errorf("failed to get forex refund history, %w", getForexDataErr)
		}

		return nil
	})

	err := errGrp.Wait()
	if err != nil {
		return fmt.Errorf("failed to get monthly data for earned benefit histories in err group, %w", err)
	}

	return nil
}

// populateMonthlyBenefitsDisplayData assumes that rewards and forex refund gathered data are sorted based on month year.
// and fails if it finds unsorted / missing data for a month
func (e *EarnedBenefitsDataCollectorService) populateMonthlyBenefitsDisplayData(months []*dataCollector.MonthYear, data *EarnedBenefitHistoriesData) error {
	if len(months) != len(data.GetMonthlyRewardsData()) || len(months) != len(data.GetMonthlyForexData()) {
		return fmt.Errorf("mismatch in length of requested data and gathered data, "+
			"len(months) - %d, len(monthlyRewardsData) - %d, len(monthlyForexData) - %d",
			len(months), len(data.GetMonthlyRewardsData()), len(data.GetMonthlyForexData()))
	}

	for idx, month := range months {
		monthRewardData := data.GetMonthlyRewardsData()[idx]
		if !dataCollector.IsMonthYearEqual(month, monthRewardData.GetMonthYear()) {
			return fmt.Errorf("mismatch in requested month and gathered rewards month, month - %s, gathered month - %s",
				month.GetStartOfMonth().Format(monthYearFormat), monthRewardData.GetMonthYear().GetStartOfMonth().Format(monthYearFormat))
		}

		monthForexData := data.GetMonthlyForexData()[idx]
		if !dataCollector.IsMonthYearEqual(month, monthForexData.GetMonthYear()) {
			return fmt.Errorf("mismatch in requested month and gathered forex month, month - %s, gathered month - %s",
				month.GetStartOfMonth().Format(monthYearFormat), monthForexData.GetMonthYear().GetStartOfMonth().Format(monthYearFormat))
		}

		cashbackEarnedValue := monthRewardData.GetCash()

		feesSaved := monthForexData.GetForexRefund()
		if shouldAdd, amountToAdd := e.shouldAddDebitCardChargesForMonth(data, month); shouldAdd {
			var sumErr error
			feesSaved, sumErr = money.Sum(feesSaved, amountToAdd)
			if sumErr != nil {
				return fmt.Errorf("failed to add fees saved and dc saved amount, %w", sumErr)
			}
		}

		if shouldAdd, amountToAdd := e.shouldAddChequeBookChargesForMonth(data, month); shouldAdd {
			var sumErr error
			feesSaved, sumErr = money.Sum(feesSaved, amountToAdd)
			if sumErr != nil {
				return fmt.Errorf("failed to add fees saved and cheque book saved amount, %w", sumErr)
			}
		}

		fiCoinsConvertedMoneyUnits := float64(monthRewardData.GetFiCoins()) * rewardsPkg.FiCoinsToCashConversionRatioForNonCCUser
		if month.GetStartOfMonth().Add(time.Second).After(accrual.GetFiCoinsToFiPointsMigrationTime()) {
			fiCoinsConvertedMoneyUnits = float64(monthRewardData.GetFiCoins()) * rewardsPkg.FiPointsToCashConversionRatio
		}

		totalBenefitsEarned, sumErr := money.Sum(feesSaved, &gmoney.Money{
			CurrencyCode: money.RupeeCurrencyCode,
			Units:        int64(math.Ceil(fiCoinsConvertedMoneyUnits)),
		})
		if sumErr != nil {
			return fmt.Errorf("failed to sum fees saved with fi coins converted money, %w", sumErr)
		}

		// skip adding cashback value for plus
		// todo: handle the edge case where user earned cashback while at infinite/salary but got credited when he was at Plus (5th of next month)
		if data.GetEarnedBenefitsData().GetTierToLoad() != tieringExtPb.Tier_TIER_FI_PLUS {
			totalBenefitsEarned, sumErr = money.Sum(totalBenefitsEarned, cashbackEarnedValue)
			if sumErr != nil {
				return fmt.Errorf("failed to sum total benefits earned with monthly rewards, %w", sumErr)
			}
		}

		displayData := &monthlyBenefitsDisplayData{
			monthYear:           month,
			monthYearStr:        month.GetStartOfMonth().Format(monthYearFormat),
			cashbackEarned:      cashbackEarnedValue,
			feesSaved:           feesSaved,
			fiCoinsEarned:       int32(monthRewardData.GetFiCoins()),
			totalBenefitsEarned: totalBenefitsEarned,
		}

		if !money.AreEquals(displayData.GetTotalBenefitsEarned(), money.ZeroINR().GetPb()) || displayData.GetMonthYear().IsCurrMonth() {
			data.monthlyBenefitsDisplayData = append(data.monthlyBenefitsDisplayData, displayData)
		}
	}

	return nil
}

func (e *EarnedBenefitsDataCollectorService) shouldAddDebitCardChargesForMonth(data *EarnedBenefitHistoriesData, month *dataCollector.MonthYear) (bool, *gmoney.Money) {
	dcOrderData := data.GetEarnedBenefitsData().GetDebitCardOrderChargesData()

	if !dcOrderData.GetHasOrderedDebitCard() {
		return false, nil
	}

	if dcOrderData.GetTierAtOrderedTime() != data.GetEarnedBenefitsData().GetTierToLoad() {
		return false, nil
	}

	if dcOrderData.GetOrderedAt().Year() != month.GetYear() || dcOrderData.GetOrderedAt().Month() != month.GetMonth() {
		return false, nil
	}

	return true, dcOrderData.GetDiscountedAmount()
}

func (e *EarnedBenefitsDataCollectorService) shouldAddChequeBookChargesForMonth(data *EarnedBenefitHistoriesData, month *dataCollector.MonthYear) (bool, *gmoney.Money) {
	chkBkOrderData := data.GetEarnedBenefitsData().GetChequeBookOrderData()

	if !chkBkOrderData.GetHasOrderedChequeBook() {
		return false, nil
	}

	if chkBkOrderData.GetTierAtOrderTime() != data.GetEarnedBenefitsData().GetTierToLoad() {
		return false, nil
	}

	if chkBkOrderData.GetOrderedAt().Year() != month.GetYear() || chkBkOrderData.GetOrderedAt().Month() != month.GetMonth() {
		return false, nil
	}

	if !chkBkOrderData.GetHasGotRefund() {
		return false, nil
	}

	return true, chkBkOrderData.GetChequeBookChargesAtOrderTime()
}

func IsTierAllowedForEarnedBenefitsScreen(tier tieringExtPb.Tier) bool {
	return lo.Contains([]tieringExtPb.Tier{
		tieringExtPb.Tier_TIER_FI_PLUS,
		tieringExtPb.Tier_TIER_FI_INFINITE,
		tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1,
		tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_2,
		tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3,
		tieringExtPb.Tier_TIER_FI_SALARY,
		tieringExtPb.Tier_TIER_FI_SALARY_BASIC,
	}, tier)
}

func ShouldShowTieringEarnedBenefitsScreen(currTier, prevTier tieringExtPb.Tier, isUserDowngraded bool) bool {
	tierToLoad := GetTierToLoad(currTier, prevTier, isUserDowngraded)
	return IsTierAllowedForEarnedBenefitsScreen(tierToLoad)
}

func (e *EarnedBenefitsDataCollectorService) getTierToPitch(ctx context.Context, tier tieringExtPb.Tier) tieringExtPb.Tier {
	higherTiers := hierarchy.GetNextHighestTiers(tier)
	if len(higherTiers) == 0 {
		return tieringExtPb.Tier_TIER_UNSPECIFIED
	}

	if tier == tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1 || tier == tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_2 {
		return tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3
	}

	if lo.Contains(higherTiers, tieringExtPb.Tier_TIER_FI_SALARY) || tier == tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3 {
		return tieringExtPb.Tier_TIER_FI_SALARY
	}

	if len(higherTiers) == 1 {
		return higherTiers[0]
	}

	return tieringExtPb.Tier_TIER_UNSPECIFIED
}

// GetTierToLoad tells which tiering benefits screen to load for the user
func GetTierToLoad(currTier, prevTier tieringExtPb.Tier, isUserDowngraded bool) tieringExtPb.Tier {
	tierToLoad := currTier
	if isUserDowngraded {
		tierToLoad = prevTier

		if prevTier.IsAaSalaryTier() && currTier.IsAaSalaryTier() {
			tierToLoad = currTier
		}
	}

	return tierToLoad
}

func (e *EarnedBenefitsDataCollectorService) shouldFailComponentForActor(actorId string) bool {
	if cfg.IsProdEnv(e.gconf.Application().Environment) {
		return false
	}

	if !lo.Contains(e.gconf.Tiering().FailEarnedBenefitComponentsForActors().ToStringArray(), actorId) {
		return false
	}

	// nolint:gosec
	if rand.Int()/2 == 0 {
		return true
	}

	return false
}

func computeTotalBenefitsEarned(data *EarnedBenefitData) (*gmoney.Money, error) {
	var sumErr error
	totalBenefitsEarned := money.ZeroINR().GetPb()

	totalBenefitsEarned, sumErr = money.Sum(totalBenefitsEarned, data.GetRewardsData().GetLifetimeTotalRewards())
	if sumErr != nil {
		return nil, fmt.Errorf("failed to add lifetime total rewards, %w", sumErr)
	}

	if !data.GetFetchFailedMap()[feTieringPb.BenefitsType_BENEFITS_TYPE_DEBIT_CARD] {
		totalBenefitsEarned, sumErr = money.Sum(totalBenefitsEarned, data.GetForexRefundAtTier())
		if sumErr != nil {
			return nil, fmt.Errorf("failed to add forex refund, %w", sumErr)
		}

		dcOrderedData := data.GetDebitCardOrderChargesData()
		if dcOrderedData.GetHasOrderedDebitCard() && dcOrderedData.GetTierAtOrderedTime() == data.GetTierToLoad() {
			totalBenefitsEarned, sumErr = money.Sum(totalBenefitsEarned, dcOrderedData.GetDiscountedAmount())
			if sumErr != nil {
				return nil, fmt.Errorf("failed to add dc discount amount, %w", sumErr)
			}
		}
	}

	if !data.GetFetchFailedMap()[feTieringPb.BenefitsType_BENEFITS_TYPE_OTHER] {
		chkBkOrderedData := data.GetChequeBookOrderData()
		if chkBkOrderedData.GetHasOrderedChequeBook() && chkBkOrderedData.GetHasGotRefund() && chkBkOrderedData.GetTierAtOrderTime() == data.GetTierToLoad() {
			totalBenefitsEarned, sumErr = money.Sum(totalBenefitsEarned, chkBkOrderedData.GetChequeBookChargesAtOrderTime())
			if sumErr != nil {
				return nil, fmt.Errorf("failed to add cheque book refund, %w", sumErr)
			}
		}
	}

	return totalBenefitsEarned, nil
}
