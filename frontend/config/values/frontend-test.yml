Application:
  Environment: "test"
  Name: "frontend"
  EnableDeviceIntegrityCheck: false
  EnableLocationInterceptor: true
  MaxGRPCTimeout: "30s"

Server:
  Ports:
    GrpcPort: 8082
    GrpcSecurePort: 9509
    HttpPort: 9887

SecureLogging:
  EnableSecureLog: true
  EnablePartnerLog: true
  SecureLogPath: "/tmp/secure.log"
  PartnerLogPath: "/tmp/partner.log"
  MaxSizeInMBs: 5
  MaxBackups: 20

RedisOptions:
  IsSecureRedis: false
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 0
    ClientName: home

LegalDocuments:
  FiTncUrl: "https://web.staging.pointz.in/T&C"
  FederalBankTncUrl: "https://www.federalbank.co.in/epifi-tandc#CASA"
  FiPrivacyPolicyUrl: "https://web.staging.pointz.in/privacy"
  FiWealthTncUrl: "https://web.staging.pointz.in/wealth/TnC"
  OpenSourceLicenses:
    Firebase: ""
    Cronet: ""
    ChromeWebView: ""

#json file path
PayErrorViewJson: "./mappingJson/errorViewMapping.json"
CardErrorViewJson: "./mappingJson/cardErrorViewMapping.json"
DisplayCategoryMappingJson: "./mappingJson/displayCategoryMapping.json"

Flags:
  SkipAddMoney: false
  TrimDebugMessageFromStatus: false
  EnableCardTracking: true
  EnableCardBlock: true
  MotherFatherKycNameCheckFeatureConfig:
    MinAndroidVersion: 10
    MinIOSVersion: 10
    FallbackToEnableFeature: true
    DisableFeature: false
  EnableCardQRCodeScan: true
  EnableCheckForAccessRevoke: true
  EnableCardOnlineTxnEnabledTile: true
  EnableVKYCOnlyForInternalUsers: false
  EnableVkycScheduleFlow: true
  EnableCardOffers: true
  SkipUserRevokeStateCheck: false
  EnableCardTrackingAndActivationChanges:
    IsEnableOnAndroid: true
    MinAndroidVersion: 117
    IsEnableOnIos: true
    MinIosVersion: 200
  EnableCCAllTransactionFeatureFlag:
    IsFeatureRestricted: false
  EnableCCRecentActivityFeatureFlag:
    IsFeatureRestricted: false
  EnableCCTimelineEventFeatureFlag:
    IsFeatureRestricted: false
  UpiMaxRetriesPinSet:
    IsEnableOnAndroid: true
    MinAndroidVersion: 1
    IsEnableOnIos: true
    MinIosVersion: 1
  EnableAutoInvestComponentOnInvestLanding:
    MinAndroidVersion: 231
    MinIOSVersion: 333
    FallbackToEnableFeature: false
    DisableFeature: false
  EnableUSStocksInstrumentCardFlag: true
  EnableMFInstrumentCardFlag: true
  EnableSDInstrumentCardFlag: true
  EnableFDInstrumentCardFlag: true
  EnableJumpInstrumentCardFlag: true
  EnableInvestmentLandingQuickLinksComponent:
    MinAndroidVersion: 10000
    MinIOSVersion: 1272
    FallbackToEnableFeature: false
    DisableFeature: false
  EnablePartnersComponentInvestLanding:
    IsEnableOnAndroid: false
    MinAndroidVersion: 1000
    IsEnableOnIos: false
    MinIosVersion: 1000
  EnableCCBillDashboardV2FeatureFlag:
    IsEnableOnAndroid: true
    MinAndroidVersion: 281
    IsEnableOnIos: true
    MinIosVersion: 1674
  EnableSecuredCardsRewardsDashboard:
    IsEnableOnAndroid: true
    MinAndroidVersion: 281
    IsEnableOnIos: true
    MinIosVersion: 381
  EnableGenericRewardsDashboard:
    IsEnableOnAndroid: false
    MinAndroidVersion: 3000
    IsEnableOnIos: false
    MinIosVersion: 3000

RudderStack:
  Host: "https://rudder.pointz.in"
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

RewardsFrontendMeta:
  MinAndroidAppVersionSupportingYourRewardsCardV2: 0
  MinIosAppVersionSupportingYourRewardsCardV2: 0
  AndroidAppVersionsNotSupportingMultiChoiceRewards: [ 0 ]
  TagsConfig:
    TagNameToConfigMap:
      reward_type_cash:
        RenderLocationToDisplayDetailsMap:
          default:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_cash_icon.png"
            DisplayText: "Win cash"
            TextColor: "#B9B9B9"
            BackgroundColor: "#383838"
            Priority: 1
      salary_exclusive:
        RenderLocationToDisplayDetailsMap:
          default:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_salary_exclusive_icon.png"
            DisplayText: "Exclusive"
            TextColor: "#B9B9B9"
            BackgroundColor: "#383838"
            Priority: 2
      discounted:
        RenderLocationToDisplayDetailsMap:
          default:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_discounted_icon.png"
            DisplayText: "Discounted"
            TextColor: "#B9B9B9"
            BackgroundColor: "#383838"
            Priority: 3
      redemption_mechanism_cbr:
        RenderLocationToDisplayDetailsMap:
          default:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_cbr_icon.png"
            DisplayText: "Play & Win"
            TextColor: "#B9B9B9"
            BackgroundColor: "#383838"
            Priority: 4
      reward_type_egv:
        RenderLocationToDisplayDetailsMap:
          default:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_egv_icon.png"
            DisplayText: "Vouchers"
            TextColor: "#B9B9B9"
            BackgroundColor: "#383838"
            Priority: 5
      reward_type_physical_merchandise:
        RenderLocationToDisplayDetailsMap:
          default:
            ImageUrl: "https://epifi-icons.pointz.in/rewards/filters_merchandise_icon.png"
            DisplayText: "Merchandise"
            TextColor: "#B9B9B9"
            BackgroundColor: "#383838"
            Priority: 6

AWS:
  Region: "ap-south-1"

Secrets:
  Ids:
    RudderWriteKey: "1eGkhVch5jk2oJBF9lrTEN4I3zB"
    GoogleCloudProfilingServiceAccountKey: "development"
    RudderClientApiKey: "rudder/client-api-key"
    AppsFlyerClientKey: "appsflyer/client-api-key"
    OneMoneyClientSecret: "onemoney/client-secret"
    DeviceIdsEnabledForSafetyNetV2: "[\"deviceId1\"]"
    MoengageAppSdkKey: "moengage/app-sdk-key"
    ActiveDeviceIntegrityTokenSigningKey: "Isignshorttokens6"
    MiAmpPushSecretJson: "mi_amp_push/mi_secret_json"
    AppsFlyerClientKeyV2: "appsflyer/client-api-key-2"
    RudderIosClientApiKey: "1eGkhVch5jk2oJBF9lrTEN4I3zB"

DeviceIntegrity:
  EnableWhitelistedTokens: true
  SkipAsyncDeviceIntegrityChecks: false
  WhitelistedTokensList: [ "DUMMY_TOKEN" ]
  DefaultHighRiskDeviceConsentDuration: "24h"
  MaxHighRiskDeviceConsentDuration: "1080h" # 45 days
  AsyncDeviceIntegrityCheck:
    DisableFeature: false
    MinAndroidVersion: 10
    MinIOSVersion: 10

VKYC:
  PopupTileDuration: 12 # in hours
  PopupTileNonDismissableAfter: 1728 # in minutes
  AccountFreezePopupNonDismissibleWithinDays: 30 # in days
  AccountFreezeThreshold: "240h" # 10 days
  SavingsBalanceLimitPercent: 70
  CreditBalanceLimitPercent: 70
  PerformEkycAfter: "72h"
  SlotDays: 5
  MorningStart: 8
  SplitMorning: 12
  SplitAfternoon: 15
  SplitLateAfternoon: 18
  EveningEnd: 22
  ScheduleToLiveCutOffMinutes: 2
  NewPopupTileAccountCreationTimeLimit: 180 # in days
  NewPopupTileDuration: 84 # in hours
  HomeBannerColorHex: "#383838"
  EnableEPAN:
    MinAndroidVersion: 1
    MinIOSVersion: 1
    FallbackToEnableFeature: true
    DisableFeature: false
  EPANRolloutPercentage: 100

AFU:
  AllowOnboardingOnRegisteredDevice: true
  AllowReOnboardingOnRegisteredDevice: true
  EnableAccountInactiveCheck: false

InsightsParams:
  GetInsightConfig:
    MarkNoticedAfter: 2

Card:
  MinAndroidVersionForFMAuth: 1
  CardDynamicTileDuration:
    ViewCardDeliveryTracking: "5m"
    QRCodeAsPrimaryTile: "15m"
    ViewCardOnlineTxnEnabledTile: "5m"
    ViewQRCodeScanTime: "10m"
    ViewSecurePinActivationTime: "20m"
  EnableSecurePinActivationFlow:
    IsEnableOnAndroid: true
    MinAndroidVersion: 250
    IsEnableOnIos: true
    MinIosVersion: 250
  MinAndroidVersionForCardOffers: 79
  MinAndroidVersionForCardTracking: 83
  MinAndroidVersionForSecurePinValidation: 100
  MinIOSVersionForSecurePinValidation: 100
  EnableSecurePinValidationAuth: true
  OffersDynamicTileExpiryTime: "31-08-2022T23:59:00"
  EnableCardOffersInformativeDynamicTile: true
  PrintingToDispatchDynamicTileDuration: "5m"
  DashboardV2Config:
    IsQuestCheckEnabledForDashboardV2: true
    IsDashboardV2EnabledByQuest: true
    SectionsConfig:
      CardSectionConfig:
        ShowTapnPaySettingOnHome:
          IsEnableOnAndroid: true
          MinAndroidVersion: 395
          IsEnableOnIos: true
          MinIosVersion: 2495
      ShortcutsSectionConfig:
        PlanTravelBudgetUrl: "https://web.staging.pointz.in/travel-budget"

Comms:
  NotificationsPageSize: 10

Screening:
  EmpVerificationCheckStatusPollIntervalInSecs: 5
  CheckCreditReportAvailabilityStatusPollIntervalInSecs: 5
  CheckCreditReportVerificationStatusPollIntervalInSecs: 5

IPInterceptorParams:
  EnableIPInterceptor: true

ConnectedAccountUserGroupParams:
  IsConnectedAccountRestricted: true
  AllowedUserGrps:
    - 1 # INTERNAL
    - 2 # FNF
    - 7 # CONNECTED_ACCOUNT

ConnectedAccount:
  HomeBannerCutoffDays: 1
  V2FlowParams:
    UseV2Flow: true
    AccountDiscoveryTitleText: "Select the bank accounts you want to link & track on Fi"
    AccountDiscoverySubtitleText: "We found these banks linked to %s"
    AccountDiscoverySubtitleSearchingText: "One moment! We're searching for bank accounts linked to %s."
    AccountDiscoveryCtaText: "Continue"
    MinVersionAndroid: 1
    MinVersionIos: 1
    AccountDiscoveryLoadingText: "Searching for your accounts"
    RegisterOtherAccountsText: "Can't see your accounts?"
  MinimumDurationRequiredToPermitDisconnect: "72h"
  EnableAccountManagerConsentRenewalEntryPoint: true

Investment:
  MinAndroidVersionToSupportMINKYCCheckForPurchase: 137
  MinIOSVersionToSupportMINKYCCheckForPurchase: 169
  MinAndroidVersionToUploadPan: 137
  MinIosVersionToUploadPan: 169
  ISKYCCheckOnPurchaseEnabledForIOS: false
  ISKYCCheckOnPurchaseEnabledForAndroid: true
  FundActivityManualInterventionSupport:
    IsEnableOnAndroid: 137
    MinAndroidVersion: 105
    IsEnableOnIos: 1
    MinIosVersion: 169
  MinAndroidVersionForNextOnboardingStep: 164
  MinIOSVersionForNextOnboardingStep: 300
  MinAndroidVersionToSupportGraph: 10000
  MinIOSVersionToSupportGraph: 285

Deposit:
  EnableTransactionFetchV1: false
  Preclosure:
    ConfirmationNudge:
      FaqCategoryId: "***********" # currently its save category, also category id is different for prod and non prod
  MaturityAmountVisibility: # deposit's maturity amount feature flags belong here
    Global: true # if false, maturity amount will be hidden everywhere irrespective of the screen
    GlobalAllowedUserGroups: [ ] # allowed user groups
    SDCreation: false  # if false, maturity amount will be hidden in SD creation flow
    FDCreation: false # if false, maturity amount will be hidden in FD creation flow
    SDDetails: false # if false, maturity amount will be hidden in SD details screen
    FDDetails: true # if false, maturity amount will be hidden in FD details screen
    SDAddFunds: false # if false, maturity amount will be hidden in SD add funds flow
  Goals:
    GoalDetailsInDepositList:
      Enable: true
    GoalDetailsInDepositDetails:
      Enable: true
  AutoSave:
    PostCreationFlow:
      Enable: true
      GlobalAllowedUserGroups:
        - 1 # INTERNAL
    DetailsFlow:
      Enable: true
      GlobalAllowedUserGroups: [ ] # allowed user groups
      EnableAutoSaveSuggestions: true
      EnableAutoSaveRuleList: true
    PreCreationFlow:
      Enable: true
      AllowedUserGroups: [ ] # allowed user groups
  SaveDeeplink: "SD"
  Statement:
    Enable: false
    AllowedUserGroups: [ ] # allowed user groups

SalaryProgram:
  SalaryLiteConfig:
    LandingPageBannerDisplaySegmentExpression: "IsMember('AWS_test-segment')"
    IsSalaryLiteMandateSetupDropOffFeedbackFlowEnabled: true
    IsSalaryLiteEnachDropOffFeedbackFlowEnabled: true
  DisableEmployerSearchByGSTIN: false
  SalaryBenefitsLandingPageQuickLinksSection:
    IsVisible: true
    MinAndroidVersionSupportingQuickLinksSection: 100
    MinIOSVersionSupportingQuickLinksSection: 100
    QuickLinksTiles:
      Link1:
        Title: "Download your bank statement"
        TitleColor: "#333333"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/coins-home-card.png"
        BgColor: "#DEEEF2"
        CTA:
          IsVisible: true
          ImageUrl: "https://epifi-icons.pointz.in/referrals/chevron-right.png"
          BgColor: "#DEEEF2"
          Deeplink:
            Screen: 98
        TileRank: 1

      Link2:
        Title: "Download your bank statement"
        TitleColor: "#333333"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/coins-home-card.png"
        BgColor: "#DEEEF2"
        CTA:
          IsVisible: true
          ImageUrl: "https://epifi-icons.pointz.in/referrals/chevron-right.png"
          BgColor: "#DEEEF2"
          Deeplink:
            Screen: 98
        TileRank: 2

      Link3:
        Title: "Download your bank statement"
        TitleColor: "#333333"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/coins-home-card.png"
        BgColor: "#DEEEF2"
        CTA:
          IsVisible: true
          ImageUrl: "https://epifi-icons.pointz.in/referrals/chevron-right.png"
          BgColor: "#DEEEF2"
          Deeplink:
            Screen: 98
        TileRank: 3

      Link4:
        Title: "Download your bank statement"
        TitleColor: "#333333"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/coins-home-card.png"
        BgColor: "#DEEEF2"
        CTA:
          IsVisible: true
          ImageUrl: "https://epifi-icons.pointz.in/referrals/chevron-right.png"
          BgColor: "#DEEEF2"
          Deeplink:
            Screen: 98
        TileRank: 4

AnalyserParams:
  ShowAnalyser: true
  GetAnalyserGenerateDummyResponse: false
  AnalyserConfigJson: "./mappingJson/analyserConfig.json"
  AnalyserHubConfig:
    Experiments:
      - EXPERIMENT_NAME_CTA_BOTTOM_LEFT_WITH_TEXT
      - EXPERIMENT_NAME_CTA_BOTTOM_LEFT_WITHOUT_TEXT
      - EXPERIMENT_NAME_CTA_BOTTOM_RIGHT_OF_BODY_WITHOUT_TEXT
      - EXPERIMENT_NAME_NO_CTA
  CreditReportParams:
    DownloadProcessExpiry: 30m
  CreditScoreAnalyserConfig:
    ExperianV2InsightsConfig:
      IsEnabled: true
      ActiveFrom: "2023-10-01T00:00:00+05:30"
      ActiveTill: "2023-12-31T23:59:59+05:30"
  AnalyserReleaseConfig:
    - ANALYSER_NAME_SPEND_TOP_MERCHANTS:
        ReleaseConfig:
          AndroidReleaseConfig:
            MinAppVersion: 189
            IsFeatureRestricted: false
          IOSReleaseConfig:
            MinAppVersion: 275
            IsFeatureRestricted: false
        LensReleaseConfigs:
          - LENS_NAME_TOP_BY_AMOUNT:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 189
                  IsFeatureRestricted: false
                IOSReleaseConfig:
                  MinAppVersion: 275
                  IsFeatureRestricted: false
          - LENS_NAME_TOP_PEOPLE_BY_AMOUNT:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 247
                  IsFeatureRestricted: true
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 328
                  IsFeatureRestricted: true
                  AllowedUserGroups:
                    - "INTERNAL"
          - LENS_NAME_TOP_MERCHANTS_BY_AMOUNT:
              ReleaseConfig:
                AndroidReleaseConfig:
                  MinAppVersion: 247
                  IsFeatureRestricted: true
                  AllowedUserGroups:
                    - "INTERNAL"
                IOSReleaseConfig:
                  MinAppVersion: 328
                  IsFeatureRestricted: true
                  AllowedUserGroups:
                    - "INTERNAL"

Signup:
  EnableKnowMoreAccountClosureFlowIOS:
    MinIOSVersion: 2000
    MinAndroidVersion: 1
    FallbackToEnableFeature: true
  BlockOnboardingDueToUnlinkedPANAndAadhaar: true

AttributionLinkParseConfigs:
  - "CONF-1":
      AcquisitionSource: "SOURCE-1"
      IsEnabled: true
      SourceIdentification:
        CampaignName: "campaign-1"
  - "CONF-2":
      AcquisitionSource: "SOURCE-2"
      IsEnabled: false
      SourceIdentification:
        CampaignName: "campaign-2"
  - "CONF-3":
      AcquisitionSource: "STUDENT_PROGRAM_VIT"
      IsEnabled: true
      SourceIdentification:
        DeepLinkSub1: "Students_VITVellore"
        DeepLinkValue: "deep_link_value"

SyncVendorApiDeviceIntegrityCheck:
  IsEnabled: false
  AllowedDevices:
    - 1 # DeviceVerificationResult_DEVICE_VERIFICATION_RESULT_PASSED
    - 2 # DeviceVerificationResult_DEVICE_VERIFICATION_RESULT_HIGH_RISK

Goals:
  GoalDiscovery:
    Enable: true
    AllowedGroups: [ ] # allowed user groups
  GoalDiscoveryInExistingInvestmentInstrument:
    Enable: true
    AllowedGroups: [ ] # allowed user groups

Profiling:
  StackDriverProfiling:
    ProjectId: "test"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

UserProfile:
  EnableEditEmployment: true
  ProfileHeaderV2MinVersion:
    MinVersionAndroid: 1
    MinVersionIos: 1
  IsEmailEditableConfig:
    DisableFeature: true
    MinAndroidVersion: 1
    MinIOSVersion: 1
  IsContactDetailsEditable:
    DisableFeature: true
    FallbackToEnableFeature: false
    MinAndroidVersion: 401
    MinIOSVersion: 99999
  IsAadhaarCommsAddressUpdateEnabled:
    DisableFeature: true
    FallbackToEnableFeature: false
    MinAndroidVersion: 99999
    MinIOSVersion: 99999

ABFeatureReleaseConfig:
  FeatureConstraints:
    - ASK_FI_HOME_SEARCH_BAR:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 0
            MinIOSVersion: 0
        Buckets:
          - CONTROL:
              Start: 70
              End: 74
          - TYPE_1:
              Start: 75
              End: 79
          - TYPE_2:
              Start: 80
              End: 84
          - TYPE_3:
              Start: 85
              End: 89
    - INVESTMENT_LANDING_COMPONENTS_ORDERING:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 100
            MinIOSVersion: 100
        Buckets:
          - CONTROL_1:
              Start: 10
              End: 13
    - INVESTMENT_LANDING_COMPONENTS_ORDERING:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 100
            MinIOSVersion: 100
        Buckets:
          - INVESTMENT_LANDING_MUTUAL_FUNDS_INSTRUMENT_DEEPLINK_CONTROL_1:
              Start: 48
              End: 59
    - JUMP_DASHBOARD_FOR_NON_INVESTED_USERS:
        Buckets:
          - ZERO_STATE_DASHBOARD_VARIANT_ENABLED:
              Start: 16
              End: 26
    - FIXED_DEPOSIT_INTEREST_RATES:
        Buckets:
          - FIXED_DEPOSIT_INTEREST_RATES_EXPERIMENT_MIN_1_YEAR:
              Start: 27
              End: 42
    - FIXED_DEPOSIT_CUSTOM_TEMPLATE_DEFAULT_TERM:
        Buckets:
          - FIXED_DEPOSIT_CUSTOM_TEMPLATE_DEFAULT_TERM_EXPERIMENT_15_MONTHS:
              Start: 27
              End: 42

FeatureReleaseConfig:
  FeatureConstraints:
    - AA_FINVU_TOKEN_AUTHENTICATION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
            - 7 # CONNECTED_ACCOUNT
    - ML_KIT_QR:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1000000
          MinIOSVersion: 1000000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - JUMP_MATURITY_CONSENT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100000
          MinIOSVersion: 100000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ENABLE_GET_VKYC_NEXT_ACTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - HEALTH_ENGINE_FOR_PAYMENTS:
        AppVersionConstraintConfig:
          MinAndroidVersion: ********
          MinIOSVersion: **********
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_HOME_DESIGN_ENHANCEMENTS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 9999
          MinIOSVersion: 9999
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MF_HOLDINGS_IMPORT_PHONE_NUMBER_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - POST_PAYMENT_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 100000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_BIOMETRIC_REVALIDATION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 2000
          MinIOSVersion: 2000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - CONNECTED_ACCOUNT_BENEFITS_SCREEN_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 10000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_SECURED_LOANS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - PAY_ASK_FI_SEARCH_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 10000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_CREDIT_CARD_RECOMMENDATION_FRAMEWORK:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1000
          MinIOSVersion: 1000
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - DEPOSIT_AUTO_RENEW_CTA:
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_REWARD_DETAILS_IN_ORDER_RECEIPT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - LOANS_IDFC_VKYC_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 328
          MinIOSVersion: 473
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_REWARD_DETAILS_IN_ALL_TRANSACTIONS_PAGE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1000
          MinIOSVersion: 1000
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
    - FEATURE_DC_TRAVEL_MODE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 387
          MinIOSVersion: 2450
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 10000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
UpiOnboardingParams:
  TpapOnboardingRestrictionParams:
    IsRestricted: false
    AllowedUserGrps: [ 1 ]

HomeRevampParams:
  NewSearchUIReleaseConfig:
    IsEnableOnAndroid: true
    MinAndroidVersion: 292
    IsEnableOnIos: true
    MinIosVersion: 1856
  AllHomeIcons:
    "qr_code-69c444b6-cd1e-4b2d-8be3-7d9b2e2a25bb":
      IconWithVersionConstraints:
        - VersionConstraints:
            IsEnableOnAndroid: true
            MinAndroidVersion: 231
            IsEnableOnIos: true
            MinIosVersion: 1285
          IconType: "QR_CODE"
          ImageUrl: "https://epifi-icons.pointz.in/home-v2/QRCodeWhite.png"
          OnclickImageUrl: ""
          Title: "Scan & Pay"
          FontColour: "#FFFFFF"
          FontStyle: "BUTTON_S"
        # The default icon parameters do not require a version constraint check
        - IconType: "QR_CODE"
          ImageUrl: "https://epifi-icons.pointz.in/home-v2/QRCode.png"
          OnclickImageUrl: ""
          Title: ""
  HomeWidgetParams:
    WidgetCriteriaMap:
      "WIDGET_TYPE_PROMOTIONAL_BANNER":
        MinDaysSinceOnboarding: 5
  SegmentLayoutMapping:
    - "AWS_segment1":
        TopWidgetList:
          - "WIDGET_TYPE_MAINTENANCE"
          - "WIDGET_TYPE_TOP_NAV_BAR"
        BottomWidgetList:
          - "WIDGET_TYPE_CRITICAL_NOTIFICATION"
          - "WIDGET_TYPE_DASHBOARD"
          - "WIDGET_TYPE_SEARCH_BAR"
          - "WIDGET_TYPE_PROMOTIONAL_BANNER"
          - "WIDGET_TYPE_SUGGESTED_FOR_YOU"
          - "WIDGET_TYPE_PROMOTIONAL_BANNER_2"
          - "WIDGET_TYPE_RECENT_UPCOMING_ACTIVITIES"
          - "WIDGET_TYPE_SALARY_ACCOUNT"
          - "WIDGET_TYPE_REWARDS"
          - "WIDGET_TYPE_INVEST_HOME_ELEMENT"
          - "WIDGET_TYPE_REFERRAL"
          - "WIDGET_TYPE_HELP"
  RecentUpcomingWidgetParams:
    FilterViewType: "FILTER_TYPE_SWITCH"
  LayoutBySegFeatureRelease:
    IsEnableOnAndroid: true
    MinAndroidVersion: 256
    IsEnableOnIos: false
    MinIosVersion: 361
  HomeLayoutUIRevamp:
    IsEnableOnAndroid: true
    MinAndroidVersion: 1
    IsEnableOnIos: true
    MinIosVersion: 1
  HomeLayoutV2Params:
    SegmentLayoutV2Mapping:
      - SegmentIdExpression: "IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b')"
        PlatformVersionCheck:
          IsEnableOnAndroid: true
          IsEnableOnIos: true
        SlotIdToScreenElementMap:
          LayoutId: "f43aec57-44df-43a5-bea1-dc921be5ac7f"
          TopNavBarSlotSection:
            LeftSlots:
              slot_1: "profile-1"
            RightSlots:
              slot_1: "refer-1"
              slot_2: "card-1"
              slot_3: "notification-1"
          DashboardSlotSection:
            slot_1: "intro-1"
            slot_2: "primarysavings-1"
            slot_3: "networth-1"
            slot_4: "invest-1"
            slot_5: "creditcards-1"
            slot_6: "loans-1"
          VerticalSlotSection:
            slot_1: "searchbar-1"
            slot_2: "promotionalbanner-1"
            slot_3: "suggestedforyou-1"
            slot_4: "shortcuts-2"
            slot_5: "promotionalbanner2-1"
            slot_6: "recentupcomingactivities-1"
            slot_7: "salaryaccount-1"
            slot_8: "rewards-1"
            slot_9: "analyser-2"
            slot_10: "invest-2"
            slot_11: "refer-2"
            slot_12: ""
            slot_13: ""
            slot_14: ""
            slot_15: ""
            slot_16: ""
            slot_17: "help-1"
          BottomNavBarSlotSection:
            slot_1: "home-1"
            slot_2: "pay-1"
            slot_3: "invest-3"
            slot_4: "analyser-1"
            slot_5: "discover-1"
          StickyIconSlotSection:
            slot_1: "qr_code-1"
            slot_2: "qr_code-2"
            slot_3: "qr_code-3"
      - SegmentIdExpression: "IsMember('85fd8ec4-c854-42ce-b925-b6313ebe2d37')" # refer removed for age group
        PlatformVersionCheck:
          IsEnableOnAndroid: true
          IsEnableOnIos: true
        SlotIdToScreenElementMap:
          LayoutId: "82e5af23-a97d-4fb7-b2ac-523f71c68a56"
          TopNavBarSlotSection:
            LeftSlots:
              slot_1: "profile-1"
            RightSlots:
              slot_1: ""
              slot_2: "card-1"
              slot_3: "notification-1"
          DashboardSlotSection:
            slot_1: "intro-1"
            slot_2: "primarysavings-1"
            slot_3: "networth-1"
            slot_4: "invest-1"
            slot_5: "creditcards-1"
            slot_6: "loans-1"
          VerticalSlotSection:
            slot_1: "searchbar-1"
            slot_2: "promotionalbanner-1"
            slot_3: "suggestedforyou-1"
            slot_4: "shortcuts-2"
            slot_5: "salaryaccount-1"
            slot_6: "recentupcomingactivities-1"
            slot_7: "promotionalbanner2-1"
            slot_8: "rewards-1"
            slot_9: "analyser-2"
            slot_10: "invest-2"
            slot_11: ""
            slot_12: ""
            slot_13: ""
            slot_14: ""
            slot_15: ""
            slot_16: ""
            slot_17: "help-1"
          BottomNavBarSlotSection:
            slot_1: "home-1"
            slot_2: "pay-1"
            slot_3: "invest-3"
            slot_4: "analyser-1"
            slot_5: "discover-1"
          StickyIconSlotSection:
            slot_1: "qr_code-1"
            slot_2: "qr_code-2"
            slot_3: "qr_code-3"
  HomeShortcutParams:
    Title: "Shortcuts"
    MaximumNoOfShortcuts: 4
    SoftIntentToShortcutMap:
      "ONBOARDING_SOFT_INTENT_ZERO_BALANCE_SAVINGS_ACCOUNT": 13 # savings summary
      "ONBOARDING_SOFT_INTENT_EXPENSE_TRACKER": 35 #spend analyser
      "ONBOARDING_SOFT_INTENT_CASHBACK_ON_UPI": 13 # savings summary
      "ONBOARDING_SOFT_INTENT_INVESTMENT_TRACKER": 51 #networth
      "ONBOARDING_SOFT_INTENT_LOANS_UP_TO_5L": 19 #pre approved loan
      "ONBOARDING_SOFT_INTENT_INSTANT_SALARY": 19 #pre approved loan
      "ONBOARDING_SOFT_INTENT_LOANS_AGAINST_MUTUAL_FUNDS": 55 #secured loans
      "ONBOARDING_SOFT_INTENT_INTERNATIONAL_DEBIT_CARD": 28 #dc
      "ONBOARDING_SOFT_INTENT_ZERO_FOREX_FEES": 28 #dc
      "ONBOARDING_SOFT_INTENT_MUTUAL_FUNDS": 20 #mf
      "ONBOARDING_SOFT_INTENT_FIXED_DEPOSITS": 21 #fd
      "ONBOARDING_SOFT_INTENT_US_STOCKS": 56 #us stocks
      "ONBOARDING_SOFT_INTENT_INDIAN_STOCKS": 51 #networth

HomeRevampParamsForUnitTests:
  HomeLayoutV2ParamsWithoutAttributes:
    SlotIdToScreenElementIdsMap:
      TopNavBarSlotSection:
        LeftSlots:
          slot_1: [ "profile-1" ]
        RightSlots:
          slot_1: [ "refer-1" ]
          slot_2: [ "card-1" ]
          slot_3: [ "notification-1" ]
      DashboardSlotSection:
        slot_1: [ "intro-1" ]
        slot_2: [ "primarysavings-1" ]
        slot_3: [ "networth-1" ]
        slot_4: [ "invest-1" ]
        slot_5: [ "creditcards-1" ]
        slot_6: [ "loans-1" ]
      VerticalSlotSection:
        slot_1: [ "searchbar-1" ]
        slot_2: [ "promotionalbanner-1" ]
        slot_3: [ "suggestedforyou-1" ]
        slot_4: [ "shortcuts-2" ]
        slot_5: [ "primary-feature-1" ]
        slot_6: [ "recentupcomingactivities-1" ]
        slot_7: [ "analyser-2" ]
        slot_8: [ "secondary-feature-1" ]
        slot_9: [ "rewards-2" ]
        slot_10: [ "rewards-3" ]
        slot_11: [ "tabbed-card-1" ]
        slot_12: [ "refer-2" ]
        slot_13: [ "" ]
        slot_14: [ "" ]
        slot_15: [ "" ]
        slot_16: [ "help-1" ]
        slot_17: [ "trust-marker" ]
      BottomNavBarSlotSection:
        slot_1: [ "home-1" ]
        slot_2: [ "pay-1" ]
        slot_3: [ "invest-3" ]
        slot_4: [ "analyser-1" ]
        slot_5: [ "discover-1" ]
      StickyIconSlotSection:
        slot_1: [ "qr_code-1" ]
        slot_2: [ "qr_code-2" ]
        slot_3: [ "qr_code-3" ]
  HomeLayoutV2ParamsWithAttributes:
    HomeElementAttributes:
      ElementIdToSegmentExpressionScores:
        "connectedaccounts-1":
          - Expression: "IsMember('segment-1')"
            Score: 1
      ElementIdToFeatureLifecycleExpressionScores:
        "loans-2":
          - Expression: "HasShownIntentForFeature('FEATURE_PL')"
            Score: 1
        "invest-3":
          - Expression: "IsEligibleForFeature('FEATURE_SA')"
            Score: 0.5
        "qr_code-1":
          - Expression: "GetFeatureEligibilityStatus('FEATURE_UPI_TPAP') != 'STATUS_PASSED'"
            Score: -1
        "qr_code-2":
          - Expression: "GetFeatureEligibilityStatus('FEATURE_UPI_TPAP') != 'STATUS_PASSED'"
            Score: -1
        "qr_code-3":
          - Expression: "GetFeatureEligibilityStatus('FEATURE_UPI_TPAP') != 'STATUS_PASSED'"
            Score: -1
        "recentupcomingactivities-1":
          - Expression: "GetFeatureEligibilityStatus('FEATURE_UPI_TPAP') != 'STATUS_PASSED'"
            Score: -1
        "pay-1":
          - Expression: "GetFeatureEligibilityStatus('FEATURE_UPI_TPAP') != 'STATUS_PASSED'"
            Score: -1
      ElementIdToUserGroupExpressionScores:
        "loans-2":
          - Expression: "!IsUserGroup('INTERNAL') || !IsUserGroup('FI_STORE_INTERNAL')"
            Score: -1
      ElementIdToCrossAttachExpressionScores:
        "loans-2":
          - Expression: "IsCrossAttachIntent('PRODUCT_TYPE_PERSONAL_LOANS')"
            Score: 2
        "invest-3":
          - Expression: "IsCrossAttachIntent('PRODUCT_TYPE_USSTOCKS')"
            Score: 5
    SlotIdToScreenElementIdsMap:
      TopNavBarSlotSection:
        LeftSlots:
          slot_1: [ "profile-1" ]
        RightSlots:
          slot_1: [ "refer-1" ]
          slot_2: [ "card-1" ]
          slot_3: [ "notification-1" ]
      DashboardSlotSection:
        slot_1: [ "intro-1" ]
        slot_2: [ "primarysavings-1" ]
        slot_3: [ "networth-1", "connectedaccounts-1" ]
        slot_4: [ "invest-1" ]
        slot_5: [ "creditcards-1" ]
        slot_6: [ "loans-1" ]
      VerticalSlotSection:
        slot_1: [ "searchbar-1" ]
        slot_2: [ "promotionalbanner-1" ]
        slot_3: [ "suggestedforyou-1" ]
        slot_4: [ "shortcuts-2" ]
        slot_5: [ "primary-feature-1" ]
        slot_6: [ "recentupcomingactivities-1" ]
        slot_7: [ "analyser-2" ]
        slot_8: [ "secondary-feature-1" ]
        slot_9: [ "rewards-2" ]
        slot_10: [ "rewards-3" ]
        slot_11: [ "tabbed-card-1" ]
        slot_12: [ "refer-2" ]
        slot_13: [ "" ]
        slot_14: [ "" ]
        slot_15: [ "" ]
        slot_16: [ "help-1" ]
        slot_17: [ "trust-marker" ]
      BottomNavBarSlotSection:
        slot_1: [ "home-1" ]
        slot_2: [ "pay-1" ]
        slot_3: [  "loans-2", "invest-3" ]
        slot_4: [ "analyser-1" ]
        slot_5: [ "discover-1" ]
      StickyIconSlotSection:
        slot_1: [ "qr_code-1" ]
        slot_2: [ "qr_code-2" ]
        slot_3: [ "qr_code-3" ]
  HomeLayoutV2ParamsWithNoDuplicateElements:
    HomeElementAttributes:
      ElementIdToSegmentExpressionScores:
        "connectedaccounts-1":
          - Expression: "IsMember('segment-1')"
            Score: 1
        "card-1":
          - Expression: "IsMember('segment-2')"
            Score: 1
    SlotIdToScreenElementIdsMap:
      TopNavBarSlotSection:
        LeftSlots:
          slot_1: [ "profile-1" ]
        RightSlots:
          slot_1: [ "refer-1" ]
          slot_2: [ "card-1", "analyser-1" ]
          slot_3: [ "notification-1" ]
      DashboardSlotSection:
        slot_1: [ "intro-1" ]
        slot_2: [ "primarysavings-1" ]
        slot_3: [ "networth-1", "connectedaccounts-1" ]
        slot_4: [ "invest-1" ]
        slot_5: [ "creditcards-1" ]
        slot_6: [ "loans-1" ]
      VerticalSlotSection:
        slot_1: [ "searchbar-1" ]
        slot_2: [ "promotionalbanner-1" ]
        slot_3: [ "suggestedforyou-1" ]
        slot_4: [ "shortcuts-2" ]
        slot_5: [ "primary-feature-1" ]
        slot_6: [ "recentupcomingactivities-1" ]
        slot_7: [ "analyser-2" ]
        slot_8: [ "secondary-feature-1" ]
        slot_9: [ "rewards-2" ]
        slot_10: [ "rewards-3" ]
        slot_11: [ "tabbed-card-1" ]
        slot_12: [ "refer-2" ]
        slot_13: [ "" ]
        slot_14: [ "" ]
        slot_15: [ "" ]
        slot_16: [ "help-1" ]
        slot_17: [ "trust-marker" ]
      BottomNavBarSlotSection:
        slot_1: [ "home-1" ]
        slot_2: [ "pay-1" ]
        slot_3: [ "invest-3" ]
        slot_4: [ "card-1", "analyser-1" ]
        slot_5: [ "discover-1" ]
      StickyIconSlotSection:
        slot_1: [ "qr_code-1" ]
        slot_2: [ "qr_code-2" ]
        slot_3: [ "qr_code-3" ]
  HomeLayoutV2ParamsWithPriorityElements:
    HomeElementAttributes:
      ElementIdToSegmentExpressionScores:
        "connectedaccounts-1":
          - Expression: "IsMember('segment-1')"
            Score: 1
        "card-1":
          - Expression: "IsMember('segment-2')"
            Score: 1
        "loans-2":
          - Expression: "IsMember('segment-3')"
            Score: 1
    SlotIdToScreenElementIdsMap:
      TopNavBarSlotSection:
        LeftSlots:
          slot_1: [ "profile-1" ]
        RightSlots:
          slot_1: [ "refer-1" ]
          slot_2: [ "card-1", "analyser-1" ]
          slot_3: [ "notification-1" ]
      DashboardSlotSection:
        slot_1: [ "intro-1" ]
        slot_2: [ "primarysavings-1" ]
        slot_3: [ "networth-1", "connectedaccounts-1" ]
        slot_4: [ "invest-1" ]
        slot_5: [ "creditcards-1" ]
        slot_6: [ "loans-1" ]
      VerticalSlotSection:
        slot_1: [ "searchbar-1" ]
        slot_2: [ "promotionalbanner-1" ]
        slot_3: [ "suggestedforyou-1" ]
        slot_4: [ "shortcuts-2" ]
        slot_5: [ "primary-feature-1" ]
        slot_6: [ "recentupcomingactivities-1" ]
        slot_7: [ "analyser-2" ]
        slot_8: [ "secondary-feature-1" ]
        slot_9: [ "rewards-2" ]
        slot_10: [ "rewards-3" ]
        slot_11: [ "tabbed-card-1" ]
        slot_12: [ "refer-2" ]
        slot_13: [ "" ]
        slot_14: [ "" ]
        slot_15: [ "" ]
        slot_16: [ "help-1" ]
        slot_17: [ "trust-marker" ]
      BottomNavBarSlotSection:
        slot_1: [ "home-1" ]
        slot_2: [ "pay-1" ]
        slot_3: [ "invest-3" ]
        slot_4: [  "loans-2", "card-1", "analyser-1" ]
        slot_5: [ "discover-1" ]
      StickyIconSlotSection:
        slot_1: [ "qr_code-1" ]
        slot_2: [ "qr_code-2" ]
        slot_3: [ "qr_code-3" ]

Tiering:
  TieringFeature:
    MinVersionAndroid: 1
    MinVersionIos: 1
  TierIntroduction:
    IsLaunchAnimationEnabled: true
    LaunchAnimationInactivitySeconds: 100
  EnableSalaryHighConfidenceNotch: false
  HomeNotchCampaigns:
    - "AWS_notch_campaign":
        StartDate: "2024-03-25T00:00:00+05:30" # will be modified to time.Now() - 15d in tests
        EndDate: "2024-04-08T00:00:00+05:30" # will be modified to time.Now() + 15d in tests
        DefaultCopy: "Claim your ₹75 cashback"
        DeeplinkScreen: "REWARD_OFFER_DETAILS_SCREEN"
        NotchCopyMap:
          - 240h: "20 days left to claim ₹75" # 10 days from start
          - 480h: "10 days left to claim ₹75" # 20 days from start
  SegmentIdsForAMB:
    - "id-1"
    - "id-2"
  ExcludeSegmentsFromAMBScreenEntrypoint:
    ExcludedSegmentIds:
      - "exclusion-id-1"
    StartTime: 1748247835
    EndTime: 2147483647

USStocks:
  PoolRetryTime: 3
  MaxPoolDuration: 60
  PriceUpdatesRetryStrategy:
    RegularInterval:
      Interval: 2
      MaxAttempts: 10
      TimeUnit: "Second"
  Explore:
    Sections:
      - ComponentType: "COMPONENT_TYPE_SEARCH_BAR"
        BackgroundColor: "#F0F3F7"
      - ComponentType: "COMPONENT_TYPE_STOCK_COLLECTION_VARIANT_TITLE_LEFT"
        CollectionId: "handpicked"
        BackgroundColor: "#E7ECF0"
  BuyTimeoutInMilliseconds: 60000
  PriceGraphURL: "https://web.staging.pointz.in/fin-charts/line-chart"
  RatioGraphURL: "https://web.staging.pointz.in/fin-charts/multi-chart"
  PriceGraphUpdatedAt: 1675794600 # Wednesday, 8 February 2023 12:00:00 AM GMT+05:30
  RatioGraphUpdatedAt: 1675794600 # Wednesday, 8 February 2023 12:00:00 AM GMT+05:30
  IsBuyDisabled: false
  IsSellDisabled: false

SavingsBalanceTracker:
  DaysOfHistoryToShow: 5
  WidthOfCalendarView: 3

Dispute:
  RaiseDisputeErrorViewConfig:
    IsBottomSheetErrorViewPropagationEnabled: true

AddFundsV2Params:
  WhitelistedActorIds: [ ]

NonTpapPspHandles: [ "fede" ]

Alfred:
  ServiceRequestHistoryPageSize: 2
  EnableCancelledCheque:
    MinAndroidVersion: 1
    MinIOSVersion: 1
    FallbackToEnableFeature: true
    DisableFeature: false
  EnableVRH:
    MinAndroidVersion: 0
    MinIOSVersion: 0
    FallbackToEnableFeature: true
    DisableFeature: false
  EnableRequestChoiceBottomSheet:
    MinAndroidVersion: 0
    MinIOSVersion: 0
    FallbackToEnableFeature: true
    DisableFeature: false

CreditCard:
  AppVersionSupport:
    MinIosVersionForCreditCard: 330
    MinAndroidVersionForCreditCard: 228
    MinIosVersionForCreditCardIntroV2: 349
    MinAndroidVersionForCreditCardIntroV2: 250
  OnboardingRetryAttemptCutoff: 10
  EnableCCAllTxnPagination: false
  PaymentSuccessBannerTimeInMinutes: 5
  ShowCreditCardTabByDefaultFromCardTab: true
  EnableNewCvpForUnsecuredCreditCard: true
  WorkflowConstraints:
    - "CARD_REQUEST_WORKFLOW_CARD_ACTIVATION":
        AppVersionConstraintConfig:
          MinAndroidVersion: 240
          MinIOSVersion: 349
    # to be used for secured card onboarding.
    - "CARD_REQUEST_WORKFLOW_CARD_ONBOARDING":
        AppVersionConstraintConfig:
          MinAndroidVersion: 276
          MinIOSVersion: 9999
  AllEligibleCcScreenConfig:
    CardComponentTemplateVersion: 1
  IsCcChoicesComponentListViewEnabled: false
  CcNetworkSelectionScreenVersionCheck:
    IsEnableOnAndroid: false
    MinAndroidVersion: 1000
    IsEnableOnIos: false
    MinIosVersion: 1000
  FiLiteBottomCtaConfigs:
    EnableFiLiteBottomCtaVersionCheckFlag:
      IsEnableOnAndroid: false
      MinAndroidVersion: 1000
      IsEnableOnIos: false
      MinIosVersion: 1000
    IsCcChoicesComponentListViewEnabled: true
  EnableDashboardSegmentationCarousels: false
  SegmentIdToCarouselObjectMap:

QrDeeplinkParams:
  MinAndroidVersionForAmountScreenDeeplink: 1
  MinIosVersionForAmountScreenDeeplink: 1

FeedbackEngineConfig:
  ResponseHeaderPopulationConfig:
    IsPopulationInGetAnalyserEnabled: false
    IsPopulationInGetP2POrderStatusEnabled: false
    IsPopulationInCollectDataFromCustomerEnabled: false
    IsPopulationInGetNextOnboardingStepEnabled: false
    IsPopulationInGetSupportTicketsForAppEnabled: false
    IsPopulationInGetChatInitInformationForActorEnabled: false
    IsPopulationInGetProfileSettingPageSectionEnabled: false
    IsPopulationInUSSGetLandingPageForNewUserEnabled: false
    IsPopulationInUSSGetLandingPageForNewWalletUserEnabled: false
    IsPopulationInUSSGetLandingPageForExistingWalletUserEnabled: false
    IsPopulationInGetWalletAddFundsDetailsForNewWalletUserEnabled: false
    IsPopulationInGetWalletAddFundsDetailsForExistingWalletUserEnabled: false
    IsPopulationInGetWalletWithdrawFundsDetailsForUserEnabled: false
    IsPopulationInCreateWalletWithdrawFundsOrderForUserEnabled: false
    IsPopulationInEKYCForOnboardingForUserEnabled: true


NetworthConfig:
  ConfigPath: "./mappingJson/networthConfig.json"
  AppVersionsForWBDashboardV2:
    MinVersionAndroid: 464
    MinVersionIos: 635
  HomeBottomNavBarHighlights:
    NavId: "networth-navbar-animation"
    HighlightLottieUrl: "https://epifi-icons.pointz.in/home-v2/networth4.json"
    MaxSessionCount: 5000
    MaxClicksCount: 10000
    RepeatCount: 3
    EnableHighlights:
      MinAndroidVersion: 200
      MinIOSVersion: 200
      DisableFeature: false

TargetGroup:
  VersionCheck:
    DisableFeature: true

ShowAutoRenewCta: true

VpaMigrationScreenParams:
  OldVpaHandle: "fede"

FiStoreCollectedOffersConfig:
  IsFiStoreCollectedOffersEnabled: true
  MinAndroidVersionForFiStoreCollectedOffers: 0
  MinIosVersionForFiStoreCollectedOffers: 0

HomeExploreConfig:
  EnableAskFiSection: false
  EnableFeedbackSection: false

HelpRecentActivity:
  IsSearchBarShown: false
  IsFeatureEnabled: true

MoneySecrets:
  MfStocksBreakdown:
    MaxStocks: 5
