package secrets

import (
	"context"
	"errors"
	"fmt"

	portfolioTrackerBuilder "github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/strategy"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"

	headerPb "github.com/epifi/gamma/api/frontend/header"
	secretsFePb "github.com/epifi/gamma/api/frontend/insights/secrets"
	secretErrors "github.com/epifi/gamma/frontend/insights/secrets/errors"
)

func (s *Service) GetPortfolioTrackerLandingPage(ctx context.Context, req *secretsFePb.GetPortfolioTrackerLandingPageRequest) (*secretsFePb.GetPortfolioTrackerLandingPageResponse, error) {
	portfolioTrackerRes, err := s.portfolioTrackerBuilder.BuildPortfolioTracker(ctx, &portfolioTrackerBuilder.BuildPortfolioTrackerRequest{
		ActorId: req.GetReq().GetAuth().GetActorId(),
	})
	if err != nil && !errors.Is(err, secretErrors.NoDataToBuildPortfolioTracker) {
		logger.Error(ctx, fmt.Sprintf("failed to get secret analyser %v", ""), zap.Error(err))
		return &secretsFePb.GetPortfolioTrackerLandingPageResponse{RespHeader: &headerPb.ResponseHeader{Status: rpcPb.StatusInternalWithDebugMsg(err.Error())}}, nil
	}
	if errors.Is(err, secretErrors.NoDataToBuildPortfolioTracker) {
		return &secretsFePb.GetPortfolioTrackerLandingPageResponse{
			RespHeader: &headerPb.ResponseHeader{Status: rpcPb.StatusRecordNotFoundWithDebugMsg(err.Error())},
		}, nil
	}
	return &secretsFePb.GetPortfolioTrackerLandingPageResponse{
		RespHeader:           &headerPb.ResponseHeader{Status: rpcPb.StatusOk()},
		FixedComponents:      portfolioTrackerRes.GetFixedComponents(),
		ScrollableComponents: portfolioTrackerRes.GetScrollableComponents(),
		FooterComponents:     portfolioTrackerRes.GetFooterComponents(),
		FloatingActionButton: portfolioTrackerRes.GetFloatingActionButton(),
	}, nil
}
