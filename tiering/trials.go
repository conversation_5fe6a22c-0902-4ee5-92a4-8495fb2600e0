package tiering

import (
	"context"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"
	gmoney "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/pkg/errors"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	segmentPb "github.com/epifi/gamma/api/segment"
	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringCriteriaPb "github.com/epifi/gamma/api/tiering/criteria"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/api/typesv2"
	tieringScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/tiering"
	tieringTypesPb "github.com/epifi/gamma/api/typesv2/tiering"
	"github.com/epifi/gamma/pkg/deeplink"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	pkgRelease "github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/pkg/tiering"
	pkgErrors "github.com/epifi/gamma/pkg/tiering/errors"
	"github.com/epifi/gamma/tiering/tiermappings"
)

func (s *Service) GetTrialDetails(ctx context.Context, req *tieringPb.GetTrialDetailsRequest) (*tieringPb.GetTrialDetailsResponse, error) {
	actorId := req.GetActorId()

	currTier, currTierErr := s.dataProcessor.GetCurrentTierDefaultToBaseTier(ctx, actorId)
	if currTierErr != nil {
		logger.Error(ctx, "error getting current tier for actor", zap.Error(currTierErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return &tieringPb.GetTrialDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error getting current tier for actor"),
		}, nil
	}

	trialDetails, getErr := s.getTierDetailsForActor(ctx, actorId)
	if getErr != nil {
		logger.Error(ctx, "error getting trial details for actor", zap.Error(getErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return &tieringPb.GetTrialDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(getErr.Error()),
		}, nil
	}

	if trialDetails.IsTrialPeriodActive() && currTier == trialDetails.GetTier() {
		duringTrialDeeplink, deeplinkErr := s.getDeeplinkForDuringTrial(tiermappings.GetExternalTrialDetailsFromInternal(trialDetails).GetTier())
		if deeplinkErr != nil {
			logger.Error(ctx, "error getting deeplink for during trial", zap.Error(deeplinkErr), zap.String(logger.ACTOR_ID_V2, actorId))
			return &tieringPb.GetTrialDetailsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg(deeplinkErr.Error()),
			}, nil
		}

		return &tieringPb.GetTrialDetailsResponse{
			Status:             rpcPb.StatusOk(),
			TrialDetails:       tiermappings.GetExternalTrialDetailsFromInternal(trialDetails),
			IsEligibleForTrial: false,
			EligibleTrialTier:  0,
			Deeplink:           duringTrialDeeplink,
		}, nil
	}

	isEligibleForTrial, eligibleTrialTier, err := s.getTrialTierAndEligibility(ctx, actorId, currTier)
	if err != nil {
		logger.Error(ctx, "error getting trial tier and eligibility", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return &tieringPb.GetTrialDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error getting trial tier and eligibility"),
		}, nil
	}

	if !isEligibleForTrial {
		return &tieringPb.GetTrialDetailsResponse{
			Status:             rpcPb.StatusOk(),
			TrialDetails:       tiermappings.GetExternalTrialDetailsFromInternal(trialDetails), // return past trial details if any
			IsEligibleForTrial: false,
		}, nil
	}

	dl, getDlErr := s.getDeeplinkForTrialEligibleUsers(ctx, eligibleTrialTier, actorId)
	if getDlErr != nil {
		logger.Error(ctx, "error getting deeplink for trial eligible users", zap.Error(getDlErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return &tieringPb.GetTrialDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error getting deeplink for trial eligible users"),
		}, nil
	}

	// todo: remove this log once the feature is stable
	logger.Info(ctx, "actor is eligible for trial", zap.String(logger.TIER, eligibleTrialTier.String()), zap.String(logger.ACTOR_ID_V2, actorId))
	return &tieringPb.GetTrialDetailsResponse{
		Status:             rpcPb.StatusOk(),
		IsEligibleForTrial: isEligibleForTrial,
		EligibleTrialTier:  eligibleTrialTier,
		Deeplink:           dl,
	}, nil
}

func (s *Service) getDeeplinkForDuringTrial(eligibleTrialTier tieringExtPb.Tier) (*deeplinkPb.Deeplink, error) {
	webpageUrl, webpageUrlErr := s.constructDuringTrialWebpageUrl(eligibleTrialTier)
	if webpageUrlErr != nil {
		return nil, fmt.Errorf("error constructing webpage URL: %w", webpageUrlErr)
	}

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_WEB_PAGE,
		ScreenOptions: &deeplinkPb.Deeplink_WebPageScreenOptions{
			WebPageScreenOptions: &deeplinkPb.WebpageScreenOptions{
				WebpageUrl: webpageUrl,
			},
		},
	}, nil
}

func (s *Service) getDeeplinkForTrialEligibleUsers(ctx context.Context, eligibleTrialTier tieringExtPb.Tier, actorId string) (*deeplinkPb.Deeplink, error) {
	balance, getBalErr := s.getSavingsAccountBalance(ctx, actorId)
	if getBalErr != nil {
		return nil, fmt.Errorf("error getting savings account balance: %w", getBalErr)
	}

	activeCriteria, activeCriteriaErr := s.criteriaManager.GetActiveCriteria(ctx)
	if activeCriteriaErr != nil {
		return nil, fmt.Errorf("error getting active criteria: %w", activeCriteriaErr)
	}

	trialThreshold, getTrialThresholdErr := getTrialThresholdFromMovementDetailsList(eligibleTrialTier, activeCriteria.GetDetails().GetMovementDetailsList())
	if getTrialThresholdErr != nil {
		return nil, fmt.Errorf("error getting trial threshold from tiering pitch: %w", getTrialThresholdErr)
	}

	balanceIsGreaterOrEqual, compareErr := moneyPkg.IsGreaterThanOrEqual(balance, trialThreshold)
	if compareErr != nil {
		return nil, fmt.Errorf("error comparing balance with trial threshold: %w", compareErr)
	}

	var ctaDeeplink *deeplinkPb.Deeplink
	if balanceIsGreaterOrEqual {
		var getDlErr error
		ctaDeeplink, getDlErr = s.getDeeplinkForManualUpgradeWithScreenOptionsV2(eligibleTrialTier)
		if getDlErr != nil {
			return nil, fmt.Errorf("error getting deeplink for manual upgrade screen: %w", getDlErr)
		}
	} else {
		amountNeeded, err := moneyPkg.Subtract(trialThreshold, balance)
		if err != nil {
			return nil, fmt.Errorf("error calculating amount needed for upgrade: %w", err)
		}

		ctaDeeplink = tiering.GetCtaDeeplinkForTierAddFunds(ctx, eligibleTrialTier, amountNeeded, actorId, s.isTieringPitchInPaymentOptionsEnabled(ctx, actorId))
	}

	webpageUrl, webpageUrlErr := s.constructTrialEligibleWebpageUrl(eligibleTrialTier, ctaDeeplink)
	if webpageUrlErr != nil {
		return nil, fmt.Errorf("error constructing webpage URL: %w", webpageUrlErr)
	}

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_WEB_PAGE,
		ScreenOptions: &deeplinkPb.Deeplink_WebPageScreenOptions{
			WebPageScreenOptions: &deeplinkPb.WebpageScreenOptions{
				WebpageUrl: webpageUrl,
			},
		},
	}, nil
}

func (s *Service) constructTrialEligibleWebpageUrl(eligibleTrialTier tieringExtPb.Tier, ctaDeeplink *deeplinkPb.Deeplink) (string, error) {
	tierNameTitleCase, _ := tiering.GetTitleCaseDisplayString(eligibleTrialTier)
	tierName := strings.ToLower(tierNameTitleCase)
	ctaText := "Start%20your%20" + tierNameTitleCase + "%20Plan%20trial%20now"
	endData := s.gconf.TieringTrialConfig().TrialEndDateWithoutGrace().Format("02-01-2006")
	base64EncodedCtaDeeplink, err := deeplink.GetBase64EncodedDeeplink(ctaDeeplink)
	if err != nil {
		return "", fmt.Errorf("error encoding deeplink to base64: %w", err)
	}

	return fmt.Sprintf("%s?tier=%s&ctaText=%s&ctaDeepLink=%s&pageType=pre-trial&endDate=%s", s.gconf.TieringTrialConfig().TrialIntroWebUrl(), tierName, ctaText, base64EncodedCtaDeeplink, endData), nil
}

func (s *Service) constructDuringTrialWebpageUrl(trialTier tieringExtPb.Tier) (string, error) {
	tierNameTitleCase, _ := tiering.GetTitleCaseDisplayString(trialTier)
	tierName := strings.ToLower(tierNameTitleCase)
	ctaText := "Earn%20more%20rewards"
	ctaDeeplink := &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PAY_LANDING_SCREEN}
	endData := s.gconf.TieringTrialConfig().TrialEndDateWithoutGrace().Format("02-01-2006")
	base64EncodedCtaDeeplink, err := deeplink.GetBase64EncodedDeeplink(ctaDeeplink)
	if err != nil {
		return "", fmt.Errorf("error encoding deeplink to base64: %w", err)
	}

	return fmt.Sprintf("%s?tier=%s&ctaText=%s&ctaDeepLink=%s&pageType=during-trial&endDate=%s", s.gconf.TieringTrialConfig().TrialIntroWebUrl(), tierName, ctaText, base64EncodedCtaDeeplink, endData), nil
}

func (s *Service) getDeeplinkForManualUpgradeWithScreenOptionsV2(eligibleTrialTier tieringExtPb.Tier) (*deeplinkPb.Deeplink, error) {
	metadata := &tieringTypesPb.TieringScreenMetaData{
		Metadata: &tieringTypesPb.TieringScreenMetaData_TierManualUpgradeScreenMetadata{
			TierManualUpgradeScreenMetadata: &tieringTypesPb.TierManualUpgradeScreenMetadata{
				Provenance: tiering.GetTrialProvenanceForTier(eligibleTrialTier).String(),
			},
		},
	}
	marshalledMetadata, marshalErr := protojson.Marshal(metadata)
	if marshalErr != nil {
		return nil, fmt.Errorf("error marshalling metadata for manual upgrade screen: %w", marshalErr)
	}

	screenOptions := &tieringScreenOptionsPb.TieringLandingScreenOptions{
		MetaData: string(marshalledMetadata),
	}
	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_TIERING_LANDING_SCREEN,
		ScreenOptionsV2: deeplinkV3.GetScreenOptionV2WithoutError(screenOptions),
	}, nil
}

func (s *Service) isTieringPitchInPaymentOptionsEnabled(ctx context.Context, actorId string) bool {
	var err error
	ctx, err = s.dataProcessor.EnrichCtxForFeatureEvaluation(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error enriching context for feature evaluation", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return false
	}

	isEnabled, _, evalErr := s.abEvaluatorGeneric.Evaluate(ctx, pkgRelease.NewCommonConstraintData(typesv2.Feature_FEATURE_TIERING_PITCH_IN_PAYMENT_OPTIONS).WithActorId(actorId))
	if evalErr != nil {
		logger.Error(ctx, "error evaluating feature flag for tiering pitch in payment options", zap.Error(evalErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return false
	}

	return isEnabled
}

func (s *Service) getTrialTierAndEligibility(ctx context.Context, actorId string, currTier tieringEnumPb.Tier) (isEligibleForTrial bool, eligibleTier tieringExtPb.Tier, err error) {
	trialConfig := s.gconf.TieringTrialConfig()
	if trialConfig == nil || !trialConfig.IsTrialEntryPeriodActive() {
		return false, tieringExtPb.Tier_TIER_UNSPECIFIED, nil
	}

	// do not show entry point to the user if time till trial end date is less than minimum trial duration
	// this should not happen as TrialEntryPointEndDate and TrialEndDate should be at-least MinimumTrialDuration apart
	if time.Until(trialConfig.TrialEndDate()) < trialConfig.MinimumTrialDuration() {
		logger.WarnWithCtx(ctx, "trial end date is less than minimum trial duration, not showing entry point",
			zap.String(logger.ACTOR_ID_V2, actorId), zap.Duration("minimumTrialDuration", trialConfig.MinimumTrialDuration()),
			zap.Time("trialEndDate", trialConfig.TrialEndDate()))
		return false, tieringExtPb.Tier_TIER_UNSPECIFIED, nil
	}

	var allTrialSegments []string
	primeTrialSegments := trialConfig.PrimeTrialSegments().ToStringArray()
	infiniteTrialSegments := trialConfig.InfiniteTrialSegments().ToStringArray()
	plusTrialSegments := trialConfig.PlusTrialSegments().ToStringArray()
	allTrialSegments = append(allTrialSegments, primeTrialSegments...)
	allTrialSegments = append(allTrialSegments, infiniteTrialSegments...)
	allTrialSegments = append(allTrialSegments, plusTrialSegments...)

	if len(allTrialSegments) > 0 {
		isMemberResp, isMemberErr := s.segmentationClient.IsMember(ctx, &segmentPb.IsMemberRequest{
			ActorId:    actorId,
			SegmentIds: allTrialSegments,
		})
		if rpcErr := epifigrpc.RPCError(isMemberResp, isMemberErr); rpcErr != nil {
			return false, tieringExtPb.Tier_TIER_UNSPECIFIED, fmt.Errorf("error checking trial segment membership: %w", rpcErr)
		}

		for _, segmentId := range primeTrialSegments {
			if isMemberResp.GetSegmentMembershipMap()[segmentId].GetIsActorMember() {
				isEligibleForTrial = true
				eligibleTier = tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3
				break
			}
		}

		for _, segmentId := range infiniteTrialSegments {
			if isMemberResp.GetSegmentMembershipMap()[segmentId].GetIsActorMember() {
				isEligibleForTrial = true
				eligibleTier = tieringExtPb.Tier_TIER_FI_INFINITE
				break
			}
		}

		for _, segmentId := range plusTrialSegments {
			if isMemberResp.GetSegmentMembershipMap()[segmentId].GetIsActorMember() {
				isEligibleForTrial = true
				eligibleTier = tieringExtPb.Tier_TIER_FI_PLUS
				break
			}
		}
	}

	if !isEligibleForTrial {
		return false, tieringExtPb.Tier_TIER_UNSPECIFIED, nil
	}

	// if the current tier is already greater than or equal to the eligible tier, then return not-eligible for trial
	eligibleTierInternal, _ := tiermappings.GetInternalTierFromExternalTier(eligibleTier)
	if currTier >= eligibleTierInternal {
		isEligibleForTrial = false
		eligibleTier = tieringExtPb.Tier_TIER_UNSPECIFIED
		return isEligibleForTrial, eligibleTier, nil
	}

	return isEligibleForTrial, eligibleTier, nil
}

func (s *Service) getTierDetailsForActor(ctx context.Context, actorId string) (*tieringPb.TrialDetails, error) {
	ati, getAtiErr := s.actorTierInfoDao.Get(ctx, actorId, tieringPb.ActorTierInfoFieldMask_ACTOR_TIER_INFO_FIELD_MASK_TRIAL_DETAILS)
	if getAtiErr != nil {
		if errors.Is(getAtiErr, epifierrors.ErrRecordNotFound) {
			return nil, nil
		}

		return nil, getAtiErr
	}

	return ati.GetTrialDetails(), nil
}

// getTrialThresholdFromMovementDetailsList extracts the trial threshold for given tier from the tiering pitch response
func getTrialThresholdFromMovementDetailsList(targetTier tieringExtPb.Tier, mmtDetails []*tieringCriteriaPb.MovementDetails) (*gmoney.Money, error) {
	targetTierInternal, _ := tiermappings.GetInternalTierFromExternalTier(targetTier)
	for _, detail := range mmtDetails {
		if detail.GetTierName() != targetTierInternal {
			continue
		}

		criteriaMinValues, err := tiering.GetAllCriteriaMinValuesFromOptions(detail.GetOptions())
		if err != nil {
			continue // Try the next detail if this one has no criteria
		}

		// Find the trial balance criteria
		for _, criteriaValue := range criteriaMinValues {
			if criteriaValue.Criteria == tieringEnumPb.CriteriaOptionType_BALANCE_TRIAL_AND_KYC && criteriaValue.MinValue != nil {
				return criteriaValue.MinValue, nil
			}
		}
	}

	return nil, pkgErrors.ErrTierHasNoTrialBalanceCriteria
}
