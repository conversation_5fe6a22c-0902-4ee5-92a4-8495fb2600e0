package ruleengine

import (
	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/ptypes" // nolint:depguard
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"

	"github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/firefly/accounting/consumer"
	ffAccountsMocks "github.com/epifi/gamma/api/firefly/accounting/mocks"
	"github.com/epifi/gamma/api/firefly/billing/events"
	orderPb "github.com/epifi/gamma/api/order"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	datacollectorPb "github.com/epifi/gamma/api/rewards/datacollector"
	projectorPb "github.com/epifi/gamma/api/rewards/projector"
	rewardOfferPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	rewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"

	rewardsPkg "github.com/epifi/gamma/pkg/rewards"
	"github.com/epifi/gamma/rewards/generator/internalerrors"
	"github.com/epifi/gamma/rewards/generator/model"
	"github.com/epifi/gamma/rewards/generator/ruleengine/fact/common"
	"github.com/epifi/gamma/rewards/generator/ruleengine/fact/creditcardbilling"
	"github.com/epifi/gamma/rewards/generator/ruleengine/fact/creditcardtxn"
	"github.com/epifi/gamma/rewards/generator/ruleengine/fact/order"
	"github.com/epifi/gamma/rewards/generator/ruleengine/fact/tiering"
	model2 "github.com/epifi/gamma/rewards/projector/dao/model"
	daoMocks "github.com/epifi/gamma/rewards/test/mocks/generator/dao"
	mocks "github.com/epifi/gamma/rewards/test/mocks/helper"
	mock_dao "github.com/epifi/gamma/rewards/test/mocks/projector/dao"
)

var (
	currentTimestamp = ptypes.TimestampNow()
)

func TestCustomRuleEngine_CalculateRewards(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	// init mocks
	mockRewardOffersDao := daoMocks.NewMockRewardOfferDao(ctr)
	mockUserHelperService := mocks.NewMockIUserHelperService(ctr)

	// rule engine instance
	customRuleEngine := &CustomRuleEngine{
		rewardOfferDao:    mockRewardOffersDao,
		userHelperService: mockUserHelperService,
		conf:              conf,
		dynConf:           dynConf,
	}

	rewardOffer1 := &rewardOffersPb.RewardOffer{
		Id: uuid.New().String(),
		RewardMeta: &rewardOffersPb.RewardMeta{
			DefaultDecideTimeInSecs: 30,
			Probability:             1,
			RewardConfigOptions: []*rewardOffersPb.RewardConfigOption{
				{
					RewardType: rewardsPb.RewardType_FI_COINS,
					DisplayConfig: &rewardOffersPb.RewardConfigOption_Display{
						BeforeClaimTextExpression: "Fi-Coins 1 day later",
						AfterClaimTextExpression:  "Fi-Coins",
						Icon:                      "icon-url-1",
						BgColor:                   "#121212",
					},
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_AbsoluteTime{AbsoluteTime: currentTimestamp},
					},
					UnitsConfig: &rewardOffersPb.RewardConfigOption_FixedProbabilityConfig{
						FixedProbabilityConfig: &rewardOffersPb.FixedProbabilityConfig{
							Value:       100,
							Probability: 1,
						},
					},
					ProductSku: "test-sku",
				},
				{
					RewardType: rewardsPb.RewardType_SMART_DEPOSIT,
					DisplayConfig: &rewardOffersPb.RewardConfigOption_Display{
						BeforeClaimTextExpression: "Smart Deposit",
						AfterClaimTextExpression:  "Smart Deposit",
						Icon:                      "icon-url-2",
						BgColor:                   "#111111",
					},
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_AbsoluteTime{AbsoluteTime: currentTimestamp},
					},
					UnitsConfig: &rewardOffersPb.RewardConfigOption_FixedProbabilityConfig{
						FixedProbabilityConfig: &rewardOffersPb.FixedProbabilityConfig{
							Value:       200,
							Probability: 1,
						},
					},
					RewardConfig: &rewardOffersPb.RewardConfigOption_SmartDepositConfig{
						SmartDepositConfig: &rewardOffersPb.SmartDepositConfig{
							MaturityDateConfig: &rewardsPb.RewardTimeConfig{
								Config: &rewardsPb.RewardTimeConfig_AbsoluteTime{AbsoluteTime: currentTimestamp},
							},
						},
					},
				},
			},
			RewardDisplayMeta: &rewardOffersPb.RewardMeta_RewardDisplayMeta{
				TileBgImageBeforeClaim: "before-claim-image-1",
				TileBgImageAfterClaim:  "after-claim-image-1",
				RewardTileThemeType:    rewardsPb.RewardTileThemeType_PLANT_THEME_CHRYSANTHEMUM,
			},
			RewardAggregates: &rewardOffersPb.RewardAggregates{
				UserAggregate:   10,
				ActionAggregate: 100,
			},
		},
		DisplayMeta:    &rewardOffersPb.DisplayMeta{ActionDesc: "Earned for doing a txn through Fi"},
		ConstraintMeta: &rewardOffersPb.ConstraintsMeta{Expression: "TXN_AMOUNT >= 100"},
	}

	rewardOffer2 := &rewardOffersPb.RewardOffer{
		Id: uuid.New().String(),
		RewardMeta: &rewardOffersPb.RewardMeta{
			RewardLockTimeConfig: &rewardsPb.RewardTimeConfig{
				Config: &rewardsPb.RewardTimeConfig_AbsoluteTime{
					AbsoluteTime: currentTimestamp,
				},
			},
			DefaultDecideTimeInSecs: 30,
			Probability:             1,
			RewardConfigOptions: []*rewardOffersPb.RewardConfigOption{
				{
					ConstraintExpression: "false",
					RewardType:           rewardsPb.RewardType_FI_COINS,
					DisplayConfig:        &rewardOffersPb.RewardConfigOption_Display{},
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_AbsoluteTime{AbsoluteTime: currentTimestamp},
					},
					UnitsConfig: &rewardOffersPb.RewardConfigOption_FixedProbabilityConfig{
						FixedProbabilityConfig: &rewardOffersPb.FixedProbabilityConfig{
							Value:       100,
							Probability: 1,
						},
					},
				},
			},
			RewardDisplayMeta: &rewardOffersPb.RewardMeta_RewardDisplayMeta{
				RewardTileThemeType: rewardsPb.RewardTileThemeType_PLANT_THEME_CHRYSANTHEMUM,
			},
			RewardAggregates: &rewardOffersPb.RewardAggregates{
				UserAggregate:   10,
				ActionAggregate: 100,
			},
		},
		DisplayMeta:    &rewardOffersPb.DisplayMeta{ActionDesc: "Earned for doing a txn through Fi"},
		ConstraintMeta: &rewardOffersPb.ConstraintsMeta{Expression: "TXN_AMOUNT >= 100"},
	}

	rewardOffer3 := &rewardOffersPb.RewardOffer{
		Id: "offer-id-1",
		RewardMeta: &rewardOffersPb.RewardMeta{
			DefaultDecideTimeInSecs: 30,
			Probability:             1,
			RewardConfigOptions: []*rewardOffersPb.RewardConfigOption{
				{
					ConstraintExpression: "true",
					RewardType:           rewardsPb.RewardType_FI_COINS,
					DisplayConfig:        &rewardOffersPb.RewardConfigOption_Display{},
					UnitsConfig: &rewardOffersPb.RewardConfigOption_FixedProbabilityConfig{
						FixedProbabilityConfig: &rewardOffersPb.FixedProbabilityConfig{
							Value:       100,
							Probability: 1,
						},
					},
				},
			},
		},
		DisplayMeta:    &rewardOffersPb.DisplayMeta{ActionDesc: "Earned for doing a txn through Fi"},
		ConstraintMeta: &rewardOffersPb.ConstraintsMeta{Expression: "TXN_AMOUNT >= 100"},
		UnlockEvent:    rewardsPb.CollectedDataType_ORDER,
	}

	rewardOffer4 := &rewardOffersPb.RewardOffer{
		Id: "offer-id-2",
		RewardMeta: &rewardOffersPb.RewardMeta{
			DefaultDecideTimeInSecs: 30,
			Probability:             1,
			RewardLockTimeConfig: &rewardsPb.RewardTimeConfig{
				Config: &rewardsPb.RewardTimeConfig_AbsoluteTime{
					AbsoluteTime: timestampPb.New(time.Date(2030, 1, 1, 1, 1, 1, 1, time.UTC)),
				},
			},
			RewardConfigOptions: []*rewardOffersPb.RewardConfigOption{
				{
					ConstraintExpression: "true",
					RewardType:           rewardsPb.RewardType_FI_COINS,
					DisplayConfig:        &rewardOffersPb.RewardConfigOption_Display{},
					UnitsConfig: &rewardOffersPb.RewardConfigOption_FixedProbabilityConfig{
						FixedProbabilityConfig: &rewardOffersPb.FixedProbabilityConfig{
							Value:       100,
							Probability: 1,
						},
					},
				},
			},
		},
		DisplayMeta:    &rewardOffersPb.DisplayMeta{ActionDesc: "Earned for doing a txn through Fi"},
		ConstraintMeta: &rewardOffersPb.ConstraintsMeta{Expression: "TXN_AMOUNT >= 100"},
	}

	rewardOffer5 := &rewardOffersPb.RewardOffer{
		Id: "offer-id-3",
		RewardMeta: &rewardOffersPb.RewardMeta{
			DefaultDecideTimeInSecs: 30,
			Probability:             1,
			RewardConfigOptions: []*rewardOffersPb.RewardConfigOption{
				{
					ConstraintExpression: "true",
					RewardType:           rewardsPb.RewardType_FI_COINS,
					DisplayConfig:        &rewardOffersPb.RewardConfigOption_Display{},
					UnitsConfig: &rewardOffersPb.RewardConfigOption_FixedProbabilityConfig{
						FixedProbabilityConfig: &rewardOffersPb.FixedProbabilityConfig{
							Value:       100,
							Probability: 1,
						},
					},
				},
			},
		},
		DisplayMeta:    &rewardOffersPb.DisplayMeta{ActionDesc: "Earned for doing a txn through Fi"},
		ConstraintMeta: &rewardOffersPb.ConstraintsMeta{Expression: "TXN_AMOUNT >= 100"},
	}

	rewardOffer6 := &rewardOffersPb.RewardOffer{
		Id: "offer-id-3",
		RewardMeta: &rewardOffersPb.RewardMeta{
			DefaultDecideTimeInSecs: 30,
			Probability:             1,
			RewardConfigOptions: []*rewardOffersPb.RewardConfigOption{
				{
					ConstraintExpression: "true",
					RewardType:           rewardsPb.RewardType_FI_COINS,
					DisplayConfig:        &rewardOffersPb.RewardConfigOption_Display{},
					UnitsConfig: &rewardOffersPb.RewardConfigOption_FixedProbabilityConfig{
						FixedProbabilityConfig: &rewardOffersPb.FixedProbabilityConfig{
							Value:       0,
							Probability: 1,
						},
					},
				},
			},
		},
		DisplayMeta:    &rewardOffersPb.DisplayMeta{ActionDesc: "Earned for doing a txn through Fi"},
		ConstraintMeta: &rewardOffersPb.ConstraintsMeta{Expression: "2>1"},
	}

	// orderUpdateEvent for use in order fact creation
	orderUpdateEvent1 := &orderPb.OrderUpdate{
		OrderWithTransactions: &orderPb.OrderWithTransactions{
			Order: &orderPb.Order{
				Amount: &money.Money{
					CurrencyCode: "INR",
					Units:        100,
				},
			},
		},
	}

	type args struct {
		ctx  context.Context
		fact common.IFact
	}
	tests := []struct {
		name       string
		args       args
		want       *model.Reward
		setUpMocks func()
		wantErr    bool
	}{
		{
			name: "reward payload should be generated",
			args: args{
				ctx: context.Background(),
				fact: &order.OrderFact{
					CommonFact: &common.CommonFact{
						RefId:       "ref-id-1",
						ActorId:     "actor-id-1",
						RewardOffer: rewardOffer1,
						ActionTime:  timestampPb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)),
					},
					OrderEvent: orderUpdateEvent1,
				},
			},
			setUpMocks: func() {
				// 	mockRewardOffersDao.EXPECT().GetActorLevelRewardOfferInventory(context.Background(), "actor-id-1", []string{rewardOffer1.GetId()}).Return([]*rewardOffersPb.RewardOfferInventory{{RemainingCount: 5, TotalCount: 10}}, nil)
				// 	mockRewardOffersDao.EXPECT().GetActionLevelRewardOfferInventory(context.Background(), []string{rewardOffer1.GetId()}).Return([]*rewardOffersPb.RewardOfferInventory{{RemainingCount: 5, TotalCount: 100}}, nil)
				mockUserHelperService.EXPECT().IsImplicitRewardLockingApplicableForActor(context.Background(), "actor-id-1").Return(false, nil)
			},

			want: &model.Reward{
				RefId:           "ref-id-1",
				SecondaryRefId:  rewardsPkg.RewardsSecondaryRefIdDefaultValue,
				ActorId:         "actor-id-1",
				OfferId:         rewardOffer1.GetId(),
				Status:          rewardsPb.RewardStatus_CREATED,
				ActionTimestamp: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
				Aggregates:      rewardOffer1.GetRewardMeta().GetRewardAggregates(),
				ClaimType:       rewardsPb.ClaimType_CLAIM_TYPE_MANUAL,
				RewardOptions: &rewardsPb.RewardOptions{
					DefaultDecideTimeInSecs: rewardOffer1.GetRewardMeta().GetDefaultDecideTimeInSecs(),
					UnlockDate:              rewardOffer1.GetRewardMeta().GetRewardLockTimeConfig().GetAbsoluteTime(),
					ActionDetails:           rewardOffer1.GetDisplayMeta().GetActionDesc(),
					Options: []*rewardsPb.RewardOption{
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Option: &rewardsPb.RewardOption_FiCoins{
								FiCoins: &rewardsPb.FiCoins{
									Units: 100,
								},
							},
							RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
								Config: &rewardsPb.RewardTimeConfig_AbsoluteTime{
									AbsoluteTime: currentTimestamp,
								},
							},
							Display: &rewardsPb.RewardOptionDisplay{
								Title:            "Fi-Coins 1 day later",
								Icon:             "icon-url-1",
								BgColor:          "#121212",
								BeforeClaimTitle: "Fi-Coins 1 day later",
								AfterClaimTitle:  "Fi-Coins",
							},
							RewardUnitsCalculationInfo: &rewardsPb.RewardUnitsCalculationInfo{
								RewardUnitsCalculationEntries: []*rewardsPb.RewardUnitsCalculationInfo_RewardUnitsCalculationEntry{
									{
										RewardValue: 100,
									},
								},
							},
							ProductSku: "test-sku",
						},
						{
							RewardType: rewardsPb.RewardType_SMART_DEPOSIT,
							Option: &rewardsPb.RewardOption_SmartDeposit{
								SmartDeposit: &rewardsPb.SmartDeposit{
									Amount: &money.Money{
										CurrencyCode: "INR",
										Units:        200,
									},
									MaturityDateConfig: &rewardsPb.RewardTimeConfig{
										Config: &rewardsPb.RewardTimeConfig_AbsoluteTime{
											AbsoluteTime: currentTimestamp,
										},
									},
								},
							},
							RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
								Config: &rewardsPb.RewardTimeConfig_AbsoluteTime{
									AbsoluteTime: currentTimestamp,
								},
							},
							Display: &rewardsPb.RewardOptionDisplay{
								Title:            "Smart Deposit",
								Icon:             "icon-url-2",
								BgColor:          "#111111",
								BeforeClaimTitle: "Smart Deposit",
								AfterClaimTitle:  "Smart Deposit",
							},
							RewardUnitsCalculationInfo: &rewardsPb.RewardUnitsCalculationInfo{
								RewardUnitsCalculationEntries: []*rewardsPb.RewardUnitsCalculationInfo_RewardUnitsCalculationEntry{
									{
										RewardValue: 200,
									},
								},
							},
						},
					},
				},
				RewardDisplay: &rewardsPb.RewardDisplay{
					TileBgImageBeforeClaim: rewardOffer1.GetRewardMeta().GetRewardDisplayMeta().GetTileBgImageBeforeClaim(),
					TileBgImageAfterClaim:  rewardOffer1.GetRewardMeta().GetRewardDisplayMeta().GetTileBgImageAfterClaim(),
					RewardTileThemeType:    rewardOffer1.GetRewardMeta().GetRewardDisplayMeta().GetRewardTileThemeType(),
				},
				RewardMetadata: &rewardsPb.RewardMetadata{},
			},
			wantErr: false,
		},
		{
			name: "not even a single RewardConfigOption constraint is satisfied, no reward options can be generated so reward payload should be nil",
			args: args{
				ctx: context.Background(),
				fact: &order.OrderFact{
					CommonFact: &common.CommonFact{
						RefId:       "ref-id-1",
						ActorId:     "actor-id-1",
						RewardOffer: rewardOffer2,
						ActionTime:  ptypes.TimestampNow(),
					},
					OrderEvent: orderUpdateEvent1,
				},
			},
			setUpMocks: func() {
				// 	mockRewardOffersDao.EXPECT().GetActorLevelRewardOfferInventory(context.Background(), "actor-id-1", []string{rewardOffer1.GetId()}).Return([]*rewardOffersPb.RewardOfferInventory{{RemainingCount: 5, TotalCount: 10}}, nil)
				// 	mockRewardOffersDao.EXPECT().GetActionLevelRewardOfferInventory(context.Background(), []string{rewardOffer1.GetId()}).Return([]*rewardOffersPb.RewardOfferInventory{{RemainingCount: 5, TotalCount: 100}}, nil)
			},

			want:    nil,
			wantErr: false,
		},
		{
			name: "should generate reward in locked state (event locked)",
			args: args{
				ctx: context.Background(),
				fact: &order.OrderFact{
					CommonFact: &common.CommonFact{
						RefId:       "ref-id-1",
						ActorId:     "actor-id-1",
						RewardOffer: rewardOffer3,
						ActionTime:  timestampPb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)),
					},
					OrderEvent: orderUpdateEvent1,
				},
			},
			setUpMocks: func() {
				// 	mockRewardOffersDao.EXPECT().GetActorLevelRewardOfferInventory(context.Background(), "actor-id-1", []string{rewardOffer1.GetId()}).Return([]*rewardOffersPb.RewardOfferInventory{{RemainingCount: 5, TotalCount: 10}}, nil)
				// 	mockRewardOffersDao.EXPECT().GetActionLevelRewardOfferInventory(context.Background(), []string{rewardOffer1.GetId()}).Return([]*rewardOffersPb.RewardOfferInventory{{RemainingCount: 5, TotalCount: 100}}, nil)
			},

			want: &model.Reward{
				RefId:           "ref-id-1",
				SecondaryRefId:  rewardsPkg.RewardsSecondaryRefIdDefaultValue,
				ActorId:         "actor-id-1",
				ActionTimestamp: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
				Status:          rewardsPb.RewardStatus_LOCKED,
				SubStatus:       rewardsPb.SubStatus_SUB_STATUS_EXPLICITLY_LOCKED,
				OfferId:         "offer-id-1",
				RewardOptions: &rewardsPb.RewardOptions{
					DefaultDecideTimeInSecs: 30,
					ActionDetails:           "Earned for doing a txn through Fi",
					Options: []*rewardsPb.RewardOption{
						{
							Display:    &rewardsPb.RewardOptionDisplay{},
							RewardType: rewardsPb.RewardType_FI_COINS,
							Option: &rewardsPb.RewardOption_FiCoins{
								FiCoins: &rewardsPb.FiCoins{Units: 100},
							},
							RewardUnitsCalculationInfo: &rewardsPb.RewardUnitsCalculationInfo{
								RewardUnitsCalculationEntries: []*rewardsPb.RewardUnitsCalculationInfo_RewardUnitsCalculationEntry{
									{
										RewardValue: 100,
									},
								},
							},
						},
					},
				},
				ClaimType:      rewardsPb.ClaimType_CLAIM_TYPE_MANUAL,
				RewardDisplay:  &rewardsPb.RewardDisplay{},
				RewardMetadata: &rewardsPb.RewardMetadata{},
			},
			wantErr: false,
		},
		{
			name: "should generate reward in locked state (time locked)",
			args: args{
				ctx: context.Background(),
				fact: &order.OrderFact{
					CommonFact: &common.CommonFact{
						RefId:       "ref-id-1",
						ActorId:     "actor-id-1",
						RewardOffer: rewardOffer4,
						ActionTime:  timestampPb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)),
					},
					OrderEvent: orderUpdateEvent1,
				},
			},
			setUpMocks: func() {
				// 	mockRewardOffersDao.EXPECT().GetActorLevelRewardOfferInventory(context.Background(), "actor-id-1", []string{rewardOffer1.GetId()}).Return([]*rewardOffersPb.RewardOfferInventory{{RemainingCount: 5, TotalCount: 10}}, nil)
				// 	mockRewardOffersDao.EXPECT().GetActionLevelRewardOfferInventory(context.Background(), []string{rewardOffer1.GetId()}).Return([]*rewardOffersPb.RewardOfferInventory{{RemainingCount: 5, TotalCount: 100}}, nil)
			},

			want: &model.Reward{
				RefId:           "ref-id-1",
				SecondaryRefId:  rewardsPkg.RewardsSecondaryRefIdDefaultValue,
				ActorId:         "actor-id-1",
				ActionTimestamp: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
				Status:          rewardsPb.RewardStatus_LOCKED,
				SubStatus:       rewardsPb.SubStatus_SUB_STATUS_EXPLICITLY_LOCKED,
				OfferId:         "offer-id-2",
				RewardOptions: &rewardsPb.RewardOptions{
					DefaultDecideTimeInSecs: 30,
					ActionDetails:           "Earned for doing a txn through Fi",
					UnlockDate:              timestampPb.New(time.Date(2030, 1, 1, 1, 1, 1, 1, time.UTC)),
					Options: []*rewardsPb.RewardOption{
						{
							Display:    &rewardsPb.RewardOptionDisplay{},
							RewardType: rewardsPb.RewardType_FI_COINS,
							Option: &rewardsPb.RewardOption_FiCoins{
								FiCoins: &rewardsPb.FiCoins{Units: 100},
							},
							RewardUnitsCalculationInfo: &rewardsPb.RewardUnitsCalculationInfo{
								RewardUnitsCalculationEntries: []*rewardsPb.RewardUnitsCalculationInfo_RewardUnitsCalculationEntry{
									{
										RewardValue: 100,
									},
								},
							},
						},
					},
				},
				ClaimType:      rewardsPb.ClaimType_CLAIM_TYPE_MANUAL,
				RewardDisplay:  &rewardsPb.RewardDisplay{},
				RewardMetadata: &rewardsPb.RewardMetadata{},
			},
			wantErr: false,
		},
		{
			name: "should generate reward in locked state (implicitly locked)",
			args: args{
				ctx: context.Background(),
				fact: &order.OrderFact{
					CommonFact: &common.CommonFact{
						RefId:       "ref-id-1",
						ActorId:     "actor-id-1",
						RewardOffer: rewardOffer5,
						ActionTime:  timestampPb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)),
					},
					OrderEvent: orderUpdateEvent1,
				},
			},
			setUpMocks: func() {
				mockUserHelperService.EXPECT().IsImplicitRewardLockingApplicableForActor(context.Background(), "actor-id-1").Return(true, nil)
			},

			want: &model.Reward{
				RefId:           "ref-id-1",
				SecondaryRefId:  rewardsPkg.RewardsSecondaryRefIdDefaultValue,
				ActorId:         "actor-id-1",
				ActionTimestamp: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
				Status:          rewardsPb.RewardStatus_LOCKED,
				SubStatus:       rewardsPb.SubStatus_SUB_STATUS_IMPLICITLY_LOCKED,
				OfferId:         "offer-id-3",
				RewardOptions: &rewardsPb.RewardOptions{
					DefaultDecideTimeInSecs: 30,
					ActionDetails:           "Earned for doing a txn through Fi",
					Options: []*rewardsPb.RewardOption{
						{
							Display:    &rewardsPb.RewardOptionDisplay{},
							RewardType: rewardsPb.RewardType_FI_COINS,
							Option: &rewardsPb.RewardOption_FiCoins{
								FiCoins: &rewardsPb.FiCoins{Units: 100},
							},
							RewardUnitsCalculationInfo: &rewardsPb.RewardUnitsCalculationInfo{
								RewardUnitsCalculationEntries: []*rewardsPb.RewardUnitsCalculationInfo_RewardUnitsCalculationEntry{
									{
										RewardValue: 100,
									},
								},
							},
						},
					},
				},
				ClaimType:      rewardsPb.ClaimType_CLAIM_TYPE_MANUAL,
				RewardDisplay:  &rewardsPb.RewardDisplay{},
				RewardMetadata: &rewardsPb.RewardMetadata{},
			},
			wantErr: false,
		},
		{
			name: "should not generate NO_REWARD type reward",
			args: args{
				ctx: context.Background(),
				fact: &order.OrderFact{
					CommonFact: &common.CommonFact{
						RefId:       "ref-id-1",
						ActorId:     "actor-id-1",
						RewardOffer: rewardOffer6,
						ActionTime:  timestampPb.New(time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)),
					},
					OrderEvent: orderUpdateEvent1,
				},
			},
			setUpMocks: func() {

			},
			want: &model.Reward{
				RefId:           "ref-id-1",
				SecondaryRefId:  rewardsPkg.RewardsSecondaryRefIdDefaultValue,
				ActorId:         "actor-id-1",
				Status:          rewardsPb.RewardStatus_PROCESSING_PENDING,
				OfferId:         "offer-id-3",
				ActionTimestamp: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
				RewardOptions: &rewardsPb.RewardOptions{
					DefaultDecideTimeInSecs: 30,
					ActionDetails:           "Earned for doing a txn through Fi",
					Options: []*rewardsPb.RewardOption{
						{
							Display: &rewardsPb.RewardOptionDisplay{
								BeforeClaimTitle: "No reward 😢",
								AfterClaimTitle:  "No reward 😢",
							},
							RewardType: rewardsPb.RewardType_NO_REWARD,
							RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
								Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: ZeroRewardUnits},
							},
							Option: &rewardsPb.RewardOption_NoReward{},
						},
					},
				},
				RewardType: rewardsPb.RewardType_NO_REWARD,
				ChosenReward: &rewardsPb.RewardOption{
					RewardType:     rewardsPb.RewardType_NO_REWARD,
					Option:         &rewardsPb.RewardOption_NoReward{},
					ProcessingDate: currentTimestamp,
					Display: &rewardsPb.RewardOptionDisplay{
						BeforeClaimTitle: "No reward 😢",
						AfterClaimTitle:  "No reward 😢",
					},
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: ZeroRewardUnits},
					},
				},
				ClaimType:      rewardsPb.ClaimType_CLAIM_TYPE_MANUAL,
				RewardDisplay:  &rewardsPb.RewardDisplay{},
				RewardMetadata: &rewardsPb.RewardMetadata{},
			},
			wantErr: false,
		},
		// {
		// 	name: "offer inventory check dao call gave error, reward calculation should still proceed and reward payload should be generated",
		// 	args: args{
		// 		ctx: context.Background(),
		// 		fact: &order.OrderFact{
		// 			CommonFact: &common.CommonFact{
		// 				RefId:       "ref-id-1",
		// 				ActorId:     "actor-id-1",
		// 				RewardOffer: rewardOffer1,
		// 				ActionTime:  ptypes.TimestampNow(),
		// 			},
		// 			OrderEvent: orderUpdateEvent1,
		// 		},
		// 	},
		// 	setUpMocks: func() {
		// 		mockRewardOffersDao.EXPECT().GetActorLevelRewardOfferInventory(context.Background(), "actor-id-1", []string{rewardOffer1.GetId()}).Return(nil, errors.New("error"))
		// 	},
		// 	want: &model.Reward{
		// 		RefId:      "ref-id-1",
		// 		ActorId:    "actor-id-1",
		// 		OfferId:    rewardOffer1.GetId(),
		// 		Status:     rewardsPb.RewardStatus_CREATED,
		// 		Aggregates: rewardOffer1.GetRewardMeta().GetRewardAggregates(),
		// 		RewardOptions: &rewardsPb.RewardOptions{
		// 			DefaultDecideTimeInSecs: rewardOffer1.GetRewardMeta().GetDefaultDecideTimeInSecs(),
		// 			UnlockDate:              rewardOffer1.GetRewardMeta().GetRewardLockTimeConfig().GetAbsoluteTime(),
		// 			ActionDetails:           rewardOffer1.GetDisplayMeta().GetActionDesc(),
		// 			Options: []*rewardsPb.RewardOption{
		// 				{
		// 					RewardType: rewardsPb.RewardType_FI_COINS,
		// 					Option: &rewardsPb.RewardOption_FiCoins{
		// 						FiCoins: &rewardsPb.FiCoins{
		// 							Units: 100,
		// 						},
		// 					},
		// 					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
		// 						Config: &rewardsPb.RewardTimeConfig_AbsoluteTime{
		// 							AbsoluteTime: currentTimestamp,
		// 						},
		// 					},
		// 					Display: &rewardsPb.RewardOptionDisplay{
		// 						Title:            "Fi-Coins 1 day later",
		// 						Icon:             "icon-url-1",
		// 						BgColor:          "#121212",
		// 						BeforeClaimTitle: "Fi-Coins 1 day later",
		// 						AfterClaimTitle:  "Fi-Coins",
		// 					},
		// 				},
		// 				{
		// 					RewardType: rewardsPb.RewardType_SMART_DEPOSIT,
		// 					Option: &rewardsPb.RewardOption_SmartDeposit{
		// 						SmartDeposit: &rewardsPb.SmartDeposit{
		// 							Amount: &money.Money{
		// 								CurrencyCode: "INR",
		// 								Units:        200,
		// 							},
		// 							MaturityDateConfig: &rewardsPb.RewardTimeConfig{
		// 								Config: &rewardsPb.RewardTimeConfig_AbsoluteTime{
		// 									AbsoluteTime: currentTimestamp,
		// 								},
		// 							},
		// 						},
		// 					},
		// 					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
		// 						Config: &rewardsPb.RewardTimeConfig_AbsoluteTime{
		// 							AbsoluteTime: currentTimestamp,
		// 						},
		// 					},
		// 					Display: &rewardsPb.RewardOptionDisplay{
		// 						Title:            "Smart Deposit",
		// 						Icon:             "icon-url-2",
		// 						BgColor:          "#111111",
		// 						BeforeClaimTitle: "Smart Deposit",
		// 						AfterClaimTitle:  "Smart Deposit",
		// 					},
		// 				},
		// 			},
		// 		},
		// 		RewardDisplay: &rewardsPb.RewardDisplay{
		// 			TileBgImageBeforeClaim: rewardOffer1.GetRewardMeta().GetRewardDisplayMeta().GetTileBgImageBeforeClaim(),
		// 			TileBgImageAfterClaim:  rewardOffer1.GetRewardMeta().GetRewardDisplayMeta().GetTileBgImageAfterClaim(),
		// 			RewardTileThemeType:    rewardOffer1.GetRewardMeta().GetRewardDisplayMeta().GetRewardTileThemeType(),
		// 		},
		// 	},
		// 	wantErr: false,
		// },
		// {
		// 	name: "actor level offer inventory is already exhausted, nil reward payload should be returned",
		// 	args: args{
		// 		ctx: context.Background(),
		// 		fact: &order.OrderFact{
		// 			CommonFact: &common.CommonFact{
		// 				RefId:       "ref-id-1",
		// 				ActorId:     "actor-id-1",
		// 				RewardOffer: rewardOffer1,
		// 				ActionTime:  ptypes.TimestampNow(),
		// 			},
		// 			OrderEvent: orderUpdateEvent1,
		// 		},
		// 	},
		// 	setUpMocks: func() {
		// 		mockRewardOffersDao.EXPECT().GetActorLevelRewardOfferInventory(context.Background(), "actor-id-1", []string{rewardOffer1.GetId()}).Return([]*rewardOffersPb.RewardOfferInventory{{RemainingCount: 0, TotalCount: 10}}, nil)
		// 	},
		// 	want:    nil,
		// 	wantErr: false,
		// },
		// {
		// 	name: "actor level inventory is remaining but action level offer inventory is already exhausted, nil reward payload should be returned",
		// 	args: args{
		// 		ctx: context.Background(),
		// 		fact: &order.OrderFact{
		// 			CommonFact: &common.CommonFact{
		// 				RefId:       "ref-id-1",
		// 				ActorId:     "actor-id-1",
		// 				RewardOffer: rewardOffer1,
		// 				ActionTime:  ptypes.TimestampNow(),
		// 			},
		// 			OrderEvent: orderUpdateEvent1,
		// 		},
		// 	},
		// 	setUpMocks: func() {
		// 		mockRewardOffersDao.EXPECT().GetActorLevelRewardOfferInventory(context.Background(), "actor-id-1", []string{rewardOffer1.GetId()}).Return([]*rewardOffersPb.RewardOfferInventory{{RemainingCount: 1, TotalCount: 10}}, nil)
		// 		mockRewardOffersDao.EXPECT().GetActionLevelRewardOfferInventory(context.Background(), []string{rewardOffer1.GetId()}).Return([]*rewardOffersPb.RewardOfferInventory{{RemainingCount: 0, TotalCount: 10}}, nil)
		// 	},
		// 	want:    nil,
		// 	wantErr: false,
		// },
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setUpMocks()

			got, err := customRuleEngine.CalculateRewards(tt.args.ctx, tt.args.fact)
			if (err != nil) != tt.wantErr {
				t.Errorf("CalculateRewards() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !compareReward(got, tt.want) {
				t.Errorf("CalculateRewards()\n got = %+v\nwant = %+v", got, tt.want)
			}
		})
	}
}

// compares actual and expected values for equality
func compareReward(actual, expected *model.Reward) bool {
	if actual == nil && expected == nil {
		return true
	}
	if actual == nil || expected == nil {
		return false
	}

	actual.CreatedAt = expected.CreatedAt
	actual.UpdatedAt = expected.UpdatedAt
	expected.RewardDisplay.AnimationSpeed = actual.RewardDisplay.GetAnimationSpeed()

	// compare reward options using custom equality function
	if !compareRewardOptions(actual.RewardOptions, expected.RewardOptions) {
		return false
	}
	// setting field to same value to ignore it in proto equality check.
	actual.RewardOptions = expected.RewardOptions

	// compare chosen option using custom equality function
	if !compareRewardOption(actual.ChosenReward, expected.ChosenReward) {
		return false
	}
	// setting field to same value to ignore it in proto equality check.
	actual.ChosenReward = expected.ChosenReward

	return reflect.DeepEqual(actual, expected)
}

// compares actual and expected values for equality
func compareRewardOptions(actual, expected *rewardsPb.RewardOptions) bool {
	if actual == nil && expected == nil {
		return true
	}
	if len(actual.GetOptions()) != len(expected.GetOptions()) {
		return false
	}
	// compare each reward option using custom equality function
	for idx := range actual.GetOptions() {
		if !compareRewardOption(actual.GetOptions()[idx], expected.GetOptions()[idx]) {
			return false
		}
	}
	// setting field to same value to ignore it in proto equality check.
	actual.Options = expected.GetOptions()

	return proto.Equal(actual, expected)
}

// compares actual and expected values for equality
func compareRewardOption(actual, expected *rewardsPb.RewardOption) bool {
	if actual == nil && expected == nil {
		return true
	}
	// ignoring some fields for equality check.
	if actual.GetFiCoins() != nil && expected.GetFiCoins() != nil {
		actual.GetFiCoins().ExpiresAt = expected.GetFiCoins().GetExpiresAt()
	}
	actual.Id = expected.Id
	actual.ProcessingDate = expected.GetProcessingDate()

	return proto.Equal(actual, expected)
}

func TestCustomRuleEngine_capRewardUnitsAtOfferAndActor(t *testing.T) {
	type args struct {
		ctx                  context.Context
		rewardUnits          float64
		rewardType           rewardsPb.RewardType
		actorId              string
		offerId              string
		rewardUnitsCapConfig *rewardOffersPb.RewardUnitsCapAggregate
	}

	type daoCallResult struct {
		successResult []*rewardOffersPb.RewardOfferRewardUnitsActorUtilisation
		err           error
	}

	tests := []struct {
		name          string
		args          args
		daoCall       bool
		daoCallResult daoCallResult
		want          float64
		wantErr       bool
	}{
		{
			name: "Returns the rewardUnits as is if no caps config is defined",
			args: args{
				ctx:                  context.Background(),
				rewardUnits:          123.0,
				rewardType:           rewardsPb.RewardType_FI_COINS,
				actorId:              "randomActorId",
				offerId:              "randomOfferId",
				rewardUnitsCapConfig: nil,
			},
			daoCall:       false,
			daoCallResult: daoCallResult{},
			want:          123.0,
			wantErr:       false,
		},
		{
			name: "Returns the rewardUnits as is if the caps config is set for some other rewardType and actor utilisation doesn't exist",
			args: args{
				ctx:         context.Background(),
				rewardUnits: 123.0,
				rewardType:  rewardsPb.RewardType_FI_COINS,
				actorId:     "randomActorId",
				offerId:     "randomOfferId",
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_CASH,
							Units:      10,
						},
					},
				},
			},
			daoCall:       false,
			daoCallResult: daoCallResult{},
			want:          123.0,
			wantErr:       false,
		},
		{
			name: "Returns the rewardUnits (Y rewardType) as is if the caps config is set for X rewardType and actor utilisation exist for Y rewardType",
			args: args{
				ctx:         context.Background(),
				rewardUnits: 123.0,
				rewardType:  rewardsPb.RewardType_FI_COINS,
				actorId:     "randomActorId",
				offerId:     "randomOfferId",
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_CASH,
							Units:      10,
						},
					},
				},
			},
			daoCall:       false,
			daoCallResult: daoCallResult{},
			want:          123.0,
			wantErr:       false,
		},
		{
			name: "Returns the rewardUnits (Y rewardType) as is if the caps config is set for X rewardType and actor utilisation exist for X rewardType",
			args: args{
				ctx:         context.Background(),
				rewardUnits: 123.0,
				rewardType:  rewardsPb.RewardType_FI_COINS,
				actorId:     "randomActorId",
				offerId:     "randomOfferId",
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_CASH,
							Units:      10,
						},
					},
				},
			},
			daoCall:       false,
			daoCallResult: daoCallResult{},
			want:          123.0,
			wantErr:       false,
		},
		{
			name: "Returns error if dao call to fetch actor utilisation of reward-units fails",
			args: args{
				ctx:         context.Background(),
				rewardUnits: 123.0,
				rewardType:  rewardsPb.RewardType_FI_COINS,
				actorId:     "randomActorId",
				offerId:     "randomOfferId",
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      10,
						},
					},
				},
			},
			daoCall: true,
			daoCallResult: daoCallResult{
				successResult: nil,
				err:           errors.New("some random error apart from ErrRecordNotFound"),
			},
			want:    0.0,
			wantErr: true,
		},
		{
			name: "Returns error if cap config is set for a rewardType which does not support capping",
			args: args{
				ctx:         context.Background(),
				rewardUnits: 123.0,
				rewardType:  rewardsPb.RewardType_LUCKY_DRAW,
				actorId:     "randomActorId",
				offerId:     "randomOfferId",
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_LUCKY_DRAW,
							Units:      10,
						},
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      100,
						},
					},
				},
			},
			daoCall: true,
			daoCallResult: daoCallResult{
				successResult: nil,
				err:           epifierrors.ErrRecordNotFound,
			},
			want:    0.0,
			wantErr: true,
		},
		{
			name: "Returns capped reward-units using the cap-config itself if actor's utilisation record is not found, i.e. assuming the utilisation is 0",
			args: args{
				ctx:         context.Background(),
				rewardUnits: 123.0,
				rewardType:  rewardsPb.RewardType_FI_COINS,
				actorId:     "randomActorId",
				offerId:     "randomOfferId",
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_CASH,
							Units:      10,
						},
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      100,
						},
					},
				},
			},
			daoCall: true,
			daoCallResult: daoCallResult{
				successResult: nil,
				err:           epifierrors.ErrRecordNotFound,
			},
			want:    100.0,
			wantErr: false,
		},
		{
			name: "Returns capped reward-units using the cap-config and actor's utilisation record",
			args: args{
				ctx:         context.Background(),
				rewardUnits: 123.0,
				rewardType:  rewardsPb.RewardType_FI_COINS,
				actorId:     "randomActorId",
				offerId:     "randomOfferId",
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_CASH,
							Units:      10,
						},
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      100,
						},
					},
				},
			},
			daoCall: true,
			daoCallResult: daoCallResult{
				successResult: []*rewardOffersPb.RewardOfferRewardUnitsActorUtilisation{
					{
						ActorId:      "randomActorId",
						OfferId:      "randomOfferId",
						FiCoinsUnits: 49,
						CashUnits:    9,
					},
				},
				err: nil,
			},
			want:    51.0,
			wantErr: false,
		},
		{
			name: "Returns the reward-units as is if the the utilisation has room for the new reward-units to be given out",
			args: args{
				ctx:         context.Background(),
				rewardUnits: 123.0,
				rewardType:  rewardsPb.RewardType_FI_COINS,
				actorId:     "randomActorId",
				offerId:     "randomOfferId",
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_CASH,
							Units:      10,
						},
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      300,
						},
					},
				},
			},
			daoCall: true,
			daoCallResult: daoCallResult{
				successResult: []*rewardOffersPb.RewardOfferRewardUnitsActorUtilisation{
					{
						ActorId:      "randomActorId",
						OfferId:      "randomOfferId",
						FiCoinsUnits: 49,
						CashUnits:    9,
					},
				},
				err: nil,
			},
			want:    123.0,
			wantErr: false,
		},
		{
			name: "Returns an error if the utilisation has already touched the max-cap, i.e. max cap reached",
			args: args{
				ctx:         context.Background(),
				rewardUnits: 123.0,
				rewardType:  rewardsPb.RewardType_FI_COINS,
				actorId:     "randomActorId",
				offerId:     "randomOfferId",
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_CASH,
							Units:      10,
						},
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      100,
						},
					},
				},
			},
			daoCall: true,
			daoCallResult: daoCallResult{
				successResult: []*rewardOffersPb.RewardOfferRewardUnitsActorUtilisation{
					{
						ActorId:      "randomActorId",
						OfferId:      "randomOfferId",
						FiCoinsUnits: 100,
						CashUnits:    9,
					},
				},
				err: nil,
			},
			want:    0.0,
			wantErr: true,
		},
		{
			name: "Returns an error if the utilisation has exceeded the cap-value, i.e. cap-value was reduced after the actor had already utilised the previous cap-limit",
			args: args{
				ctx:         context.Background(),
				rewardUnits: 123.0,
				rewardType:  rewardsPb.RewardType_FI_COINS,
				actorId:     "randomActorId",
				offerId:     "randomOfferId",
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_CASH,
							Units:      10,
						},
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      100,
						},
					},
				},
			},
			daoCall: true,
			daoCallResult: daoCallResult{
				successResult: []*rewardOffersPb.RewardOfferRewardUnitsActorUtilisation{
					{
						ActorId:      "randomActorId",
						OfferId:      "randomOfferId",
						FiCoinsUnits: 200,
						CashUnits:    9,
					},
				},
				err: nil,
			},
			want:    0.0,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			rewardOfferDao := daoMocks.NewMockRewardOfferDao(ctr)
			offerGroupDao := daoMocks.NewMockRewardOfferGroupDao(ctr)

			customRuleEngine := &CustomRuleEngine{
				rewardOfferDao: rewardOfferDao,
				offerGroupDao:  offerGroupDao,
			}

			if tt.daoCall {
				rewardOfferDao.EXPECT().GetOffersRewardUnitsUtilized(tt.args.ctx, tt.args.actorId, []string{tt.args.offerId}).Return(tt.daoCallResult.successResult, tt.daoCallResult.err)
			}
			got, err := customRuleEngine.capRewardUnitsAtOfferAndActor(tt.args.ctx, tt.args.rewardUnits, tt.args.rewardType, tt.args.actorId, tt.args.offerId, tt.args.rewardUnitsCapConfig)
			if (err != nil) != tt.wantErr {
				t.Errorf("capRewardUnitsAtOfferAndActor() got error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("capRewardUnitsAtOfferAndActor() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCustomRuleEngine_capRewardUnitsAtGroupAndActor(t *testing.T) {
	type args struct {
		ctx                  context.Context
		rewardUnits          float64
		rewardType           rewardsPb.RewardType
		actorId              string
		offerGroupId         string
		rewardUnitsCapConfig *rewardOffersPb.RewardUnitsCapAggregate
	}
	type daoCallResult struct {
		successResult []*rewardOffersPb.RewardOfferGroupRewardUnitsActorUtilisation
		err           error
	}

	tests := []struct {
		name          string
		args          args
		daoCall       bool
		daoCallResult daoCallResult
		want          float64
		wantErr       bool
	}{
		{
			name: "Returns the rewardUnits as is if no caps config is defined",
			args: args{
				ctx:                  context.Background(),
				rewardUnits:          123.0,
				rewardType:           rewardsPb.RewardType_FI_COINS,
				actorId:              "randomActorId",
				offerGroupId:         "randomOfferGroupId",
				rewardUnitsCapConfig: nil,
			},
			daoCall:       false,
			daoCallResult: daoCallResult{},
			want:          123.0,
			wantErr:       false,
		},
		{
			name: "Returns the rewardUnits as is if the caps config is set for some other rewardType and actor utilisation doesn't exist",
			args: args{
				ctx:          context.Background(),
				rewardUnits:  123.0,
				rewardType:   rewardsPb.RewardType_FI_COINS,
				actorId:      "randomActorId",
				offerGroupId: "randomOfferGroupId",
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_CASH,
							Units:      10,
						},
					},
				},
			},
			daoCall:       false,
			daoCallResult: daoCallResult{},
			want:          123.0,
			wantErr:       false,
		},
		{
			name: "Returns the rewardUnits (Y rewardType) as is if the caps config is set for X rewardType and actor utilisation exist for Y rewardType",
			args: args{
				ctx:          context.Background(),
				rewardUnits:  123.0,
				rewardType:   rewardsPb.RewardType_FI_COINS,
				actorId:      "randomActorId",
				offerGroupId: "randomOfferGroupId",
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_CASH,
							Units:      10,
						},
					},
				},
			},
			daoCall:       false,
			daoCallResult: daoCallResult{},
			want:          123.0,
			wantErr:       false,
		},
		{
			name: "Returns the rewardUnits (Y rewardType) as is if the caps config is set for X rewardType and actor utilisation exist for X rewardType",
			args: args{
				ctx:          context.Background(),
				rewardUnits:  123.0,
				rewardType:   rewardsPb.RewardType_FI_COINS,
				actorId:      "randomActorId",
				offerGroupId: "randomOfferGroupId",
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_CASH,
							Units:      10,
						},
					},
				},
			},
			daoCall:       false,
			daoCallResult: daoCallResult{},
			want:          123.0,
			wantErr:       false,
		},
		{
			name: "Returns error if dao call to fetch actor utilisation of reward-units fails",
			args: args{
				ctx:          context.Background(),
				rewardUnits:  123.0,
				rewardType:   rewardsPb.RewardType_FI_COINS,
				actorId:      "randomActorId",
				offerGroupId: "randomOfferGroupId",
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      10,
						},
					},
				},
			},
			daoCall: true,
			daoCallResult: daoCallResult{
				successResult: nil,
				err:           errors.New("some random error apart from ErrRecordNotFound"),
			},
			want:    0.0,
			wantErr: true,
		},
		{
			name: "Returns error if cap config is set for a rewardType which does not support capping",
			args: args{
				ctx:          context.Background(),
				rewardUnits:  123.0,
				rewardType:   rewardsPb.RewardType_LUCKY_DRAW,
				actorId:      "randomActorId",
				offerGroupId: "randomOfferGroupId",
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_LUCKY_DRAW,
							Units:      10,
						},
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      100,
						},
					},
				},
			},
			daoCall: true,
			daoCallResult: daoCallResult{
				successResult: nil,
				err:           epifierrors.ErrRecordNotFound,
			},
			want:    0.0,
			wantErr: true,
		},
		{
			name: "Returns capped reward-units using the cap-config itself if actor's utilisation record is not found, i.e. assuming the utilisation is 0",
			args: args{
				ctx:          context.Background(),
				rewardUnits:  123.0,
				rewardType:   rewardsPb.RewardType_FI_COINS,
				actorId:      "randomActorId",
				offerGroupId: "randomOfferGroupId",
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_CASH,
							Units:      10,
						},
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      100,
						},
					},
				},
			},
			daoCall: true,
			daoCallResult: daoCallResult{
				successResult: nil,
				err:           epifierrors.ErrRecordNotFound,
			},
			want:    100.0,
			wantErr: false,
		},
		{
			name: "Returns capped reward-units using the cap-config and actor's utilisation record",
			args: args{
				ctx:          context.Background(),
				rewardUnits:  123.0,
				rewardType:   rewardsPb.RewardType_FI_COINS,
				actorId:      "randomActorId",
				offerGroupId: "randomOfferGroupId",
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_CASH,
							Units:      10,
						},
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      100,
						},
					},
				},
			},
			daoCall: true,
			daoCallResult: daoCallResult{
				successResult: []*rewardOffersPb.RewardOfferGroupRewardUnitsActorUtilisation{
					{
						FiCoinsUnits: 49,
						CashUnits:    9,
					},
				},
				err: nil,
			},
			want:    51.0,
			wantErr: false,
		},
		{
			name: "Returns the reward-units as is if the the utilisation has room for the new reward-units to be given out",
			args: args{
				ctx:          context.Background(),
				rewardUnits:  123.0,
				rewardType:   rewardsPb.RewardType_FI_COINS,
				actorId:      "randomActorId",
				offerGroupId: "randomOfferGroupId",
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_CASH,
							Units:      10,
						},
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      300,
						},
					},
				},
			},
			daoCall: true,
			daoCallResult: daoCallResult{
				successResult: []*rewardOffersPb.RewardOfferGroupRewardUnitsActorUtilisation{
					{
						FiCoinsUnits: 49,
						CashUnits:    9,
					},
				},
				err: nil,
			},
			want:    123.0,
			wantErr: false,
		},
		{
			name: "Returns an error if the utilisation has already touched the max-cap",
			args: args{
				ctx:          context.Background(),
				rewardUnits:  123.0,
				rewardType:   rewardsPb.RewardType_FI_COINS,
				actorId:      "randomActorId",
				offerGroupId: "randomOfferGroupId",
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_CASH,
							Units:      10,
						},
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      100,
						},
					},
				},
			},
			daoCall: true,
			daoCallResult: daoCallResult{
				successResult: []*rewardOffersPb.RewardOfferGroupRewardUnitsActorUtilisation{
					{
						FiCoinsUnits: 100,
						CashUnits:    9,
					},
				},
				err: nil,
			},
			want:    0.0,
			wantErr: true,
		},
		{
			name: "Returns an error if the utilisation has exceeded the cap-value, i.e. cap-value was reduced after the actor had already utilised the previous cap-limit",
			args: args{
				ctx:          context.Background(),
				rewardUnits:  123.0,
				rewardType:   rewardsPb.RewardType_FI_COINS,
				actorId:      "randomActorId",
				offerGroupId: "randomOfferGroupId",
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_CASH,
							Units:      10,
						},
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      100,
						},
					},
				},
			},
			daoCall: true,
			daoCallResult: daoCallResult{
				successResult: []*rewardOffersPb.RewardOfferGroupRewardUnitsActorUtilisation{
					{
						FiCoinsUnits: 200,
						CashUnits:    9,
					},
				},
				err: nil,
			},
			want:    0.0,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()
			rewardOfferDao := daoMocks.NewMockRewardOfferDao(ctr)
			offerGroupDao := daoMocks.NewMockRewardOfferGroupDao(ctr)
			customRuleEngine := &CustomRuleEngine{
				rewardOfferDao: rewardOfferDao,
				offerGroupDao:  offerGroupDao,
			}

			if tt.daoCall {
				offerGroupDao.EXPECT().GetOfferGroupsRewardUnitsUtilized(tt.args.ctx, tt.args.actorId, []string{tt.args.offerGroupId}).Return(tt.daoCallResult.successResult, tt.daoCallResult.err)
			}
			got, err := customRuleEngine.capRewardUnitsAtGroupAndActor(tt.args.ctx, tt.args.rewardUnits, tt.args.rewardType, tt.args.actorId, tt.args.offerGroupId, tt.args.rewardUnitsCapConfig)
			if (err != nil) != tt.wantErr {
				t.Errorf("capRewardUnitsAtGroupAndActor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("capRewardUnitsAtGroupAndActor() got = %v, want %v", got, tt.want)
			}
		})
	}
}

// TestCustomRuleEngine_capRewardUnitsUsingCapConfigs doesn't contain the exhaustive cases. Please refer TestCustomRuleEngine_capRewardUnitsAtGroupAndActor
// and TestCustomRuleEngine_capRewardUnitsAtOfferAndActor for all the possible scenarios
func TestCustomRuleEngine_capRewardUnitsUsingCapConfigs(t *testing.T) {
	rewardOffer1 := &rewardOffersPb.RewardOffer{
		Id:      "randomOfferId",
		GroupId: "randomOfferGroupId",
		RewardMeta: &rewardOffersPb.RewardMeta{
			RewardAggregates: &rewardOffersPb.RewardAggregates{
				RewardUnitsCapUserAggregate: &rewardOffersPb.RewardUnitsCapAggregate{
					IsHardCheck: true,
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      170,
						},
						{
							RewardType: rewardsPb.RewardType_CASH,
							Units:      50,
						},
						{
							RewardType: rewardsPb.RewardType_SMART_DEPOSIT,
							Units:      60,
						},
					},
				},
			},
		},
	}

	reward1 := &model.Reward{
		ActorId: "randomActorId",
		OfferGroupRewardUnitsCapActorConfig: &rewardOffersPb.RewardUnitsCapAggregate{
			IsHardCheck: true,
			UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
				{
					RewardType: rewardsPb.RewardType_FI_COINS,
					Units:      30,
				},
				{
					RewardType: rewardsPb.RewardType_CASH,
					Units:      20,
				},
				{
					RewardType: rewardsPb.RewardType_SMART_DEPOSIT,
					Units:      10,
				},
			},
		},
	}

	fact1 := &order.OrderFact{
		CommonFact: &common.CommonFact{
			ActorId:     "randomActorId",
			RewardOffer: rewardOffer1,
		},
	}

	type args struct {
		ctx         context.Context
		rewardUnits float64
		rewardType  rewardsPb.RewardType
		reward      *model.Reward
		fact        common.IFact
	}
	tests := []struct {
		name    string
		args    args
		want    float64
		wantErr bool
		daoCall bool
	}{
		{
			name: "Caps the reward-units by running via offer-level and group-level cap-configs",
			args: args{
				ctx:         context.Background(),
				rewardUnits: 123.0,
				rewardType:  rewardsPb.RewardType_FI_COINS,
				fact:        fact1,
				reward:      reward1,
			},
			want:    30,
			wantErr: false,
			daoCall: true,
		},
		{
			name: "Returns the reward units as is if they are already zero",
			args: args{
				ctx:         context.Background(),
				rewardUnits: 0.0,
				rewardType:  rewardsPb.RewardType_FI_COINS,
				fact:        fact1,
				reward:      reward1,
			},
			want:    0.0,
			wantErr: false,
			daoCall: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()
			rewardOfferDao := daoMocks.NewMockRewardOfferDao(ctr)
			offerGroupDao := daoMocks.NewMockRewardOfferGroupDao(ctr)
			customRuleEngine := &CustomRuleEngine{
				rewardOfferDao: rewardOfferDao,
				offerGroupDao:  offerGroupDao,
			}

			if tt.daoCall {
				fact := tt.args.fact
				rewardOfferDao.EXPECT().GetOffersRewardUnitsUtilized(tt.args.ctx, fact.GetActorId(), []string{fact.GetRewardOffer().GetId()}).Return(nil, epifierrors.ErrRecordNotFound)
				offerGroupDao.EXPECT().GetOfferGroupsRewardUnitsUtilized(tt.args.ctx, fact.GetActorId(), []string{fact.GetRewardOffer().GetGroupId()}).Return(nil, epifierrors.ErrRecordNotFound)
			}

			got, err := customRuleEngine.capRewardUnitsUsingCapConfigs(tt.args.ctx, tt.args.rewardUnits, tt.args.rewardType, tt.args.fact, tt.args.reward)
			if (err != nil) != tt.wantErr {
				t.Errorf("capRewardUnitsUsingCapConfigs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("capRewardUnitsUsingCapConfigs() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCustomRuleEngine_applyBoostersToRewardUnits(t *testing.T) {
	customRuleEngine := &CustomRuleEngine{}

	type args struct {
		ctx            context.Context
		rewardUnits    float64
		boostersConfig *rewardOffersPb.BoostersConfig
		fact           common.IFact
	}
	tests := []struct {
		name    string
		args    args
		want    []*rewardsPb.RewardUnitsCalculationInfo_RewardUnitsCalculationEntry
		wantErr bool
	}{
		{
			name: "should return successfully with empty results if boosterConfig passed is nil",
			args: args{
				ctx:            context.Background(),
				rewardUnits:    123.0,
				boostersConfig: nil,
				fact: &order.OrderFact{
					CommonFact: &common.CommonFact{
						RefId:   "ref-id-1",
						ActorId: "actor-id-1",
					},
				},
			},
			wantErr: false,
			want:    nil,
		},
		{
			name: "should return the calculation entries successfully",
			args: args{
				ctx:         context.Background(),
				rewardUnits: 100,
				boostersConfig: &rewardOffersPb.BoostersConfig{
					Boosters: []*rewardOffersPb.BoosterConfig{
						{
							UnitsConfig: &rewardOffersPb.BoosterConfig_ConditionalExpressionBoosterConfig{
								ConditionalExpressionBoosterConfig: &rewardOffersPb.ConditionalExpressionBoosterConfig{
									ConfigUnits: []*rewardOffersPb.ConditionalExpressionBoosterConfig_ConfigUnit{
										// this will evaluate to false
										{
											ConditionExpression:        "false",
											OperationWithPreviousValue: rewardOffersPb.MathematicalOperation_MULTIPLICATION,
											UnitsConfig: &rewardOffersPb.ConditionalExpressionBoosterConfig_ConfigUnit_ExpressionRangeProbabilityConfig{
												ExpressionRangeProbabilityConfig: &rewardOffersPb.ExpressionRangeProbabilityConfig{
													Expression: "2",
													RangeProbabilityUnits: []*rewardOffersPb.ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit{
														{
															UnitPercentageStart: 100,
															UnitPercentageEnd:   100,
															Percentage:          100,
														},
													},
													UpperLimit: 300,
													LowerLimit: 50,
												},
											},
											DisplayDetails: &rewardOffersPb.ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails{
												Tags: []rewardsPb.RewardTag{rewardsPb.RewardTag_TIER_FI_BASIC},
											},
										},
										// this will take the LowerLimit and multiply by PREVIOUS_VALUE, i.e. 100 (base value) => 5000
										{
											ConditionExpression:        "true",
											OperationWithPreviousValue: rewardOffersPb.MathematicalOperation_MULTIPLICATION,
											UnitsConfig: &rewardOffersPb.ConditionalExpressionBoosterConfig_ConfigUnit_ExpressionRangeProbabilityConfig{
												ExpressionRangeProbabilityConfig: &rewardOffersPb.ExpressionRangeProbabilityConfig{
													Expression: "3",
													RangeProbabilityUnits: []*rewardOffersPb.ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit{
														{
															UnitPercentageStart: 100,
															UnitPercentageEnd:   100,
															Percentage:          100,
														},
													},
													UpperLimit: 900,
													LowerLimit: 50,
												},
											},
											DisplayDetails: &rewardOffersPb.ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails{
												Tags: []rewardsPb.RewardTag{rewardsPb.RewardTag_TIER_FI_PLUS},
											},
										},
									},
								},
							},
						},
						{
							UnitsConfig: &rewardOffersPb.BoosterConfig_ConditionalExpressionBoosterConfig{
								ConditionalExpressionBoosterConfig: &rewardOffersPb.ConditionalExpressionBoosterConfig{
									ConfigUnits: []*rewardOffersPb.ConditionalExpressionBoosterConfig_ConfigUnit{
										// this will evaluate to false
										{
											ConditionExpression:        "false",
											OperationWithPreviousValue: rewardOffersPb.MathematicalOperation_ADDITION,
											UnitsConfig: &rewardOffersPb.ConditionalExpressionBoosterConfig_ConfigUnit_ExpressionRangeProbabilityConfig{
												ExpressionRangeProbabilityConfig: &rewardOffersPb.ExpressionRangeProbabilityConfig{
													Expression: "BASE_VALUE * 2",
													RangeProbabilityUnits: []*rewardOffersPb.ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit{
														{
															UnitPercentageStart: 100,
															UnitPercentageEnd:   100,
															Percentage:          100,
														},
													},
													UpperLimit: 300,
													LowerLimit: 50,
												},
											},
											DisplayDetails: &rewardOffersPb.ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails{
												Tags: []rewardsPb.RewardTag{rewardsPb.RewardTag_BOOSTER_2X},
											},
										},
									},
								},
							},
						},
						{
							UnitsConfig: &rewardOffersPb.BoosterConfig_ConditionalExpressionBoosterConfig{
								ConditionalExpressionBoosterConfig: &rewardOffersPb.ConditionalExpressionBoosterConfig{
									ConfigUnits: []*rewardOffersPb.ConditionalExpressionBoosterConfig_ConfigUnit{
										// this will add 100 * 2 to the PREVIOUS_VALUE, 5000 + 200
										{
											ConditionExpression:        "true",
											OperationWithPreviousValue: rewardOffersPb.MathematicalOperation_ADDITION,
											UnitsConfig: &rewardOffersPb.ConditionalExpressionBoosterConfig_ConfigUnit_ExpressionRangeProbabilityConfig{
												ExpressionRangeProbabilityConfig: &rewardOffersPb.ExpressionRangeProbabilityConfig{
													Expression: "BASE_VALUE * 2",
													RangeProbabilityUnits: []*rewardOffersPb.ExpressionRangeProbabilityConfig_RangeProbabilityConfigUnit{
														{
															UnitPercentageStart: 100,
															UnitPercentageEnd:   100,
															Percentage:          100,
														},
													},
													UpperLimit: 300000,
													LowerLimit: 50,
												},
											},
											DisplayDetails: &rewardOffersPb.ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails{
												Tags: []rewardsPb.RewardTag{rewardsPb.RewardTag_BOOSTER_3X},
											},
										},
									},
								},
							},
						},
					},
					ClaimFlowRewardValueRevealType: rewardsPb.ClaimFlowRewardValueRevealType_CLAIM_FLOW_REWARD_VALUE_REVEAL_TYPE_INTERMEDIATE_VALUES,
				},
				fact: &order.OrderFact{
					CommonFact: &common.CommonFact{
						RefId:   "ref-id-1",
						ActorId: "actor-id-1",
					},
				},
			},
			wantErr: false,
			want: []*rewardsPb.RewardUnitsCalculationInfo_RewardUnitsCalculationEntry{
				{
					RewardValue: 5000,
					DisplayDetails: &rewardsPb.RewardUnitsCalculationInfo_RewardUnitsCalculationEntry_DisplayDetails{
						Tags: []rewardsPb.RewardTag{rewardsPb.RewardTag_TIER_FI_PLUS},
					},
				},
				{
					RewardValue: 5200,
					DisplayDetails: &rewardsPb.RewardUnitsCalculationInfo_RewardUnitsCalculationEntry_DisplayDetails{
						Tags: []rewardsPb.RewardTag{rewardsPb.RewardTag_BOOSTER_3X},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := customRuleEngine.applyBoostersToRewardUnits(tt.args.ctx, tt.args.rewardUnits, tt.args.boostersConfig, tt.args.fact)
			if (err != nil) != tt.wantErr {
				t.Errorf("applyBoostersToRewardUnits() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("applyBoostersToRewardUnits() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCustomRuleEngine_calculateRewardUnitsUsingProjections(t *testing.T) {
	var (
		date1                    = time.Date(2023, 9, 1, 0, 0, 0, 0, time.UTC)
		date2                    = time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
		date3                    = time.Date(2024, 1, 31, 0, 0, 0, 0, time.UTC)
		projectionQueryFieldMask = []projectorPb.ProjectionFieldMask{
			projectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_ID,
			projectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_PROJECTED_OPTIONS,
			projectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_REWARD_ID,
			projectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_ACTION_TIME,
			projectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_ACTION_TYPE,
			projectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_OFFER_ID,
			projectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_OFFER_TYPE,
		}
	)

	type args struct {
		ctx                context.Context
		fact               common.IFact
		rewardConfigOption *rewardOfferPb.RewardConfigOption
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockedProjectionDao *mock_dao.MockRewardsProjectionDao, mockedFfAccountingClient *ffAccountsMocks.MockAccountingClient)
		want       *RewardUnitsInfo
		wantErr    bool
	}{
		{
			name: "should return error when we're trying to generate a reward using projections for an unsupported event",
			args: args{
				ctx: context.Background(),
				fact: &creditcardtxn.CreditCardTransactionFact{
					CommonFact: &common.CommonFact{
						ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION,
					},
					TransactionEvent: &datacollectorPb.RewardsCreditCardTxnEvent{
						CreditCardTransactionEvent: &consumer.CreditCardTransactionEvent{
							TransactionAdditionalInfo: &accounting.TransactionAdditionalInfo{
								ActorFrom: "act-1",
								ActorTo:   "act-2",
							},
						},
						ActorIdToTxnCategoryOntologyIds: nil,
					},
				},
			},
			setupMocks: func(mockedProjectionDao *mock_dao.MockRewardsProjectionDao, mockedFfAccountingClient *ffAccountsMocks.MockAccountingClient) {
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return error when GetTransactionIdsByBillId RPC returns an unsuccessful response",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_BILLING,
					},
					CreditCardBillingEvent: &events.CreditCardBillGenerationEvent{
						ActorId: "act-1",
						BillId:  "bill-1",
						// AccountId: "acc-1",
					},
				},
			},
			setupMocks: func(mockedProjectionDao *mock_dao.MockRewardsProjectionDao, mockedFfAccountingClient *ffAccountsMocks.MockAccountingClient) {
				mockedFfAccountingClient.EXPECT().GetTransactionIdsByBillId(context.Background(), &accounting.GetTransactionIdsByBillIdRequest{
					BillId: "bill-1",
				}).Return(&accounting.GetTransactionIdsByBillIdResponse{Status: rpc.StatusInternalWithDebugMsg("err")}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return error when FetchPaginatedProjectionsByFilters dao method returns an error",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_BILLING,
						ActionTime: timestampPb.New(date1),
						ActorId:    "act-1",
					},
					CreditCardBillingEvent: &events.CreditCardBillGenerationEvent{
						ActorId: "act-1",
						BillId:  "bill-1",
						// AccountId: "acc-1",
					},
				},
			},
			setupMocks: func(mockedProjectionDao *mock_dao.MockRewardsProjectionDao, mockedFfAccountingClient *ffAccountsMocks.MockAccountingClient) {
				mockedFfAccountingClient.EXPECT().GetTransactionIdsByBillId(context.Background(), &accounting.GetTransactionIdsByBillIdRequest{
					BillId: "bill-1",
				}).Return(&accounting.GetTransactionIdsByBillIdResponse{
					Status: rpc.StatusOk(),
					TransactionIdResponses: []*accounting.GetTransactionIdsByBillIdResponse_TransactionIdResponse{
						{ExternalTxnId: "ext-txn-id-1"},
						{ExternalTxnId: "ext-txn-id-2"},
						{ExternalTxnId: "ext-txn-id-3"},
						{ExternalTxnId: "ext-txn-id-4"},
					},
				}, nil)
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					nil,
					30,
					projectionQueryFieldMask,
				).Return(nil, nil, fmt.Errorf("error"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return correct projections when all required info is fetched from projections DB in a single call (no pagination)",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_BILLING,
						ActionTime: timestampPb.New(date1),
						ActorId:    "act-1",
					},
					CreditCardBillingEvent: &events.CreditCardBillGenerationEvent{
						ActorId: "act-1",
						BillId:  "bill-1",
						// AccountId: "acc-1",
					},
				},
				rewardConfigOption: &rewardOffersPb.RewardConfigOption{
					RewardType:                    rewardsPb.RewardType_FI_COINS,
					ShouldGenerateFromProjections: true,
				},
			},
			setupMocks: func(mockedProjectionDao *mock_dao.MockRewardsProjectionDao, mockedFfAccountingClient *ffAccountsMocks.MockAccountingClient) {
				mockedFfAccountingClient.EXPECT().GetTransactionIdsByBillId(context.Background(), &accounting.GetTransactionIdsByBillIdRequest{
					BillId: "bill-1",
				}).Return(&accounting.GetTransactionIdsByBillIdResponse{
					Status: rpc.StatusOk(),
					TransactionIdResponses: []*accounting.GetTransactionIdsByBillIdResponse_TransactionIdResponse{
						{ExternalTxnId: "ext-txn-id-1"},
						{ExternalTxnId: "ext-txn-id-2"},
						{ExternalTxnId: "ext-txn-id-3"},
						{ExternalTxnId: "ext-txn-id-4"},
					},
				}, nil)
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					nil,
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-1", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 10}}}, RefId: "ext-txn-id-1"},
					{Id: "proj-2", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 20}}}, RefId: "ext-txn-id-2"},
					{Id: "proj-3", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 30}}}, RefId: "ext-txn-id-3"},
					{Id: "proj-4", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 40}}}, RefId: "ext-txn-id-4"},
				}, nil, nil)
			},
			want: &RewardUnitsInfo{
				rewardUnits: 100,
				projectionIdsToOptionMap: map[string]*projectorPb.RewardOption{
					"proj-1": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 10},
					"proj-2": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 20},
					"proj-3": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 30},
					"proj-4": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 40},
				},
			},
			wantErr: false,
		},
		{
			name: "should return error when all required info is fetched from projections DB in a single call and net reward units < 0",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_BILLING,
						ActionTime: timestampPb.New(date1),
						ActorId:    "act-1",
					},
					CreditCardBillingEvent: &events.CreditCardBillGenerationEvent{
						ActorId: "act-1",
						BillId:  "bill-1",
						// AccountId: "acc-1",
					},
				},
				rewardConfigOption: &rewardOffersPb.RewardConfigOption{
					RewardType:                    rewardsPb.RewardType_FI_COINS,
					ShouldGenerateFromProjections: true,
				},
			},
			setupMocks: func(mockedProjectionDao *mock_dao.MockRewardsProjectionDao, mockedFfAccountingClient *ffAccountsMocks.MockAccountingClient) {
				mockedFfAccountingClient.EXPECT().GetTransactionIdsByBillId(context.Background(), &accounting.GetTransactionIdsByBillIdRequest{
					BillId: "bill-1",
				}).Return(&accounting.GetTransactionIdsByBillIdResponse{
					Status: rpc.StatusOk(),
					TransactionIdResponses: []*accounting.GetTransactionIdsByBillIdResponse_TransactionIdResponse{
						{ExternalTxnId: "ext-txn-id-1"},
						{ExternalTxnId: "ext-txn-id-2"},
						{ExternalTxnId: "ext-txn-id-3"},
						{ExternalTxnId: "ext-txn-id-4"},
					},
				}, nil)
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					nil,
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-1", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 10}}}, RefId: "ext-txn-id-1"},
					{Id: "proj-2", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -20}}}, RefId: "ext-txn-id-2"},
					{Id: "proj-3", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 30}}}, RefId: "ext-txn-id-3"},
					{Id: "proj-4", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -40}}}, RefId: "ext-txn-id-4"},
				}, nil, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return correct projections when all required info is fetched from projections DB (with pagination)",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_BILLING,
						ActionTime: timestampPb.New(date1),
						ActorId:    "act-1",
					},
					CreditCardBillingEvent: &events.CreditCardBillGenerationEvent{
						ActorId: "act-1",
						BillId:  "bill-1",
						// AccountId: "acc-1",
					},
				},
				rewardConfigOption: &rewardOffersPb.RewardConfigOption{
					RewardType:                    rewardsPb.RewardType_FI_COINS,
					ShouldGenerateFromProjections: true,
				},
			},
			setupMocks: func(mockedProjectionDao *mock_dao.MockRewardsProjectionDao, mockedFfAccountingClient *ffAccountsMocks.MockAccountingClient) {
				mockedFfAccountingClient.EXPECT().GetTransactionIdsByBillId(context.Background(), &accounting.GetTransactionIdsByBillIdRequest{
					BillId: "bill-1",
				}).Return(&accounting.GetTransactionIdsByBillIdResponse{
					Status: rpc.StatusOk(),
					TransactionIdResponses: []*accounting.GetTransactionIdsByBillIdResponse_TransactionIdResponse{
						{ExternalTxnId: "ext-txn-id-1"},
						{ExternalTxnId: "ext-txn-id-2"},
						{ExternalTxnId: "ext-txn-id-3"},
						{ExternalTxnId: "ext-txn-id-4"},
					},
				}, nil)
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					nil,
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-1", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 10}}}, RefId: "ext-txn-id-1"},
					{Id: "proj-2", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 20}}}, RefId: "ext-txn-id-2"},
				}, &rpc.PageContextResponse{HasAfter: true, AfterToken: "ewogICJUaW1lc3RhbXAiOiB7IlNlY29uZHMiOjEyMzR9LAogICJPZmZzZXQiOiAyLAogICJJc1JldmVyc2UiOiBmYWxzZQp9"}, nil)

				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					&pagination.PageToken{
						Timestamp: &timestampPb.Timestamp{Seconds: 1234},
						Offset:    2,
					},
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-3", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 30}}}, RefId: "ext-txn-id-3"},
					{Id: "proj-4", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 40}}}, RefId: "ext-txn-id-4"},
				}, nil, nil)
			},
			want: &RewardUnitsInfo{
				rewardUnits: 100,
				projectionIdsToOptionMap: map[string]*projectorPb.RewardOption{
					"proj-1": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 10},
					"proj-2": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 20},
					"proj-3": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 30},
					"proj-4": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 40},
				},
			},
			wantErr: false,
		},
		{
			name: "should return correct projections when all required info is fetched from projections DB (with pagination and capping)",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_BILLING,
						ActionTime: timestampPb.New(date1),
						ActorId:    "act-1",
					},
					CreditCardBillingEvent: &events.CreditCardBillGenerationEvent{
						ActorId: "act-1",
						BillId:  "bill-1",
						// AccountId: "acc-1",
					},
				},
				rewardConfigOption: &rewardOffersPb.RewardConfigOption{
					RewardType:                    rewardsPb.RewardType_FI_COINS,
					ShouldGenerateFromProjections: true,
					RewardUnitsUpperLimit:         70,
				},
			},
			setupMocks: func(mockedProjectionDao *mock_dao.MockRewardsProjectionDao, mockedFfAccountingClient *ffAccountsMocks.MockAccountingClient) {
				mockedFfAccountingClient.EXPECT().GetTransactionIdsByBillId(context.Background(), &accounting.GetTransactionIdsByBillIdRequest{
					BillId: "bill-1",
				}).Return(&accounting.GetTransactionIdsByBillIdResponse{
					Status: rpc.StatusOk(),
					TransactionIdResponses: []*accounting.GetTransactionIdsByBillIdResponse_TransactionIdResponse{
						{ExternalTxnId: "ext-txn-id-1"},
						{ExternalTxnId: "ext-txn-id-2"},
						{ExternalTxnId: "ext-txn-id-3"},
						{ExternalTxnId: "ext-txn-id-4"},
					},
				}, nil)
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					nil,
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-1", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 10}}}, RefId: "ext-txn-id-1"},
					{Id: "proj-2", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 20}}}, RefId: "ext-txn-id-2"},
				}, &rpc.PageContextResponse{HasAfter: true, AfterToken: "ewogICJUaW1lc3RhbXAiOiB7IlNlY29uZHMiOjEyMzR9LAogICJPZmZzZXQiOiAyLAogICJJc1JldmVyc2UiOiBmYWxzZQp9"}, nil)

				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					&pagination.PageToken{
						Timestamp: &timestampPb.Timestamp{Seconds: 1234},
						Offset:    2,
					},
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-3", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 30}}}, RefId: "ext-txn-id-3"},
					{Id: "proj-4", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 40}}}, RefId: "ext-txn-id-4"},
				}, nil, nil)
			},
			want: &RewardUnitsInfo{
				rewardUnits: 70,
				projectionIdsToOptionMap: map[string]*projectorPb.RewardOption{
					"proj-1": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 10},
					"proj-2": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 20},
					"proj-3": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 30},
					"proj-4": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 10},
				},
			},
			wantErr: false,
		},
		{
			name: "should return correct projections when all required info is fetched from projections DB (with pagination, capping, and negative projections) - 1",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_BILLING,
						ActionTime: timestampPb.New(date1),
						ActorId:    "act-1",
					},
					CreditCardBillingEvent: &events.CreditCardBillGenerationEvent{
						ActorId: "act-1",
						BillId:  "bill-1",
						// AccountId: "acc-1",
					},
				},
				rewardConfigOption: &rewardOffersPb.RewardConfigOption{
					RewardType:                    rewardsPb.RewardType_FI_COINS,
					ShouldGenerateFromProjections: true,
					RewardUnitsUpperLimit:         70,
				},
			},
			setupMocks: func(mockedProjectionDao *mock_dao.MockRewardsProjectionDao, mockedFfAccountingClient *ffAccountsMocks.MockAccountingClient) {
				mockedFfAccountingClient.EXPECT().GetTransactionIdsByBillId(context.Background(), &accounting.GetTransactionIdsByBillIdRequest{
					BillId: "bill-1",
				}).Return(&accounting.GetTransactionIdsByBillIdResponse{
					Status: rpc.StatusOk(),
					TransactionIdResponses: []*accounting.GetTransactionIdsByBillIdResponse_TransactionIdResponse{
						{ExternalTxnId: "ext-txn-id-1"},
						{ExternalTxnId: "ext-txn-id-2"},
						{ExternalTxnId: "ext-txn-id-3"},
						{ExternalTxnId: "ext-txn-id-4"},
					},
				}, nil)
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					nil,
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-1", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 40}}}, RefId: "ext-txn-id-1"},
					{Id: "proj-2", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 10}}}, RefId: "ext-txn-id-2"},
				}, &rpc.PageContextResponse{HasAfter: true, AfterToken: "ewogICJUaW1lc3RhbXAiOiB7IlNlY29uZHMiOjEyMzR9LAogICJPZmZzZXQiOiAyLAogICJJc1JldmVyc2UiOiBmYWxzZQp9"}, nil)

				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					&pagination.PageToken{
						Timestamp: &timestampPb.Timestamp{Seconds: 1234},
						Offset:    2,
					},
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-3", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -30}}}, RefId: "ext-txn-id-3"},
					{Id: "proj-4", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 50}}}, RefId: "ext-txn-id-4"},
				}, nil, nil)
			},
			want: &RewardUnitsInfo{
				rewardUnits: 70,
				projectionIdsToOptionMap: map[string]*projectorPb.RewardOption{
					"proj-1": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 40},
					"proj-2": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 10},
					"proj-3": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -30},
					"proj-4": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 50},
				},
			},
			wantErr: false,
		},
		{
			name: "should return correct projections when all required info is fetched from projections DB (with pagination, capping, and negative projections) - 2",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_BILLING,
						ActionTime: timestampPb.New(date1),
						ActorId:    "act-1",
					},
					CreditCardBillingEvent: &events.CreditCardBillGenerationEvent{
						ActorId: "act-1",
						BillId:  "bill-1",
						// AccountId: "acc-1",
					},
				},
				rewardConfigOption: &rewardOffersPb.RewardConfigOption{
					RewardType:                    rewardsPb.RewardType_FI_COINS,
					ShouldGenerateFromProjections: true,
					RewardUnitsUpperLimit:         300,
				},
			},
			setupMocks: func(mockedProjectionDao *mock_dao.MockRewardsProjectionDao, mockedFfAccountingClient *ffAccountsMocks.MockAccountingClient) {
				mockedFfAccountingClient.EXPECT().GetTransactionIdsByBillId(context.Background(), &accounting.GetTransactionIdsByBillIdRequest{
					BillId: "bill-1",
				}).Return(&accounting.GetTransactionIdsByBillIdResponse{
					Status: rpc.StatusOk(),
					TransactionIdResponses: []*accounting.GetTransactionIdsByBillIdResponse_TransactionIdResponse{
						{ExternalTxnId: "ext-txn-id-1"},
						{ExternalTxnId: "ext-txn-id-2"},
						{ExternalTxnId: "ext-txn-id-3"},
						{ExternalTxnId: "ext-txn-id-4"},
					},
				}, nil)
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					nil,
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-1", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -100}}}, RefId: "ext-txn-id-1"},
					{Id: "proj-2", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}}, RefId: "ext-txn-id-2"},
				}, &rpc.PageContextResponse{HasAfter: true, AfterToken: "ewogICJUaW1lc3RhbXAiOiB7IlNlY29uZHMiOjEyMzR9LAogICJPZmZzZXQiOiAyLAogICJJc1JldmVyc2UiOiBmYWxzZQp9"}, nil)

				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					&pagination.PageToken{
						Timestamp: &timestampPb.Timestamp{Seconds: 1234},
						Offset:    2,
					},
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-3", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 300}}}, RefId: "ext-txn-id-3"},
					{Id: "proj-4", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}}, RefId: "ext-txn-id-4"},
				}, nil, nil)
			},
			want: &RewardUnitsInfo{
				rewardUnits: 300,
				projectionIdsToOptionMap: map[string]*projectorPb.RewardOption{
					"proj-1": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -100},
					"proj-2": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200},
					"proj-3": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200},
					"proj-4": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 0},
				},
			},
			wantErr: false,
		},
		{
			name: "should return correct projections when all required info is fetched from projections DB (with pagination, capping, and negative projections) - 3",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_BILLING,
						ActionTime: timestampPb.New(date1),
						ActorId:    "act-1",
					},
					CreditCardBillingEvent: &events.CreditCardBillGenerationEvent{
						ActorId: "act-1",
						BillId:  "bill-1",
						// AccountId: "acc-1",
					},
				},
				rewardConfigOption: &rewardOffersPb.RewardConfigOption{
					RewardType:                    rewardsPb.RewardType_FI_COINS,
					ShouldGenerateFromProjections: true,
					RewardUnitsUpperLimit:         200,
				},
			},
			setupMocks: func(mockedProjectionDao *mock_dao.MockRewardsProjectionDao, mockedFfAccountingClient *ffAccountsMocks.MockAccountingClient) {
				mockedFfAccountingClient.EXPECT().GetTransactionIdsByBillId(context.Background(), &accounting.GetTransactionIdsByBillIdRequest{
					BillId: "bill-1",
				}).Return(&accounting.GetTransactionIdsByBillIdResponse{
					Status: rpc.StatusOk(),
					TransactionIdResponses: []*accounting.GetTransactionIdsByBillIdResponse_TransactionIdResponse{
						{ExternalTxnId: "ext-txn-id-1"},
						{ExternalTxnId: "ext-txn-id-2"},
						{ExternalTxnId: "ext-txn-id-3"},
						{ExternalTxnId: "ext-txn-id-4"},
					},
				}, nil)
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					nil,
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-1", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}}, RefId: "ext-txn-id-1"},
					{Id: "proj-2", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 300}}}, RefId: "ext-txn-id-2"},
				}, &rpc.PageContextResponse{HasAfter: true, AfterToken: "ewogICJUaW1lc3RhbXAiOiB7IlNlY29uZHMiOjEyMzR9LAogICJPZmZzZXQiOiAyLAogICJJc1JldmVyc2UiOiBmYWxzZQp9"}, nil)

				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					&pagination.PageToken{
						Timestamp: &timestampPb.Timestamp{Seconds: 1234},
						Offset:    2,
					},
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-3", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 100}}}, RefId: "ext-txn-id-3"},
					{Id: "proj-4", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -200}}}, RefId: "ext-txn-id-4"},
				}, nil, nil)
			},
			want: &RewardUnitsInfo{
				rewardUnits: 200,
				projectionIdsToOptionMap: map[string]*projectorPb.RewardOption{
					"proj-1": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200},
					"proj-2": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200},
					"proj-3": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 0},
					"proj-4": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -200},
				},
			},
			wantErr: false,
		},
		{
			name: "should return correct projections when all required info is fetched from projections DB (with pagination, capping, and negative projections) - 4",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_BILLING,
						ActionTime: timestampPb.New(date1),
						ActorId:    "act-1",
					},
					CreditCardBillingEvent: &events.CreditCardBillGenerationEvent{
						ActorId: "act-1",
						BillId:  "bill-1",
						// AccountId: "acc-1",
					},
				},
				rewardConfigOption: &rewardOffersPb.RewardConfigOption{
					RewardType:                    rewardsPb.RewardType_FI_COINS,
					ShouldGenerateFromProjections: true,
					RewardUnitsUpperLimit:         200,
				},
			},
			setupMocks: func(mockedProjectionDao *mock_dao.MockRewardsProjectionDao, mockedFfAccountingClient *ffAccountsMocks.MockAccountingClient) {
				mockedFfAccountingClient.EXPECT().GetTransactionIdsByBillId(context.Background(), &accounting.GetTransactionIdsByBillIdRequest{
					BillId: "bill-1",
				}).Return(&accounting.GetTransactionIdsByBillIdResponse{
					Status: rpc.StatusOk(),
					TransactionIdResponses: []*accounting.GetTransactionIdsByBillIdResponse_TransactionIdResponse{
						{ExternalTxnId: "ext-txn-id-1"},
						{ExternalTxnId: "ext-txn-id-2"},
						{ExternalTxnId: "ext-txn-id-3"},
						{ExternalTxnId: "ext-txn-id-4"},
					},
				}, nil)
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					nil,
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-1", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}}, RefId: "ext-txn-id-1"},
					{Id: "proj-2", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 300}}}, RefId: "ext-txn-id-2"},
				}, &rpc.PageContextResponse{HasAfter: true, AfterToken: "ewogICJUaW1lc3RhbXAiOiB7IlNlY29uZHMiOjEyMzR9LAogICJPZmZzZXQiOiAyLAogICJJc1JldmVyc2UiOiBmYWxzZQp9"}, nil)

				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					&pagination.PageToken{
						Timestamp: &timestampPb.Timestamp{Seconds: 1234},
						Offset:    2,
					},
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-3", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 100}}}, RefId: "ext-txn-id-3"},
					{Id: "proj-4", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -500}}}, RefId: "ext-txn-id-4"},
				}, nil, nil)
			},
			want: &RewardUnitsInfo{
				rewardUnits: 100,
				projectionIdsToOptionMap: map[string]*projectorPb.RewardOption{
					"proj-1": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200},
					"proj-2": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 300},
					"proj-3": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 100},
					"proj-4": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -500},
				},
			},
			wantErr: false,
		},
		{
			name: "should return correct projections when all required info is fetched from projections DB (with pagination, overall capping, daily capping and negative projections) - 5",
			args: args{
				ctx: context.Background(),
				fact: &tiering.TieringPeriodicRewardFact{
					CommonFact: &common.CommonFact{
						ActionType: rewardsPb.CollectedDataType_TIERING_PERIODIC_REWARD_EVENT,
						ActionTime: timestampPb.New(date3),
						ActorId:    "act-1",
					},
					TieringPeriodicRewardEvent: &datacollectorPb.TieringPeriodicRewardEvent{
						ActorId:  "act-1",
						FromTime: timestampPb.New(date2),
						ToTime:   timestampPb.New(date3),
					},
				},
				rewardConfigOption: &rewardOffersPb.RewardConfigOption{
					RewardType:                    rewardsPb.RewardType_FI_COINS,
					ShouldGenerateFromProjections: true,
					RewardUnitsUpperLimit:         5000,
				},
			},
			setupMocks: func(mockedProjectionDao *mock_dao.MockRewardsProjectionDao, mockedFfAccountingClient *ffAccountsMocks.MockAccountingClient) {
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							ActorId:   "act-1",
							FromTime:  date2,
							UptoTime:  date3,
						},
					},
					nil,
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-1", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 2500}}}, RefId: "ext-txn-id-1", ActionTime: timestampPb.New(time.Date(2024, 01, 01, 1, 0, 0, 0, datetime.UTC))},
					{Id: "proj-2", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 500}}}, RefId: "ext-txn-id-2", ActionTime: timestampPb.New(time.Date(2024, 01, 01, 2, 0, 0, 0, datetime.UTC))},
				}, &rpc.PageContextResponse{HasAfter: true, AfterToken: "ewogICJUaW1lc3RhbXAiOiB7IlNlY29uZHMiOjEyMzR9LAogICJPZmZzZXQiOiAyLAogICJJc1JldmVyc2UiOiBmYWxzZQp9"}, nil)

				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							ActorId:   "act-1",
							FromTime:  date2,
							UptoTime:  date3,
						},
					},
					&pagination.PageToken{
						Timestamp: &timestampPb.Timestamp{Seconds: 1234},
						Offset:    2,
					},
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-3", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 1500}}}, RefId: "ext-txn-id-3", ActionTime: timestampPb.New(time.Date(2024, 01, 01, 3, 0, 0, 0, datetime.UTC))},
					{Id: "proj-4", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 3000}}}, RefId: "ext-txn-id-4", ActionTime: timestampPb.New(time.Date(2024, 01, 01, 4, 0, 0, 0, datetime.UTC))},
				}, nil, nil)
			},
			want: &RewardUnitsInfo{
				rewardUnits: 4500,
				projectionIdsToOptionMap: map[string]*projectorPb.RewardOption{
					"proj-1": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 2500},
					"proj-2": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 500},
					"proj-3": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 1500},
					"proj-4": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 0},
				},
			},
			wantErr: false,
		},
		{
			name: "should return error when all required info is fetched from projections DB (with pagination, capping, and negative projections), and a net negative amount is computed",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_BILLING,
						ActionTime: timestampPb.New(date1),
						ActorId:    "act-1",
					},
					CreditCardBillingEvent: &events.CreditCardBillGenerationEvent{
						ActorId: "act-1",
						BillId:  "bill-1",
						// AccountId: "acc-1",
					},
				},
				rewardConfigOption: &rewardOffersPb.RewardConfigOption{
					RewardType:                    rewardsPb.RewardType_FI_COINS,
					ShouldGenerateFromProjections: true,
					RewardUnitsUpperLimit:         200,
				},
			},
			setupMocks: func(mockedProjectionDao *mock_dao.MockRewardsProjectionDao, mockedFfAccountingClient *ffAccountsMocks.MockAccountingClient) {
				mockedFfAccountingClient.EXPECT().GetTransactionIdsByBillId(context.Background(), &accounting.GetTransactionIdsByBillIdRequest{
					BillId: "bill-1",
				}).Return(&accounting.GetTransactionIdsByBillIdResponse{
					Status: rpc.StatusOk(),
					TransactionIdResponses: []*accounting.GetTransactionIdsByBillIdResponse_TransactionIdResponse{
						{ExternalTxnId: "ext-txn-id-1"},
						{ExternalTxnId: "ext-txn-id-2"},
						{ExternalTxnId: "ext-txn-id-3"},
						{ExternalTxnId: "ext-txn-id-4"},
					},
				}, nil)
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					nil,
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-1", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}}, RefId: "ext-txn-id-1"},
					{Id: "proj-2", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 100}}}, RefId: "ext-txn-id-2"},
				}, &rpc.PageContextResponse{HasAfter: true, AfterToken: "ewogICJUaW1lc3RhbXAiOiB7IlNlY29uZHMiOjEyMzR9LAogICJPZmZzZXQiOiAyLAogICJJc1JldmVyc2UiOiBmYWxzZQp9"}, nil)

				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					&pagination.PageToken{
						Timestamp: &timestampPb.Timestamp{Seconds: 1234},
						Offset:    2,
					},
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-3", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 100}}}, RefId: "ext-txn-id-3"},
					{Id: "proj-4", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -500}}}, RefId: "ext-txn-id-4"},
				}, nil, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return correct projections when all required info is fetched from projections DB, capped at offerType level",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_BILLING,
						ActionTime: timestampPb.New(date1),
						ActorId:    "act-1",
					},
					CreditCardBillingEvent: &events.CreditCardBillGenerationEvent{
						ActorId: "act-1",
						BillId:  "bill-1",
					},
				},
				rewardConfigOption: &rewardOffersPb.RewardConfigOption{
					RewardType:                    rewardsPb.RewardType_FI_COINS,
					ShouldGenerateFromProjections: true,
					RewardUnitsUpperLimit:         300,
					RewardUnitsGeneratedFromProjectionUnitCaps: &rewardOffersPb.RewardUnitsGeneratedFromProjectionUnitCaps{
						OfferTypeToUnitCapsMap: map[string]uint32{
							rewardsPb.RewardOfferType_MASS_UNSECURED_CREDIT_CARD_BASE_OFFER.String(): 150,
						},
					},
				},
			},
			setupMocks: func(mockedProjectionDao *mock_dao.MockRewardsProjectionDao, mockedFfAccountingClient *ffAccountsMocks.MockAccountingClient) {
				mockedFfAccountingClient.EXPECT().GetTransactionIdsByBillId(context.Background(), &accounting.GetTransactionIdsByBillIdRequest{
					BillId: "bill-1",
				}).Return(&accounting.GetTransactionIdsByBillIdResponse{
					Status: rpc.StatusOk(),
					TransactionIdResponses: []*accounting.GetTransactionIdsByBillIdResponse_TransactionIdResponse{
						{ExternalTxnId: "ext-txn-id-1"},
						{ExternalTxnId: "ext-txn-id-2"},
						{ExternalTxnId: "ext-txn-id-3"},
						{ExternalTxnId: "ext-txn-id-4"},
					},
				}, nil)
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					nil,
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-1", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -100}}}, RefId: "ext-txn-id-1", OfferType: rewardsPb.RewardOfferType_MASS_UNSECURED_CREDIT_CARD_BASE_OFFER},
					{Id: "proj-2", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}}, RefId: "ext-txn-id-2", OfferType: rewardsPb.RewardOfferType_MASS_UNSECURED_CREDIT_CARD_BASE_OFFER},
				}, &rpc.PageContextResponse{HasAfter: true, AfterToken: "ewogICJUaW1lc3RhbXAiOiB7IlNlY29uZHMiOjEyMzR9LAogICJPZmZzZXQiOiAyLAogICJJc1JldmVyc2UiOiBmYWxzZQp9"}, nil)

				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					&pagination.PageToken{
						Timestamp: &timestampPb.Timestamp{Seconds: 1234},
						Offset:    2,
					},
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-3", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 300}}}, RefId: "ext-txn-id-3", OfferType: rewardsPb.RewardOfferType_MASS_UNSECURED_CREDIT_CARD_BASE_OFFER},
					{Id: "proj-4", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}}, RefId: "ext-txn-id-4", OfferType: rewardsPb.RewardOfferType_MASS_UNSECURED_CREDIT_CARD_BASE_OFFER},
				}, nil, nil)
			},
			want: &RewardUnitsInfo{
				rewardUnits: 150,
				projectionIdsToOptionMap: map[string]*projectorPb.RewardOption{
					"proj-1": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -100},
					"proj-2": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200},
					"proj-3": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 50},
					"proj-4": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 0},
				},
			},
			wantErr: false,
		},
		{
			name: "should return correct projections when all required info is fetched from projections DB, capped at actionType level",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_BILLING,
						ActionTime: timestampPb.New(date1),
						ActorId:    "act-1",
					},
					CreditCardBillingEvent: &events.CreditCardBillGenerationEvent{
						ActorId: "act-1",
						BillId:  "bill-1",
					},
				},
				rewardConfigOption: &rewardOffersPb.RewardConfigOption{
					RewardType:                    rewardsPb.RewardType_FI_COINS,
					ShouldGenerateFromProjections: true,
					RewardUnitsUpperLimit:         300,
					RewardUnitsGeneratedFromProjectionUnitCaps: &rewardOffersPb.RewardUnitsGeneratedFromProjectionUnitCaps{
						ActionTypeToUnitCapsMap: map[string]uint32{
							rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION.String(): 150,
						},
					},
				},
			},
			setupMocks: func(mockedProjectionDao *mock_dao.MockRewardsProjectionDao, mockedFfAccountingClient *ffAccountsMocks.MockAccountingClient) {
				mockedFfAccountingClient.EXPECT().GetTransactionIdsByBillId(context.Background(), &accounting.GetTransactionIdsByBillIdRequest{
					BillId: "bill-1",
				}).Return(&accounting.GetTransactionIdsByBillIdResponse{
					Status: rpc.StatusOk(),
					TransactionIdResponses: []*accounting.GetTransactionIdsByBillIdResponse_TransactionIdResponse{
						{ExternalTxnId: "ext-txn-id-1"},
						{ExternalTxnId: "ext-txn-id-2"},
						{ExternalTxnId: "ext-txn-id-3"},
						{ExternalTxnId: "ext-txn-id-4"},
					},
				}, nil)
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					nil,
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-1", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -100}}}, RefId: "ext-txn-id-1", ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION},
					{Id: "proj-2", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}}, RefId: "ext-txn-id-2", ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION},
				}, &rpc.PageContextResponse{HasAfter: true, AfterToken: "ewogICJUaW1lc3RhbXAiOiB7IlNlY29uZHMiOjEyMzR9LAogICJPZmZzZXQiOiAyLAogICJJc1JldmVyc2UiOiBmYWxzZQp9"}, nil)

				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					&pagination.PageToken{
						Timestamp: &timestampPb.Timestamp{Seconds: 1234},
						Offset:    2,
					},
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-3", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 300}}}, RefId: "ext-txn-id-3", ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION},
					{Id: "proj-4", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}}, RefId: "ext-txn-id-4", ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION},
				}, nil, nil)
			},
			want: &RewardUnitsInfo{
				rewardUnits: 150,
				projectionIdsToOptionMap: map[string]*projectorPb.RewardOption{
					"proj-1": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -100},
					"proj-2": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200},
					"proj-3": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 50},
					"proj-4": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 0},
				},
			},
			wantErr: false,
		},
		{
			name: "should return correct projections when all required info is fetched from projections DB, capped at offerId level",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_BILLING,
						ActionTime: timestampPb.New(date1),
						ActorId:    "act-1",
					},
					CreditCardBillingEvent: &events.CreditCardBillGenerationEvent{
						ActorId: "act-1",
						BillId:  "bill-1",
					},
				},
				rewardConfigOption: &rewardOffersPb.RewardConfigOption{
					RewardType:                    rewardsPb.RewardType_FI_COINS,
					ShouldGenerateFromProjections: true,
					RewardUnitsUpperLimit:         300,
					RewardUnitsGeneratedFromProjectionUnitCaps: &rewardOffersPb.RewardUnitsGeneratedFromProjectionUnitCaps{
						OfferIdToUnitCapsMap: map[string]uint32{
							"offer-1": 150,
						},
					},
				},
			},
			setupMocks: func(mockedProjectionDao *mock_dao.MockRewardsProjectionDao, mockedFfAccountingClient *ffAccountsMocks.MockAccountingClient) {
				mockedFfAccountingClient.EXPECT().GetTransactionIdsByBillId(context.Background(), &accounting.GetTransactionIdsByBillIdRequest{
					BillId: "bill-1",
				}).Return(&accounting.GetTransactionIdsByBillIdResponse{
					Status: rpc.StatusOk(),
					TransactionIdResponses: []*accounting.GetTransactionIdsByBillIdResponse_TransactionIdResponse{
						{ExternalTxnId: "ext-txn-id-1"},
						{ExternalTxnId: "ext-txn-id-2"},
						{ExternalTxnId: "ext-txn-id-3"},
						{ExternalTxnId: "ext-txn-id-4"},
					},
				}, nil)
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					nil,
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-1", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -100}}}, RefId: "ext-txn-id-1", ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION, OfferId: "offer-1"},
					{Id: "proj-2", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}}, RefId: "ext-txn-id-2", ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION, OfferId: "offer-1"},
				}, &rpc.PageContextResponse{HasAfter: true, AfterToken: "ewogICJUaW1lc3RhbXAiOiB7IlNlY29uZHMiOjEyMzR9LAogICJPZmZzZXQiOiAyLAogICJJc1JldmVyc2UiOiBmYWxzZQp9"}, nil)

				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					&pagination.PageToken{
						Timestamp: &timestampPb.Timestamp{Seconds: 1234},
						Offset:    2,
					},
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-3", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 300}}}, RefId: "ext-txn-id-3", ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION, OfferId: "offer-1"},
					{Id: "proj-4", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}}, RefId: "ext-txn-id-4", ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION, OfferId: "offer-1"},
				}, nil, nil)
			},
			want: &RewardUnitsInfo{
				rewardUnits: 150,
				projectionIdsToOptionMap: map[string]*projectorPb.RewardOption{
					"proj-1": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -100},
					"proj-2": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200},
					"proj-3": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 50},
					"proj-4": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 0},
				},
			},
			wantErr: false,
		},
		{
			name: "should return correct projections when all required info is fetched from projections DB, capped at offerId and offerType level",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_BILLING,
						ActionTime: timestampPb.New(date1),
						ActorId:    "act-1",
					},
					CreditCardBillingEvent: &events.CreditCardBillGenerationEvent{
						ActorId: "act-1",
						BillId:  "bill-1",
					},
				},
				rewardConfigOption: &rewardOffersPb.RewardConfigOption{
					RewardType:                    rewardsPb.RewardType_FI_COINS,
					ShouldGenerateFromProjections: true,
					RewardUnitsUpperLimit:         300,
					RewardUnitsGeneratedFromProjectionUnitCaps: &rewardOffersPb.RewardUnitsGeneratedFromProjectionUnitCaps{
						OfferIdToUnitCapsMap: map[string]uint32{
							"offer-1": 150,
						},
						OfferTypeToUnitCapsMap: map[string]uint32{
							rewardsPb.RewardOfferType_MASS_UNSECURED_CREDIT_CARD_BASE_OFFER.String(): 130,
						},
					},
				},
			},
			setupMocks: func(mockedProjectionDao *mock_dao.MockRewardsProjectionDao, mockedFfAccountingClient *ffAccountsMocks.MockAccountingClient) {
				mockedFfAccountingClient.EXPECT().GetTransactionIdsByBillId(context.Background(), &accounting.GetTransactionIdsByBillIdRequest{
					BillId: "bill-1",
				}).Return(&accounting.GetTransactionIdsByBillIdResponse{
					Status: rpc.StatusOk(),
					TransactionIdResponses: []*accounting.GetTransactionIdsByBillIdResponse_TransactionIdResponse{
						{ExternalTxnId: "ext-txn-id-1"},
						{ExternalTxnId: "ext-txn-id-2"},
						{ExternalTxnId: "ext-txn-id-3"},
						{ExternalTxnId: "ext-txn-id-4"},
					},
				}, nil)
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					nil,
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-1", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -100}}}, RefId: "ext-txn-id-1", ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION, OfferId: "offer-1", OfferType: rewardsPb.RewardOfferType_MASS_UNSECURED_CREDIT_CARD_BASE_OFFER},
					{Id: "proj-2", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}}, RefId: "ext-txn-id-2", ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION, OfferId: "offer-1", OfferType: rewardsPb.RewardOfferType_MASS_UNSECURED_CREDIT_CARD_BASE_OFFER},
				}, &rpc.PageContextResponse{HasAfter: true, AfterToken: "ewogICJUaW1lc3RhbXAiOiB7IlNlY29uZHMiOjEyMzR9LAogICJPZmZzZXQiOiAyLAogICJJc1JldmVyc2UiOiBmYWxzZQp9"}, nil)

				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					&pagination.PageToken{
						Timestamp: &timestampPb.Timestamp{Seconds: 1234},
						Offset:    2,
					},
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-3", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 300}}}, RefId: "ext-txn-id-3", ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION, OfferId: "offer-1", OfferType: rewardsPb.RewardOfferType_MASS_UNSECURED_CREDIT_CARD_BASE_OFFER},
					{Id: "proj-4", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}}, RefId: "ext-txn-id-4", ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION, OfferId: "offer-1", OfferType: rewardsPb.RewardOfferType_MASS_UNSECURED_CREDIT_CARD_BASE_OFFER},
				}, nil, nil)
			},
			want: &RewardUnitsInfo{
				rewardUnits: 130,
				projectionIdsToOptionMap: map[string]*projectorPb.RewardOption{
					"proj-1": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -100},
					"proj-2": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200},
					"proj-3": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 30},
					"proj-4": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 0},
				},
			},
			wantErr: false,
		},
		{
			name: "should return correct projections when all required info is fetched from projections DB, capped at offerId, offerType, and actionType level",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_BILLING,
						ActionTime: timestampPb.New(date1),
						ActorId:    "act-1",
					},
					CreditCardBillingEvent: &events.CreditCardBillGenerationEvent{
						ActorId: "act-1",
						BillId:  "bill-1",
					},
				},
				rewardConfigOption: &rewardOffersPb.RewardConfigOption{
					RewardType:                    rewardsPb.RewardType_FI_COINS,
					ShouldGenerateFromProjections: true,
					RewardUnitsUpperLimit:         300,
					RewardUnitsGeneratedFromProjectionUnitCaps: &rewardOffersPb.RewardUnitsGeneratedFromProjectionUnitCaps{
						OfferIdToUnitCapsMap: map[string]uint32{
							"offer-1": 150,
						},
						OfferTypeToUnitCapsMap: map[string]uint32{
							rewardsPb.RewardOfferType_MASS_UNSECURED_CREDIT_CARD_BASE_OFFER.String(): 130,
						},
						ActionTypeToUnitCapsMap: map[string]uint32{
							rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION.String(): 110,
						},
					},
				},
			},
			setupMocks: func(mockedProjectionDao *mock_dao.MockRewardsProjectionDao, mockedFfAccountingClient *ffAccountsMocks.MockAccountingClient) {
				mockedFfAccountingClient.EXPECT().GetTransactionIdsByBillId(context.Background(), &accounting.GetTransactionIdsByBillIdRequest{
					BillId: "bill-1",
				}).Return(&accounting.GetTransactionIdsByBillIdResponse{
					Status: rpc.StatusOk(),
					TransactionIdResponses: []*accounting.GetTransactionIdsByBillIdResponse_TransactionIdResponse{
						{ExternalTxnId: "ext-txn-id-1"},
						{ExternalTxnId: "ext-txn-id-2"},
						{ExternalTxnId: "ext-txn-id-3"},
						{ExternalTxnId: "ext-txn-id-4"},
					},
				}, nil)
				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					nil,
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-1", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -100}}}, RefId: "ext-txn-id-1", ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION, OfferId: "offer-1", OfferType: rewardsPb.RewardOfferType_MASS_UNSECURED_CREDIT_CARD_BASE_OFFER},
					{Id: "proj-2", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}}, RefId: "ext-txn-id-2", ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION, OfferId: "offer-1", OfferType: rewardsPb.RewardOfferType_MASS_UNSECURED_CREDIT_CARD_BASE_OFFER},
				}, &rpc.PageContextResponse{HasAfter: true, AfterToken: "ewogICJUaW1lc3RhbXAiOiB7IlNlY29uZHMiOjEyMzR9LAogICJPZmZzZXQiOiAyLAogICJJc1JldmVyc2UiOiBmYWxzZQp9"}, nil)

				mockedProjectionDao.EXPECT().FetchPaginatedProjectionsByFilters(
					context.Background(),
					&model2.QueryProjectionsFilter{
						AndFilter: &model2.AndProjectionFilter{
							RewardIds: []string{""},
							RefIds: []string{
								"ext-txn-id-1",
								"ext-txn-id-2",
								"ext-txn-id-3",
								"ext-txn-id-4",
							},
							ActorId: "act-1",
							// AccountId: "acc-1",
							UptoTime: date1,
						},
					},
					&pagination.PageToken{
						Timestamp: &timestampPb.Timestamp{Seconds: 1234},
						Offset:    2,
					},
					30,
					projectionQueryFieldMask,
				).Return([]*projectorPb.Projection{
					{Id: "proj-3", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 300}}}, RefId: "ext-txn-id-3", ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION, OfferId: "offer-1", OfferType: rewardsPb.RewardOfferType_MASS_UNSECURED_CREDIT_CARD_BASE_OFFER},
					{Id: "proj-4", ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}}, RefId: "ext-txn-id-4", ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION, OfferId: "offer-1", OfferType: rewardsPb.RewardOfferType_MASS_UNSECURED_CREDIT_CARD_BASE_OFFER},
				}, nil, nil)
			},
			want: &RewardUnitsInfo{
				rewardUnits: 110,
				projectionIdsToOptionMap: map[string]*projectorPb.RewardOption{
					"proj-1": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -100},
					"proj-2": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200},
					"proj-3": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 10},
					"proj-4": {RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 0},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockedProjectionDao := mock_dao.NewMockRewardsProjectionDao(ctrl)
			mockedAccountingClient := ffAccountsMocks.NewMockAccountingClient(ctrl)

			tt.setupMocks(mockedProjectionDao, mockedAccountingClient)

			re := &CustomRuleEngine{
				conf:           conf,
				dynConf:        dynConf,
				projectionsDao: mockedProjectionDao,
				ffAccClient:    mockedAccountingClient,
			}
			got, err := re.calculateRewardUnitsUsingProjections(tt.args.ctx, tt.args.fact, tt.args.rewardConfigOption)
			if (err != nil) != tt.wantErr {
				t.Errorf("calculateRewardUnitsUsingProjections() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("calculateRewardUnitsUsingProjections() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCustomRuleEngine_calculateAdjustmentRewardUnitsUsingProjections(t *testing.T) {
	unActualisedProjections := []*projectorPb.Projection{
		{
			Id:               "proj-1",
			ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 300}}},
			RefId:            "ext-txn-id-1",
			OfferId:          "offer-1",
		},
		{
			Id:               "proj-2",
			ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}},
			RefId:            "ext-txn-id-2",
			OfferId:          "offer-1",
		},
		{
			Id:               "proj-5",
			ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}},
			RefId:            "ext-txn-id-5",
			OfferId:          "offer-1",
		},
		{
			Id:               "negative-proj-1",
			ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: -200}}},
			RefId:            "ext-txn-id-6",
			OfferId:          "offer-1",
		},
	}

	actualisedProjections := []*projectorPb.Projection{
		{
			Id:               "proj-3",
			ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 300}}},
			RefId:            "ext-txn-id-3",
			OfferId:          "offer-1",
			RewardId:         "reward-1",
		},
		{
			Id:               "proj-4",
			ProjectedOptions: &projectorPb.OptionsInfo{RewardUnitsWithTypes: []*projectorPb.RewardOption{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200}}},
			RefId:            "ext-txn-id-4",
			OfferId:          "offer-1",
			RewardId:         "reward-1",
		},
	}

	type mockStruct struct {
		mockRewardsDao *daoMocks.MockRewardsDao
	}

	type args struct {
		ctx                   context.Context
		fact                  common.IFact
		calculatedRewardUnits float64
		allProjections        []*projectorPb.Projection
		rewardConfigOption    *rewardOfferPb.RewardConfigOption
	}
	tests := []struct {
		name      string
		args      args
		setupMock func(m *mockStruct)
		want      *RewardUnitsInfo
		wantErr   bool
	}{
		{
			name: "should successfully generate rewardUnitsInfo for adjustment reward",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActorId: "actor-1",
						RefId:   "ref-1",
						RewardOffer: &rewardOffersPb.RewardOffer{
							Id: "reward-offer-1",
						},
					},
				},
				calculatedRewardUnits: 400,
				allProjections:        append(unActualisedProjections, actualisedProjections...),
				rewardConfigOption: &rewardOffersPb.RewardConfigOption{
					RewardType: rewardsPb.RewardType_FI_COINS,
				},
			},
			setupMock: func(m *mockStruct) {
				m.mockRewardsDao.EXPECT().FetchPaginatedRewardsByFiltersV2(context.Background(), "actor-1", &model.QueryRewardsFilterV2{
					AndFilter: &model.AndRewardsFilter{
						RefIds:        []string{"ref-1"},
						RewardOfferId: "reward-offer-1",
					},
				}, nil, 20).Return([]*model.Reward{
					{
						Id:             "reward-1",
						SecondaryRefId: "NA",
						OfferId:        "reward-offer-1",
						RefId:          "ref-1",
						RewardOptions: &rewardsPb.RewardOptions{
							Options: []*rewardsPb.RewardOption{
								{
									Id:         "1",
									RewardType: rewardsPb.RewardType_FI_COINS,
									Option: &rewardsPb.RewardOption_FiCoins{
										FiCoins: &rewardsPb.FiCoins{
											Units: 50,
										},
									},
								},
								{
									Id:         "2",
									RewardType: rewardsPb.RewardType_CASH,
									Option: &rewardsPb.RewardOption_Cash{
										Cash: &rewardsPb.Cash{
											Amount: &money.Money{
												CurrencyCode: "INR",
												Units:        5,
											},
										},
									},
								},
							},
						},
					},
					{
						Id:             "reward-2",
						SecondaryRefId: "ADJUSTMENT_REWARD_1",
						OfferId:        "reward-offer-1",
						RefId:          "ref-1",
						RewardOptions: &rewardsPb.RewardOptions{
							Options: []*rewardsPb.RewardOption{
								{
									Id:         "1",
									RewardType: rewardsPb.RewardType_FI_COINS,
									Option: &rewardsPb.RewardOption_FiCoins{
										FiCoins: &rewardsPb.FiCoins{
											Units: 10,
										},
									},
								},
								{
									Id:         "2",
									RewardType: rewardsPb.RewardType_CASH,
									Option: &rewardsPb.RewardOption_Cash{
										Cash: &rewardsPb.Cash{
											Amount: &money.Money{
												CurrencyCode: "INR",
												Units:        5,
											},
										},
									},
								},
							},
						},
					},
				}, nil, nil)
			},
			want: &RewardUnitsInfo{
				rewardUnits: 340,
				projectionIdsToOptionMap: map[string]*projectorPb.RewardOption{
					"proj-1": {
						RewardType:  rewardsPb.RewardType_FI_COINS,
						RewardUnits: 300,
					},
					"proj-2": {
						RewardType:  rewardsPb.RewardType_FI_COINS,
						RewardUnits: 40,
					},
					"proj-5": {
						RewardType:  rewardsPb.RewardType_FI_COINS,
						RewardUnits: 0,
					},
					"negative-proj-1": {
						RewardType:  rewardsPb.RewardType_FI_COINS,
						RewardUnits: -200,
					},
				},
				adjustmentRewardOptionDetails: &AdjustmentRewardOptionDetails{
					isAdjustmentReward:             true,
					isOriginalRewardOptionChosen:   false,
					adjustmentRewardSecondaryRefId: "ADJUSTMENT_REWARD_2",
				},
			},
			wantErr: false,
		},
		{
			name: "should return error where more than one original reward present",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActorId: "actor-1",
						RefId:   "ref-1",
						RewardOffer: &rewardOffersPb.RewardOffer{
							Id: "reward-offer-1",
						},
					},
				},
				calculatedRewardUnits: 400,
				allProjections:        append(unActualisedProjections, actualisedProjections...),
				rewardConfigOption: &rewardOffersPb.RewardConfigOption{
					RewardType: rewardsPb.RewardType_FI_COINS,
				},
			},
			setupMock: func(m *mockStruct) {
				m.mockRewardsDao.EXPECT().FetchPaginatedRewardsByFiltersV2(context.Background(), "actor-1", &model.QueryRewardsFilterV2{
					AndFilter: &model.AndRewardsFilter{
						RefIds:        []string{"ref-1"},
						RewardOfferId: "reward-offer-1",
					},
				}, nil, 20).Return([]*model.Reward{
					{
						Id:             "reward-1",
						SecondaryRefId: "NA",
						OfferId:        "reward-offer-1",
						RefId:          "ref-1",
						RewardOptions: &rewardsPb.RewardOptions{
							Options: []*rewardsPb.RewardOption{
								{
									Id:         "1",
									RewardType: rewardsPb.RewardType_FI_COINS,
									Option: &rewardsPb.RewardOption_FiCoins{
										FiCoins: &rewardsPb.FiCoins{
											Units: 50,
										},
									},
								},
								{
									Id:         "2",
									RewardType: rewardsPb.RewardType_CASH,
									Option: &rewardsPb.RewardOption_Cash{
										Cash: &rewardsPb.Cash{
											Amount: &money.Money{
												CurrencyCode: "INR",
												Units:        5,
											},
										},
									},
								},
							},
						},
					},
					{
						Id:             "reward-2",
						SecondaryRefId: "NA",
						OfferId:        "reward-offer-1",
						RefId:          "ref-1",
						RewardOptions: &rewardsPb.RewardOptions{
							Options: []*rewardsPb.RewardOption{
								{
									Id:         "1",
									RewardType: rewardsPb.RewardType_FI_COINS,
									Option: &rewardsPb.RewardOption_FiCoins{
										FiCoins: &rewardsPb.FiCoins{
											Units: 10,
										},
									},
								},
								{
									Id:         "2",
									RewardType: rewardsPb.RewardType_CASH,
									Option: &rewardsPb.RewardOption_Cash{
										Cash: &rewardsPb.Cash{
											Amount: &money.Money{
												CurrencyCode: "INR",
												Units:        5,
											},
										},
									},
								},
							},
						},
					},
				}, nil, nil)
			},
			wantErr: true,
		},
		{
			name: "should not generate adjustment reward when there is no un-actualised projections",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActorId: "actor-1",
						RefId:   "ref-1",
						RewardOffer: &rewardOffersPb.RewardOffer{
							Id: "reward-offer-1",
						},
					},
				},
				calculatedRewardUnits: 400,
				allProjections:        actualisedProjections,
				rewardConfigOption: &rewardOffersPb.RewardConfigOption{
					RewardType: rewardsPb.RewardType_FI_COINS,
				},
			},
			setupMock: func(m *mockStruct) {
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "should not generate adjustment reward when all projections are un-actualised",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActorId: "actor-1",
						RefId:   "ref-1",
						RewardOffer: &rewardOffersPb.RewardOffer{
							Id: "reward-offer-1",
						},
					},
				},
				calculatedRewardUnits: 400,
				allProjections:        unActualisedProjections,
				rewardConfigOption: &rewardOffersPb.RewardConfigOption{
					RewardType: rewardsPb.RewardType_FI_COINS,
				},
			},
			setupMock: func(m *mockStruct) {
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "should return error when fetch reward dao call failed",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActorId: "actor-1",
						RefId:   "ref-1",
						RewardOffer: &rewardOffersPb.RewardOffer{
							Id: "reward-offer-1",
						},
					},
				},
				calculatedRewardUnits: 400,
				allProjections:        append(unActualisedProjections, actualisedProjections...),
				rewardConfigOption: &rewardOffersPb.RewardConfigOption{
					RewardType: rewardsPb.RewardType_FI_COINS,
				},
			},
			setupMock: func(m *mockStruct) {
				m.mockRewardsDao.EXPECT().FetchPaginatedRewardsByFiltersV2(context.Background(), "actor-1", &model.QueryRewardsFilterV2{
					AndFilter: &model.AndRewardsFilter{
						RefIds:        []string{"ref-1"},
						RewardOfferId: "reward-offer-1",
					},
				}, nil, 20).Return(
					nil, nil, errors.New("err"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should return error when there are more than 20 rewards present for a (actor_id, offer_id, ref_id) combo",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActorId: "actor-1",
						RefId:   "ref-1",
						RewardOffer: &rewardOffersPb.RewardOffer{
							Id: "reward-offer-1",
						},
					},
				},
				calculatedRewardUnits: 400,
				allProjections:        append(unActualisedProjections, actualisedProjections...),
				rewardConfigOption: &rewardOffersPb.RewardConfigOption{
					RewardType: rewardsPb.RewardType_FI_COINS,
				},
			},
			setupMock: func(m *mockStruct) {
				m.mockRewardsDao.EXPECT().FetchPaginatedRewardsByFiltersV2(context.Background(), "actor-1", &model.QueryRewardsFilterV2{
					AndFilter: &model.AndRewardsFilter{
						RefIds:        []string{"ref-1"},
						RewardOfferId: "reward-offer-1",
					},
				}, nil, 20).Return(
					[]*model.Reward{
						{},
					}, &rpc.PageContextResponse{
						HasAfter: true,
					}, nil)
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "should not generate adjustment reward when original reward is not generated yet",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActorId: "actor-1",
						RefId:   "ref-1",
						RewardOffer: &rewardOffersPb.RewardOffer{
							Id: "reward-offer-1",
						},
					},
				},
				calculatedRewardUnits: 400,
				allProjections:        append(unActualisedProjections, actualisedProjections...),
				rewardConfigOption: &rewardOffersPb.RewardConfigOption{
					RewardType: rewardsPb.RewardType_FI_COINS,
				},
			},
			setupMock: func(m *mockStruct) {
				m.mockRewardsDao.EXPECT().FetchPaginatedRewardsByFiltersV2(context.Background(), "actor-1", &model.QueryRewardsFilterV2{
					AndFilter: &model.AndRewardsFilter{
						RefIds:        []string{"ref-1"},
						RewardOfferId: "reward-offer-1",
					},
				}, nil, 20).Return(
					nil, nil, nil)
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "should not generate adjustment reward when total calculated reward unit against all projections is less or equal to already given reward units",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActorId: "actor-1",
						RefId:   "ref-1",
						RewardOffer: &rewardOffersPb.RewardOffer{
							Id: "reward-offer-1",
						},
					},
				},
				calculatedRewardUnits: 400,
				allProjections:        append(unActualisedProjections, actualisedProjections...),
				rewardConfigOption: &rewardOffersPb.RewardConfigOption{
					RewardType: rewardsPb.RewardType_FI_COINS,
				},
			},
			setupMock: func(m *mockStruct) {
				m.mockRewardsDao.EXPECT().FetchPaginatedRewardsByFiltersV2(context.Background(), "actor-1", &model.QueryRewardsFilterV2{
					AndFilter: &model.AndRewardsFilter{
						RefIds:        []string{"ref-1"},
						RewardOfferId: "reward-offer-1",
					},
				}, nil, 20).Return([]*model.Reward{
					{
						Id:             "reward-1",
						SecondaryRefId: "NA",
						OfferId:        "reward-offer-1",
						RefId:          "ref-1",
						RewardOptions: &rewardsPb.RewardOptions{
							Options: []*rewardsPb.RewardOption{
								{
									Id:         "1",
									RewardType: rewardsPb.RewardType_FI_COINS,
									Option: &rewardsPb.RewardOption_FiCoins{
										FiCoins: &rewardsPb.FiCoins{
											Units: 200,
										},
									},
								},
								{
									Id:         "2",
									RewardType: rewardsPb.RewardType_CASH,
									Option: &rewardsPb.RewardOption_Cash{
										Cash: &rewardsPb.Cash{
											Amount: &money.Money{
												CurrencyCode: "INR",
												Units:        5,
											},
										},
									},
								},
							},
						},
					},
					{
						Id:             "reward-2",
						SecondaryRefId: "ADJUSTMENT_REWARD_1",
						OfferId:        "reward-offer-1",
						RefId:          "ref-1",
						RewardOptions: &rewardsPb.RewardOptions{
							Options: []*rewardsPb.RewardOption{
								{
									Id:         "1",
									RewardType: rewardsPb.RewardType_FI_COINS,
									Option: &rewardsPb.RewardOption_FiCoins{
										FiCoins: &rewardsPb.FiCoins{
											Units: 200,
										},
									},
								},
								{
									Id:         "2",
									RewardType: rewardsPb.RewardType_CASH,
									Option: &rewardsPb.RewardOption_Cash{
										Cash: &rewardsPb.Cash{
											Amount: &money.Money{
												CurrencyCode: "INR",
												Units:        5,
											},
										},
									},
								},
							},
						},
					},
				}, nil, nil)
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "should not generate adjustment reward when original reward chosen reward type is not same as passed reward type in input",
			args: args{
				ctx: context.Background(),
				fact: &creditcardbilling.CcBillingFact{
					CommonFact: &common.CommonFact{
						ActorId: "actor-1",
						RefId:   "ref-1",
						RewardOffer: &rewardOffersPb.RewardOffer{
							Id: "reward-offer-1",
						},
					},
				},
				calculatedRewardUnits: 400,
				allProjections:        append(unActualisedProjections, actualisedProjections...),
				rewardConfigOption: &rewardOffersPb.RewardConfigOption{
					RewardType: rewardsPb.RewardType_FI_COINS,
				},
			},
			setupMock: func(m *mockStruct) {
				m.mockRewardsDao.EXPECT().FetchPaginatedRewardsByFiltersV2(context.Background(), "actor-1", &model.QueryRewardsFilterV2{
					AndFilter: &model.AndRewardsFilter{
						RefIds:        []string{"ref-1"},
						RewardOfferId: "reward-offer-1",
					},
				}, nil, 20).Return([]*model.Reward{
					{
						Id:             "reward-2",
						SecondaryRefId: "ADJUSTMENT_REWARD_1",
						OfferId:        "reward-offer-1",
						RefId:          "ref-1",
						RewardOptions: &rewardsPb.RewardOptions{
							Options: []*rewardsPb.RewardOption{
								{
									Id:         "1",
									RewardType: rewardsPb.RewardType_FI_COINS,
									Option: &rewardsPb.RewardOption_FiCoins{
										FiCoins: &rewardsPb.FiCoins{
											Units: 200,
										},
									},
								},
								{
									Id:         "2",
									RewardType: rewardsPb.RewardType_CASH,
									Option: &rewardsPb.RewardOption_Cash{
										Cash: &rewardsPb.Cash{
											Amount: &money.Money{
												CurrencyCode: "INR",
												Units:        5,
											},
										},
									},
								},
							},
						},
					},
					{
						Id:             "reward-1",
						SecondaryRefId: "NA",
						OfferId:        "reward-offer-1",
						RefId:          "ref-1",
						ChosenReward: &rewardsPb.RewardOption{
							Id:         "2",
							RewardType: rewardsPb.RewardType_CASH,
							Option: &rewardsPb.RewardOption_Cash{
								Cash: &rewardsPb.Cash{
									Amount: &money.Money{
										CurrencyCode: "INR",
										Units:        5,
									},
								},
							},
						},
						RewardOptions: &rewardsPb.RewardOptions{
							Options: []*rewardsPb.RewardOption{
								{
									Id:         "1",
									RewardType: rewardsPb.RewardType_FI_COINS,
									Option: &rewardsPb.RewardOption_FiCoins{
										FiCoins: &rewardsPb.FiCoins{
											Units: 200,
										},
									},
								},
								{
									Id:         "2",
									RewardType: rewardsPb.RewardType_CASH,
									Option: &rewardsPb.RewardOption_Cash{
										Cash: &rewardsPb.Cash{
											Amount: &money.Money{
												CurrencyCode: "INR",
												Units:        5,
											},
										},
									},
								},
							},
						},
					},
				}, nil, nil)
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockRewardsDao := daoMocks.NewMockRewardsDao(ctrl)

			m := &mockStruct{
				mockRewardsDao: mockRewardsDao,
			}

			tt.setupMock(m)

			re := &CustomRuleEngine{
				conf:       conf,
				dynConf:    dynConf,
				rewardsDao: mockRewardsDao,
			}
			got, err := re.calculateAdjustmentRewardUnitsUsingProjections(tt.args.ctx, tt.args.fact, tt.args.calculatedRewardUnits, tt.args.allProjections, tt.args.rewardConfigOption)
			fmt.Println("err:", err)
			if (err != nil) != tt.wantErr {
				t.Errorf("calculateAdjustmentRewardUnitsUsingProjections() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !areRewardUnitsInfoEqual(got, tt.want) {
				logger.DebugNoCtx("calculateAdjustmentRewardUnitsUsingProjections", zap.Any("got", got), zap.Any("want", tt.want))
				t.Errorf("calculateAdjustmentRewardUnitsUsingProjections()\n got = %v\nwant = %v", got, tt.want)
			}
		})
	}
}

func areRewardUnitsInfoEqual(got, want *RewardUnitsInfo) bool {
	if got == nil && want == nil {
		return true
	}
	if got == nil || want == nil {
		return false
	}
	if !areProjectionIdToOptionMapEqual(got.projectionIdsToOptionMap, want.projectionIdsToOptionMap) {
		return false
	} else {
		got.projectionIdsToOptionMap = nil
		want.projectionIdsToOptionMap = nil
	}

	return reflect.DeepEqual(got, want)
}

func areProjectionIdToOptionMapEqual(got, want map[string]*projectorPb.RewardOption) bool {
	if got == nil && want == nil {
		return true
	}
	if got == nil || want == nil {
		return false
	}

	for key, val := range got {
		if wantVal, ok := want[key]; !ok {
			fmt.Println("key not found", key)
			return false
		} else if !proto.Equal(val, wantVal) {
			fmt.Println("val not equal", val, wantVal)
			return false
		}
	}

	return true
}

func TestCustomRuleEngine_capRewardUnitsAtOfferAndActorInTimePeriod(t *testing.T) {
	var (
		monthStartFromTime = datetime.StartOfMonth(time.Now().In(datetime.IST))
		monthEndTillTime   = *(datetime.AddNMonths(&monthStartFromTime, 1))
	)

	type args struct {
		ctx                  context.Context
		rewardUnits          float64
		rewardType           rewardsPb.RewardType
		actorId              string
		offerId              string
		fromTime             time.Time
		tillTime             time.Time
		rewardUnitsCapConfig *rewardOffersPb.RewardUnitsCapAggregate
	}

	type daoCall struct {
		isCalled      bool
		successResult []*rewardsPb.RewardOfferRewardUnitsActorUtilisationInTimePeriod
		err           error
	}

	tests := []struct {
		name                string
		args                args
		daoCall             daoCall
		want                float64
		wantErr             bool
		expectedErrorString string
	}{
		{
			name: "success: no cap config defined",
			args: args{
				ctx:         context.Background(),
				rewardUnits: 100,
				rewardType:  rewardsPb.RewardType_FI_COINS,
				actorId:     "actor_1",
				offerId:     "offer_1",
				fromTime:    monthStartFromTime,
				tillTime:    monthEndTillTime,
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{},
				},
			},
			want:    100,
			wantErr: false,
		},
		{
			name: "success: cap config defined but not for given reward type",
			args: args{
				ctx:         context.Background(),
				rewardUnits: 100,
				rewardType:  rewardsPb.RewardType_FI_COINS,
				actorId:     "actor_1",
				offerId:     "offer_1",
				fromTime:    monthStartFromTime,
				tillTime:    monthEndTillTime,
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_CASH,
							Units:      50,
						},
					},
				},
			},
			want:    100,
			wantErr: false,
		},
		{
			name: "success: cap config defined, no utilization record found",
			args: args{
				ctx:         context.Background(),
				rewardUnits: 100,
				rewardType:  rewardsPb.RewardType_FI_COINS,
				actorId:     "actor_1",
				offerId:     "offer_1",
				fromTime:    monthStartFromTime,
				tillTime:    monthEndTillTime,
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      150,
						},
					},
				},
			},
			daoCall: daoCall{
				isCalled:      true,
				successResult: []*rewardsPb.RewardOfferRewardUnitsActorUtilisationInTimePeriod{},
				err:           epifierrors.ErrRecordNotFound,
			},
			want:    100,
			wantErr: false,
		},
		{
			name: "success: cap config defined, utilization record found, cap not reached",
			args: args{
				ctx:         context.Background(),
				rewardUnits: 100,
				rewardType:  rewardsPb.RewardType_FI_COINS,
				actorId:     "actor_1",
				offerId:     "offer_1",
				fromTime:    monthStartFromTime,
				tillTime:    monthEndTillTime,
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      150,
						},
					},
				},
			},
			daoCall: daoCall{
				isCalled: true,
				successResult: []*rewardsPb.RewardOfferRewardUnitsActorUtilisationInTimePeriod{
					{
						FiCoinUnits: 20,
					},
				},
			},
			want:    100,
			wantErr: false,
		},
		{
			name: "success: cap config defined, utilization record found, cap reached partially",
			args: args{
				ctx:         context.Background(),
				rewardUnits: 100,
				rewardType:  rewardsPb.RewardType_FI_COINS,
				actorId:     "actor_1",
				offerId:     "offer_1",
				fromTime:    monthStartFromTime,
				tillTime:    monthEndTillTime,
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      150,
						},
					},
				},
			},
			daoCall: daoCall{
				isCalled: true,
				successResult: []*rewardsPb.RewardOfferRewardUnitsActorUtilisationInTimePeriod{
					{
						FiCoinUnits: 100,
					},
				},
			},
			want:    50,
			wantErr: false,
		},
		{
			name: "error: cap config defined, utilization record found, cap already reached",
			args: args{
				ctx:         context.Background(),
				rewardUnits: 100,
				rewardType:  rewardsPb.RewardType_FI_COINS,
				actorId:     "actor_1",
				offerId:     "offer_1",
				fromTime:    monthStartFromTime,
				tillTime:    monthEndTillTime,
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      150,
						},
					},
				},
			},
			daoCall: daoCall{
				isCalled: true,
				successResult: []*rewardsPb.RewardOfferRewardUnitsActorUtilisationInTimePeriod{
					{
						FiCoinUnits: 150,
					},
				},
			},
			want:                0,
			wantErr:             true,
			expectedErrorString: internalerrors.RewardUnitsMaxCapReached.Error(),
		},
		{
			name: "error: dao call failed",
			args: args{
				ctx:         context.Background(),
				rewardUnits: 100,
				rewardType:  rewardsPb.RewardType_FI_COINS,
				actorId:     "actor_1",
				offerId:     "offer_1",
				fromTime:    monthStartFromTime,
				tillTime:    monthEndTillTime,
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      150,
						},
					},
				},
			},
			daoCall: daoCall{
				isCalled: true,
				err:      errors.New("dao call failed"),
			},
			want:                0,
			wantErr:             true,
			expectedErrorString: "Error in fetching RewardOfferRewardUnitsCapForActor: dao call failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockRewardOfferDao := daoMocks.NewMockRewardOfferDao(ctrl)
			if tt.daoCall.isCalled {
				mockRewardOfferDao.EXPECT().
					GetOffersRewardUnitsUtilizedInTimePeriod(tt.args.ctx, tt.args.actorId, []string{tt.args.offerId}, tt.args.fromTime, tt.args.tillTime).
					Return(tt.daoCall.successResult, tt.daoCall.err)
			}

			re := &CustomRuleEngine{
				rewardOfferDao: mockRewardOfferDao,
			}

			got, err := re.capRewardUnitsAtOfferAndActorInTimePeriod(tt.args.ctx, tt.args.rewardUnits, tt.args.rewardType,
				tt.args.actorId, tt.args.offerId, tt.args.fromTime, tt.args.tillTime, tt.args.rewardUnitsCapConfig)

			if (err != nil) != tt.wantErr {
				t.Errorf("capRewardUnitsAtOfferAndActorInTimePeriod() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr && err.Error() != tt.expectedErrorString {
				t.Errorf("capRewardUnitsAtOfferAndActorInTimePeriod() error = %v, expectedErrorString %v", err, tt.expectedErrorString)
				return
			}

			if got != tt.want {
				t.Errorf("capRewardUnitsAtOfferAndActorInTimePeriod() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCustomRuleEngine_capRewardUnitsAtGroupAndActorInTimePeriod(t *testing.T) {
	var (
		monthStartFromTime = datetime.StartOfMonth(time.Now().In(datetime.IST))
		monthEndTillTime   = *(datetime.AddNMonths(&monthStartFromTime, 1))
	)

	type args struct {
		ctx                  context.Context
		rewardUnits          float64
		rewardType           rewardsPb.RewardType
		actorId              string
		offerGroupId         string
		fromTime             time.Time
		tillTime             time.Time
		rewardUnitsCapConfig *rewardOffersPb.RewardUnitsCapAggregate
	}

	type daoCall struct {
		isCalled      bool
		successResult []*rewardsPb.RewardOfferGroupRewardUnitsActorUtilisationInTimePeriod
		err           error
	}

	tests := []struct {
		name                string
		args                args
		daoCall             daoCall
		want                float64
		wantErr             bool
		expectedErrorString string
	}{
		{
			name: "success: no group id provided",
			args: args{
				ctx:         context.Background(),
				rewardUnits: 100,
				rewardType:  rewardsPb.RewardType_FI_COINS,
				actorId:     "actor_1",
				fromTime:    monthStartFromTime,
				tillTime:    monthEndTillTime,
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      150,
						},
					},
				},
			},
			want:    100,
			wantErr: false,
		},
		{
			name: "success: no cap config defined",
			args: args{
				ctx:          context.Background(),
				rewardUnits:  100,
				rewardType:   rewardsPb.RewardType_FI_COINS,
				actorId:      "actor_1",
				offerGroupId: "group_1",
				fromTime:     monthStartFromTime,
				tillTime:     monthEndTillTime,
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{},
				},
			},
			want:    100,
			wantErr: false,
		},
		{
			name: "success: cap config defined but not for given reward type",
			args: args{
				ctx:          context.Background(),
				rewardUnits:  100,
				rewardType:   rewardsPb.RewardType_FI_COINS,
				actorId:      "actor_1",
				offerGroupId: "group_1",
				fromTime:     monthStartFromTime,
				tillTime:     monthEndTillTime,
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_CASH,
							Units:      50,
						},
					},
				},
			},
			want:    100,
			wantErr: false,
		},
		{
			name: "success: cap config defined, no utilization record found",
			args: args{
				ctx:          context.Background(),
				rewardUnits:  100,
				rewardType:   rewardsPb.RewardType_FI_COINS,
				actorId:      "actor_1",
				offerGroupId: "group_1",
				fromTime:     monthStartFromTime,
				tillTime:     monthEndTillTime,
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      150,
						},
					},
				},
			},
			daoCall: daoCall{
				isCalled:      true,
				successResult: []*rewardsPb.RewardOfferGroupRewardUnitsActorUtilisationInTimePeriod{},
				err:           epifierrors.ErrRecordNotFound,
			},
			want:    100,
			wantErr: false,
		},
		{
			name: "success: cap config defined, utilization record found, cap not reached",
			args: args{
				ctx:          context.Background(),
				rewardUnits:  100,
				rewardType:   rewardsPb.RewardType_FI_COINS,
				actorId:      "actor_1",
				offerGroupId: "group_1",
				fromTime:     monthStartFromTime,
				tillTime:     monthEndTillTime,
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      150,
						},
					},
				},
			},
			daoCall: daoCall{
				isCalled: true,
				successResult: []*rewardsPb.RewardOfferGroupRewardUnitsActorUtilisationInTimePeriod{
					{
						FiCoinUnits: 20,
					},
				},
			},
			want:    100,
			wantErr: false,
		},
		{
			name: "success: cap config defined, utilization record found, cap reached partially",
			args: args{
				ctx:          context.Background(),
				rewardUnits:  100,
				rewardType:   rewardsPb.RewardType_FI_COINS,
				actorId:      "actor_1",
				offerGroupId: "group_1",
				fromTime:     monthStartFromTime,
				tillTime:     monthEndTillTime,
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      150,
						},
					},
				},
			},
			daoCall: daoCall{
				isCalled: true,
				successResult: []*rewardsPb.RewardOfferGroupRewardUnitsActorUtilisationInTimePeriod{
					{
						FiCoinUnits: 100,
					},
				},
			},
			want:    50,
			wantErr: false,
		},
		{
			name: "error: cap config defined, utilization record found, cap already reached",
			args: args{
				ctx:          context.Background(),
				rewardUnits:  100,
				rewardType:   rewardsPb.RewardType_FI_COINS,
				actorId:      "actor_1",
				offerGroupId: "group_1",
				fromTime:     monthStartFromTime,
				tillTime:     monthEndTillTime,
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      150,
						},
					},
				},
			},
			daoCall: daoCall{
				isCalled: true,
				successResult: []*rewardsPb.RewardOfferGroupRewardUnitsActorUtilisationInTimePeriod{
					{
						FiCoinUnits: 150,
					},
				},
			},
			want:                0,
			wantErr:             true,
			expectedErrorString: internalerrors.RewardUnitsMaxCapReached.Error(),
		},
		{
			name: "error: dao call failed",
			args: args{
				ctx:          context.Background(),
				rewardUnits:  100,
				rewardType:   rewardsPb.RewardType_FI_COINS,
				actorId:      "actor_1",
				offerGroupId: "group_1",
				fromTime:     monthStartFromTime,
				tillTime:     monthEndTillTime,
				rewardUnitsCapConfig: &rewardOffersPb.RewardUnitsCapAggregate{
					UnitsCaps: []*rewardOffersPb.RewardUnitsCapAggregate_RewardUnitsCap{
						{
							RewardType: rewardsPb.RewardType_FI_COINS,
							Units:      150,
						},
					},
				},
			},
			daoCall: daoCall{
				isCalled: true,
				err:      errors.New("dao call failed"),
			},
			want:                0,
			wantErr:             true,
			expectedErrorString: "Error in fetching RewardOfferGroupRewardUnitsCapForActor: dao call failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockOfferGroupDao := daoMocks.NewMockRewardOfferGroupDao(ctrl)
			if tt.daoCall.isCalled {
				mockOfferGroupDao.EXPECT().
					GetOfferGroupsRewardUnitsUtilizedInTimePeriod(tt.args.ctx, tt.args.actorId, []string{tt.args.offerGroupId}, tt.args.fromTime, tt.args.tillTime).
					Return(tt.daoCall.successResult, tt.daoCall.err)
			}

			re := &CustomRuleEngine{
				offerGroupDao: mockOfferGroupDao,
			}

			got, err := re.capRewardUnitsAtGroupAndActorInTimePeriod(tt.args.ctx, tt.args.rewardUnits, tt.args.rewardType,
				tt.args.actorId, tt.args.offerGroupId, tt.args.fromTime, tt.args.tillTime, tt.args.rewardUnitsCapConfig)

			if (err != nil) != tt.wantErr {
				t.Errorf("capRewardUnitsAtGroupAndActorInTimePeriod() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr && err.Error() != tt.expectedErrorString {
				t.Errorf("capRewardUnitsAtGroupAndActorInTimePeriod() error = %v, expectedErrorString %v", err, tt.expectedErrorString)
				return
			}

			if got != tt.want {
				t.Errorf("capRewardUnitsAtGroupAndActorInTimePeriod() got = %v, want %v", got, tt.want)
			}
		})
	}
}
