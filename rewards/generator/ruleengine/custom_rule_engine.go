package ruleengine

import (
	"context"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/Knetic/govaluate"
	"github.com/golang/protobuf/ptypes" // nolint:depguard
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"

	ffAccountsPb "github.com/epifi/gamma/api/firefly/accounting"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	luckydrawPb "github.com/epifi/gamma/api/rewards/luckydraw"
	projectorPb "github.com/epifi/gamma/api/rewards/projector"
	rewardOfferPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"

	accrualconf "github.com/epifi/gamma/accrual/config"
	rewardsPkg "github.com/epifi/gamma/pkg/rewards"
	"github.com/epifi/gamma/rewards/config"
	"github.com/epifi/gamma/rewards/config/genconf"
	"github.com/epifi/gamma/rewards/generator"
	"github.com/epifi/gamma/rewards/generator/dao"
	"github.com/epifi/gamma/rewards/generator/internalerrors"
	"github.com/epifi/gamma/rewards/generator/model"
	"github.com/epifi/gamma/rewards/generator/ruleengine/fact/common"
	"github.com/epifi/gamma/rewards/generator/ruleengine/fact/creditcardbilling"
	"github.com/epifi/gamma/rewards/generator/ruleengine/fact/tiering"
	"github.com/epifi/gamma/rewards/helper"
	projectorDao "github.com/epifi/gamma/rewards/projector/dao"
	model2 "github.com/epifi/gamma/rewards/projector/dao/model"
)

var (
	RewardUnitsKeyV2 = "{{REWARD_UNITS}}"
	ActionRefKeyV2   = "{{ACTION_REF}}"
	ActionTimeKeyV2  = "{{ACTION_TIME}}"

	RewardUnitsByTwoKey              = "{{REWARD_UNITS_BY_TWO}}"
	RewardUnitsMultiplier            = "{{REWARD_UNITS_MULTIPLIER}}"
	RewardUnitsBeforeMultiplierStage = "{{REWARD_UNITS_BEFORE_MULTIPLIER_STAGE}}"
)

type RewardUnitsComputedFromProjectionCaps struct {
	actionLevelCap    map[string]uint32
	offerTypeLevelCap map[string]uint32
	offerIdLevelCap   map[string]uint32
}

type RewardUnitsInfo struct {
	rewardUnits                      float64
	rewardUnitsBeforeMultiplierStage float64
	multiplier                       uint32
	rewardUnitsCalculationInfo       *rewardsPb.RewardUnitsCalculationInfo
	// map of projection ID to actual contribution in reward generation
	// will ONLY be populated if reward units are calculated using projections.
	projectionIdsToOptionMap map[string]*projectorPb.RewardOption
	// adjustment reward specific details
	// will be nil if generated reward option is for original reward
	adjustmentRewardOptionDetails *AdjustmentRewardOptionDetails
}

type AdjustmentRewardOptionDetails struct {
	// will true ONLY if reward units are calculated using projections
	// and reward units calculated are for adjustment to already generated reward (original reward)
	isAdjustmentReward bool
	// flag to denote whether the original reward (first reward generated on a [actor_id, offer_id, ref_id] combination) is already chosen
	// will true ONLY if isAdjustmentReward is true and the corresponding original reward is already chosen
	isOriginalRewardOptionChosen bool
	// SecondaryRefId of adjustment reward
	adjustmentRewardSecondaryRefId string
}

const (
	ZeroRewardUnits                      = 0.0
	adjustmentRewardSecondaryRefIdPrefix = "ADJUSTMENT_REWARD_"
)

type CustomRuleEngine struct {
	offerGroupDao      dao.RewardOfferGroupDao
	rewardOfferDao     dao.RewardOfferDao
	rewardsDao         dao.RewardsDao
	projectionsDao     projectorDao.RewardsProjectionDao
	luckyDrawSvcClient luckydrawPb.LuckyDrawServiceClient
	userHelperService  helper.IUserHelperService
	ffAccClient        ffAccountsPb.AccountingClient
	conf               *config.Config
	dynConf            *genconf.Config
}

func NewCustomRuleEngine(rewardOfferGroupDao dao.RewardOfferGroupDao, rewardOfferDao dao.RewardOfferDao, projectionsDao projectorDao.RewardsProjectionDao, luckyDrawSvcClient luckydrawPb.LuckyDrawServiceClient, userHelperService helper.IUserHelperService, ffAccClient ffAccountsPb.AccountingClient, rewardsDao dao.RewardsDao, conf *config.Config, dynConf *genconf.Config) *CustomRuleEngine {
	return &CustomRuleEngine{offerGroupDao: rewardOfferGroupDao, rewardOfferDao: rewardOfferDao, projectionsDao: projectionsDao, luckyDrawSvcClient: luckyDrawSvcClient, userHelperService: userHelperService, ffAccClient: ffAccClient, rewardsDao: rewardsDao, conf: conf, dynConf: dynConf}
}

func (re *CustomRuleEngine) EvaluateConstraint(fact common.IFact, expression string) (bool, error) {
	result, err := re.evaluateExpression(expression, fact, nil)
	if err != nil {
		return false, errors.Wrap(err, "error while evaluating constraint")
	}
	res, _ := result.(bool)
	return res, nil
}

func (re *CustomRuleEngine) evaluateExpression(expressionString string, fact common.IFact, paramsMap map[string]interface{}) (interface{}, error) {
	expression, err := govaluate.NewEvaluableExpressionWithFunctions(expressionString, fact.GetExpressionFunctionMap())
	if err != nil {
		return nil, err
	}
	expressionParametersMap := fact.GetExpressionParametersMap()
	for k, v := range paramsMap {
		expressionParametersMap[k] = v
	}
	result, err := expression.Evaluate(expressionParametersMap)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (re *CustomRuleEngine) evaluateTemplateTextExpression(templateTextExpression string, fact common.IFact, templateParamsMap map[string]string) string {
	result := templateTextExpression
	// use templateParamsMap for evaluating template params.
	for paramName, paramValue := range templateParamsMap {
		result = strings.ReplaceAll(result, paramName, paramValue)
	}
	// use fact metadata for evaluating template params.
	for paramName, paramValue := range fact.GetMetadataMap() {
		result = strings.ReplaceAll(result, paramName, paramValue)
	}
	return result
}

// evaluateRewardConfigOptionConstraint returns true if constraint present in RewardConfigOption is satisfied.
func (re *CustomRuleEngine) evaluateRewardConfigOptionConstraint(rewardConfigOption *rewardOfferPb.RewardConfigOption, fact common.IFact) (bool, error) {
	// if constraint expression is empty, it implies no constraint was applied so returning true
	if rewardConfigOption.GetConstraintExpression() == "" {
		return true, nil
	}
	result, err := re.evaluateExpression(rewardConfigOption.GetConstraintExpression(), fact, nil)
	if err != nil {
		return false, errors.Wrap(err, "error while evaluating reward option constraint expression")
	}
	res, ok := result.(bool)
	if !ok {
		return false, errors.New("result of reward option constraint expression is not of bool type")
	}
	return res, nil
}

// nolint:funlen
func (re *CustomRuleEngine) calculateBaseRewardUnits(rewardConfigOption *rewardOfferPb.RewardConfigOption, fact common.IFact, optionIndexToRewardUnitsMap map[int]uint32) (float64, error) {
	// for those configs where unit doesn't make sense like lucky draw
	if rewardConfigOption.GetUnitsConfig() == nil {
		return 0.0, nil
	}
	switch rewardConfigOption.UnitsConfig.(type) {

	// calculate units using FixedProbabilityConfig
	case *rewardOfferPb.RewardConfigOption_FixedProbabilityConfig:
		return float64(rewardConfigOption.GetFixedProbabilityConfig().Value), nil

	// calculate units using ExpressionProbabilityConfig
	case *rewardOfferPb.RewardConfigOption_ExpressionProbabilityConfig:
		result, err := re.evaluateExpression(rewardConfigOption.GetExpressionProbabilityConfig().Expression, fact, nil)
		if err != nil {
			return 0.0, err
		}
		res, ok := result.(float64)
		if !ok {
			return 0.0, errors.New("unable to convert expression result to float for ExpressionProbabilityConfig")
		}
		return res, nil

	// calculate units using RangeProbabilityConfig
	case *rewardOfferPb.RewardConfigOption_RangeProbabilityConfig:
		return generator.RewardUnitsCalculator.CalculateUsingRangeProbabilityConfig(rewardConfigOption.GetRangeProbabilityConfig())

	// calculate units using ExpressionRangeProbabilityConfig
	case *rewardOfferPb.RewardConfigOption_ExpressionRangeProbabilityConfig:
		result, err := re.evaluateExpression(rewardConfigOption.GetExpressionRangeProbabilityConfig().Expression, fact, nil)
		if err != nil {
			return 0.0, err
		}
		res, ok := result.(float64)
		if !ok {
			return 0.0, errors.New("unable to convert expression result to float for ExpressionRangeProbabilityConfig")
		}
		return generator.RewardUnitsCalculator.CalculateUsingExpressionRangeProbabilityConfig(res, rewardConfigOption.GetExpressionRangeProbabilityConfig(), fact)

	// calculate units using DeriveFromSeedRewardOptionConfig
	case *rewardOfferPb.RewardConfigOption_DeriveFromSeedRewardOptionConfig:
		deriveFromSeedRewardOptionConfig := rewardConfigOption.GetDeriveFromSeedRewardOptionConfig()
		// get seed reward option units
		seedRewardOptionIndex := deriveFromSeedRewardOptionConfig.GetSeedRewardOptionIndex()
		if seedRewardOptionIndex < 1 || seedRewardOptionIndex > int32(len(optionIndexToRewardUnitsMap)) {
			return 0.0, errors.New("invalid seedRewardOptionIndex in DeriveFromSeedRewardOptionConfig unit config")
		}
		seedRewardOptionValue := optionIndexToRewardUnitsMap[int(seedRewardOptionIndex)-1]
		// derive units from seedRewardOptionValue and delta/multiplier derivation config
		if deriveFromSeedRewardOptionConfig.GetDeltaDerivationConfig() != nil {
			return float64(int32(seedRewardOptionValue) + deriveFromSeedRewardOptionConfig.GetDeltaDerivationConfig().GetDelta()), nil
		}
		if deriveFromSeedRewardOptionConfig.GetMultiplierDerivationConfig() != nil {
			return float64(float32(seedRewardOptionValue) * deriveFromSeedRewardOptionConfig.GetMultiplierDerivationConfig().GetMultiplier()), nil
		}
		return 0.0, errors.New("invalid DeriveFromSeedRewardOptionConfig unit config")

	case *rewardOfferPb.RewardConfigOption_ConditionalExpRangeProbabilityConfig:
		conditionalExpRangeProbConfig := rewardConfigOption.GetConditionalExpRangeProbabilityConfig()
		// check condition and use the corresponding expression range distribution to calculate units if condition is satisfied.
		for _, configUnit := range conditionalExpRangeProbConfig.GetConfigUnits() {
			conditionResult, err := re.evaluateExpression(configUnit.GetConditionExpression(), fact, nil)
			if err != nil {
				return 0.0, errors.Wrap(err, "error evaluating condition expression of conditionalExpRangeProbConfig")
			}
			isConditionSatisfied, ok := conditionResult.(bool)
			if !ok {
				return 0.0, errors.New("result of condition expression of conditionalExpRangeProbConfig is not of boolean type")
			}
			// if condition is not satisfied, ignore this config unit.
			if !isConditionSatisfied {
				continue
			}
			// use the expression range prob config of current configUnit to calculate reward units.
			expressionRangeProbConfig := configUnit.GetExpressionRangeProbabilityConfig()
			seedUnitsResult, err := re.evaluateExpression(expressionRangeProbConfig.GetExpression(), fact, nil)
			if err != nil {
				return 0.0, errors.Wrap(err, "error evaluating seed unit value expression of conditionalExpRangeProbConfig")
			}
			seedUnitValue, ok := seedUnitsResult.(float64)
			if !ok {
				return 0.0, errors.New("result of seed unit value expression of conditionalExpRangeProbConfig is not of float type")
			}
			return generator.RewardUnitsCalculator.CalculateUsingExpressionRangeProbabilityConfig(seedUnitValue, expressionRangeProbConfig, fact)
		}
		// todo (utkarsh) : should we not return error as treat is as zero reward case only ?
		return 0.0, errors.New("invalid ConditionalExpRangeProbabilityConfig unit config")

	default:
		return 0.0, errors.New("unimplemented reward config option")
	}
}

// applyRewardUnitsMultiplier applies multiplier (based on multiplier config) on reward units.
func (re *CustomRuleEngine) applyRewardUnitsMultiplier(rewardUnits float64, rewardConfigOption *rewardOfferPb.RewardConfigOption) (rewardUnitsAfterMultiplier float64, multiplier uint32, err error) {
	// multiplier not configured
	if rewardConfigOption.GetMultiplierConfig() == nil {
		return rewardUnits, 1, nil
	}
	switch rewardConfigOption.GetMultiplierConfig().(type) {
	case *rewardOfferPb.RewardConfigOption_FixedMultiplierConfig:
		return rewardUnits * float64(rewardConfigOption.GetFixedMultiplierConfig().GetMultiplier()), rewardConfigOption.GetFixedMultiplierConfig().GetMultiplier(), nil
	default:
		return 0.0, 0, fmt.Errorf("unsupported multiplier config encountered")
	}
}

// capRewardUnitsAtOfferAndActor caps the passed rewardUnits based on the max-cap config and the actor's utilisation at offer level.
// Note: It returns an error if the final rewardUnits calculated turn out to be 0. It assumes that the rewardUnits passed to the method are non-zero.
// Thus, make sure not to call this method if the rewardUnits being passed are already zero.
// nolint:funlen,dupl
func (re *CustomRuleEngine) capRewardUnitsAtOfferAndActor(ctx context.Context, rewardUnits float64, rewardType rewardsPb.RewardType, actorId, offerId string, rewardUnitsCapConfig *rewardOfferPb.RewardUnitsCapAggregate) (float64, error) {
	var (
		offerRewardUnitsUtilized *rewardOfferPb.RewardOfferRewardUnitsActorUtilisation
		rewardUnitsCap           uint32
		rewardUnitsCapDefined    bool
	)

	// 1. Validate if we need to apply any caps
	// 1.a. check if offer-level config cap config is defined or not
	if len(rewardUnitsCapConfig.GetUnitsCaps()) == 0 {
		return rewardUnits, nil
	}

	// 1.b. check if cap is defined for the required rewardType or not
	for _, rewardTypeCap := range rewardUnitsCapConfig.GetUnitsCaps() {
		if rewardTypeCap.GetRewardType() == rewardType {
			rewardUnitsCap = rewardTypeCap.GetUnits()
			rewardUnitsCapDefined = true
			break
		}
	}
	// no need to proceed if cap is not defined for the reward type
	if !rewardUnitsCapDefined {
		return rewardUnits, nil
	}

	// 2. Get the current reward-units utilisation for that actor of that offer
	offersRewardUnitsUtilized, daoErr := re.rewardOfferDao.GetOffersRewardUnitsUtilized(ctx, actorId, []string{offerId})
	if daoErr != nil && !errors.Is(daoErr, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "Error in fetching RewardOfferRewardUnitsCapForActor", zap.String(logger.ACTOR_ID, actorId),
			zap.String(logger.REWARD_OFFER_ID, offerId), zap.Error(daoErr),
		)

		return 0.0, errors.Wrap(daoErr, "Error in fetching RewardOfferRewardUnitsCapForActor")
	}

	// 3. proceed for caps check even if no actor specific record is found since we still need to cap the rewardUnits
	// based on the caps config
	if len(offersRewardUnitsUtilized) == 0 || offersRewardUnitsUtilized[0] == nil {
		offerRewardUnitsUtilized = &rewardOfferPb.RewardOfferRewardUnitsActorUtilisation{}
	} else {
		offerRewardUnitsUtilized = offersRewardUnitsUtilized[0]
	}

	currentUtilisation, err := offerRewardUnitsUtilized.GetOfferUnitsUtilizedForRewardType(rewardType)
	if err != nil {
		// should never reach this state. If it reaches, it means cap-config is created for an unsupported rewardType
		logger.Error(ctx, "reward-type not handled for capping reward-units",
			zap.String(logger.REWARD_TYPE, rewardType.String()), zap.String(logger.ACTOR_ID, actorId),
			zap.String(logger.REWARD_OFFER_ID, offerId),
		)

		return 0.0, errors.Wrap(err, "reward-type not handled for capping reward-units")
	}

	availableUnits := int32(rewardUnitsCap) - currentUtilisation
	// safety-check of math.Max so that the units don't fall below 0. Possible if currentUtilisation > cap (revised value)
	rewardUnits = math.Max(ZeroRewardUnits, math.Min(rewardUnits, float64(availableUnits)))

	// returning error state to convey that capping led to 0 reward-units
	if int32(rewardUnits) == 0 {
		return 0.0, internalerrors.RewardUnitsMaxCapReached
	}

	return rewardUnits, nil
}

// capRewardUnitsAtOfferAndActorInTimePeriod caps the passed rewardUnits based on the monthly max-cap config and the actor's utilisation at offer level.
// Note: It returns an error if the final rewardUnits calculated turn out to be 0. It assumes that the rewardUnits passed to the method are non-zero.
// Thus, make sure not to call this method if the rewardUnits being passed are already zero.
// nolint:funlen,dupl
func (re *CustomRuleEngine) capRewardUnitsAtOfferAndActorInTimePeriod(ctx context.Context, rewardUnits float64, rewardType rewardsPb.RewardType, actorId, offerId string, fromTime, tillTime time.Time, rewardUnitsCapConfig *rewardOfferPb.RewardUnitsCapAggregate) (float64, error) {
	var (
		offerRewardUnitsUtilizedInTimePeriod *rewardsPb.RewardOfferRewardUnitsActorUtilisationInTimePeriod
		rewardUnitsCap                       uint32
		rewardUnitsCapDefined                = false
	)

	// 1. Validate if we need to apply any caps
	// 1.a. check if offer-level config cap config is defined or not
	if len(rewardUnitsCapConfig.GetUnitsCaps()) == 0 {
		return rewardUnits, nil
	}

	// 1.b. check if cap is defined for the required rewardType or not
	for _, rewardTypeCap := range rewardUnitsCapConfig.GetUnitsCaps() {
		if rewardTypeCap.GetRewardType() == rewardType {
			rewardUnitsCap = rewardTypeCap.GetUnits()
			rewardUnitsCapDefined = true
			break
		}
	}
	// no need to proceed if cap is not defined for the reward type
	if !rewardUnitsCapDefined {
		return rewardUnits, nil
	}

	// 2. Get the current reward-units utilisation for that actor of that offer for the current
	offersRewardUnitsUtilized, daoErr := re.rewardOfferDao.GetOffersRewardUnitsUtilizedInTimePeriod(ctx, actorId, []string{offerId}, fromTime, tillTime)
	if daoErr != nil && !errors.Is(daoErr, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "Error in fetching RewardOfferRewardUnitsCapForActor", zap.String(logger.ACTOR_ID, actorId),
			zap.String(logger.REWARD_OFFER_ID, offerId), zap.Error(daoErr),
		)

		return 0.0, errors.Wrap(daoErr, "Error in fetching RewardOfferRewardUnitsCapForActor")
	}

	// 3. proceed for caps check even if no actor specific record is found since we still need to cap the rewardUnits
	// based on the caps config
	if len(offersRewardUnitsUtilized) == 0 || offersRewardUnitsUtilized[0] == nil {
		offerRewardUnitsUtilizedInTimePeriod = &rewardsPb.RewardOfferRewardUnitsActorUtilisationInTimePeriod{}
	} else {
		offerRewardUnitsUtilizedInTimePeriod = offersRewardUnitsUtilized[0]
	}

	currentUtilisation, err := offerRewardUnitsUtilizedInTimePeriod.GetOfferUnitsUtilizedForRewardType(rewardType)
	if err != nil {
		// should never reach this state. If it reaches, it means cap-config is created for an unsupported rewardType
		logger.Error(ctx, "reward-type not handled for capping reward-units",
			zap.String(logger.REWARD_TYPE, rewardType.String()), zap.String(logger.ACTOR_ID, actorId),
			zap.String(logger.REWARD_OFFER_ID, offerId),
		)

		return 0.0, errors.Wrap(err, "reward-type not handled for capping reward-units")
	}

	availableUnits := int32(rewardUnitsCap) - currentUtilisation
	// safety-check of math.Max so that the units don't fall below 0. Possible if currentUtilisation > cap (revised value)
	rewardUnits = math.Max(ZeroRewardUnits, math.Min(rewardUnits, float64(availableUnits)))

	// returning error state to convey that capping led to 0 reward-units
	if int32(rewardUnits) == 0 {
		return 0.0, internalerrors.RewardUnitsMaxCapReached
	}

	return rewardUnits, nil
}

// capRewardUnitsAtGroupAndActor caps the passed rewardUnits based on the max-cap config and the actor's utilisation at group level.
// Note: It returns an error if the final rewardUnits calculated turn out to be 0. It assumes that the rewardUnits passed to the method are non-zero.
// Thus, make sure not to call this method if the rewardUnits being passed are already zero.
// nolint:funlen,dupl
func (re *CustomRuleEngine) capRewardUnitsAtGroupAndActor(ctx context.Context, rewardUnits float64, rewardType rewardsPb.RewardType, actorId, offerGroupId string, rewardUnitsCapConfig *rewardOfferPb.RewardUnitsCapAggregate) (float64, error) {
	var (
		offerGroupLevelRewardUnitsUtilized *rewardOfferPb.RewardOfferGroupRewardUnitsActorUtilisation
		rewardUnitsCap                     uint32
		rewardUnitsCapDefined              bool
	)

	// 1. Validate if we need to apply any caps
	// 1.a. check if group-level config cap config is defined or not
	if offerGroupId == "" || len(rewardUnitsCapConfig.GetUnitsCaps()) == 0 {
		return rewardUnits, nil
	}

	// 1.b. check if cap is defined for the required rewardType or not
	for _, rewardTypeCap := range rewardUnitsCapConfig.GetUnitsCaps() {
		if rewardTypeCap.GetRewardType() == rewardType {
			rewardUnitsCap = rewardTypeCap.GetUnits()
			rewardUnitsCapDefined = true
			break
		}
	}
	// no need to proceed if cap is not defined for the reward type
	if !rewardUnitsCapDefined {
		return rewardUnits, nil
	}

	// 2. get the current reward-units utilisation for that actor of that group
	offerGroupsRewardUnitsUtilized, daoErr := re.offerGroupDao.GetOfferGroupsRewardUnitsUtilized(ctx, actorId, []string{offerGroupId})
	if daoErr != nil && !errors.Is(daoErr, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "Error in fetching RewardOfferGroupRewardUnitsCapForActor", zap.String(logger.ACTOR_ID, actorId),
			zap.String("offerGroupId", offerGroupId), zap.Error(daoErr),
		)

		return 0.0, errors.Wrap(daoErr, "Error in fetching RewardOfferGroupRewardUnitsCapForActor")
	}

	// 3. proceed for caps check even if no actor specific record is found since we still need to cap the rewardUnits
	// based on the caps config
	if len(offerGroupsRewardUnitsUtilized) == 0 || offerGroupsRewardUnitsUtilized[0] == nil {
		offerGroupLevelRewardUnitsUtilized = &rewardOfferPb.RewardOfferGroupRewardUnitsActorUtilisation{}
	} else {
		offerGroupLevelRewardUnitsUtilized = offerGroupsRewardUnitsUtilized[0]
	}

	currentUtilisation, err := offerGroupLevelRewardUnitsUtilized.GetGroupOfferUnitsUtilizedForRewardType(rewardType)
	if err != nil {
		// should never reach this state. If it reaches, it means cap-config is created for an unsupported rewardType
		logger.Error(ctx, "reward-type not handled for capping reward-units",
			zap.String(logger.REWARD_TYPE, rewardType.String()), zap.String(logger.ACTOR_ID, actorId),
			zap.String("offerGroupId", offerGroupId),
		)

		return 0.0, errors.Wrap(err, "reward-type not handled for capping reward-units")
	}

	availableUnits := int32(rewardUnitsCap) - currentUtilisation
	// safety-check of math.Max so that the units don't fall below 0. Possible if currentUtilisation > cap (revised value)
	rewardUnits = math.Max(ZeroRewardUnits, math.Min(rewardUnits, float64(availableUnits)))

	// returning error state to convey that capping led to 0 reward-units
	if int32(rewardUnits) == 0 {
		return 0.0, internalerrors.RewardUnitsMaxCapReached
	}

	return rewardUnits, nil
}

// capRewardUnitsAtGroupAndActorInTimePeriod caps the passed rewardUnits based on the monthly max-cap config and the actor's utilisation at group level.
// Note: It returns an error if the final rewardUnits calculated turn out to be 0. It assumes that the rewardUnits passed to the method are non-zero.
// Thus, make sure not to call this method if the rewardUnits being passed are already zero.
// nolint:funlen,dupl
func (re *CustomRuleEngine) capRewardUnitsAtGroupAndActorInTimePeriod(ctx context.Context, rewardUnits float64, rewardType rewardsPb.RewardType, actorId, offerGroupId string, fromTime, tillTime time.Time, rewardUnitsCapConfig *rewardOfferPb.RewardUnitsCapAggregate) (float64, error) {
	var (
		offerGroupLevelRewardUnitsUtilizedInTimePeriod *rewardsPb.RewardOfferGroupRewardUnitsActorUtilisationInTimePeriod
		rewardUnitsCap                                 uint32
		rewardUnitsCapDefined                          bool
	)

	// 1. Validate if we need to apply any caps
	// 1.a. check if group-level config cap config is defined or not
	if offerGroupId == "" || len(rewardUnitsCapConfig.GetUnitsCaps()) == 0 {
		return rewardUnits, nil
	}

	// 1.b. check if cap is defined for the required rewardType or not
	for _, rewardTypeCap := range rewardUnitsCapConfig.GetUnitsCaps() {
		if rewardTypeCap.GetRewardType() == rewardType {
			rewardUnitsCap = rewardTypeCap.GetUnits()
			rewardUnitsCapDefined = true
			break
		}
	}
	// no need to proceed if cap is not defined for the reward type
	if !rewardUnitsCapDefined {
		return rewardUnits, nil
	}

	// 2. get the current reward-units utilisation for that actor of that group
	offerGroupsRewardUnitsUtilized, daoErr := re.offerGroupDao.GetOfferGroupsRewardUnitsUtilizedInTimePeriod(ctx, actorId, []string{offerGroupId}, fromTime, tillTime)
	if daoErr != nil && !errors.Is(daoErr, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "Error in fetching RewardOfferGroupRewardUnitsCapForActor", zap.String(logger.ACTOR_ID, actorId),
			zap.String("offerGroupId", offerGroupId), zap.Error(daoErr),
		)

		return 0.0, errors.Wrap(daoErr, "Error in fetching RewardOfferGroupRewardUnitsCapForActor")
	}

	// 3. proceed for caps check even if no actor specific record is found since we still need to cap the rewardUnits
	// based on the caps config
	if len(offerGroupsRewardUnitsUtilized) == 0 || offerGroupsRewardUnitsUtilized[0] == nil {
		offerGroupLevelRewardUnitsUtilizedInTimePeriod = &rewardsPb.RewardOfferGroupRewardUnitsActorUtilisationInTimePeriod{}
	} else {
		offerGroupLevelRewardUnitsUtilizedInTimePeriod = offerGroupsRewardUnitsUtilized[0]
	}

	currentUtilisation, err := offerGroupLevelRewardUnitsUtilizedInTimePeriod.GetGroupOfferUnitsUtilizedForRewardType(rewardType)
	if err != nil {
		// should never reach this state. If it reaches, it means cap-config is created for an unsupported rewardType
		logger.Error(ctx, "reward-type not handled for capping reward-units",
			zap.String(logger.REWARD_TYPE, rewardType.String()), zap.String(logger.ACTOR_ID, actorId),
			zap.String("offerGroupId", offerGroupId),
		)

		return 0.0, errors.Wrap(err, "reward-type not handled for capping reward-units")
	}

	availableUnits := int32(rewardUnitsCap) - currentUtilisation
	// safety-check of math.Max so that the units don't fall below 0. Possible if currentUtilisation > cap (revised value)
	rewardUnits = math.Max(ZeroRewardUnits, math.Min(rewardUnits, float64(availableUnits)))

	// returning error state to convey that capping led to 0 reward-units
	if int32(rewardUnits) == 0 {
		return 0.0, internalerrors.RewardUnitsMaxCapReached
	}

	return rewardUnits, nil
}

// applyBoostersToRewardUnits iterates over the boosters in the boosters config to apply them in order on the rewardUnits being passed.
// A ledger of the reward-calculations (with intermediate reward-values after each boosters' application) will be returned along with the necessary display details.
// nolint:funlen
func (re *CustomRuleEngine) applyBoostersToRewardUnits(ctx context.Context, rewardUnits float64, boostersConfig *rewardOfferPb.BoostersConfig, fact common.IFact) ([]*rewardsPb.RewardUnitsCalculationInfo_RewardUnitsCalculationEntry, error) {
	if boostersConfig == nil {
		return nil, nil
	}

	var (
		rewardCalculationEntries []*rewardsPb.RewardUnitsCalculationInfo_RewardUnitsCalculationEntry
		// stores the base reward units
		baseRewardUnits = rewardUnits
		// will be passed on to subsequent booster configs
		previousValueOfRewardCalculation = rewardUnits
	)

	for _, boosterConfig := range boostersConfig.GetBoosters() {
		configUnits := boosterConfig.GetConditionalExpressionBoosterConfig().GetConfigUnits()
		for _, configUnit := range configUnits {
			paramsMap := map[string]interface{}{"BASE_VALUE": baseRewardUnits, "PREVIOUS_VALUE": previousValueOfRewardCalculation}

			conditionResult, err := re.evaluateExpression(configUnit.GetConditionExpression(), fact, paramsMap)
			if err != nil {
				return nil, fmt.Errorf("error evaluating condition expression of conditionalExpressionBoosterConfig: %w", err)
			}
			isConditionSatisfied, ok := conditionResult.(bool)
			if !ok {
				return nil, fmt.Errorf("result of condition expression of conditionalExpressionBoosterConfig is not of boolean type")
			}

			if !isConditionSatisfied {
				continue
			}

			var rewardUnitsFromUnitsConfig float64

			switch configUnit.GetUnitsConfig().(type) {
			case *rewardOfferPb.ConditionalExpressionBoosterConfig_ConfigUnit_ExpressionRangeProbabilityConfig:
				expressionRangeUnitsConfig := configUnit.GetExpressionRangeProbabilityConfig()
				seedUnitsResult, evalErr := re.evaluateExpression(expressionRangeUnitsConfig.GetExpression(), fact, paramsMap)
				if evalErr != nil {
					return nil, fmt.Errorf("error evaluating seed unit value expression of conditionalExpressionBoosterConfig: %w", evalErr)
				}
				seedUnitValue, ok := seedUnitsResult.(float64)
				if !ok {
					return nil, fmt.Errorf("result of seed unit value expression of conditionalExpressionBoosterConfig is not of type float64")
				}
				rewardUnitsFromUnitsConfig, err = generator.RewardUnitsCalculator.CalculateUsingExpressionRangeProbabilityConfig(seedUnitValue, expressionRangeUnitsConfig, fact)
				if err != nil {
					return nil, fmt.Errorf("error calculating using expressionRangeProbabilityConfig in conditionalExpressionBoosterConfig: %w", err)
				}
			default:
				return nil, fmt.Errorf("unsupported units-config in conditionalExpressionBoosterConfig: %T", configUnit.GetUnitsConfig())
			}

			intermediateRewardValue, err := generator.RewardUnitsCalculator.PerformMathematicalOperationOnUnits(
				int32(previousValueOfRewardCalculation), int32(rewardUnitsFromUnitsConfig), configUnit.GetOperationWithPreviousValue(), configUnit.GetUnitsUpperCapAfterOperationWithPreviousValue(),
			)
			if err != nil {
				return nil, fmt.Errorf("error while performing mathematical operation on previous value in conditionalExpressionBoosterConfig: %w", err)
			}

			calculationEntryDisplayDetails := getRewardCalculationDisplayFromBoosterUnitsConfig(ctx, configUnit.GetDisplayDetails())

			// append an entry for rewardCalculationEntry
			rewardCalculationEntries = append(rewardCalculationEntries, &rewardsPb.RewardUnitsCalculationInfo_RewardUnitsCalculationEntry{
				RewardValue:    float32(intermediateRewardValue),
				DisplayDetails: calculationEntryDisplayDetails,
			})

			// update previously calculated reward-units with the updated reward-units after the booster's application
			previousValueOfRewardCalculation = float64(intermediateRewardValue)

			// no need to check further config units as the current one got satisfied. We can move to the next booster
			break
		}
	}

	return rewardCalculationEntries, nil
}

// capRewardUnitsUsingCapConfigs is a high-level method which performs respective capping by calling their methods.
// Note: It returns the rewardUnits as is if they are already 0 when passed. But it need not be true for the individual methods
// which perform the capping.
func (re *CustomRuleEngine) capRewardUnitsUsingCapConfigs(ctx context.Context, rewardUnits float64, rewardType rewardsPb.RewardType, fact common.IFact, reward *model.Reward) (float64, error) {
	var (
		actorId                               = fact.GetActorId()
		rewardOffer                           = fact.GetRewardOffer()
		offerId                               = rewardOffer.GetId()
		offerGroupId                          = rewardOffer.GetGroupId()
		offerRewardUnitsCapConfig             = rewardOffer.GetRewardMeta().GetRewardAggregates().GetRewardUnitsCapUserAggregate()
		offerMonthlyRewardUnitsCapConfig      = rewardOffer.GetRewardMeta().GetRewardAggregates().GetRewardUnitsCapMonthlyUserAggregate()
		offerGroupRewardUnitsCapConfig        = reward.OfferGroupRewardUnitsCapActorConfig
		offerGroupMonthlyRewardUnitsCapConfig = reward.OfferGroupRewardUnitsMonthlyCapActorConfig

		monthStartFromTime = datetime.StartOfMonth(reward.ActionTimestamp.In(datetime.IST))
		monthEndTillTime   = *(datetime.AddNMonths(&monthStartFromTime, 1))

		capErr error
	)

	// no need to perform caps processing if the reward-units are already 0.
	// no error returned from here since the reward-units didn't get capped down to 0, they were already 0.
	if int32(rewardUnits) == 0 {
		return rewardUnits, nil
	}

	// (I) check for offer-actor overall unit utilization cap
	if offerRewardUnitsCapConfig.GetIsHardCheck() {
		rewardUnits, capErr = re.capRewardUnitsAtOfferAndActor(ctx, rewardUnits, rewardType, actorId, offerId, offerRewardUnitsCapConfig)
		if capErr != nil {
			return 0.0, capErr
		}
	}

	// (II) check for offer-actor monthly unit utilization cap
	if offerMonthlyRewardUnitsCapConfig.GetIsHardCheck() {
		rewardUnits, capErr = re.capRewardUnitsAtOfferAndActorInTimePeriod(ctx, rewardUnits, rewardType, actorId, offerId, monthStartFromTime, monthEndTillTime, offerMonthlyRewardUnitsCapConfig)
		if capErr != nil {
			return 0.0, capErr
		}
	}

	// (III) check for group-actor overall unit utilization cap
	if offerGroupRewardUnitsCapConfig.GetIsHardCheck() {
		rewardUnits, capErr = re.capRewardUnitsAtGroupAndActor(ctx, rewardUnits, rewardType, actorId, offerGroupId, offerGroupRewardUnitsCapConfig)
		if capErr != nil {
			return 0.0, capErr
		}
	}

	// (IV) check for group-actor monthly unit utilization cap
	if offerGroupMonthlyRewardUnitsCapConfig.GetIsHardCheck() {
		rewardUnits, capErr = re.capRewardUnitsAtGroupAndActorInTimePeriod(ctx, rewardUnits, rewardType, actorId, offerGroupId, monthStartFromTime, monthEndTillTime, offerGroupMonthlyRewardUnitsCapConfig)
		if capErr != nil {
			return 0.0, capErr
		}
	}

	// TODO : add monthly and quarterly caps as well for this.

	return rewardUnits, nil
}

// nolint: funlen
func (re *CustomRuleEngine) getRewardUnits(ctx context.Context, rewardConfigOption *rewardOfferPb.RewardConfigOption, fact common.IFact, optionIndexToRewardUnitsMap map[int]uint32, reward *model.Reward) (*RewardUnitsInfo, error) {
	var (
		rewardUnits     float64
		err             error
		rewardUnitsInfo = &RewardUnitsInfo{}
	)
	if rewardConfigOption.GetShouldGenerateFromProjections() {
		rewardUnitsInfo, err = re.calculateRewardUnitsUsingProjections(ctx, fact, rewardConfigOption)
		if err != nil {
			return nil, fmt.Errorf("error while calculating reward amount using projections, err: %w", err)
		}
		rewardUnits = rewardUnitsInfo.rewardUnits
	} else {
		rewardUnits, err = re.calculateBaseRewardUnits(rewardConfigOption, fact, optionIndexToRewardUnitsMap)
		if err != nil {
			return nil, err
		}
	}

	// first entry within the calculation info (ledger) is the base reward value
	rewardUnitsCalculationInfo := &rewardsPb.RewardUnitsCalculationInfo{
		RewardUnitsCalculationEntries: []*rewardsPb.RewardUnitsCalculationInfo_RewardUnitsCalculationEntry{
			{
				RewardValue: float32(rewardUnits),
				// todo: should we add display details? depends though. Shall decide once we have figma ready
				DisplayDetails: nil,
			},
		},
	}

	// if booster config is present, proceed for the boosters' application on the reward units
	if rewardConfigOption.GetBoostersConfig() != nil {
		boosterRewardCalculationEntries, boosterErr := re.applyBoostersToRewardUnits(ctx, rewardUnits, rewardConfigOption.GetBoostersConfig(), fact)
		if boosterErr != nil {
			return nil, fmt.Errorf("error while trying to apply boosters: %w", boosterErr)
		}

		// append the calculation info of individual booster to the ledger
		rewardUnitsCalculationInfo.RewardUnitsCalculationEntries = append(rewardUnitsCalculationInfo.RewardUnitsCalculationEntries, boosterRewardCalculationEntries...)
		rewardUnitsCalculationInfo.ClaimFlowRewardValueRevealType = rewardConfigOption.GetBoostersConfig().GetClaimFlowRewardValueRevealType()
		// update the reward-units with the final value calculated after boosters' application
		if len(boosterRewardCalculationEntries) != 0 {
			rewardUnits = float64(boosterRewardCalculationEntries[len(boosterRewardCalculationEntries)-1].GetRewardValue())
		}
	}
	rewardUnitsInfo.rewardUnitsCalculationInfo = rewardUnitsCalculationInfo
	rewardUnitsInfo.rewardUnitsBeforeMultiplierStage = rewardUnits

	// older multiplier logic (not related to boosters)
	// todo: see if we can get rid of this since we have boosters in place now.
	multipliedRewardUnits, multiplier, err := re.applyRewardUnitsMultiplier(rewardUnits, rewardConfigOption)
	if err != nil {
		return nil, fmt.Errorf("failed to multiply calculated reward units with multiplier, err: %w", err)
	}
	rewardUnitsInfo.multiplier = multiplier
	rewardUnitsInfo.rewardUnits = multipliedRewardUnits

	cappedRewardUnits, err := re.capRewardUnitsUsingCapConfigs(ctx, multipliedRewardUnits, rewardConfigOption.GetRewardType(), fact, reward)
	if err != nil {
		return nil, fmt.Errorf("error while capping reward units using cap configs, err: %w", err)
	}

	// note: a known flaw here is that if the capping happens which ends up reducing the `multipliedRewardUnits`, then the
	// `multiplier` being returned from here will represent incorrect value.
	rewardUnitsInfo.rewardUnits = cappedRewardUnits

	return rewardUnitsInfo, nil
}

func (re *CustomRuleEngine) getOptionDisplay(rewardConfigOption *rewardOfferPb.RewardConfigOption, fact common.IFact, rewardUnitsInfo *RewardUnitsInfo) *rewardsPb.RewardOptionDisplay {
	rewardUnits := uint32(rewardUnitsInfo.rewardUnits)
	rewardUnitsBeforeMultiplierStage := uint32(rewardUnitsInfo.rewardUnitsBeforeMultiplierStage)

	rewardUnitsByTwoKeyReplacementString := fmt.Sprintf("%d", rewardUnits/2)
	if rewardUnits%2 != 0 {
		rewardUnitsByTwoKeyReplacementString = fmt.Sprintf("%.1f", float32(rewardUnits)/2)
	}

	// order is important in this
	paramsMap := map[string]string{
		RewardUnitsKeyV2:                 fmt.Sprint(rewardUnits),
		ActionRefKeyV2:                   fact.GetRefId(),
		ActionTimeKeyV2:                  time.Unix(fact.GetActionTime().GetSeconds(), 0).Format(time.RFC850),
		RewardUnitsByTwoKey:              rewardUnitsByTwoKeyReplacementString,
		RewardUnitsMultiplier:            fmt.Sprint(rewardUnitsInfo.multiplier),
		RewardUnitsBeforeMultiplierStage: fmt.Sprint(rewardUnitsBeforeMultiplierStage),
	}
	rewardOptionDisplayConfig := rewardConfigOption.GetDisplayConfig()

	afterClaimDisplayText := re.evaluateTemplateTextExpression(rewardOptionDisplayConfig.GetAfterClaimTextExpression(), fact, paramsMap)
	beforeClaimDisplayText := re.evaluateTemplateTextExpression(rewardOptionDisplayConfig.GetBeforeClaimTextExpression(), fact, paramsMap)

	var htmlFormattedDetails []*rewardsPb.RewardOptionDisplay_HtmlFormattedDetail
	for _, htmlFormattedDetail := range rewardOptionDisplayConfig.GetHtmlFormattedDetails() {
		htmlFormattedDetails = append(htmlFormattedDetails, &rewardsPb.RewardOptionDisplay_HtmlFormattedDetail{
			Header: htmlFormattedDetail.GetHeader(),
			Body:   htmlFormattedDetail.GetBody(),
		})
	}

	var rewardTags []rewardsPb.RewardTag
	rewardTags = append(rewardTags, rewardConfigOption.GetDisplayConfig().GetTags()...)
	for _, calcEntry := range rewardUnitsInfo.rewardUnitsCalculationInfo.GetRewardUnitsCalculationEntries() {
		rewardTags = lo.Union(rewardTags, calcEntry.GetDisplayDetails().GetTags())
	}

	return &rewardsPb.RewardOptionDisplay{
		Title:                 beforeClaimDisplayText,
		BeforeClaimTitle:      beforeClaimDisplayText,
		AfterClaimTitle:       afterClaimDisplayText,
		BeforeClaimBannerText: rewardConfigOption.GetDisplayConfig().GetBeforeClaimBannerText(),
		Icon:                  rewardOptionDisplayConfig.GetIcon(),
		BgColor:               rewardOptionDisplayConfig.GetBgColor(),
		HtmlFormattedDetails:  htmlFormattedDetails,
		Tags:                  rewardTags,
	}
}

//nolint:funlen, govet
func (re *CustomRuleEngine) CalculateRewards(ctx context.Context, fact common.IFact) (*model.Reward, error) {
	// todo (utkarsh) : uncomment it after debugging the high iops issue
	// // do a soft check if offer inventory is remaining for the actor.
	// // hard check on inventory is done at persistence layer for achieving strong consistency.
	// isInventoryAlreadyExhausted, err := re.isOfferInventoryAlreadyExhausted(ctx, fact.GetRewardOffer(), fact.GetActorId())
	// switch {
	// // muting this error as isOfferInventoryAlreadyExhausted is a soft check at this stage and should not block reward generation.
	// // hard check for inventory is enforced again at persistence layer.
	// case err != nil:
	// 	logger.WarnWithCtx(ctx, "error checking if offer inventory is already exhausted or not", zap.String(logger.REWARD_OFFER_ID, fact.GetRewardOffer().GetId()), zap.String(logger.ACTOR_ID, fact.GetActorId()), zap.Error(err))
	// case isInventoryAlreadyExhausted:
	// 	logger.Info(ctx, "not generating reward, offer inventory is already exhausted for the actor", zap.String(logger.REWARD_OFFER_ID, fact.GetRewardOffer().GetId()), zap.String(logger.ACTOR_ID, fact.GetActorId()))
	// 	return nil, nil
	// }

	res, err := re.EvaluateConstraint(fact, fact.GetRewardOffer().ConstraintMeta.Expression)
	if err != nil {
		return nil, err
	}
	if !res {
		return nil, nil
	}
	reward := &model.Reward{
		RefId:           fact.GetRefId(),
		SecondaryRefId:  rewardsPkg.RewardsSecondaryRefIdDefaultValue,
		ActorId:         fact.GetActorId(),
		OfferId:         fact.GetRewardOffer().Id,
		OfferType:       fact.GetRewardOffer().GetOfferType(),
		ActionType:      fact.GetActionType(),
		ActionTimestamp: fact.GetActionTime().AsTime(),
		Aggregates:      fact.GetRewardOffer().RewardMeta.RewardAggregates,
		RewardDisplay: &rewardsPb.RewardDisplay{
			TileBgImageAfterClaim:  fact.GetRewardOffer().GetRewardMeta().GetRewardDisplayMeta().GetTileBgImageAfterClaim(),
			TileBgImageBeforeClaim: fact.GetRewardOffer().GetRewardMeta().GetRewardDisplayMeta().GetTileBgImageBeforeClaim(),
			RewardTileThemeType:    fact.GetRewardOffer().GetRewardMeta().GetRewardDisplayMeta().GetRewardTileThemeType(),
			IsAnimationUnskippable: fact.GetRewardOffer().GetRewardMeta().GetRewardDisplayMeta().GetIsAnimationUnskippable(),
			SkipAnimation:          fact.GetRewardOffer().GetRewardMeta().GetRewardDisplayMeta().GetSkipAnimation(),
			AnimationSpeed:         fact.GetRewardOffer().GetRewardMeta().GetRewardDisplayMeta().GetAnimationSpeedWithDefault(),
		},
		IsVisible:      fact.GetRewardOffer().RewardMeta.IsVisible,
		ExternalRef:    fact.GetExternalRefId(),
		GlobalDedupeId: fact.GetRewardGlobalDedupeId(),
		Tags:           fact.GetRewardOffer().GetRewardMeta().GetRewardTags(),
		RewardMetadata: &rewardsPb.RewardMetadata{
			OfferTypeSpecificMetadata: fact.GetRewardOfferTypeSpecificRewardMetadata(),
		},
		ClaimType: getClaimTypeForReward(fact.GetRewardOffer()),
	}

	if fact.GetRewardOffer().GetRewardMeta().GetRewardExpiryTimeConfig().IsNonZero() {
		// getting delay time for the notification
		expiresAt, getRewardConfigDateErr := rewardsPb.GetRewardConfigDate(fact.GetRewardOffer().GetRewardMeta().GetRewardExpiryTimeConfig(), time.Now())
		if getRewardConfigDateErr != nil {
			return nil, fmt.Errorf("error in GetRewardConfigDate func, err: %w", getRewardConfigDateErr)
		}
		reward.ExpiresAt = expiresAt.AsTime()
	}

	// populate reward with any additional information specific to the offer type
	err = populateRewardAdditionalInfo(reward, fact)
	if err != nil {
		return nil, fmt.Errorf("error while populating offer type specific additional information related for reward. err: %w", err)
	}

	// if offer belongs to a group, enrich reward with group id and aggregates.
	offerGroupId := fact.GetRewardOffer().GetGroupId()
	if offerGroupId != "" {
		offerGroup, err1 := re.offerGroupDao.FetchOfferGroupById(ctx, offerGroupId)
		if err1 != nil {
			logger.Error(ctx, "error fetching offer group by id", zap.String("offer_group_id", offerGroupId), zap.String(logger.REWARD_OFFER_ID, fact.GetRewardOffer().GetId()), zap.Error(err))
			return nil, errors.Wrap(err1, "error fetching offer group by id")
		}
		reward.OfferGroupId = offerGroupId
		reward.OfferGroupActorAggregate = offerGroup.GetUserRewardAggregate()
		reward.OfferGroupRewardUnitsCapActorConfig = offerGroup.GetRewardUnitsCapUserAggregate()
		reward.OfferGroupRewardUnitsMonthlyCapActorConfig = offerGroup.GetRewardUnitsCapUserMonthlyAggregate()
	}

	if fact.GetRewardsCappingInfos() != nil {
		var cappingInfos []*model.RewardsCappingInfo
		for _, cappingInfo := range fact.GetRewardsCappingInfos() {
			cappingInfos = append(cappingInfos, &model.RewardsCappingInfo{
				CappingEffectiveDate: cappingInfo.CappingEffectiveDate,
				CapCount:             cappingInfo.CapCount,
				CappingPeriod:        cappingInfo.CappingPeriod,
				CappingDuration:      cappingInfo.CappingDuration,
			})
		}
		reward.RewardsCappingInfos = cappingInfos
	}
	if fact.GetRewardsCountDuringGeneration() != nil {
		reward.RewardsCountDuringGeneration = &model.RewardsCountInfo{
			TotalRewardsGivenForRewardOfferGroup: fact.GetRewardsCountDuringGeneration().TotalRewardsGivenForRewardOfferGroup,
		}
	}

	rewardMeta := fact.GetRewardOffer().RewardMeta

	// map of reward option index to reward units given in that reward option.
	// required for evaluating of rewardOfferPb.RewardConfigOption_DeriveFromSeedRewardOptionConfig unit config
	optionIndexToRewardUnitsMap := make(map[int]uint32, len(rewardMeta.GetRewardConfigOptions()))

	var (
		// areAllOptionsOfNoRewardType checks whether the whole reward will be NO_REWARD type by checking for all its options
		areAllOptionsOfNoRewardType    = true
		rewardOptions                  []*rewardsPb.RewardOption
		adjustmentRewardOptions        []*rewardsPb.RewardOption
		isOriginalRewardOptionChosen   = false
		adjustmentRewardSecondaryRefId string
	)
	for idx, rewardConfigOption := range rewardMeta.RewardConfigOptions {
		// check if reward config option constraint is satisfied, only then the config option should be used to generate a reward option.
		isConfigOptionConstraintSatisfied, evalErr := re.evaluateRewardConfigOptionConstraint(rewardConfigOption, fact)
		if evalErr != nil {
			return nil, evalErr
		}
		if !isConfigOptionConstraintSatisfied {
			continue
		}

		rewardUnitsInfo, err1 := re.getRewardUnits(ctx, rewardConfigOption, fact, optionIndexToRewardUnitsMap, reward)
		if err1 != nil {
			switch {
			case errors.Is(err1, internalerrors.RewardUnitsMaxCapReached):
				// not giving out any reward in case the reward-units got capped to 0.
				// NOTE: this is different than the reward-units getting calculated as 0 prior to capping itself.
				// TODO(rohanchougule): see if we can handle this in a neater manner
				logger.Info(ctx, "reward-units max cap reached in rule-engine. Thus, not giving any reward", zap.String(logger.ACTOR_ID, fact.GetActorId()),
					zap.String(logger.REWARD_OFFER_ID, fact.GetRewardOffer().GetId()), zap.String(logger.REFERENCE_ID, fact.GetRefId()),
				)
				return nil, nil
			default:
				return nil, errors.Wrap(err1, "unable to calculate reward units")
			}
		}

		logger.Debug(ctx, "calculate reward units", zap.String(logger.REWARD_OFFER_ID,
			fact.GetRewardOffer().GetId()), zap.Any("units", rewardUnitsInfo), zap.Float64("rewardUnits", rewardUnitsInfo.rewardUnits))

		rewardUnits := uint32(rewardUnitsInfo.rewardUnits)
		optionIndexToRewardUnitsMap[idx] = rewardUnits

		display := re.getOptionDisplay(rewardConfigOption, fact, rewardUnitsInfo)

		rewardOption := &rewardsPb.RewardOption{
			// id to be used as ref while choosing the option
			Id:                         uuid.New().String(),
			RewardType:                 rewardConfigOption.RewardType,
			Display:                    display,
			RewardUnitsCalculationInfo: rewardUnitsInfo.rewardUnitsCalculationInfo,
			RewardProcessingTimeConfig: rewardConfigOption.RewardProcessingTimeConfig,
			ProductSku:                 rewardConfigOption.GetProductSku(),
		}
		switch rewardConfigOption.RewardType {
		case rewardsPb.RewardType_CASH:
			cashTxnRemark := re.evaluateTemplateTextExpression(rewardConfigOption.GetCashConfig().GetTxnRemarkExpression(), fact, nil)
			rewardOption.Option = &rewardsPb.RewardOption_Cash{
				Cash: &rewardsPb.Cash{
					Amount: &money.Money{
						CurrencyCode: "INR",
						Units:        int64(rewardUnits),
					},
					TxnRemark: cashTxnRemark,
				},
			}
		case rewardsPb.RewardType_FI_COINS:
			rewardOption.Option = &rewardsPb.RewardOption_FiCoins{
				FiCoins: &rewardsPb.FiCoins{
					Units:      rewardUnits,
					ExpiresAt:  accrualconf.FetchDefaultCurrencyExpiryTimestamp(accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now())),
					IsFiPoints: rewardConfigOption.GetFiCoinsRewardConfig().GetIsFiPoints().ToBool(),
				},
			}
		case rewardsPb.RewardType_LUCKY_DRAW:
			luckyDrawId := rewardConfigOption.GetLuckyDrawConfig().GetLuckyDrawId()

			// if lucky draw registration is already over then reward cannot be generated so return nil
			getLuckyDrawRes, getLuckyDrawErr := re.luckyDrawSvcClient.GetLuckyDrawById(ctx, &luckydrawPb.GetLuckyDrawByIdRequest{Id: luckyDrawId})
			if getLuckyDrawErr != nil || !getLuckyDrawRes.GetStatus().IsSuccess() {
				logger.Error(ctx, "luckyDrawSvcClient.GetLuckyDrawById rpc call failed", zap.String(logger.LUCKY_DRAW_ID, luckyDrawId), zap.Any(logger.RPC_STATUS, getLuckyDrawRes.GetStatus()), zap.Error(getLuckyDrawErr))
				return nil, errors.New("luckyDrawSvcClient.GetLuckyDrawById rpc call failed")
			}
			if getLuckyDrawRes.GetLuckyDraw().GetRegistrationTill().AsTime().Before(time.Now()) {
				logger.WarnWithCtx(ctx, "cannot generate lucky draw reward, lucky draw registration is already over", zap.String(logger.ACTOR_ID, fact.GetActorId()), zap.String(logger.LUCKY_DRAW_ID, luckyDrawId), zap.String(logger.REWARD_OFFER_ID, fact.GetRewardOffer().GetId()))
				return nil, nil
			}

			rewardOption.Option = &rewardsPb.RewardOption_LuckyDraw{
				LuckyDraw: &rewardsPb.LuckyDraw{
					LuckyDrawId: luckyDrawId,
				},
			}
		case rewardsPb.RewardType_SMART_DEPOSIT:
			// used to calculate maturity date at time of claim
			maturityDateConfig := rewardConfigOption.GetSmartDepositConfig().GetMaturityDateConfig()
			rewardOption.Option = &rewardsPb.RewardOption_SmartDeposit{
				SmartDeposit: &rewardsPb.SmartDeposit{
					Amount: &money.Money{
						CurrencyCode: "INR",
						Units:        int64(rewardUnits),
					},
					MaturityDateConfig: maturityDateConfig,
				},
			}
		case rewardsPb.RewardType_GIFT_HAMPER:
			// config for creating gift hamper reward option
			giftHamperConfig := rewardConfigOption.GetGiftHamperConfig()
			rewardOption.Option = &rewardsPb.RewardOption_GiftHamper{
				GiftHamper: &rewardsPb.GiftHamper{
					VendorName:      giftHamperConfig.GetVendorName(),
					VendorProductId: giftHamperConfig.GetVendorProductId(),
					ProductName:     giftHamperConfig.GetProductName(),
				},
			}
		case rewardsPb.RewardType_METAL_CREDIT_CARD:
			rewardOption.Option = &rewardsPb.RewardOption_MetalCreditCard{
				MetalCreditCard: &rewardsPb.MetalCreditCard{},
			}
		case rewardsPb.RewardType_EGV_BASKET:
			rewardOption.Option = &rewardsPb.RewardOption_EgvBasket{
				EgvBasket: &rewardsPb.EgvBasket{
					EgvOfferIds: rewardConfigOption.GetEgvBasketConfig().GetEgvOfferIds(),
				},
			}
		case rewardsPb.RewardType_THRIWE_BENEFITS_PACKAGE:
			rewardOption.Option = &rewardsPb.RewardOption_ThriweBenefitsPackage{
				ThriweBenefitsPackage: &rewardsPb.ThriweBenefitsPackage{
					OfferId: rewardConfigOption.GetThriweBenefitsPackageConfig().GetOfferId(),
				},
			}

		case rewardsPb.RewardType_US_STOCK:
			rewardOption.Option = &rewardsPb.RewardOption_UsstockReward{
				UsstockReward: &rewardsPb.USStockReward{
					Amount: &money.Money{
						CurrencyCode: "INR",
						Units:        int64(rewardUnits),
					},
					StockId: rewardConfigOption.GetUsstockRewardConfig().GetStockId(),
				},
			}
		case rewardsPb.RewardType_CREDIT_CARD_BILL_ERASER:
			rewardOption.Option = &rewardsPb.RewardOption_CreditCardBillEraser{
				CreditCardBillEraser: &rewardsPb.CreditCardBillEraser{
					Amount: &money.Money{
						CurrencyCode: "INR",
						Units:        int64(rewardUnits),
					},
				},
			}

		default:
			return nil, fmt.Errorf("failed to build reward option: unimplemented reward type, rewardType: %s", rewardConfigOption.GetRewardType().String())
		}

		// if the reward value is zero for the reward option, update the option to make it a 'NO_REWARD' type reward option.
		if isZeroValuedRewardOption(rewardOption) {
			updatedRewardOption := &rewardsPb.RewardOption{
				Id:         rewardOption.GetId(),
				RewardType: rewardsPb.RewardType_NO_REWARD,
				Display: &rewardsPb.RewardOptionDisplay{
					BeforeClaimTitle: "No reward 😢",
					AfterClaimTitle:  "No reward 😢",
				},
				Option: &rewardsPb.RewardOption_NoReward{
					NoReward: &rewardsPb.NoReward{
						// TODO(utkarsh) : add if anything is required
					},
				},
				RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
					Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{
						RelativeTimeInMinutes: 0,
					},
				},
			}
			rewardOption = updatedRewardOption
		} else {
			// setting areAllOptionsOfNoRewardType to false even if one reward is other than NO_REWARD type,
			// so that user receives the reward
			areAllOptionsOfNoRewardType = false
		}

		if rewardConfigOption.GetShouldGenerateFromProjections() && len(rewardUnitsInfo.projectionIdsToOptionMap) > 0 {
			if reward.RewardAdditionalInfo == nil {
				reward.RewardAdditionalInfo = &model.RewardAdditionalInfo{ProjectionIdToContributionsMap: map[string]*projectorPb.OptionsInfo{}}
			}
			// merge projections and add to additionalInfo
			reward.RewardAdditionalInfo.ProjectionIdToContributionsMap = re.mergeProjectionDetailsMap(reward.RewardAdditionalInfo.ProjectionIdToContributionsMap, rewardUnitsInfo.projectionIdsToOptionMap)

			if rewardUnitsInfo.adjustmentRewardOptionDetails != nil && rewardUnitsInfo.adjustmentRewardOptionDetails.isAdjustmentReward {
				adjustmentRewardOptions = append(adjustmentRewardOptions, rewardOption)
				adjustmentRewardSecondaryRefId = rewardUnitsInfo.adjustmentRewardOptionDetails.adjustmentRewardSecondaryRefId
				// isOriginalRewardOptionChosen can be true only if adjustment reward option is generated
				if rewardUnitsInfo.adjustmentRewardOptionDetails.isOriginalRewardOptionChosen {
					isOriginalRewardOptionChosen = true
					// only projections corresponding to original reward should be updated in db
					reward.RewardAdditionalInfo.ProjectionIdToContributionsMap = re.mergeProjectionDetailsMap(map[string]*projectorPb.OptionsInfo{}, rewardUnitsInfo.projectionIdsToOptionMap)
				}
			}
		}

		rewardOptions = append(rewardOptions, rewardOption)
	}
	// since no reward options are there, reward cannot be generated, so returning nil
	if len(rewardOptions) == 0 {
		logger.Info(ctx, "no reward options are generated", zap.String(logger.ACTOR_ID, fact.GetActorId()), zap.String(logger.REFERENCE_ID, fact.GetRefId()))
		return nil, nil
	}

	if len(adjustmentRewardOptions) > 0 {
		// For adjustment reward, if original reward is not chosen then count of total option generated should be equal to count of adjustment reward options generated
		// This condition can fail if Adjustment reward option is not generated for all the options,
		// because if original reward have two options [o1, o2] and user choose o2 and adjustment reward is generated only for o1 then user will get both: o2 + adjustment against o1 (which is not expected)
		if !isOriginalRewardOptionChosen && len(rewardOptions) != len(adjustmentRewardOptions) {
			logger.WarnWithCtx(ctx, "number of adjustment reward options generated is not equal to total options generated",
				zap.String(logger.REFERENCE_ID, fact.GetRefId()), zap.String(logger.REWARD_OFFER_ID, fact.GetRewardOffer().GetId()), zap.String(logger.ACTOR_ID_V2, fact.GetActorId()),
				zap.Int("totalOptionsCount", len(rewardOptions)), zap.Int("adjustmentOptionsCount", len(adjustmentRewardOptions)))
			return nil, nil
		}

		// if original reward is already chosen then adjustment reward should be generated only against already chosen option
		if isOriginalRewardOptionChosen {
			if len(adjustmentRewardOptions) != 1 {
				logger.Error(ctx, "count of adjustment reward options generated for already chosen original reward is not 1", zap.Int(logger.LENGTH, len(rewardOptions)))
				return nil, fmt.Errorf("count of adjustment reward options generated for already chosen original reward is not 1")
			}
			rewardOptions = adjustmentRewardOptions
		}

		reward.SecondaryRefId = adjustmentRewardSecondaryRefId
	}

	var unlockDate *timestampPb.Timestamp
	if rewardMeta.GetRewardLockTimeConfig().IsNonZero() {
		unlockDate, err = rewardsPb.GetRewardConfigDate(rewardMeta.GetRewardLockTimeConfig(), reward.ActionTimestamp)
		if err != nil {
			return nil, errors.Wrap(err, "Unable to calculate unlock date")
		}
	}

	var autoClaimTime *timestampPb.Timestamp
	if rewardMeta.GetAutoClaimTimeConfig().IsNonZero() {
		autoClaimTime, err = rewardsPb.GetRewardConfigDate(rewardMeta.GetAutoClaimTimeConfig(), reward.ActionTimestamp)
		if err != nil {
			return nil, errors.Wrap(err, "error calculating reward auto-claim time")
		}
	}

	actionDesc := re.evaluateTemplateTextExpression(fact.GetRewardOffer().GetDisplayMeta().ActionDesc, fact, nil)
	reward.RewardOptions = &rewardsPb.RewardOptions{
		DefaultDecideTimeInSecs:   rewardMeta.DefaultDecideTimeInSecs,
		UnlockDate:                unlockDate,
		Options:                   rewardOptions,
		ActionDetails:             actionDesc,
		IsUnlockedForMinKyc:       fact.GetRewardOffer().GetRewardMeta().GetIsUnlockedForMinKyc(),
		IsImplicitLockingDisabled: fact.GetRewardOffer().GetRewardMeta().GetIsImplicitLockingDisabled(),
		AutoClaimTime:             autoClaimTime,
	}

	// add status to the generated reward (LOCKED or CREATED). It requires checking
	// user's state (KYC and SA), and reward offer's details. Hence, checking it at
	// last so that we may only decide if reward is to be generated in LOCKED state
	// at last, when we know that the reward is to be generated, i.e. all conditions
	// for generating reward have passed and capping isn't hit.
	rewardStatus, rewardSubStatus, err := re.getRewardStatusForRewardGeneratedFromOffer(ctx, fact, areAllOptionsOfNoRewardType)
	if err != nil {
		return nil, fmt.Errorf("error while getting status for the generated reward, err: %w", err)
	}
	reward.Status = rewardStatus
	reward.SubStatus = rewardSubStatus

	// if reward is to be auto processed (and is not in LOCKED state) then following params need to be set.
	// duplicated from RewardsService.ChooseReward rpc  todo (utkarsh) : can we avoid duplication ?
	// todo (utkarsh) : add a check for len(rewardConfigOptions) == 1 for auto process rewardOffer during creation ?
	if rewardMeta.GetAutoProcessReward() && reward.Status != rewardsPb.RewardStatus_LOCKED || areAllOptionsOfNoRewardType {
		reward.Status = rewardsPb.RewardStatus_PROCESSING_PENDING
		// it is assumed that auto processed reward would have a single option
		// and that option should get auto-processed immediately
		processOption := *rewardOptions[0]
		processOption.ProcessingDate = ptypes.TimestampNow()
		reward.RewardType = processOption.RewardType
		reward.ChosenReward = &processOption
	}
	return reward, nil
}

func (re *CustomRuleEngine) isOfferInventoryAlreadyExhausted(ctx context.Context, offer *rewardOfferPb.RewardOffer, actorId string) (bool, error) {
	if offer == nil || actorId == "" {
		return false, errors.New("offer or actor id is empty")
	}
	// check if actor level offer inventory is exhausted
	actorLevelOfferInventory, err := re.rewardOfferDao.GetActorLevelRewardOfferInventory(ctx, actorId, []string{offer.GetId()})
	if err != nil {
		return false, errors.Wrap(err, "error fetching actor level offer inventory")
	}
	// if actor level inventory is present, check if its already exhausted or not.
	if len(actorLevelOfferInventory) > 0 {
		inventory := actorLevelOfferInventory[0]
		// if total count is zero then it implies inventory is infinite so ignoring the inventory check
		if inventory.GetTotalCount() > 0 && inventory.GetRemainingCount() == 0 {
			return true, nil
		}
	}

	// check if action level offer inventory is exhausted
	actionLevelOfferInventory, err := re.rewardOfferDao.GetActionLevelRewardOfferInventory(ctx, []string{offer.GetId()})
	if err != nil {
		return false, errors.Wrap(err, "error fetching action level offer inventory")
	}
	// if action level inventory is present, check if its already exhausted or not.
	if len(actionLevelOfferInventory) > 0 {
		inventory := actionLevelOfferInventory[0]
		// if total count is zero then it implies inventory is infinite so ignoring the inventory check
		if inventory.GetTotalCount() > 0 && inventory.GetRemainingCount() == 0 {
			return true, nil
		}
	}

	// if offer in a part of an offer group, check if actor level offer group inventory is exhausted
	if offer.GetGroupId() != "" {
		actorLevelGroupInventory, err := re.offerGroupDao.GetActorLevelOfferGroupInventory(ctx, actorId, []string{offer.GetGroupId()})
		if err != nil {
			return false, errors.Wrap(err, "error fetching offer group level inventory for actor")
		}
		// if group level inventory is present, check if its already exhausted or not.
		if len(actorLevelGroupInventory) > 0 {
			inventory := actorLevelGroupInventory[0]
			// if total count is zero then it implies inventory is infinite so ignoring the inventory check
			if inventory.GetTotalCount() > 0 && inventory.GetRemainingCount() == 0 {
				return true, nil
			}
		}
	}
	return false, nil
}

// getRewardStatusForRewardGeneratedFromOffer returns the status of a reward that's being generated for the given offer.
// reward will be generated in LOCKED state if:
// 1. An UnlockEvent is specified in the reward offer OR GetRewardLockTimeConfig gives an unlock time of at least 1 minute in the future.
// 2. User is MinKYC or FI-Lite user and `IsImplicitLockingDisabled` (part of offer.RewardMeta) is not set.
// *** NOTE *** - Currently we're fetching KYC status and FI SA status separately.
// this can be merged into a single status check in future when support for a single user state is introduced.
func (re *CustomRuleEngine) getRewardStatusForRewardGeneratedFromOffer(ctx context.Context, fact common.IFact, areAllOptionsOfNoRewardType bool) (rewardsPb.RewardStatus, rewardsPb.SubStatus, error) {
	var (
		actorId    = fact.GetActorId()
		offer      = fact.GetRewardOffer()
		actionTime = fact.GetActionTime().AsTime()
	)

	if areAllOptionsOfNoRewardType {
		return rewardsPb.RewardStatus_PROCESSING_PENDING, rewardsPb.SubStatus_SUB_STATUS_UNSPECIFIED, nil
	}
	if !re.dynConf.Flags().EnableGenerationOfRewardsInLockedState() {
		return rewardsPb.RewardStatus_CREATED, rewardsPb.SubStatus_SUB_STATUS_UNSPECIFIED, nil
	}

	var (
		unlockTime *timestampPb.Timestamp
		err        error
	)
	if offer.GetRewardMeta().GetRewardLockTimeConfig().IsNonZero() {
		unlockTime, err = rewardsPb.GetRewardConfigDate(offer.GetRewardMeta().GetRewardLockTimeConfig(), actionTime)
		if err != nil {
			logger.Error(ctx, "error while getting configured unlock time from config", zap.Error(err))
		}
	}
	// if offer is EXPLICITLY configured to be generated in locked state, we don't
	// need to check user's state and can directly generate reward in locked state.

	// we're comparing the unlock time with now() + some delta and only generating a
	// locked reward if the unlock time is more than this as sometimes the unlock
	// time could be configured with `0` as the relative unlock time after reward
	// generation. in such cases, we don't want the reward to be actually generated
	// in LOCKED state.
	if offer.GetUnlockEvent() != rewardsPb.CollectedDataType_UNSPECIFIED_COLLECTED_DATA_TYPE || unlockTime.AsTime().After(time.Now().Add(re.conf.MinTimeDelayBetweenNowAndUnlockDateForLockingReward)) {
		return rewardsPb.RewardStatus_LOCKED, rewardsPb.SubStatus_SUB_STATUS_EXPLICITLY_LOCKED, nil
	}
	// checking for IMPLICIT locking of reward based on user's state
	if !offer.GetRewardMeta().GetIsImplicitLockingDisabled() {
		// a reward will be generated in IMPLICITLY_LOCKED state when implicit locking is applicable
		isImplicitLockingApplicable, checkErr := re.userHelperService.IsImplicitRewardLockingApplicableForActor(ctx, actorId)
		if checkErr != nil {
			logger.Error(ctx, "error while checking if implicit locking is applicable for user or not", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(checkErr))
			return rewardsPb.RewardStatus_CREATED, rewardsPb.SubStatus_SUB_STATUS_UNSPECIFIED, fmt.Errorf("error while checking if implicit locking is applicable for user or not, err: %w", checkErr)
		}

		if isImplicitLockingApplicable {
			return rewardsPb.RewardStatus_LOCKED, rewardsPb.SubStatus_SUB_STATUS_IMPLICITLY_LOCKED, nil
		}
	}
	return rewardsPb.RewardStatus_CREATED, rewardsPb.SubStatus_SUB_STATUS_UNSPECIFIED, nil
}

// isZeroValuedRewardOption returns true if reward amount for the option is zero.
func isZeroValuedRewardOption(option *rewardsPb.RewardOption) bool {
	// zero value check is applicable on following reward types only.
	switch option.GetRewardType() {
	case rewardsPb.RewardType_FI_COINS:
		return option.GetFiCoins().GetUnits() == 0
	case rewardsPb.RewardType_SMART_DEPOSIT:
		return option.GetSmartDeposit().GetAmount().GetUnits() == 0
	case rewardsPb.RewardType_CASH:
		return option.GetCash().GetAmount().GetUnits() == 0
	case rewardsPb.RewardType_CREDIT_CARD_BILL_ERASER:
		return option.GetCreditCardBillEraser().GetAmount().GetUnits() == 0
	case rewardsPb.RewardType_EGV_BASKET:
		return len(option.GetEgvBasket().GetEgvOfferIds()) == 0
	case rewardsPb.RewardType_THRIWE_BENEFITS_PACKAGE:
		return len(option.GetThriweBenefitsPackage().GetOfferId()) == 0
	default:
		return false
	}
}

// getRewardCalculationDisplayFromBoosterUnitsConfig generates the display related details for reward-calculation-entry from the boosters' units-config display details
func getRewardCalculationDisplayFromBoosterUnitsConfig(_ context.Context, boosterDisplayConfig *rewardOfferPb.ConditionalExpressionBoosterConfig_ConfigUnit_DisplayDetails) *rewardsPb.RewardUnitsCalculationInfo_RewardUnitsCalculationEntry_DisplayDetails {
	return &rewardsPb.RewardUnitsCalculationInfo_RewardUnitsCalculationEntry_DisplayDetails{
		Title:      boosterDisplayConfig.GetTitle(),
		TitleColor: boosterDisplayConfig.GetTitleColor(),
		BgColor:    boosterDisplayConfig.GetBgColor(),
		Tags:       boosterDisplayConfig.GetTags(),
	}
}

// populateRewardAdditionalInfo populates the reward with any additional
// information that's needed to be persisted along with the reward
func populateRewardAdditionalInfo(reward *model.Reward, fact common.IFact) error {
	// populate credit card details if present in fact
	if fact.GetRewardAdditionalInfo() != nil && fact.GetRewardAdditionalInfo().CreditCardRewardAdditionalDetails != nil {
		if fact.GetRewardAdditionalInfo().CreditCardRewardAdditionalDetails.CreditCardAccountId == "" {
			return fmt.Errorf("CC account id not found in fact")
		}
		reward.RewardAdditionalInfo = &model.RewardAdditionalInfo{
			CreditCardRewardAdditionalInfo: &model.CreditCardRewardAdditionalInfo{
				CreditCardAccountId:  fact.GetRewardAdditionalInfo().CreditCardRewardAdditionalDetails.CreditCardAccountId,
				CreditCardId:         fact.GetRewardAdditionalInfo().CreditCardRewardAdditionalDetails.CreditCardId,
				CreditCardSchemeCode: fact.GetRewardAdditionalInfo().CreditCardRewardAdditionalDetails.CreditCardSchemeCode,
				TxnMerchantId:        fact.GetRewardAdditionalInfo().CreditCardRewardAdditionalDetails.TxnMerchantId,
				// todo(divyadeep): rename this and move it to model.Reward (ActionTime)
				// todo: also add txnTime separately in cc_rewards_info table
				TxnTime: fact.GetActionTime().AsTime(),
			},
		}
	}
	return nil
}

// getClaimTypeForReward returns CLAIM_TYPE_AUTOMATIC if there's an auto
// processing config present for the reward offer, otherwise CLAIM_TYPE_MANUAL
func getClaimTypeForReward(rewardOffer *rewardOfferPb.RewardOffer) rewardsPb.ClaimType {
	if rewardOffer.GetRewardMeta().GetAutoProcessReward() ||
		rewardOffer.GetRewardMeta().GetAutoClaimTimeConfig().GetAbsoluteTime() != nil ||
		rewardOffer.GetRewardMeta().GetAutoClaimTimeConfig().GetRelativeTimeInMinutes() != 0 {
		return rewardsPb.ClaimType_CLAIM_TYPE_AUTOMATIC
	}
	return rewardsPb.ClaimType_CLAIM_TYPE_MANUAL
}

func (re *CustomRuleEngine) getProjectionGenerationEventsRefs(ctx context.Context, fact common.IFact) ([]string, error) {
	switch fact.GetActionType() {
	case rewardsPb.CollectedDataType_CREDIT_CARD_BILLING:
		ccBillingFact, ok := fact.(*creditcardbilling.CcBillingFact)
		if !ok {
			return nil, fmt.Errorf("couldn't convert fact to CC billing required type")
		}
		txnIdsRes, err := re.ffAccClient.GetTransactionIdsByBillId(ctx, &ffAccountsPb.GetTransactionIdsByBillIdRequest{
			BillId: ccBillingFact.CreditCardBillingEvent.GetBillId(),
		})
		switch {
		case txnIdsRes.GetStatus().IsRecordNotFound():
			return nil, epifierrors.ErrRecordNotFound
		case err != nil || !txnIdsRes.GetStatus().IsSuccess():
			return nil, fmt.Errorf("error while fetching transactions for bill, err: %w, rpcStatus: %s", err, txnIdsRes.GetStatus().String())
		default:
			externalTxIds := lo.Map(txnIdsRes.GetTransactionIdResponses(), func(item *ffAccountsPb.GetTransactionIdsByBillIdResponse_TransactionIdResponse, i int) string {
				return item.GetExternalTxnId()
			})

			return externalTxIds, nil
		}
	default:
		return nil, fmt.Errorf("unsupported event encountered during fetching of projection generating event refs for event, eventType: %s", fact.GetActionType().String())
	}
}

// nolint:funlen
func (re *CustomRuleEngine) calculateRewardUnitsUsingProjections(ctx context.Context, fact common.IFact, rewardConfigOption *rewardOfferPb.RewardConfigOption) (*RewardUnitsInfo, error) {
	var (
		pageCtxReq     *rpc.PageContextRequest
		pageSize       = 30
		requiredFields = []projectorPb.ProjectionFieldMask{
			projectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_ID,
			projectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_PROJECTED_OPTIONS,
			projectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_REWARD_ID,
			projectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_ACTION_TIME,
			projectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_ACTION_TYPE,
			projectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_OFFER_ID,
			projectorPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_OFFER_TYPE,
		}
		configuredFilters = rewardConfigOption.GetProjectionsQueryFilters()
		rewardType        = rewardConfigOption.GetRewardType()

		// *** NOTE *** only upper cap is evaluated while computing reward from projection
		upperCap       = rewardConfigOption.GetRewardUnitsUpperLimit()
		allProjections []*projectorPb.Projection
	)

	// fetch all references that would've generated projections
	projectionsQueryFilter, err := re.getProjectionsQueryFilter(ctx, fact, configuredFilters)
	if err != nil {
		return nil, fmt.Errorf("error while getting projections query filter, err: %w", err)
	}

	// store all projections in allProjections (in memory)
	for {
		pageToken, err := pagination.GetPageToken(pageCtxReq)
		if err != nil {
			return nil, fmt.Errorf("error while getting page token from pageCtxReq, err: %w", err)
		}

		projections, pageCtxRes, err := re.projectionsDao.FetchPaginatedProjectionsByFilters(ctx, projectionsQueryFilter, pageToken, pageSize, requiredFields)
		if err != nil {
			return nil, fmt.Errorf("error while fetching projections, err: %w", err)
		}
		allProjections = append(allProjections, projections...)

		if !pageCtxRes.GetHasAfter() {
			break
		}

		pageCtxReq = &rpc.PageContextRequest{
			PageSize: uint32(pageSize),
			Token: &rpc.PageContextRequest_AfterToken{
				AfterToken: pageCtxRes.GetAfterToken(),
			},
		}
	}

	if len(allProjections) > 100 {
		logger.WarnWithCtx(ctx, "more than 100 projections found for actor/filter", zap.String(logger.ACTOR_ID_V2, fact.GetActorId()), zap.String(logger.REWARD_OFFER_ID, fact.GetRewardOffer().GetId()), zap.String(logger.EVENT_ID, fact.GetRefId()))
	}

	// order projections by action time as we'll cap them in this order
	sort.Slice(allProjections, func(i, j int) bool {
		return allProjections[i].GetActionTime().AsTime().In(datetime.IST).Before(allProjections[j].GetActionTime().AsTime().In(datetime.IST))
	})

	rewardUnits, projectionIdToRewardContributionMap := re.getRewardUnitsAndProjectionIdsToRewardOptionMap(ctx, allProjections, float32(upperCap), rewardType, fact.GetActionType(), rewardConfigOption.GetRewardUnitsGeneratedFromProjectionUnitCaps())

	// todo(divyadeep/himanshu): add handling for net negative reward units.
	if rewardUnits < 0 {
		logger.Error(ctx, "negative reward units generated using projections, not generating a reward", zap.Float64("rewardUnits", rewardUnits), zap.String(logger.ACTOR_ID_V2, fact.GetActorId()), zap.String(logger.REWARD_OFFER_ID, fact.GetRewardOffer().GetId()), zap.String(logger.EVENT_ID, fact.GetRefId()))
		return nil, fmt.Errorf("negative reward units generated using projections, not generating a reward. err: %w", internalerrors.FactEvaluationPreConditionFailed)
	}

	adjustmentRewardUnitsInfo, err := re.calculateAdjustmentRewardUnitsUsingProjections(ctx, fact, rewardUnits, allProjections, rewardConfigOption)
	if err != nil {
		return nil, fmt.Errorf("error calculating adjustment reward from projection, err: %w", err)
	}
	if adjustmentRewardUnitsInfo != nil && adjustmentRewardUnitsInfo.adjustmentRewardOptionDetails.isAdjustmentReward {
		return adjustmentRewardUnitsInfo, nil
	}

	return &RewardUnitsInfo{
		rewardUnits:              rewardUnits,
		projectionIdsToOptionMap: projectionIdToRewardContributionMap,
	}, nil
}

// calculateAdjustmentRewardUnitsUsingProjections returns RewardUnitsInfo for adjustment reward option If all the conditions to generate an adjustment reward are met else return nil
// All the following condition should meet to generate adjustment reward:
// 1. There should be at-least one un-actualised projection present
// 2. At-least one actualised projection should be present
// 3. Original reward should be present
// 4. If original reward is already chosen then current option's reward type should be same as original reward chosen option reward type
// 5. Already given reward units should be less than currently calculated reward units
// nolint:funlen
func (re *CustomRuleEngine) calculateAdjustmentRewardUnitsUsingProjections(ctx context.Context, fact common.IFact, calculatedRewardUnits float64, allProjections []*projectorPb.Projection, rewardConfigOption *rewardOfferPb.RewardConfigOption) (*RewardUnitsInfo, error) {
	var (
		unActualisedProjections      []*projectorPb.Projection
		rewardType                   = rewardConfigOption.GetRewardType()
		isOriginalRewardOptionChosen bool
	)

	for _, projection := range allProjections {
		if projection.GetRewardId() == "" {
			unActualisedProjections = append(unActualisedProjections, projection)
		}
	}
	// if there is no un-actualised projections then reward adjustment is not required
	if len(unActualisedProjections) == 0 {
		logger.Debug(ctx, "no un-actualised projection present",
			zap.String(logger.REFERENCE_ID, fact.GetRefId()), zap.String(logger.REWARD_OFFER_ID, fact.GetRewardOffer().GetId()), zap.String(logger.ACTOR_ID, fact.GetActorId()))
		return nil, nil
	}

	// if all the projections are un-actualised means original reward is not generated yet
	if len(unActualisedProjections) == len(allProjections) {
		logger.Debug(ctx, "all projections are un-actualised",
			zap.String(logger.REFERENCE_ID, fact.GetRefId()), zap.String(logger.REWARD_OFFER_ID, fact.GetRewardOffer().GetId()), zap.String(logger.ACTOR_ID, fact.GetActorId()))
		return nil, nil
	}

	// keeping page size 20, ASSUMING there won't be more than 19 adjustment reward against an original reward
	alreadyGeneratedRewards, pageCtxRes, err := re.rewardsDao.FetchPaginatedRewardsByFiltersV2(ctx, fact.GetActorId(), &model.QueryRewardsFilterV2{
		AndFilter: &model.AndRewardsFilter{
			RefIds:        []string{fact.GetRefId()},
			RewardOfferId: fact.GetRewardOffer().GetId(),
		},
	}, nil, 20)
	if err != nil {
		return nil, fmt.Errorf("error fetching alreadyGeneratedRewards, err: %w", err)
	}
	// original reward is not generated yet
	if len(alreadyGeneratedRewards) == 0 {
		logger.Debug(ctx, "original reward not generated",
			zap.String(logger.REFERENCE_ID, fact.GetRefId()), zap.String(logger.REWARD_OFFER_ID, fact.GetRewardOffer().GetId()), zap.String(logger.ACTOR_ID, fact.GetActorId()))
		return nil, nil
	}
	// ideally we shouldn't have so many adjustment rewards
	if pageCtxRes.GetHasAfter() {
		return nil, fmt.Errorf("more than 20 rewards generated for a (actor_id, offer_id, ref_id) combination")
	}

	// get original reward out of all rewards generated for a (actor_id, offer_id, ref_id) combination
	originalReward, err := re.getOriginalReward(alreadyGeneratedRewards)
	if err != nil {
		return nil, fmt.Errorf("error getting original reward, err: %w", err)
	}

	if originalReward.ChosenReward != nil {
		if originalReward.ChosenReward.GetRewardType() != rewardType {
			logger.Debug(ctx, "original reward chosen reward type is not same as offer option reward type",
				zap.String("chosenRewardType", originalReward.ChosenReward.GetRewardType().String()), zap.String("rewardType", rewardType.String()),
				zap.String(logger.REFERENCE_ID, fact.GetRefId()), zap.String(logger.REWARD_OFFER_ID, fact.GetRewardOffer().GetId()), zap.String(logger.ACTOR_ID, fact.GetActorId()))
			return nil, nil
		}
		isOriginalRewardOptionChosen = true
	}

	var alreadyGivenRewardUnits float64
	// *** NOTE ***: If multiple options are generated of same type then alreadyGivenRewardUnits could be mis-calculated
	for _, reward := range alreadyGeneratedRewards {
		for _, rewardOption := range reward.RewardOptions.GetOptions() {
			if rewardOption.GetRewardType() == rewardType {
				rewardUnits, err1 := rewardsPb.GetRewardUnitsFromRewardOption(rewardOption)
				if err1 != nil {
					return nil, err1
				}
				alreadyGivenRewardUnits += float64(rewardUnits)
			}
		}
	}

	if alreadyGivenRewardUnits >= calculatedRewardUnits {
		logger.Debug(ctx, "calculatedRewardUnits is less than alreadyGivenRewardUnits",
			zap.Float64("alreadyGivenRewardUnits", alreadyGivenRewardUnits), zap.Float64("calculatedRewardUnits", calculatedRewardUnits),
			zap.String(logger.REFERENCE_ID, fact.GetRefId()), zap.String(logger.REWARD_OFFER_ID, fact.GetRewardOffer().GetId()), zap.String(logger.ACTOR_ID, fact.GetActorId()))
		return nil, nil
	}

	adjustmentRewardUnits := calculatedRewardUnits - alreadyGivenRewardUnits
	var adjustmentRewardUnitsGivenSoFar float64
	unActualisedProjectionIdToRewardContributionMap := make(map[string]*projectorPb.RewardOption)
	for _, unActualisedProjection := range unActualisedProjections {
		for _, projectedOption := range unActualisedProjection.GetProjectedOptions().GetRewardUnitsWithTypes() {
			if projectedOption.GetRewardType() == rewardType {
				if projectedOption.GetRewardUnits() >= 0 {
					switch {
					case adjustmentRewardUnits >= adjustmentRewardUnitsGivenSoFar+float64(projectedOption.GetRewardUnits()):
						unActualisedProjectionIdToRewardContributionMap[unActualisedProjection.GetId()] = &projectorPb.RewardOption{
							RewardType:                 rewardType,
							RewardUnits:                projectedOption.GetRewardUnits(),
							RewardTypeSpecificMetadata: projectedOption.GetRewardTypeSpecificMetadata(),
						}
						adjustmentRewardUnitsGivenSoFar += float64(projectedOption.GetRewardUnits())
					case adjustmentRewardUnits > adjustmentRewardUnitsGivenSoFar:
						unActualisedProjectionIdToRewardContributionMap[unActualisedProjection.GetId()] = &projectorPb.RewardOption{
							RewardType:                 rewardType,
							RewardUnits:                float32(adjustmentRewardUnits - adjustmentRewardUnitsGivenSoFar),
							RewardTypeSpecificMetadata: projectedOption.GetRewardTypeSpecificMetadata(),
						}
						adjustmentRewardUnitsGivenSoFar = adjustmentRewardUnits
					default:
						unActualisedProjectionIdToRewardContributionMap[unActualisedProjection.GetId()] = &projectorPb.RewardOption{
							RewardType:                 rewardType,
							RewardUnits:                ZeroRewardUnits,
							RewardTypeSpecificMetadata: projectedOption.GetRewardTypeSpecificMetadata(),
						}
					}
				} else {
					unActualisedProjectionIdToRewardContributionMap[unActualisedProjection.GetId()] = projectedOption
				}
			}
		}
	}

	logger.Debug(ctx, "adjustment reward option generated", zap.Float64("rewardUnits", adjustmentRewardUnits),
		zap.Any("contributionMap", unActualisedProjectionIdToRewardContributionMap), zap.Bool("isOriginalRewardOptionChosen", isOriginalRewardOptionChosen),
		zap.String(logger.REFERENCE_ID, fact.GetRefId()), zap.String(logger.REWARD_OFFER_ID, fact.GetRewardOffer().GetId()), zap.String(logger.ACTOR_ID, fact.GetActorId()))

	return &RewardUnitsInfo{
		rewardUnits:              adjustmentRewardUnits,
		projectionIdsToOptionMap: unActualisedProjectionIdToRewardContributionMap,
		adjustmentRewardOptionDetails: &AdjustmentRewardOptionDetails{
			isAdjustmentReward:             true,
			isOriginalRewardOptionChosen:   isOriginalRewardOptionChosen,
			adjustmentRewardSecondaryRefId: re.getAdjustmentRewardSecondaryRefId(len(alreadyGeneratedRewards)),
		},
	}, nil
}

// getOriginalReward returns original reward out of all rewards generated for a (actor_id, offer_id, ref_id) combination
func (re *CustomRuleEngine) getOriginalReward(rewards []*model.Reward) (*model.Reward, error) {
	for _, reward := range rewards {
		if reward.SecondaryRefId != rewardsPkg.RewardsSecondaryRefIdDefaultValue && !strings.HasPrefix(reward.SecondaryRefId, adjustmentRewardSecondaryRefIdPrefix) {
			return nil, fmt.Errorf("incorrect secondary ref id present in reward, rewardId: %s", reward.Id)
		}
	}

	originalRewards := lo.Filter(rewards, func(item *model.Reward, index int) bool {
		return item.SecondaryRefId == rewardsPkg.RewardsSecondaryRefIdDefaultValue
	})
	if len(originalRewards) != 1 {
		return nil, fmt.Errorf("incorrect number of original reward present, len: %v", len(originalRewards))
	}

	return originalRewards[0], nil
}

func (re *CustomRuleEngine) getAdjustmentRewardSecondaryRefId(noOfAlreadyGeneratedRewards int) string {
	return adjustmentRewardSecondaryRefIdPrefix + strconv.FormatInt(int64(noOfAlreadyGeneratedRewards), 10)
}

func (re *CustomRuleEngine) getProjectionsQueryFilter(ctx context.Context, fact common.IFact, filtersConfiguredInRewardOffer *rewardOfferPb.RewardConfigOption_ProjectionsQueryFilters) (*model2.QueryProjectionsFilter, error) {
	projectionsQueryFilter := &model2.QueryProjectionsFilter{
		AndFilter: &model2.AndProjectionFilter{
			OfferIds:    filtersConfiguredInRewardOffer.GetRewardOfferIds(),
			OfferTypes:  filtersConfiguredInRewardOffer.GetRewardOfferTypes(),
			ActionTypes: filtersConfiguredInRewardOffer.GetCollectedDataTypes(),
			RewardIds:   []string{""},
		},
	}

	switch fact.GetActionType() {
	case rewardsPb.CollectedDataType_CREDIT_CARD_BILLING:
		ccBillingFact, ok := fact.(*creditcardbilling.CcBillingFact)
		if !ok {
			return nil, fmt.Errorf("couldn't convert fact to CC billing required type")
		}
		// fetch all references that would've generated projections
		eventsRefs, err := re.getProjectionGenerationEventsRefs(ctx, ccBillingFact)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				// since no event refs (txn IDs) are present for the bill, we'll not generate a reward
				return nil, internalerrors.FactEvaluationPreConditionFailed
			}
			return nil, fmt.Errorf("error while fetching projection generating event refs, eventRefId: %s, err: %w", fact.GetRefId(), err)
		}
		projectionsQueryFilter.AndFilter.RefIds = eventsRefs
		projectionsQueryFilter.AndFilter.ActorId = ccBillingFact.GetActorId()
		// todo(divyadeep): uncomment after backfilling accountIds on prod
		// projectionsQueryFilter.AndFilter.AccountId = ccBillingFact.CreditCardBillingEvent.GetAccountId()
	case rewardsPb.CollectedDataType_TIERING_PERIODIC_REWARD_EVENT:
		tieringPeriodicRewardFact, ok := fact.(*tiering.TieringPeriodicRewardFact)
		if !ok {
			return nil, fmt.Errorf("couldn't convert fact to tiering periodic reward required type")
		}
		// fetch all projections generated in the tiering reward period
		projectionsQueryFilter.AndFilter.ActorId = tieringPeriodicRewardFact.GetActorId()
		projectionsQueryFilter.AndFilter.FromTime = tieringPeriodicRewardFact.TieringPeriodicRewardEvent.GetFromTime().AsTime()
		projectionsQueryFilter.AndFilter.UptoTime = tieringPeriodicRewardFact.TieringPeriodicRewardEvent.GetToTime().AsTime()
	default:
		return nil, fmt.Errorf("unsupported event type encountered while trying to generate projections query filter, eventType: %s", fact.GetActionType().String())
	}

	// we're adding uptoTime filter as action time at max as there could be new
	// projections added between replays of events, we may still fetch the same
	// projections, as the newly added projections will have a created_at time of
	// after the event time.
	if projectionsQueryFilter.AndFilter.UptoTime.IsZero() || projectionsQueryFilter.AndFilter.UptoTime.After(fact.GetActionTime().AsTime()) {
		projectionsQueryFilter.AndFilter.UptoTime = fact.GetActionTime().AsTime()
	}

	return projectionsQueryFilter, nil
}

// nolint:funlen
func (re *CustomRuleEngine) getRewardUnitsAndProjectionIdsToRewardOptionMap(ctx context.Context, projections []*projectorPb.Projection, upperCap float32, rewardType rewardsPb.RewardType, actionType rewardsPb.CollectedDataType, rewardUnitsComputedFromProjectionsCaps *rewardOfferPb.RewardUnitsGeneratedFromProjectionUnitCaps) (float64, map[string]*projectorPb.RewardOption) {
	// NOTE: This is a very specific flow wrt to tiering 2% cashback rewards
	// Daily cap logic applied here should NOT be re-used anywhere else
	dailyUpperCap := re.dynConf.TieringPeriodicRewardConfig().RewardTypeToDailyUpperCapMap().Get(rewardType.String())
	if rewardUnitsComputedFromProjectionsCaps.GetDailyUnitsCap() != 0 {
		dailyUpperCap = float32(rewardUnitsComputedFromProjectionsCaps.GetDailyUnitsCap())
	}
	if re.shouldTieringRewardDailyUpperCapBeApplied(actionType, dailyUpperCap, projections, rewardType) {
		logger.Info(ctx, "applying daily upper cap for user", zap.String(logger.ACTOR_ID_V2, projections[0].GetActorId()))
		return re.getTieringRewardUnitsAndProjectionIdsToRewardOptionMapWithDailyUpperCap(projections, dailyUpperCap, upperCap, rewardType)
	}

	// if no upper cap is defined, the actual amount will simply be the sum of all the projections and actual and projected values will be same
	var (
		rewardUnits                   = float32(0)
		projectionIdToRewardOptionMap = make(map[string]*projectorPb.RewardOption)
	)
	if upperCap == 0 {
		for _, projection := range projections {
			// iterate on all options and aggregate the one of required type
			// *** NOTE ***: If multiple options are generated of same type then aggregations could be mis-calculated
			for _, option := range projection.GetProjectedOptions().GetRewardUnitsWithTypes() {
				if option.RewardType == rewardType {
					rewardUnits += option.RewardUnits
					projectionIdToRewardOptionMap[projection.GetId()] = option
					break
				}
			}
		}

		return float64(rewardUnits), projectionIdToRewardOptionMap
	}

	// if upperCap is defined, the actual reward units will be calculated as follows:
	// 1. Sum all the negative projections and add them to the totalActualUnitsSoFar. The actual reward units will also be same as projected units for them.
	// 2. Iterate on all the projections and allot the respective reward units as actual units to them, like so:
	//		a. If totalActualUnitsSoFar + projectedUnits < upperCap: contribution of this projection = projected units
	//		b. If totalActualUnitsSoFar + projectedUnits > upperCap but totalActualUnitsSoFar < upperCap: contribution of this projection = upperCap - totalActualUnitsSoFar
	// 		c. If totalActualUnitsSoFar >= upperCap: contribution of this projection = 0
	//    2.1. the computed value if then capped on actionType, offerType, and offerId level (in that order), if any of the caps is defined.

	// 1. sum all the negative projections and add them to totalActualUnitsSoFar.
	currentActionTypeLevelUtilisation := map[string]float32{}
	currentOfferTypeLevelUtilisation := map[string]float32{}
	currentOfferIdLevelUtilisation := map[string]float32{}
	totalActualUnitsSoFar := float32(0)
	var cappedUnits float32
	for _, projection := range projections {
		for _, option := range projection.GetProjectedOptions().GetRewardUnitsWithTypes() {
			if option.GetRewardType() == rewardType && option.GetRewardUnits() < 0 {
				// cap units on action type level
				cappedUnits = re.getRewardUnitsCappedOnActionLevel(option.GetRewardUnits(), projection.GetActionType(), rewardUnitsComputedFromProjectionsCaps.GetActionTypeToUnitCapsMap(), currentActionTypeLevelUtilisation)
				// cap units on offer type level
				cappedUnits = re.getRewardUnitsCappedOnOfferTypeLevel(cappedUnits, projection.GetOfferType(), rewardUnitsComputedFromProjectionsCaps.GetOfferTypeToUnitCapsMap(), currentOfferTypeLevelUtilisation)
				// cap units on offer ID level
				cappedUnits = re.getRewardUnitsCappedOnOfferIdLevel(cappedUnits, projection.GetOfferId(), rewardUnitsComputedFromProjectionsCaps.GetOfferIdToUnitCapsMap(), currentOfferIdLevelUtilisation)
				totalActualUnitsSoFar += cappedUnits
				projectionIdToRewardOptionMap[projection.GetId()] = option
				break
			}
		}
	}

	// 2. distribute the new limit across all the projections
	for _, projection := range projections {
		for _, option := range projection.GetProjectedOptions().GetRewardUnitsWithTypes() {
			if option.RewardType == rewardType {
				if option.GetRewardUnits() >= 0 {
					switch {
					case totalActualUnitsSoFar >= upperCap:
						projectionIdToRewardOptionMap[projection.GetId()] = &projectorPb.RewardOption{
							RewardType:                 rewardType,
							RewardUnits:                ZeroRewardUnits,
							RewardTypeSpecificMetadata: option.GetRewardTypeSpecificMetadata(),
						}
					case totalActualUnitsSoFar+option.GetRewardUnits() >= upperCap:
						cappedUnits = upperCap - totalActualUnitsSoFar
						// cap units on action type level
						cappedUnits = re.getRewardUnitsCappedOnActionLevel(cappedUnits, projection.GetActionType(), rewardUnitsComputedFromProjectionsCaps.GetActionTypeToUnitCapsMap(), currentActionTypeLevelUtilisation)
						// cap units on offer type level
						cappedUnits = re.getRewardUnitsCappedOnOfferTypeLevel(cappedUnits, projection.GetOfferType(), rewardUnitsComputedFromProjectionsCaps.GetOfferTypeToUnitCapsMap(), currentOfferTypeLevelUtilisation)
						// cap units on offer ID level
						cappedUnits = re.getRewardUnitsCappedOnOfferIdLevel(cappedUnits, projection.GetOfferId(), rewardUnitsComputedFromProjectionsCaps.GetOfferIdToUnitCapsMap(), currentOfferIdLevelUtilisation)
						projectionIdToRewardOptionMap[projection.GetId()] = &projectorPb.RewardOption{
							RewardType:                 rewardType,
							RewardUnits:                cappedUnits,
							RewardTypeSpecificMetadata: option.GetRewardTypeSpecificMetadata(),
						}
						totalActualUnitsSoFar += cappedUnits
					case totalActualUnitsSoFar+option.GetRewardUnits() < upperCap:
						// cap units on action type level
						cappedUnits = re.getRewardUnitsCappedOnActionLevel(option.GetRewardUnits(), projection.GetActionType(), rewardUnitsComputedFromProjectionsCaps.GetActionTypeToUnitCapsMap(), currentActionTypeLevelUtilisation)
						// cap units on offer type level
						cappedUnits = re.getRewardUnitsCappedOnOfferTypeLevel(cappedUnits, projection.GetOfferType(), rewardUnitsComputedFromProjectionsCaps.GetOfferTypeToUnitCapsMap(), currentOfferTypeLevelUtilisation)
						// cap units on offer ID level
						cappedUnits = re.getRewardUnitsCappedOnOfferIdLevel(cappedUnits, projection.GetOfferId(), rewardUnitsComputedFromProjectionsCaps.GetOfferIdToUnitCapsMap(), currentOfferIdLevelUtilisation)
						projectionIdToRewardOptionMap[projection.GetId()] = option
						totalActualUnitsSoFar += cappedUnits
					}
				} else {
					projectionIdToRewardOptionMap[projection.GetId()] = option
				}
			}
		}
	}
	rewardUnits = totalActualUnitsSoFar

	return float64(rewardUnits), projectionIdToRewardOptionMap
}

func (re *CustomRuleEngine) mergeProjectionDetailsMap(projectionIdToContributionsMap map[string]*projectorPb.OptionsInfo, projectionIdToProjectedOptionMap map[string]*projectorPb.RewardOption) map[string]*projectorPb.OptionsInfo {
	if projectionIdToContributionsMap == nil {
		projectionIdToContributionsMap = make(map[string]*projectorPb.OptionsInfo)
	}

	for projectionId, projectedOption := range projectionIdToProjectedOptionMap {
		if contribution, ok := projectionIdToContributionsMap[projectionId]; ok && contribution != nil {
			contribution.RewardUnitsWithTypes = append(contribution.RewardUnitsWithTypes, projectedOption)
		} else {
			projectionIdToContributionsMap[projectionId] = &projectorPb.OptionsInfo{
				RewardUnitsWithTypes: []*projectorPb.RewardOption{projectedOption},
			}
		}
	}

	return projectionIdToContributionsMap
}

func (re *CustomRuleEngine) getRewardUnitsCappedOnActionLevel(uncappedUnits float32, actionType rewardsPb.CollectedDataType, actionLevelCaps map[string]uint32, currentActionLevelUtilisation map[string]float32) float32 {
	capValueUint, ok := actionLevelCaps[actionType.String()]
	if !ok {
		return uncappedUnits
	}
	capValue := float32(capValueUint)

	currentUtilisation := currentActionLevelUtilisation[actionType.String()]
	switch {
	case currentUtilisation >= capValue:
		return 0
	case currentUtilisation+uncappedUnits >= capValue:
		cappedUnits := capValue - currentUtilisation
		currentActionLevelUtilisation[actionType.String()] += cappedUnits
		return cappedUnits
	default:
		currentActionLevelUtilisation[actionType.String()] += uncappedUnits
		return uncappedUnits
	}
}

func (re *CustomRuleEngine) getRewardUnitsCappedOnOfferTypeLevel(uncappedUnits float32, offerType rewardsPb.RewardOfferType, offerTypeLevelCaps map[string]uint32, currentOfferTypeLevelUtilisation map[string]float32) float32 {
	capValueUint, ok := offerTypeLevelCaps[offerType.String()]
	if !ok {
		return uncappedUnits
	}
	capValue := float32(capValueUint)

	currentUtilisation := currentOfferTypeLevelUtilisation[offerType.String()]
	switch {
	case currentUtilisation >= capValue:
		return 0
	case currentUtilisation+uncappedUnits >= capValue:
		cappedUnits := capValue - currentUtilisation
		currentOfferTypeLevelUtilisation[offerType.String()] += cappedUnits
		return cappedUnits
	default:
		currentOfferTypeLevelUtilisation[offerType.String()] += uncappedUnits
		return uncappedUnits
	}
}

func (re *CustomRuleEngine) getRewardUnitsCappedOnOfferIdLevel(uncappedUnits float32, offerId string, offerIdLevelCaps map[string]uint32, currentOfferIdLevelUtilisation map[string]float32) float32 {
	capValueUint, ok := offerIdLevelCaps[offerId]
	if !ok {
		return uncappedUnits
	}
	capValue := float32(capValueUint)

	currentUtilisation := currentOfferIdLevelUtilisation[offerId]
	switch {
	case currentUtilisation >= capValue:
		return 0
	case currentUtilisation+uncappedUnits >= capValue:
		cappedUnits := capValue - currentUtilisation
		currentOfferIdLevelUtilisation[offerId] += cappedUnits
		return cappedUnits
	default:
		currentOfferIdLevelUtilisation[offerId] += uncappedUnits
		return uncappedUnits
	}
}
