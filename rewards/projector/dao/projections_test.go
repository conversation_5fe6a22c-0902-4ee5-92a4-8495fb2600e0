package dao

import (
	"context"
	"reflect"
	"testing"

	"github.com/google/uuid"
	datePb "google.golang.org/genproto/googleapis/type/date"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/pagination"
	pkgTestV2 "github.com/epifi/be-common/pkg/test/v2"

	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardsProjectionPb "github.com/epifi/gamma/api/rewards/projector"
	"github.com/epifi/gamma/rewards/projector/dao/model"
	"github.com/epifi/gamma/rewards/test"
)

func TestPGDBRewardsProjectionDao_FetchPaginatedProjectionsByFilters(t *testing.T) {
	projectionsDao := NewPGDBRewardsProjectionDao(db)
	type args struct {
		ctx               context.Context
		filter            *model.QueryProjectionsFilter
		pageToken         *pagination.PageToken
		pageSize          int
		selectedFieldMask []rewardsProjectionPb.ProjectionFieldMask
		orderingField     rewardsProjectionPb.ProjectionFieldMask
		orderingType      OrderingType
	}
	tests := []struct {
		name        string
		args        args
		want        []*rewardsProjectionPb.Projection
		wantPageCtx *rpc.PageContextResponse
		wantErr     bool
	}{
		{
			name: "Nil filter",
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "Invalid filter with all identifiers missing",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryProjectionsFilter{
					AndFilter: &model.AndProjectionFilter{},
				},
			},
			wantErr: true,
		},
		{
			name: "Filter with ids should return records containing only those ids and no more records to fetch later.",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryProjectionsFilter{
					AndFilter: &model.AndProjectionFilter{
						Ids: []string{fixtureProjection2.GetId(), fixtureProjection3.GetId(), fixtureProjection4.GetId()},
					},
				},
				pageSize: 3,
			},
			wantErr:     false,
			want:        []*rewardsProjectionPb.Projection{fixtureProjection4, fixtureProjection3, fixtureProjection2},
			wantPageCtx: &rpc.PageContextResponse{},
		},
		{
			name: "Filter with ids for records 1-8, should return record 8,7 and token for next page.",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryProjectionsFilter{
					AndFilter: &model.AndProjectionFilter{
						Ids: []string{
							fixtureProjection1.GetId(),
							fixtureProjection2.GetId(),
							fixtureProjection3.GetId(),
							fixtureProjection4.GetId(),
							fixtureProjection5.GetId(),
							fixtureProjection6.GetId(),
							fixtureProjection7.GetId(),
							fixtureProjection8.GetId(),
						},
					},
				},
				pageSize: 2,
			},
			wantErr: false,
			want:    []*rewardsProjectionPb.Projection{fixtureProjection8, fixtureProjection7},
			wantPageCtx: &rpc.PageContextResponse{
				HasAfter:   true,
				AfterToken: "****************************************************************************************************************",
			},
		},
		{
			name: "Filter with ids for records 1-8, should return record 6,7 and token for next page.",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryProjectionsFilter{
					AndFilter: &model.AndProjectionFilter{
						Ids: []string{
							fixtureProjection1.GetId(),
							fixtureProjection2.GetId(),
							fixtureProjection3.GetId(),
							fixtureProjection4.GetId(),
							fixtureProjection5.GetId(),
							fixtureProjection6.GetId(),
							fixtureProjection7.GetId(),
							fixtureProjection8.GetId(),
						},
					},
				},
				pageSize: 2,
				pageToken: &pagination.PageToken{
					Timestamp: &timestampPb.Timestamp{
						Seconds: 1697998373,
						Nanos:   10000000,
					},
					Offset:    1,
					IsReverse: false,
				},
			},
			wantErr: false,
			want:    []*rewardsProjectionPb.Projection{fixtureProjection6, fixtureProjection5},
			wantPageCtx: &rpc.PageContextResponse{
				HasAfter:    true,
				AfterToken:  "eyJUaW1lc3RhbXAiOnsic2Vjb25kcyI6MTY5NzgyNTU3MywibmFub3MiOjEwMDAwMDAwfSwiT2Zmc2V0IjoxLCJJc1JldmVyc2UiOmZhbHNlfQ==",
				HasBefore:   true,
				BeforeToken: "eyJUaW1lc3RhbXAiOnsic2Vjb25kcyI6MTY5NzkxMTk3MywibmFub3MiOjEwMDAwMDAwfSwiT2Zmc2V0IjoxLCJJc1JldmVyc2UiOnRydWV9",
			},
		},
		{
			name: "Filter with ids for records 1-8, should return record 4,3 and token for next page.",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryProjectionsFilter{
					AndFilter: &model.AndProjectionFilter{
						Ids: []string{
							fixtureProjection1.GetId(),
							fixtureProjection2.GetId(),
							fixtureProjection3.GetId(),
							fixtureProjection4.GetId(),
							fixtureProjection5.GetId(),
							fixtureProjection6.GetId(),
							fixtureProjection7.GetId(),
							fixtureProjection8.GetId(),
						},
					},
				},
				pageSize: 2,
				pageToken: &pagination.PageToken{
					Timestamp: &timestampPb.Timestamp{
						Seconds: 1697825573,
						Nanos:   10000000,
					},
					Offset:    1,
					IsReverse: false,
				},
			},
			wantErr: false,
			want:    []*rewardsProjectionPb.Projection{fixtureProjection4, fixtureProjection3},
			wantPageCtx: &rpc.PageContextResponse{
				HasAfter:    true,
				AfterToken:  "****************************************************************************************************************",
				HasBefore:   true,
				BeforeToken: "eyJUaW1lc3RhbXAiOnsic2Vjb25kcyI6MTY5NzczOTE3MywibmFub3MiOjEwMDAwMDAwfSwiT2Zmc2V0IjoxLCJJc1JldmVyc2UiOnRydWV9",
			},
		},
		{
			name: "Filter with ids for records 1-8, should return record 2,1 and no next token.",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryProjectionsFilter{
					AndFilter: &model.AndProjectionFilter{
						Ids: []string{
							fixtureProjection1.GetId(),
							fixtureProjection2.GetId(),
							fixtureProjection3.GetId(),
							fixtureProjection4.GetId(),
							fixtureProjection5.GetId(),
							fixtureProjection6.GetId(),
							fixtureProjection7.GetId(),
							fixtureProjection8.GetId(),
						},
					},
				},
				pageSize: 2,
				pageToken: &pagination.PageToken{
					Timestamp: &timestampPb.Timestamp{
						Seconds: 1697652773,
						Nanos:   10000000,
					},
					Offset:    1,
					IsReverse: false,
				},
			},
			wantErr: false,
			want:    []*rewardsProjectionPb.Projection{fixtureProjection2, fixtureProjection1},
			wantPageCtx: &rpc.PageContextResponse{
				HasAfter:    false,
				AfterToken:  "",
				HasBefore:   true,
				BeforeToken: "eyJUaW1lc3RhbXAiOnsic2Vjb25kcyI6MTY5NzU2NjM3MywibmFub3MiOjEwMDAwMDAwfSwiT2Zmc2V0IjoxLCJJc1JldmVyc2UiOnRydWV9",
			},
		},
		{
			name: "Filter with Actor Id should return all records within page size 3",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryProjectionsFilter{
					AndFilter: &model.AndProjectionFilter{
						ActorId: "temp-actor-id-1",
					},
				},
				pageSize: 3,
			},
			wantErr: false,
			want:    []*rewardsProjectionPb.Projection{fixtureProjection5, fixtureProjection4, fixtureProjection3},
			wantPageCtx: &rpc.PageContextResponse{
				HasAfter:    true,
				AfterToken:  "****************************************************************************************************************",
				HasBefore:   false,
				BeforeToken: "",
			},
		},
		{
			name: "Filter with Account Id should return all records within page size 5",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryProjectionsFilter{
					AndFilter: &model.AndProjectionFilter{
						AccountId: "temp-acc-id-1",
					},
				},
				pageSize: 4,
			},
			wantErr: false,
			want:    []*rewardsProjectionPb.Projection{fixtureProjection5, fixtureProjection4, fixtureProjection3, fixtureProjection2},
			wantPageCtx: &rpc.PageContextResponse{
				HasAfter:    false,
				AfterToken:  "",
				HasBefore:   false,
				BeforeToken: "",
			},
		},
		{
			name: "Filter with Account Id and reward Id should return only one record",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryProjectionsFilter{
					AndFilter: &model.AndProjectionFilter{
						AccountId: "temp-acc-id-1",
						RewardIds: []string{"reward-id-4"},
					},
				},
				pageSize: 1,
			},
			wantErr: false,
			want:    []*rewardsProjectionPb.Projection{fixtureProjection5},
			wantPageCtx: &rpc.PageContextResponse{
				HasAfter:    false,
				AfterToken:  "",
				HasBefore:   false,
				BeforeToken: "",
			},
		},
		{
			name: "Filter with Account Id and actor id and reward Id",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryProjectionsFilter{
					AndFilter: &model.AndProjectionFilter{
						AccountId: "temp-acc-id-2",
						ActorId:   "temp-actor-id-2",
						RewardIds: []string{"reward-id-7", "reward-id-6", "reward-id-5"},
					},
				},
				pageSize: 10,
			},
			wantErr: false,
			want:    []*rewardsProjectionPb.Projection{fixtureProjection8, fixtureProjection7, fixtureProjection6},
			wantPageCtx: &rpc.PageContextResponse{
				HasAfter:    false,
				AfterToken:  "",
				HasBefore:   false,
				BeforeToken: "",
			},
		},
		{
			name: "Filter with Account Id and actor id and reward Id and from time and upto time filter",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryProjectionsFilter{
					AndFilter: &model.AndProjectionFilter{
						AccountId: "temp-acc-id-2",
						ActorId:   "temp-actor-id-2",
						RewardIds: []string{"reward-id-7", "reward-id-6", "reward-id-5"},
						FromTime: datetime.DateToTimestamp(&datePb.Date{
							Year:  2023,
							Month: 10,
							Day:   22,
						}, datetime.IST).AsTime(),
						UptoTime: datetime.DateToTimestamp(&datePb.Date{
							Year:  2023,
							Month: 10,
							Day:   24,
						}, datetime.IST).AsTime(),
					},
				},
				pageSize: 5,
			},
			wantErr: false,
			want:    []*rewardsProjectionPb.Projection{fixtureProjection8, fixtureProjection7},
			wantPageCtx: &rpc.PageContextResponse{
				HasAfter:    false,
				AfterToken:  "",
				HasBefore:   false,
				BeforeToken: "",
			},
		},
		{
			name: "Filter with Account Id and actor id and empty reward id to only fetch un actualized projections",
			args: args{
				ctx: context.Background(),
				filter: &model.QueryProjectionsFilter{
					AndFilter: &model.AndProjectionFilter{
						AccountId: "temp-acc-id-2",
						ActorId:   "temp-actor-id-2",
						RewardIds: []string{""},
					},
				},
				pageSize: 5,
			},
			wantErr: false,
			want:    []*rewardsProjectionPb.Projection{fixtureProjection11},
			wantPageCtx: &rpc.PageContextResponse{
				HasAfter:    false,
				AfterToken:  "",
				HasBefore:   false,
				BeforeToken: "",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, db, conf.RewardsDb.GetName(), test.AllTables)
			projections, pageContext, err := projectionsDao.FetchPaginatedProjectionsByFilters(tt.args.ctx, tt.args.filter, tt.args.pageToken, tt.args.pageSize, tt.args.selectedFieldMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("FetchPaginatedProjectionsByFilters() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if len(projections) != len(tt.want) {
				t.Errorf("FetchPaginatedProjectionsByFilters() got = %v projections, want %v projections", len(projections), len(tt.want))
			}

			for idx, _ := range projections {
				if !IsProjectionEqual(projections[idx], tt.want[idx]) {
					t.Errorf("FetchPaginatedProjectionsByFilters() gotProjection = %v, wantProjection %v", projections[idx], tt.want[idx])
				}
			}
			if !reflect.DeepEqual(pageContext, tt.wantPageCtx) {
				t.Errorf("FetchPaginatedProjectionsByFilters() gotPageContext = %v, wantPageContext %v", pageContext, tt.wantPageCtx)
			}
		})
	}
}

func TestPGDBRewardsProjectionDao_PersistProjection(t *testing.T) {
	projectionsDao := NewPGDBRewardsProjectionDao(db)
	tests := []struct {
		name       string
		projection *rewardsProjectionPb.Projection
		wantErr    bool
	}{
		{
			name: "Projection created successfully",
			projection: &rewardsProjectionPb.Projection{
				Id:         uuid.New().String(),
				ActorId:    "temp-actor-id",
				AccountId:  "temp-acc-id",
				RewardType: rewardsPb.RewardType_FI_COINS,
				ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
					{
						RewardType:  rewardsPb.RewardType_FI_COINS,
						RewardUnits: 1000,
					},
				}},
				RewardContributions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
					{
						RewardType:  rewardsPb.RewardType_FI_COINS,
						RewardUnits: 1000,
					},
				}},
				RewardId:   "reward-id",
				ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_BILLING,
				ActionTime: timestampPb.Now(),
				RefId:      "ref-id",
				OfferType:  rewardsPb.RewardOfferType_SECURED_CREDIT_CARD_WEEKEND_OFFER,
				OfferId:    uuid.New().String(),
			},
			wantErr: false,
		},
		{
			name: "Projection With same id already exists",
			projection: &rewardsProjectionPb.Projection{
				Id:         fixtureProjection1.GetId(),
				ActorId:    "temp-actor-id",
				AccountId:  "temp-acc-id",
				RewardType: rewardsPb.RewardType_FI_COINS,
				ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
					{
						RewardType:  rewardsPb.RewardType_FI_COINS,
						RewardUnits: 1000,
					},
				}},
				RewardContributions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
					{
						RewardType:  rewardsPb.RewardType_FI_COINS,
						RewardUnits: 1000,
					},
				}},
				RewardId:   "reward-id",
				ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_BILLING,
				ActionTime: timestampPb.Now(),
				RefId:      "ref-id",
				OfferType:  rewardsPb.RewardOfferType_SECURED_CREDIT_CARD_WEEKEND_OFFER,
				OfferId:    uuid.New().String(),
			},
			wantErr: true,
		},
		{
			name: "Projection Actor id is empty",
			projection: &rewardsProjectionPb.Projection{
				AccountId:  "temp-acc-id",
				RewardType: rewardsPb.RewardType_FI_COINS,
				ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
					{
						RewardType:  rewardsPb.RewardType_FI_COINS,
						RewardUnits: 1000,
					},
				}},
				RewardContributions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
					{
						RewardType:  rewardsPb.RewardType_FI_COINS,
						RewardUnits: 1000,
					},
				}},
				RewardId:   "reward-id",
				ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_BILLING,
				ActionTime: timestampPb.Now(),
				RefId:      "ref-id",
				OfferType:  rewardsPb.RewardOfferType_SECURED_CREDIT_CARD_WEEKEND_OFFER,
				OfferId:    uuid.New().String(),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, db, conf.RewardsDb.GetName(), test.AllTables)
			_, err := projectionsDao.PersistProjection(context.Background(), tt.projection)
			if (err != nil) != tt.wantErr {
				t.Errorf("PersistProjection() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestPGDBRewardsProjectionDao_UpdateProjection(t *testing.T) {
	projectionsDao := NewPGDBRewardsProjectionDao(db)
	tests := []struct {
		name              string
		projection        *rewardsProjectionPb.Projection
		wantErr           bool
		selectedFieldMask []rewardsProjectionPb.ProjectionFieldMask
	}{
		{
			name: "Projection updated successfully",
			projection: &rewardsProjectionPb.Projection{
				Id:        fixtureProjection9.GetId(),
				AccountId: "temp-acc-id-3",
				ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
					{
						RewardType:  rewardsPb.RewardType_FI_COINS,
						RewardUnits: 1000,
					},
				}},
				RewardContributions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
					{
						RewardType:  rewardsPb.RewardType_FI_COINS,
						RewardUnits: 1000,
					},
				}},
			},
			selectedFieldMask: []rewardsProjectionPb.ProjectionFieldMask{
				rewardsProjectionPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_ACCOUNT_ID,
				rewardsProjectionPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_PROJECTED_OPTIONS,
				rewardsProjectionPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_REWARD_CONTRIBUTIONS,
			},
			wantErr: false,
		},
		{
			name: "Projection not updated due to reward id already present in projeciton",
			projection: &rewardsProjectionPb.Projection{
				Id:        fixtureProjection10.GetId(),
				AccountId: "temp-acc-id-3",
				ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
					{
						RewardType:  rewardsPb.RewardType_FI_COINS,
						RewardUnits: 1000,
					},
				}},
				RewardContributions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
					{
						RewardType:  rewardsPb.RewardType_FI_COINS,
						RewardUnits: 1000,
					},
				}},
			},
			selectedFieldMask: []rewardsProjectionPb.ProjectionFieldMask{
				rewardsProjectionPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_ACCOUNT_ID,
				rewardsProjectionPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_PROJECTED_OPTIONS,
				rewardsProjectionPb.ProjectionFieldMask_PROJECTION_FIELD_MASK_REWARD_CONTRIBUTIONS,
			},
			wantErr: true,
		},
		{
			name: "Field masks is empty",
			projection: &rewardsProjectionPb.Projection{
				Id:        fixtureProjection1.GetId(),
				AccountId: "temp-acc-id-2",
				ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
					{
						RewardType:  rewardsPb.RewardType_FI_COINS,
						RewardUnits: 1000,
					},
				}},
				RewardContributions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
					{
						RewardType:  rewardsPb.RewardType_FI_COINS,
						RewardUnits: 1000,
					},
				}},
			},
			selectedFieldMask: []rewardsProjectionPb.ProjectionFieldMask{},
			wantErr:           true,
		},
		{
			name: "Id is empty",
			projection: &rewardsProjectionPb.Projection{
				AccountId: "temp-acc-id-2",
				ProjectedOptions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
					{
						RewardType:  rewardsPb.RewardType_FI_COINS,
						RewardUnits: 1000,
					},
				}},
				RewardContributions: &rewardsProjectionPb.OptionsInfo{RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
					{
						RewardType:  rewardsPb.RewardType_FI_COINS,
						RewardUnits: 1000,
					},
				}},
			},
			selectedFieldMask: []rewardsProjectionPb.ProjectionFieldMask{},
			wantErr:           true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, db, conf.RewardsDb.GetName(), test.AllTables)
			_, err := projectionsDao.UpdateProjection(context.Background(), tt.projection, tt.selectedFieldMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateProjection() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func IsProjectionEqual(gotProjection *rewardsProjectionPb.Projection, wantProjection *rewardsProjectionPb.Projection) bool {
	return gotProjection.GetId() == wantProjection.GetId()
}

func TestPGDBRewardsProjectionDao_UpdateExistingProjectionOptionsIfRewardNotCreatedForIt(t *testing.T) {
	type args struct {
		ctx        context.Context
		projection *rewardsProjectionPb.Projection
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "should fail to update projection if actorId is not present in projection to update",
			args: args{
				ctx: context.Background(),
				projection: &rewardsProjectionPb.Projection{
					ActorId: "",
					ProjectedOptions: &rewardsProjectionPb.OptionsInfo{
						RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
							{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200},
						},
					},
					RefId:   "ref-1",
					OfferId: "offer-1",
				},
			},
			wantErr: true,
		},
		{
			name: "should fail to update projection if refId is not present in projection to update",
			args: args{
				ctx: context.Background(),
				projection: &rewardsProjectionPb.Projection{
					ActorId: "act-1",
					ProjectedOptions: &rewardsProjectionPb.OptionsInfo{
						RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
							{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200},
						},
					},
					RefId:   "",
					OfferId: "offer-1",
				},
			},
			wantErr: true,
		},
		{
			name: "should fail to update projection if offerId is not present in projection to update",
			args: args{
				ctx: context.Background(),
				projection: &rewardsProjectionPb.Projection{
					ActorId: "",
					ProjectedOptions: &rewardsProjectionPb.OptionsInfo{
						RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
							{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200},
						},
					},
					RefId:   "ref-1",
					OfferId: "",
				},
			},
			wantErr: true,
		},
		{
			name: "should fail to update projection if a rewardId exists against the projection that we're trying to update",
			args: args{
				ctx: context.Background(),
				projection: &rewardsProjectionPb.Projection{
					ActorId: "temp-actor-id-2",
					ProjectedOptions: &rewardsProjectionPb.OptionsInfo{
						RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
							{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200},
						},
					},
					RefId:   "ref-id-7",
					OfferId: "fd1f671d-50f3-4a69-bb19-c6daa5e066e4",
				},
			},
			wantErr: true,
		},
		{
			name: "should update projection if no reward id exists against it and all required fields are present",
			args: args{
				ctx: context.Background(),
				projection: &rewardsProjectionPb.Projection{
					ActorId: "temp-actor-id-3",
					ProjectedOptions: &rewardsProjectionPb.OptionsInfo{
						RewardUnitsWithTypes: []*rewardsProjectionPb.RewardOption{
							{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 200},
						},
					},
					RefId:   "ref-id-8",
					OfferId: "fd1f671d-50f3-4a69-bb19-c6daa5e066e4",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, db, conf.RewardsDb.GetName(), test.AllTables)
			projectionsDao := NewPGDBRewardsProjectionDao(db)
			_, err := projectionsDao.UpdateExistingProjectionOptionsIfRewardNotCreatedForIt(tt.args.ctx, tt.args.projection)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateExistingProjectionOptionsIfRewardNotCreatedForIt() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
