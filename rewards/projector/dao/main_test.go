package dao

import (
	"os"
	"testing"

	gormv2 "gorm.io/gorm"

	rewardsProjectionPb "github.com/epifi/gamma/api/rewards/projector"
	"github.com/epifi/gamma/rewards/config"
	"github.com/epifi/gamma/rewards/test"
)

var (
	db   *gormv2.DB
	conf *config.Config
)

var (
	fixtureProjection1 = &rewardsProjectionPb.Projection{
		Id:        "fd1f671d-d0f3-4a69-bb19-c6daa5e066e4",
		ActorId:   "temp-actor-id",
		AccountId: "temp-acc-id",
		OfferId:   "fd1f671d-d0f3-4a69-bb19-c6daa5e066e4",
		RewardId:  "reward-id",
		RefId:     "ref-id",
	}
	fixtureProjection2 = &rewardsProjectionPb.Projection{
		Id:        "fd1f671d-e0f3-4a69-bb19-c6daa5e066e4",
		ActorId:   "temp-actor-id-1",
		AccountId: "temp-acc-id-1",
		OfferId:   "fd1f671d-e0f3-4a69-bb19-c6daa5e066e4",
		RewardId:  "reward-id-1",
		RefId:     "ref-id-1"}
	fixtureProjection3 = &rewardsProjectionPb.Projection{
		Id:        "fd1f671d-10f3-4a69-bb19-c6daa5e066e4",
		ActorId:   "temp-actor-id-1",
		AccountId: "temp-acc-id-1",
		OfferId:   "fd1f671d-f0f3-4a69-bb19-c6daa5e066e4",
		RewardId:  "reward-id-2",
		RefId:     "ref-id-2"}
	fixtureProjection4 = &rewardsProjectionPb.Projection{
		Id:        "fd1f671d-20f3-4a69-bb19-c6daa5e066e4",
		ActorId:   "temp-actor-id-1",
		AccountId: "temp-acc-id-1",
		OfferId:   "fd1f671d-10f3-4a69-bb19-c6daa5e066e4",
		RewardId:  "reward-id-3",
		RefId:     "ref-id-3"}
	fixtureProjection5 = &rewardsProjectionPb.Projection{
		Id:        "fd1f671d-30f3-4a69-bb19-c6daa5e066e4",
		ActorId:   "temp-actor-id-1",
		AccountId: "temp-acc-id-1",
		OfferId:   "fd1f671d-20f3-4a69-bb19-c6daa5e066e4",
		RewardId:  "reward-id-4",
		RefId:     "ref-id-4"}
	fixtureProjection6 = &rewardsProjectionPb.Projection{
		Id:        "fd1f671d-40f3-4a69-bb19-c6daa5e066e4",
		ActorId:   "temp-actor-id-2",
		AccountId: "temp-acc-id-2",
		OfferId:   "fd1f671d-30f3-4a69-bb19-c6daa5e066e4",
		RewardId:  "reward-id-5",
		RefId:     "ref-id-5"}
	fixtureProjection7 = &rewardsProjectionPb.Projection{
		Id:        "fd1f671d-50f3-4a69-bb19-c6daa5e066e4",
		ActorId:   "temp-actor-id-2",
		AccountId: "temp-acc-id-2",
		OfferId:   "fd1f671d-40f3-4a69-bb19-c6daa5e066e4",
		RewardId:  "reward-id-6",
		RefId:     "ref-id-6"}
	fixtureProjection8 = &rewardsProjectionPb.Projection{Id: "fd1f671d-60f3-4a69-bb19-c6daa5e066e4",
		ActorId:   "temp-actor-id-2",
		AccountId: "temp-acc-id-2",
		OfferId:   "fd1f671d-50f3-4a69-bb19-c6daa5e066e4",
		RewardId:  "reward-id-7",
		RefId:     "ref-id-7"}
	fixtureProjection9 = &rewardsProjectionPb.Projection{Id: "fd1f671d-70f3-4a69-bb19-c6daa5e066e4",
		ActorId:   "temp-actor-id-3",
		AccountId: "temp-acc-id-3",
		OfferId:   "fd1f671d-50f3-4a69-bb19-c6daa5e066e4",
		RewardId:  "reward-id-7",
		RefId:     "ref-id-8"}
	fixtureProjection10 = &rewardsProjectionPb.Projection{Id: "fd1f671d-80f3-4a69-bb19-c6daa5e066e4",
		ActorId:   "temp-actor-id-3",
		AccountId: "temp-acc-id-3",
		OfferId:   "fd1f671d-50f3-4a69-bb19-c6daa5e066e4",
		RewardId:  "reward-id-7",
		RefId:     "ref-id-9"}
	fixtureProjection11 = &rewardsProjectionPb.Projection{Id: "ea838103-af38-47ed-8dfc-630d5f13b285",
		ActorId:   "temp-actor-id-2",
		AccountId: "temp-acc-id-2",
		OfferId:   "fd1f671d-50f3-4a69-bb19-c6daa5e066e4",
		RefId:     "ref-id-10"}
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	var teardown func()
	conf, _, db, teardown = test.InitTestServer(true)
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
