package travel

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"time"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	"github.com/shopspring/decimal"

	"github.com/pkg/errors"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"

	actorPb "github.com/epifi/gamma/api/actor"
	beCcPb "github.com/epifi/gamma/api/card/control"
	bePb "github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/casper"
	beCasperPb "github.com/epifi/gamma/api/casper"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	currencyInsightsVgPb "github.com/epifi/gamma/api/vendorgateway/currencyinsights"
	travelPb "github.com/epifi/gamma/api/webfe/travel"
	cardWireTypes "github.com/epifi/gamma/card/wire/types"
	deeplink2 "github.com/epifi/gamma/pkg/deeplink"
	webfeConfig "github.com/epifi/gamma/webfe/config"
)

// Service is the implementation of the webFe travel service
type Service struct {
	travelPb.UnimplementedTravelBudgetServer
	actorClient              actorPb.ActorClient
	cardProvisioningClient   bePb.CardProvisioningClient
	rewardsListingClient     beCasperPb.OfferListingServiceClient
	dcDocsS3Client           cardWireTypes.DcDocS3Client
	webfeConf                *webfeConfig.Config
	travelBudget             map[string]interface{}
	cardCtrlClient           beCcPb.CardControlClient
	currencyInsightsVgClient currencyInsightsVgPb.ServiceClient
}

// CountryFlagMappings represents the structure of the JSON data
type CountryFlagMappings map[string]string

func NewService(actorClient actorPb.ActorClient, cardProvisioningClient bePb.CardProvisioningClient, rewardsListingClient beCasperPb.OfferListingServiceClient, dcDocsS3Client cardWireTypes.DcDocS3Client, webfeConf *webfeConfig.Config, cardCtrlClient beCcPb.CardControlClient, currencyInsightsVgClient currencyInsightsVgPb.ServiceClient) (*Service, error) {
	travelBudget, err := fetchTravelBudgetRecord(context.Background(), dcDocsS3Client, webfeConf)
	if err != nil {
		logger.ErrorNoCtx("failed to fetch travel budget record", zap.Error(err))
		return nil, err
	}
	return &Service{
		actorClient:              actorClient,
		cardProvisioningClient:   cardProvisioningClient,
		rewardsListingClient:     rewardsListingClient,
		dcDocsS3Client:           dcDocsS3Client,
		webfeConf:                webfeConf,
		travelBudget:             travelBudget,
		cardCtrlClient:           cardCtrlClient,
		currencyInsightsVgClient: currencyInsightsVgClient,
	}, nil
}

func (s *Service) GetTravelDestinations(ctx context.Context, request *travelPb.GetTravelDestinationsRequest) (*travelPb.GetTravelDestinationsResponse, error) {

	var countries []*travelPb.Country
	for country, info := range CountryInfoMap {
		if info.isFavTravelDestination {
			countries = append(countries, &travelPb.Country{Name: country, Flag: info.Flag})
		}
	}

	// Sort the slice by country name.
	sort.Slice(countries, func(i, j int) bool {
		return countries[i].GetName() < countries[j].GetName()
	})

	var popularDestinations []*travelPb.PopularDestinations
	for _, popularCountry := range popularCountries {
		startDate := time.Now().AddDate(0, 0, 1)
		endDate := startDate.AddDate(0, 0, 5)
		numberOfCouples, numberOfSingles, days := calculatePeopleAndDays(startDate, endDate, 2)

		// Extract relevant country and travel style data
		travelStyle := travelPb.TravelStyle_ECONOMIC.MappingString()
		month := startDate.Month().String()
		countryData := extractCountryData(ctx, s.travelBudget, popularCountry, travelStyle, month)

		// Calculate daily expenses
		_, totalExpense := calculateDailyExpenses(ctx, countryData, numberOfCouples, numberOfSingles, days)
		popularDestinations = append(popularDestinations, &travelPb.PopularDestinations{
			Country:          &travelPb.Country{Name: popularCountry, Flag: CountryInfoMap[popularCountry].Flag},
			DestinationImage: &travelPb.Image{MobileImageUrl: CountryInfoMap[popularCountry].ShortImage},
			Description:      fmt.Sprintf("%s onwards", money.ToDisplayStringInIndianFormat(money.ParseFloat(totalExpense, ""), 0, true)),
		})
	}

	response := &travelPb.GetTravelDestinationsResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		TravelDestinations: &travelPb.GetTravelDestinationsResponse_TravelDestinations{
			Destinations:        countries,
			PopularDestinations: popularDestinations,
		},
	}

	return response, nil
}

func (s *Service) GetTravelExpense(ctx context.Context, req *travelPb.GetTravelExpenseRequest) (*travelPb.GetTravelExpenseResponse, error) {

	// Set default values if missing
	setDefaultValues(req)

	// Get country-related data
	countryName := req.GetExpenseData().GetCountry()
	countryInfo := CountryInfoMap[countryName]
	numberOfCouples, numberOfSingles, days := calculatePeopleAndDays(req.GetExpenseData().GetStartDate().AsTime(), req.GetExpenseData().GetEndDate().AsTime(), int64(req.GetExpenseData().GetPeopleCount()))

	// Extract relevant country and travel style data
	travelStyle := req.GetExpenseData().GetTravelStyle().MappingString()
	month := req.GetExpenseData().GetStartDate().AsTime().Month().String()
	countryData := extractCountryData(ctx, s.travelBudget, countryName, travelStyle, month)

	// Calculate daily expenses
	dailyExpenses, totalExpense := calculateDailyExpenses(ctx, countryData, numberOfCouples, numberOfSingles, days)

	// Calculate savings
	zeroMarkupSavings, cardOffersSavings, calculateSavingsErr := calculateSavings(s.webfeConf, decimal.NewFromFloat(totalExpense))
	if calculateSavingsErr != nil {
		logger.Error(ctx, "error while calculating savings", zap.Error(calculateSavingsErr))
		return &travelPb.GetTravelExpenseResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()},
		}, nil
	}

	ctaTitle := "Explore"
	ctaDeepLinkBase64 := deeplink2.GenerateDeeplinkBase64(&deeplink.Deeplink{Screen: deeplink.Screen_DC_USAGE_AND_LIMIT_SETTINGS_SCREEN})

	getOffersResp, getOffersErr := s.rewardsListingClient.GetCardOffers(ctx, &beCasperPb.GetCardOffersRequest{
		RedemptionMode: beCasperPb.OfferRedemptionMode_FI_CARD,
		FiltersV2: &beCasperPb.CatalogFiltersV2{
			OrTags:  []beCasperPb.TagName{beCasperPb.TagName_INTERNATIONAL},
			AndTags: nil,
		},
	})

	if te := epifigrpc.RPCError(getOffersResp, getOffersErr); te != nil {
		logger.Error(ctx, "failed to get offers", zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.Error(te))
	}

	var travelOffers []*travelPb.TravelOffer

	for _, offer := range getOffersResp.GetOffers() {
		bgImage, brandImage := func() (*casper.OfferImage, *casper.OfferImage) {
			var bgImg, brandImg *casper.OfferImage
			for _, image := range offer.GetImages() {
				if image.GetImageType() == casper.ImageType_BACKGROUND_IMAGE {
					bgImg = image
				} else if image.GetImageType() == casper.ImageType_BRAND_IMAGE {
					brandImg = image
				}
			}
			return bgImg, brandImg
		}()
		travelOffers = append(travelOffers, &travelPb.TravelOffer{
			OfferImages: []*travelPb.Image{
				{MobileImageUrl: brandImage.GetUrl()},
				{MobileImageUrl: bgImage.GetUrl()},
			},
			Description: offer.GetName(),
		})
	}

	// Build response
	return &travelPb.GetTravelExpenseResponse{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
		ExpenseBudget: &travelPb.GetTravelExpenseResponse_TravelExpenseBudget{
			DailyExpenses:                  dailyExpenses,
			TravelOffers:                   travelOffers,
			Country:                        &travelPb.Country{Name: countryName, Flag: countryInfo.Flag},
			UnitTotalEstimateExpenseAmount: totalExpense,
			CurrencyConversionRate:         countryInfo.OneINRValue, // TODO: @Sathya Integrate VISA Api to get the latest conversion rate
			TravelDays:                     int32(days),
			DestinationNativeCurrency:      countryInfo.CurrencyCode,
			CountryImage:                   &travelPb.Image{MobileImageUrl: countryInfo.BgImage},
			PeopleCount:                    req.GetExpenseData().GetPeopleCount(),
			Cta: &travelPb.Cta{
				Title:                ctaTitle,
				Base64DeeplinkString: ctaDeepLinkBase64,
			},
			TravelStyle:             req.GetExpenseData().GetTravelStyle(),
			TotalSavingsAmount:      zeroMarkupSavings.Add(cardOffersSavings).InexactFloat64(),
			ZeroMarkupSavingsAmount: zeroMarkupSavings.InexactFloat64(),
			CardOffersSavingsAmount: cardOffersSavings.InexactFloat64(),
			NativeCurrencySymbol:    countryInfo.CurrencySymbol,
			StartDate:               req.GetExpenseData().GetStartDate(),
			EndDate:                 req.GetExpenseData().GetEndDate(),
		},
	}, nil
}

func (s *Service) GetInternationalATMWithdrawalLimits(ctx context.Context, req *travelPb.GetInternationalATMWithdrawalLimitsRequest) (*travelPb.GetInternationalATMWithdrawalLimitsResponse, error) {
	// Fetch international ATM limits from card control service
	internationalLimitsResp, rpcErr := s.cardCtrlClient.GetInternationalAtmLimits(ctx, &beCcPb.GetInternationalAtmLimitsRequest{
		GetBy: &beCcPb.GetInternationalAtmLimitsRequest_GetAll{GetAll: true},
	})
	if grpcErr := epifigrpc.RPCError(internationalLimitsResp, rpcErr); grpcErr != nil {
		logger.Error(ctx, "error in getting international ATM limits from card control service", zap.Error(grpcErr))
		return &travelPb.GetInternationalATMWithdrawalLimitsResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()},
		}, nil
	}

	internationalLimitDetails := make([]*travelPb.GetInternationalATMWithdrawalLimitsResponse_ATMLimit, 0)
	for _, limit := range internationalLimitsResp.GetInternationalAtmLimits() {
		internationalLimitDetails = append(internationalLimitDetails, &travelPb.GetInternationalATMWithdrawalLimitsResponse_ATMLimit{
			CountryName: limit.GetCountryName(),
			CountryFlag: limit.GetCountryFlag(),
			AtmLimit:    limit.GetMaxAtmWithdrawalLimit(),
		})
	}

	return &travelPb.GetInternationalATMWithdrawalLimitsResponse{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
		AtmLimits:  internationalLimitDetails,
	}, nil
}

func (s *Service) GetForexExchangeRate(ctx context.Context, req *travelPb.GetForexExchangeRateRequest) (*travelPb.GetForexExchangeRateResponse, error) {
	var (
		res = &travelPb.GetForexExchangeRateResponse{}
	)

	req.GetAmount().CurrencyCode = req.GetFromCountryCode()

	// Ideal would be to return same amount if both currency codes are same. But the response also includes savings
	// amount which wouldn't make sense if both currency codes are same. Also, not a big UX hit.
	if req.GetFromCountryCode() != "" && req.GetFromCountryCode() == req.GetToCountryCode() {
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusInvalidArgumentWithDebugMsg("from and to currency codes cannot be same")}
		return res, nil
	}

	exchangeRatesResp, err := s.currencyInsightsVgClient.GetEnhancedForeignExchangeRates(ctx, &currencyInsightsVgPb.GetEnhancedForeignExchangeRatesRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_VISA,
		},
		ToCurrency:   req.GetToCountryCode(),
		FromCurrency: req.GetFromCountryCode(),
		AsOfDate:     timestamppb.New(time.Now()),
	})
	if te := epifigrpc.RPCError(exchangeRatesResp, err); te != nil {
		logger.Error(ctx, "error while getting foreign exchange rates",
			zap.String("FromCurrencyCode", req.GetFromCountryCode()), zap.String("ToCurrencyCode", req.GetToCountryCode()), zap.Error(te))
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusInternal()}
		return res, nil
	}
	res.ConvertedAmount = money.ParseDecimal(
		decimal.NewFromFloat32(exchangeRatesResp.GetExchangeRateVisa()).Mul(money.ToDecimal(req.GetAmount())),
		req.GetToCountryCode(),
	)

	inrValue := decimal.NewFromInt(0)
	switch {
	case req.GetFromCountryCode() == money.RupeeCurrencyCode:
		inrValue = money.ToDecimal(req.GetAmount())

	case req.GetToCountryCode() == money.RupeeCurrencyCode:
		inrValue = money.ToDecimal(res.GetConvertedAmount())
	default:
		// Convert the amount to INR
		exchangeRatesResp, err := s.currencyInsightsVgClient.GetEnhancedForeignExchangeRates(ctx, &currencyInsightsVgPb.GetEnhancedForeignExchangeRatesRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: commonvgpb.Vendor_VISA,
			},
			ToCurrency:   money.RupeeCurrencyCode,
			FromCurrency: req.GetFromCountryCode(),
			AsOfDate:     timestamppb.New(time.Now()),
		})
		if te := epifigrpc.RPCError(exchangeRatesResp, err); te != nil {
			logger.Error(ctx, "error while getting foreign exchange rates",
				zap.String("FromCurrencyCode", req.GetToCountryCode()), zap.String("ToCurrencyCode", money.RupeeCurrencyCode), zap.Error(te))
			res.RespHeader = &header.ResponseHeader{Status: rpc.StatusInternal()}
			return res, nil
		}
		inrValue = decimal.NewFromFloat32(exchangeRatesResp.GetExchangeRateVisa()).Mul(money.ToDecimal(req.GetAmount()))
	}

	forexSavingsAmount, cardOffersAmount, calculateSavingsErr := calculateSavings(s.webfeConf, inrValue)
	if calculateSavingsErr != nil {
		logger.Error(ctx, "error while calculating savings", zap.Error(calculateSavingsErr))
		return &travelPb.GetForexExchangeRateResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()},
		}, nil
	}

	res.ForexSavingsAmount = money.ParseDecimal(forexSavingsAmount, money.RupeeCurrencyCode)
	res.CardOffersAmount = money.ParseDecimal(cardOffersAmount, money.RupeeCurrencyCode)
	res.RespHeader = &header.ResponseHeader{Status: rpc.StatusOk()}
	return res, nil
}

// setDefaultValues sets default values for StartDate, EndDate, PeopleCount, TravelStyle, and Country if missing.
func setDefaultValues(req *travelPb.GetTravelExpenseRequest) {
	if req.GetExpenseData().GetStartDate() == nil || req.GetExpenseData().GetEndDate() == nil {
		req.GetExpenseData().StartDate = timestamppb.New(time.Now().AddDate(0, 0, 1))
		req.GetExpenseData().EndDate = timestamppb.New(time.Now().AddDate(0, 0, 6))
	}
	if req.GetExpenseData().GetPeopleCount() == 0 {
		req.GetExpenseData().PeopleCount = 2
	}
	if req.GetExpenseData().GetTravelStyle() == travelPb.TravelStyle_TRAVEL_STYLE_UNSPECIFIED {
		req.GetExpenseData().TravelStyle = travelPb.TravelStyle_ECONOMIC
	}
	if req.GetExpenseData().GetCountry() == "" {
		req.GetExpenseData().Country = "Singapore"
	}
}

// calculatePeopleAndDays calculates the number of couples, singles, and the total number of travel days.
func calculatePeopleAndDays(startDate time.Time, endDate time.Time, peopleCount int64) (int64, int64, int) {
	numberOfCouples := peopleCount / 2
	numberOfSingles := peopleCount % 2
	days := int(endDate.Sub(startDate).Hours() / 24)
	return numberOfCouples, numberOfSingles, days
}

// extractCountryData retrieves the relevant travel style and month-specific data from the JSON
func extractCountryData(ctx context.Context, travelBudgetData map[string]interface{}, countryName, travelStyle, month string) map[string]interface{} {
	// Check if the country exists
	countryData, ok := travelBudgetData[countryName].(map[string]interface{})
	if !ok {
		logger.Error(ctx, "Country data not found.")
		return nil
	}

	// Check if the travel style exists
	styleData, ok := countryData[travelStyle].(map[string]interface{})
	if !ok {
		logger.Error(ctx, "Travel style data not found.")
		return nil
	}

	// Check if the month exists
	monthData, ok := styleData[month].(map[string]interface{})
	if !ok {
		logger.Error(ctx, "Month data not found.")
		return nil
	}

	return monthData
}

func extractExpenseDataFromMonthData(data map[string]interface{}, keys ...string) (float64, error) {
	var current interface{} = data
	for _, key := range keys {
		if m, ok := current.(map[string]interface{}); ok {
			current = m[key]
		} else {
			return 0, fmt.Errorf("key %s not found or not a map", key)
		}
	}
	if value, ok := current.(float64); ok {
		return value, nil
	}
	return 0, fmt.Errorf("value at keys %v is not a float64", keys)
}

// calculateDailyExpenses calculates the daily expenses for singles and couples and returns the total expenses.
func calculateDailyExpenses(ctx context.Context, countryData map[string]interface{}, numberOfCouples, numberOfSingles int64, days int) ([]*travelPb.DailyExpense, float64) {
	var dailyExpenses []*travelPb.DailyExpense
	totalExpense := 0.0

	for _, categoryDetail := range categoryDetailList {
		expenseCost := 0.0
		if numberOfSingles > 0 {
			cost, err := extractExpenseDataFromMonthData(countryData, "1", categoryDetail.Name, "cost")
			if err != nil {
				logger.Error(ctx, "error while extracting expense data", zap.Error(err))
				continue
			}
			expenseCost += cost
		}

		if numberOfCouples > 0 {
			cost, err := extractExpenseDataFromMonthData(countryData, "2", categoryDetail.Name, "cost")
			if err != nil {
				logger.Error(ctx, "error while extracting expense data", zap.Error(err))
				continue
			}
			expenseCost += cost * float64(numberOfCouples)
		}

		expenseCost *= 83.75 // Conversion rate from USD // TODO: @sathya Integrate VISA Api to get the latest conversion rate
		dailyExpenses = append(dailyExpenses, &travelPb.DailyExpense{
			ExpenseImage: &travelPb.Image{MobileImageUrl: categoryDetail.ImageURL},
			Type:         categoryDetail.Name,
			Amount:       expenseCost,
		})
		totalExpense += expenseCost * float64(days)
	}

	return dailyExpenses, totalExpense
}

/*
The following JSON structure represents a travel budget for a specific country, organized as follows:

- **country**: "Armenia"
  - **travelStyle**: "Budget" (indicates the type of travel)
    - **month**: Contains nested objects for each month (e.g., "April", "August")
      - **day**: Each day of the month (e.g., "1", "2")
        - **categories**: Various expense categories, each with:
          - **currency**: The currency used (e.g., "USD")
          - **cost**: The amount spent in that currency

For example, the budget for Armenia under the travel style "Budget" in April for day 1 includes:
- Accommodation cost: 38.4 USD
- Alcohol cost: 2 USD
- Entertainment cost: 6.72 USD
- Food & Water cost: 9.6 USD
- Souvenirs cost: 5 USD
- Tips and Handouts cost: 1 USD
- Transportation cost: 6.72 USD
- Overall Daily Cost: 50 USD
*/
// Method to process dispatched card records and return JSON as map
func fetchTravelBudgetRecord(ctx context.Context, dcDocsS3Client cardWireTypes.DcDocS3Client, webfeConf *webfeConfig.Config) (map[string]interface{}, error) {

	env, envErr := cfg.GetEnvironment()
	if envErr == nil {
		if cfg.IsLocalEnv(env) || cfg.IsTestTenantEnabled() {
			return BudgetData, nil
		}
	}

	// Read JSON file from s3 bucket
	jsonData, jsonReadError := dcDocsS3Client.Read(ctx, webfeConf.TravelBudgetS3Path)
	if jsonReadError != nil {
		return nil, errors.Wrap(jsonReadError, "read from s3Client failed")
	}

	// Unmarshal JSON data into a map
	var travelBudgetData map[string]interface{}
	if unmarshalError := json.Unmarshal(jsonData, &travelBudgetData); unmarshalError != nil {
		return nil, errors.Wrap(unmarshalError, "unmarshal JSON failed")
	}

	// Return the unmarshalled map and no error
	return travelBudgetData, nil
}

// Calculate savings for forex and card offers
func calculateSavings(config *webfeConfig.Config, inrValue decimal.Decimal) (decimal.Decimal, decimal.Decimal, error) {
	// Parse forexMarkupRate
	forexMarkupRate, err := decimal.NewFromString(config.ForexCardOfferConfig.ForexMarkupRate)
	if err != nil {
		return decimal.Zero, decimal.Zero, errors.Wrap(err, "failed to parse forexMarkupRate")
	}

	// Parse lowExpenseThreshold
	lowExpenseThreshold, err := decimal.NewFromString(config.ForexCardOfferConfig.LowExpenseThreshold)
	if err != nil {
		return decimal.Zero, decimal.Zero, errors.Wrap(err, "failed to parse lowExpenseThreshold")
	}

	// Parse lowCardOffersAmount
	lowCardOffersAmount, err := decimal.NewFromString(config.ForexCardOfferConfig.LowCardOffersAmount)
	if err != nil {
		return decimal.Zero, decimal.Zero, errors.Wrap(err, "failed to parse lowCardOffersAmount")
	}

	// Parse highCardOffersAmount
	highCardOffersAmount, err := decimal.NewFromString(config.ForexCardOfferConfig.HighCardOffersAmount)
	if err != nil {
		return decimal.Zero, decimal.Zero, errors.Wrap(err, "failed to parse highCardOffersAmount")
	}

	// Calculate savings
	// ForexMarkupRate is the markup rate applied to forex transactions.
	forexSavingsAmount := inrValue.Mul(forexMarkupRate).Ceil()

	// LowExpenseThreshold is the threshold below which the LowCardOffersAmount is applied, otherwise HighCardOffersAmount
	// Example calculation
	// Assuming:
	// - inrValue = 15000
	// - lowExpenseThreshold = 10000
	// - lowCardOffersAmount = 500
	// - highCardOffersAmount = 1000
	//
	// Since 15000 > 10000, cardOffersAmount will be 1000
	cardOffersAmount := lowCardOffersAmount
	if inrValue.Cmp(lowExpenseThreshold) > 0 {
		cardOffersAmount = highCardOffersAmount
	}

	return forexSavingsAmount, cardOffersAmount, nil
}
