package travel

import (
	"context"
	"testing"

	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/frontend/header"
	travelPb "github.com/epifi/gamma/api/webfe/travel"
)

func TestExtractExpenseDataFromMonthData(t *testing.T) {
	data := map[string]interface{}{
		"1": map[string]interface{}{
			"Accommodation": map[string]interface{}{
				"cost": 38.4,
			},
		},
	}

	tests := []struct {
		name    string
		data    map[string]interface{}
		keys    []string
		want    float64
		wantErr bool
	}{
		{
			name:    "Valid data",
			data:    data,
			keys:    []string{"1", "Accommodation", "cost"},
			want:    38.4,
			wantErr: false,
		},
		{
			name:    "Invalid key",
			data:    data,
			keys:    []string{"April", "1", "Invalid", "cost"},
			want:    0,
			wantErr: true,
		},
		{
			name:    "Non-float value",
			data:    map[string]interface{}{"April": map[string]interface{}{"1": map[string]interface{}{"Accommodation": map[string]interface{}{"cost": "invalid"}}}},
			keys:    []string{"April", "1", "Accommodation", "cost"},
			want:    0,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := extractExpenseDataFromMonthData(tt.data, tt.keys...)
			if (err != nil) != tt.wantErr {
				t.Errorf("extractExpenseDataFromMonthData() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("extractExpenseDataFromMonthData() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetForexExchangeRate(t *testing.T) {
	type args struct {
		ctx context.Context
		req *travelPb.GetForexExchangeRateRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func()
		want       *travelPb.GetForexExchangeRateResponse
		wantErr    bool
	}{
		{
			name: "Same currency codes should return validation error",
			args: args{
				ctx: context.Background(),
				req: &travelPb.GetForexExchangeRateRequest{
					Req:             &header.RequestHeader{},
					FromCountryCode: "USD",
					ToCountryCode:   "USD",
					Amount:          &money.Money{Units: 100, Nanos: 0, CurrencyCode: "USD"},
				},
			},
			setupMocks: func() {
				// No mocks needed for validation error case
			},
			want: &travelPb.GetForexExchangeRateResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusInvalidArgumentWithDebugMsg("from and to currency codes cannot be same"),
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &Service{}

			if tt.setupMocks != nil {
				tt.setupMocks()
			}

			got, err := service.GetForexExchangeRate(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetForexExchangeRate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !proto.Equal(got, tt.want) {
				t.Errorf("GetForexExchangeRate() got = %v, want %v", got, tt.want)
			}
		})
	}
}
