// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"context"
	"errors"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/aws/v2/kinesis"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cfg"
	genconf3 "github.com/epifi/be-common/pkg/cfg/genconf"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/crypto/cryptormap"
	"github.com/epifi/be-common/pkg/crypto/pgp"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/ratelimiter"
	"github.com/epifi/be-common/pkg/ratelimiter/store"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/biometrics"
	"github.com/epifi/gamma/api/auth/orchestrator"
	"github.com/epifi/gamma/api/bankcust"
	bre2 "github.com/epifi/gamma/api/bre"
	"github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/casper/redemption"
	"github.com/epifi/gamma/api/categorizer"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/comms/inapptargetedcomms"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/credit_limit_estimator"
	"github.com/epifi/gamma/api/creditreportv2"
	"github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/docs"
	"github.com/epifi/gamma/api/employment"
	firefly2 "github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/firefly/billing"
	"github.com/epifi/gamma/api/firefly/card_recommendation"
	"github.com/epifi/gamma/api/firefly/lms"
	"github.com/epifi/gamma/api/firefly/pinot"
	"github.com/epifi/gamma/api/firefly/v2"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/merchant"
	"github.com/epifi/gamma/api/nudge"
	"github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/pan"
	"github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/product"
	"github.com/epifi/gamma/api/rewards"
	pinot2 "github.com/epifi/gamma/api/rewards/pinot"
	"github.com/epifi/gamma/api/rewards/projector"
	"github.com/epifi/gamma/api/risk"
	"github.com/epifi/gamma/api/risk/profile"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/credit_report"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/obfuscator"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/vendorgateway/fennel"
	"github.com/epifi/gamma/api/vendorgateway/lending/bre"
	"github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	"github.com/epifi/gamma/api/vendorgateway/lending/creditline"
	pan2 "github.com/epifi/gamma/api/vendorgateway/pan"
	"github.com/epifi/gamma/api/vendorgateway/profilevalidation"
	"github.com/epifi/gamma/api/vendorgateway/scienaptic"
	"github.com/epifi/gamma/api/vendorgateway/shipway"
	"github.com/epifi/gamma/featurestore"
	fennel2 "github.com/epifi/gamma/featurestore/fennel"
	scienaptic2 "github.com/epifi/gamma/featurestore/scienaptic"
	"github.com/epifi/gamma/firefly"
	impl2 "github.com/epifi/gamma/firefly/accounting/dao/impl"
	"github.com/epifi/gamma/firefly/activity"
	"github.com/epifi/gamma/firefly/activity/factory/create_card_and_account_provider"
	"github.com/epifi/gamma/firefly/activity/factory/next_action_provider"
	"github.com/epifi/gamma/firefly/activity/factory/next_action_provider/fi_lite"
	"github.com/epifi/gamma/firefly/activity/factory/next_action_provider/massunsecured"
	"github.com/epifi/gamma/firefly/activity/factory/next_action_provider/unsecured"
	"github.com/epifi/gamma/firefly/activity/factory/register_customer_provider"
	"github.com/epifi/gamma/firefly/activity/factory/user_communication_provider"
	impl4 "github.com/epifi/gamma/firefly/billing/dao/impl"
	card_recommendation2 "github.com/epifi/gamma/firefly/card_recommendation"
	common2 "github.com/epifi/gamma/firefly/common"
	comms2 "github.com/epifi/gamma/firefly/comms"
	"github.com/epifi/gamma/firefly/config"
	"github.com/epifi/gamma/firefly/config/common"
	"github.com/epifi/gamma/firefly/config/genconf"
	"github.com/epifi/gamma/firefly/config/worker"
	genconf2 "github.com/epifi/gamma/firefly/config/worker/genconf"
	consumer2 "github.com/epifi/gamma/firefly/consumer"
	"github.com/epifi/gamma/firefly/cryptor"
	"github.com/epifi/gamma/firefly/cx"
	"github.com/epifi/gamma/firefly/dao"
	"github.com/epifi/gamma/firefly/dao/impl"
	"github.com/epifi/gamma/firefly/developer"
	"github.com/epifi/gamma/firefly/developer/processor"
	"github.com/epifi/gamma/firefly/helper"
	actor2 "github.com/epifi/gamma/firefly/internal/actor"
	user2 "github.com/epifi/gamma/firefly/internal/user"
	impl3 "github.com/epifi/gamma/firefly/lms/dao/impl"
	pinot3 "github.com/epifi/gamma/firefly/pinot"
	"github.com/epifi/gamma/firefly/pinot/consumer"
	dao2 "github.com/epifi/gamma/firefly/pinot/dao"
	impl5 "github.com/epifi/gamma/firefly/v2/dao/impl"
	types3 "github.com/epifi/gamma/firefly/v2/wire/types"
	types2 "github.com/epifi/gamma/firefly/wire/types"
	"github.com/epifi/gamma/pkg/feature/release"
	genconf4 "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	pinot4 "github.com/epifi/gamma/pkg/pinot"
	"gorm.io/gorm"
)

// Injectors from wire.go:

func InitialiseFireflySvc(creditCardDb types.CreditCardPGDB, celestialClient celestial.CelestialClient, signalWorkflowPublisher types2.SignalWorkflowPublisher, conf *config.Config, actorClient actor.ActorClient, ffAccClient accounting.AccountingClient, ccVgClient types2.CreditCardVgClientWithInterceptors, userClient user.UsersClient, bankCustClient bankcust.BankCustomerServiceClient, billingClient billing.BillingClient, docsClient docs.DocsClient, orderClient order.OrderServiceClient, payClient pay.PayClient, rewardsClient rewards.RewardsGeneratorClient, limitEstimatorClient credit_limit_estimator.CreditLimitEstimatorClient, pinotClient pinot.TxnAggregatesClient, lmsClient lms.LoanManagementSystemClient, cCVgPciClient firefly.CreditCardClientWithInterceptorsToVendorGatewayPCIServer, releaseConfig *genconf.Config, userGroupClient group.GroupClient, eventBroker events.Broker, depositClient deposit.DepositClient, fireflyClient firefly2.FireflyClient, savingsClient savings.SavingsClient, biometricClient biometrics.BiometricsServiceClient, temporalClient types2.FireflyClient, rewardsProjectionClient projector.ProjectorServiceClient, rewardsAggregatesClient pinot2.RewardsAggregatesClient, rateLimiterCl types.RateLimiterRedisStore, profileClient profile.ProfileClient, redemptionClient redemption.OfferRedemptionServiceClient, ffCardRecommendationClient card_recommendation.CardRecommendationServiceClient, fireflyRedisStore types2.FireflyRedisStore, commsClient comms.CommsClient, consentClient consent.ConsentClient, dcClient provisioning.CardProvisioningClient, segmentationClient segment.SegmentationServiceClient, piClient paymentinstrument.PiClient, client inapptargetedcomms.InAppTargetedCommsClient, fireflyV2Client v2.FireflyV2Client) *firefly.Service {
	ccCacheStorage := types2.CcCacheStorageProvider(conf, fireflyRedisStore)
	cardRequestCacheConfig := types2.CardRequestCacheConfigProvider(conf)
	db := types.CreditCardPGDBGormDBProvider(creditCardDb)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	cardRequestDao := impl.NewCrdbCardRequest(db, domainIdGenerator)
	doOnce := DoOnceProvider(db)
	atmTxnSuccessRule := comms2.NewAtmTxnSuccessRule()
	atmTxnFailureRule := comms2.NewAtmTxnFailureRule()
	crossBorderTxnSuccessRule := comms2.NewCrossBorderTxnSuccessRule()
	txnSuccessRule := comms2.NewTxnSuccessRule()
	creditCardClient := types2.CreditCardVgClientProvider(ccVgClient)
	commsDataHelper := helper.NewCommsDataHelper(fireflyClient, creditCardClient, actorClient, userClient, ffAccClient, billingClient, depositClient)
	txnFailureRule := comms2.NewTxnFailureRule(commsDataHelper)
	txnReversalSuccessRule := comms2.NewTxnReversalSuccessRule()
	onlineSettingsEnabledRule := comms2.NewOnlineSettingsEnabledRule()
	onlineSettingsDisabledRule := comms2.NewOnlineSettingsDisabledRule()
	posSettingsEnabledRule := comms2.NewPosSettingsEnabledRule()
	posSettingsDisabledRule := comms2.NewPosSettingsDisabledRule()
	internationalSettingsEnabledRule := comms2.NewInternationalSettingsEnabledRule()
	internationalSettingsDisabledRule := comms2.NewInternationalSettingsDisabledRule()
	contactlessSettingsEnabledRule := comms2.NewContactlessSettingsEnabledRule()
	contactlessSettingsDisabledRule := comms2.NewContactlessSettingsDisabledRule()
	cardUnfreezeSuccessRule := comms2.NewCardUnfreezeSuccessRule(commsDataHelper)
	creditCardPosPurchaseLimitChangedRule := comms2.NewCreditCardPosPurchaseLimitChangedRule(commsDataHelper)
	creditCardOnlinePurchaseLimitChangedRule := comms2.NewCreditCardOnlinePurchaseLimitChangedRule(commsDataHelper)
	cardFreezeSuccessRule := comms2.NewCardFreezeSuccessRule(commsDataHelper)
	creditCardCardUsageChangeFailureRule := comms2.NewCreditCardCardUsageChangeFailureRule(commsDataHelper)
	creditCardContactlessPurchaseLimitChangedRule := comms2.NewCreditCardContactlessPurchaseLimitChangedRule(commsDataHelper)
	cardFreezeFailureRule := comms2.NewCardFreezeFailureRule(commsDataHelper)
	creditCardLimitChangeFailureRule := comms2.NewCreditCardLimitChangeFailureRule(commsDataHelper)
	creditCardPinChangeSuccessRule := comms2.NewCreditCardPinChangeSuccessRule(commsDataHelper)
	cardUnFreezeFailureRule := comms2.NewCardUnFreezeFailureRule(commsDataHelper)
	cardActivationFailureRule := comms2.NewCardActivationFailureRule()
	cardActivationSuccessRule := comms2.NewCardActivationSuccessRule()
	cardReissueSuccessRule := comms2.NewCardReissueSuccessRule()
	issuePhysicalCardSuccessRule := comms2.NewIssuePhysicalCardSuccessRule()
	txnFailurePinRetriesExceededRule := comms2.NewTxnFailurePinRetriesExceededRule(commsDataHelper)
	paymentReminderWithInterestRule := comms2.NewPaymentReminderWithInterestRule(commsDataHelper)
	paymentReminderWithoutInterestRule := comms2.NewPaymentReminderWithoutInterestRule(commsDataHelper)
	paymentReminderBeforeDueDateRule := comms2.NewPaymentReminderBeforeDueDateRule(commsDataHelper)
	statementGeneratedRule := comms2.NewStatementGeneratedRule(commsDataHelper)
	cashWithdrawalFeeRule := comms2.NewCashWithdrawalFeeRule(commsDataHelper)
	successfulRepaymentRule := comms2.NewSuccessfulRepaymentRule(commsDataHelper)
	creditUtilisationReachingThresholdRule := comms2.NewCreditUtilisationReachingThresholdRule(commsDataHelper)
	unpaidDuesFeesRule := comms2.NewUnpaidDuesFeesRule(commsDataHelper)
	joiningFeeRule := comms2.NewJoiningFeeRule(commsDataHelper)
	genericCreditRule := comms2.NewGenericCreditRule(commsDataHelper)
	cardIssueSuccessWithLimitRule := comms2.NewCardIssueSuccessWithLimitRule(commsDataHelper)
	creditCardDispatchedRule := comms2.NewCreditCardDispatchedRule(commsDataHelper)
	welcomeOfferClaimRule := comms2.NewWelcomeOfferClaimRule(commsDataHelper)
	cardNotActivatedRule := comms2.NewCardNotActivatedRule(commsDataHelper)
	creditCardClosedRule := comms2.NewCreditCardClosedRule()
	securedCardFdCreationSuccessRule := comms2.NewSecuredCardFdCreationSuccessRule(commsDataHelper)
	securedCardFdLienMarkingSuccessSuccessRule := comms2.NewSecuredCardFdLienMarkingSuccessSuccessRule(commsDataHelper)
	ccProfileValidationSuccessRule := comms2.NewCCProfileValidationSuccessRule()
	ccProfileValidationFailureRule := comms2.NewCCProfileValidationFailureRule()
	creditCardPaymentReminderWhatsAppOnBillGenDayRule := comms2.NewCreditCardPaymentReminderWhatsAppOnBillGenDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppBeforeDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppBeforeDueDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppOnDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppOnDueDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppAfterDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppAfterDueDayRule(commsDataHelper)
	creditCardDeliveredRule := comms2.NewCreditCardDeliveredRule(commsDataHelper)
	creditCardReturnedRule := comms2.NewCreditCardReturnedRule(commsDataHelper)
	creditCardOutForDeliveryRule := comms2.NewCreditCardOutForDeliveryRule(commsDataHelper)
	emiCreatedRule := comms2.NewEmiCreatedRule(commsDataHelper)
	emiClosedRule := comms2.NewEmiClosedRule()
	emiPreClosedRule := comms2.NewEmiPreClosedRule()
	emiCancelledRule := comms2.NewEmiCancelledRule()
	webEligibilityDropOffSmsRule := comms2.NewWebEligibilityDropOffSmsRule(commsDataHelper)
	webEligibilityDropOffEmailRule := comms2.NewWebEligibilityDropOffEmailRule(commsDataHelper)
	webEligibilityDropOffWhatsappRule := comms2.NewWebEligibilityDropOffWhatsappRule(commsDataHelper)
	creditCardWelcomeRule := comms2.NewCreditCardWelcomeRule()
	magnifiWelcomeRule := comms2.NewMagnifiWelcomeRule()
	frmDeclineTxnRule := comms2.NewFrmDeclineTxnRule()
	ccDeactivateInXDaysRule := comms2.NewCcDeactivateInXDaysRule()
	asyncProcessor := comms2.NewAsyncProcessor(commsClient, actorClient, doOnce, atmTxnSuccessRule, atmTxnFailureRule, crossBorderTxnSuccessRule, txnSuccessRule, txnFailureRule, txnReversalSuccessRule, onlineSettingsEnabledRule, onlineSettingsDisabledRule, posSettingsEnabledRule, posSettingsDisabledRule, internationalSettingsEnabledRule, internationalSettingsDisabledRule, contactlessSettingsEnabledRule, contactlessSettingsDisabledRule, cardUnfreezeSuccessRule, creditCardPosPurchaseLimitChangedRule, creditCardOnlinePurchaseLimitChangedRule, cardFreezeSuccessRule, creditCardCardUsageChangeFailureRule, creditCardContactlessPurchaseLimitChangedRule, cardFreezeFailureRule, creditCardLimitChangeFailureRule, creditCardPinChangeSuccessRule, cardUnFreezeFailureRule, cardActivationFailureRule, cardActivationSuccessRule, cardReissueSuccessRule, issuePhysicalCardSuccessRule, txnFailurePinRetriesExceededRule, paymentReminderWithInterestRule, paymentReminderWithoutInterestRule, paymentReminderBeforeDueDateRule, statementGeneratedRule, cashWithdrawalFeeRule, successfulRepaymentRule, creditUtilisationReachingThresholdRule, unpaidDuesFeesRule, joiningFeeRule, genericCreditRule, cardIssueSuccessWithLimitRule, creditCardDispatchedRule, welcomeOfferClaimRule, cardNotActivatedRule, creditCardClosedRule, securedCardFdCreationSuccessRule, securedCardFdLienMarkingSuccessSuccessRule, ccProfileValidationSuccessRule, ccProfileValidationFailureRule, creditCardPaymentReminderWhatsAppOnBillGenDayRule, creditCardPaymentReminderWhatsAppBeforeDueDayRule, creditCardPaymentReminderWhatsAppOnDueDayRule, creditCardPaymentReminderWhatsAppAfterDueDayRule, creditCardDeliveredRule, creditCardReturnedRule, creditCardOutForDeliveryRule, emiCreatedRule, emiClosedRule, emiPreClosedRule, emiCancelledRule, webEligibilityDropOffSmsRule, webEligibilityDropOffEmailRule, webEligibilityDropOffWhatsappRule, creditCardWelcomeRule, magnifiWelcomeRule, frmDeclineTxnRule, ccDeactivateInXDaysRule)
	cardRequestDaoWithInstrumentation := dao.NewCardRequestDaoWithInstrumentation(ccCacheStorage, cardRequestCacheConfig, cardRequestDao, eventBroker, asyncProcessor)
	daoCardRequestDao := dao.ProvideCardRequestDao(cardRequestDaoWithInstrumentation)
	cardRequestStageDao := impl.NewCrdbCardRequestStage(db, domainIdGenerator)
	creditCardDao := impl.NewCrdbCreditCard(db, domainIdGenerator)
	creditCardOfferCacheConfig := types2.CreditCardOfferCacheConfigProvider(conf)
	crdbCreditCardOfferDao := impl.NewCrdbCreditCardOfferDao(db, domainIdGenerator)
	creditCardOfferDaoCache := dao.NewCreditCardOfferDaoCache(ccCacheStorage, creditCardOfferCacheConfig, crdbCreditCardOfferDao)
	creditCardOffersDao := dao.ProvideCreditCardOfferDao(creditCardOfferDaoCache)
	crdbCreditCardOfferEligibilityCriteriaDao := impl.NewCrdbCreditCardOfferEligibilityCriteriaDao(db, domainIdGenerator)
	rpcHelper := helper.NewRpcHelper(creditCardClient, actorClient, userClient, celestialClient, bankCustClient, ffAccClient, billingClient, docsClient, payClient, orderClient, rewardsClient, pinotClient, lmsClient, fireflyClient, depositClient, savingsClient, rewardsProjectionClient, profileClient)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(db)
	fireflyCryptorMap := cryptor.NewFireflyCryptorMap(conf)
	featureReleaseConfig := ReleaseConfigProvider(releaseConfig)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	clientClient := types2.FireflyClientProvider(temporalClient)
	redisClient := types.RateLimiterRedisStoreRedisClientProvider(rateLimiterCl)
	slidingWindowLogWithRedisImpl := store.NewSlidingWindowLogWithRedis(redisClient)
	rateLimitConfig := RateLimiterConfigProvider(releaseConfig)
	v := DummyRateLimiterOptionsProvider()
	rateLimiterImpl := ratelimiter.NewRateLimiterV2(slidingWindowLogWithRedisImpl, rateLimitConfig, v...)
	service := firefly.NewService(daoCardRequestDao, cardRequestStageDao, creditCardDao, creditCardOffersDao, crdbCreditCardOfferEligibilityCriteriaDao, celestialClient, creditCardClient, userClient, signalWorkflowPublisher, conf, ffAccClient, actorClient, rpcHelper, gormTxnExecutor, fireflyCryptorMap, rewardsClient, limitEstimatorClient, cCVgPciClient, eventBroker, evaluator, biometricClient, releaseConfig, clientClient, rateLimiterImpl, doOnce, redemptionClient, ffCardRecommendationClient, redisClient, consentClient, dcClient, segmentationClient, piClient, rewardsAggregatesClient, pinotClient, billingClient, client, fireflyV2Client, userGroupClient)
	return service
}

func InitializeFireflyDevEntityService(db types.CreditCardPGDB, cfg *config.Config, FireflyRedisStore types2.FireflyRedisStore, commsClient comms.CommsClient, depositClient deposit.DepositClient, billingClient billing.BillingClient, accountingClient accounting.AccountingClient, userClient user.UsersClient, ccVgClient creditcard.CreditCardClient, ffClient firefly2.FireflyClient, actorClient actor.ActorClient, eventBroker events.Broker, ccFederalDb types3.CreditCardFederalPGDB) *developer.FireflyDevEntity {
	gormDB := types.CreditCardPGDBGormDBProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	creditCardDao := impl.NewCrdbCreditCard(gormDB, domainIdGenerator)
	devCreditCardEntity := processor.NewDevCreditCardEntity(creditCardDao)
	ccCacheStorage := types2.CcCacheStorageProvider(cfg, FireflyRedisStore)
	cardRequestCacheConfig := types2.CardRequestCacheConfigProvider(cfg)
	cardRequestDao := impl.NewCrdbCardRequest(gormDB, domainIdGenerator)
	doOnce := DoOnceProvider(gormDB)
	atmTxnSuccessRule := comms2.NewAtmTxnSuccessRule()
	atmTxnFailureRule := comms2.NewAtmTxnFailureRule()
	crossBorderTxnSuccessRule := comms2.NewCrossBorderTxnSuccessRule()
	txnSuccessRule := comms2.NewTxnSuccessRule()
	commsDataHelper := helper.NewCommsDataHelper(ffClient, ccVgClient, actorClient, userClient, accountingClient, billingClient, depositClient)
	txnFailureRule := comms2.NewTxnFailureRule(commsDataHelper)
	txnReversalSuccessRule := comms2.NewTxnReversalSuccessRule()
	onlineSettingsEnabledRule := comms2.NewOnlineSettingsEnabledRule()
	onlineSettingsDisabledRule := comms2.NewOnlineSettingsDisabledRule()
	posSettingsEnabledRule := comms2.NewPosSettingsEnabledRule()
	posSettingsDisabledRule := comms2.NewPosSettingsDisabledRule()
	internationalSettingsEnabledRule := comms2.NewInternationalSettingsEnabledRule()
	internationalSettingsDisabledRule := comms2.NewInternationalSettingsDisabledRule()
	contactlessSettingsEnabledRule := comms2.NewContactlessSettingsEnabledRule()
	contactlessSettingsDisabledRule := comms2.NewContactlessSettingsDisabledRule()
	cardUnfreezeSuccessRule := comms2.NewCardUnfreezeSuccessRule(commsDataHelper)
	creditCardPosPurchaseLimitChangedRule := comms2.NewCreditCardPosPurchaseLimitChangedRule(commsDataHelper)
	creditCardOnlinePurchaseLimitChangedRule := comms2.NewCreditCardOnlinePurchaseLimitChangedRule(commsDataHelper)
	cardFreezeSuccessRule := comms2.NewCardFreezeSuccessRule(commsDataHelper)
	creditCardCardUsageChangeFailureRule := comms2.NewCreditCardCardUsageChangeFailureRule(commsDataHelper)
	creditCardContactlessPurchaseLimitChangedRule := comms2.NewCreditCardContactlessPurchaseLimitChangedRule(commsDataHelper)
	cardFreezeFailureRule := comms2.NewCardFreezeFailureRule(commsDataHelper)
	creditCardLimitChangeFailureRule := comms2.NewCreditCardLimitChangeFailureRule(commsDataHelper)
	creditCardPinChangeSuccessRule := comms2.NewCreditCardPinChangeSuccessRule(commsDataHelper)
	cardUnFreezeFailureRule := comms2.NewCardUnFreezeFailureRule(commsDataHelper)
	cardActivationFailureRule := comms2.NewCardActivationFailureRule()
	cardActivationSuccessRule := comms2.NewCardActivationSuccessRule()
	cardReissueSuccessRule := comms2.NewCardReissueSuccessRule()
	issuePhysicalCardSuccessRule := comms2.NewIssuePhysicalCardSuccessRule()
	txnFailurePinRetriesExceededRule := comms2.NewTxnFailurePinRetriesExceededRule(commsDataHelper)
	paymentReminderWithInterestRule := comms2.NewPaymentReminderWithInterestRule(commsDataHelper)
	paymentReminderWithoutInterestRule := comms2.NewPaymentReminderWithoutInterestRule(commsDataHelper)
	paymentReminderBeforeDueDateRule := comms2.NewPaymentReminderBeforeDueDateRule(commsDataHelper)
	statementGeneratedRule := comms2.NewStatementGeneratedRule(commsDataHelper)
	cashWithdrawalFeeRule := comms2.NewCashWithdrawalFeeRule(commsDataHelper)
	successfulRepaymentRule := comms2.NewSuccessfulRepaymentRule(commsDataHelper)
	creditUtilisationReachingThresholdRule := comms2.NewCreditUtilisationReachingThresholdRule(commsDataHelper)
	unpaidDuesFeesRule := comms2.NewUnpaidDuesFeesRule(commsDataHelper)
	joiningFeeRule := comms2.NewJoiningFeeRule(commsDataHelper)
	genericCreditRule := comms2.NewGenericCreditRule(commsDataHelper)
	cardIssueSuccessWithLimitRule := comms2.NewCardIssueSuccessWithLimitRule(commsDataHelper)
	creditCardDispatchedRule := comms2.NewCreditCardDispatchedRule(commsDataHelper)
	welcomeOfferClaimRule := comms2.NewWelcomeOfferClaimRule(commsDataHelper)
	cardNotActivatedRule := comms2.NewCardNotActivatedRule(commsDataHelper)
	creditCardClosedRule := comms2.NewCreditCardClosedRule()
	securedCardFdCreationSuccessRule := comms2.NewSecuredCardFdCreationSuccessRule(commsDataHelper)
	securedCardFdLienMarkingSuccessSuccessRule := comms2.NewSecuredCardFdLienMarkingSuccessSuccessRule(commsDataHelper)
	ccProfileValidationSuccessRule := comms2.NewCCProfileValidationSuccessRule()
	ccProfileValidationFailureRule := comms2.NewCCProfileValidationFailureRule()
	creditCardPaymentReminderWhatsAppOnBillGenDayRule := comms2.NewCreditCardPaymentReminderWhatsAppOnBillGenDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppBeforeDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppBeforeDueDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppOnDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppOnDueDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppAfterDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppAfterDueDayRule(commsDataHelper)
	creditCardDeliveredRule := comms2.NewCreditCardDeliveredRule(commsDataHelper)
	creditCardReturnedRule := comms2.NewCreditCardReturnedRule(commsDataHelper)
	creditCardOutForDeliveryRule := comms2.NewCreditCardOutForDeliveryRule(commsDataHelper)
	emiCreatedRule := comms2.NewEmiCreatedRule(commsDataHelper)
	emiClosedRule := comms2.NewEmiClosedRule()
	emiPreClosedRule := comms2.NewEmiPreClosedRule()
	emiCancelledRule := comms2.NewEmiCancelledRule()
	webEligibilityDropOffSmsRule := comms2.NewWebEligibilityDropOffSmsRule(commsDataHelper)
	webEligibilityDropOffEmailRule := comms2.NewWebEligibilityDropOffEmailRule(commsDataHelper)
	webEligibilityDropOffWhatsappRule := comms2.NewWebEligibilityDropOffWhatsappRule(commsDataHelper)
	creditCardWelcomeRule := comms2.NewCreditCardWelcomeRule()
	magnifiWelcomeRule := comms2.NewMagnifiWelcomeRule()
	frmDeclineTxnRule := comms2.NewFrmDeclineTxnRule()
	ccDeactivateInXDaysRule := comms2.NewCcDeactivateInXDaysRule()
	asyncProcessor := comms2.NewAsyncProcessor(commsClient, actorClient, doOnce, atmTxnSuccessRule, atmTxnFailureRule, crossBorderTxnSuccessRule, txnSuccessRule, txnFailureRule, txnReversalSuccessRule, onlineSettingsEnabledRule, onlineSettingsDisabledRule, posSettingsEnabledRule, posSettingsDisabledRule, internationalSettingsEnabledRule, internationalSettingsDisabledRule, contactlessSettingsEnabledRule, contactlessSettingsDisabledRule, cardUnfreezeSuccessRule, creditCardPosPurchaseLimitChangedRule, creditCardOnlinePurchaseLimitChangedRule, cardFreezeSuccessRule, creditCardCardUsageChangeFailureRule, creditCardContactlessPurchaseLimitChangedRule, cardFreezeFailureRule, creditCardLimitChangeFailureRule, creditCardPinChangeSuccessRule, cardUnFreezeFailureRule, cardActivationFailureRule, cardActivationSuccessRule, cardReissueSuccessRule, issuePhysicalCardSuccessRule, txnFailurePinRetriesExceededRule, paymentReminderWithInterestRule, paymentReminderWithoutInterestRule, paymentReminderBeforeDueDateRule, statementGeneratedRule, cashWithdrawalFeeRule, successfulRepaymentRule, creditUtilisationReachingThresholdRule, unpaidDuesFeesRule, joiningFeeRule, genericCreditRule, cardIssueSuccessWithLimitRule, creditCardDispatchedRule, welcomeOfferClaimRule, cardNotActivatedRule, creditCardClosedRule, securedCardFdCreationSuccessRule, securedCardFdLienMarkingSuccessSuccessRule, ccProfileValidationSuccessRule, ccProfileValidationFailureRule, creditCardPaymentReminderWhatsAppOnBillGenDayRule, creditCardPaymentReminderWhatsAppBeforeDueDayRule, creditCardPaymentReminderWhatsAppOnDueDayRule, creditCardPaymentReminderWhatsAppAfterDueDayRule, creditCardDeliveredRule, creditCardReturnedRule, creditCardOutForDeliveryRule, emiCreatedRule, emiClosedRule, emiPreClosedRule, emiCancelledRule, webEligibilityDropOffSmsRule, webEligibilityDropOffEmailRule, webEligibilityDropOffWhatsappRule, creditCardWelcomeRule, magnifiWelcomeRule, frmDeclineTxnRule, ccDeactivateInXDaysRule)
	cardRequestDaoWithInstrumentation := dao.NewCardRequestDaoWithInstrumentation(ccCacheStorage, cardRequestCacheConfig, cardRequestDao, eventBroker, asyncProcessor)
	daoCardRequestDao := dao.ProvideCardRequestDao(cardRequestDaoWithInstrumentation)
	devCardRequestEntity := processor.NewDevCardRequestEntity(daoCardRequestDao)
	creditAccountDaoCRDB := impl2.NewCreditAccountDaoCRDB(gormDB, domainIdGenerator)
	devCreditAccountEntity := processor.NewDevCreditAccountEntity(creditAccountDaoCRDB)
	creditCardTransactionDaoCRDB := impl2.NewCreditCardTransactionDaoCRDB(gormDB, domainIdGenerator)
	devCreditCardTransactionEntity := processor.NewDevCreditCardTransactionEntity(creditCardTransactionDaoCRDB)
	transactionAdditionalInfoDaoCRDB := impl2.NewTransactionAdditionalInfoDaoCRDB(gormDB)
	devTransactionAdditionalInfoEntity := processor.NewDevTransactionAdditionalInfoEntity(transactionAdditionalInfoDaoCRDB)
	cardRequestStageDao := impl.NewCrdbCardRequestStage(gormDB, domainIdGenerator)
	devCardRequestStageEntity := processor.NewDevCardRequestStageEntity(cardRequestStageDao)
	crdbLoanAccountDao := impl3.NewCrdbLoanAccountDao(gormDB, domainIdGenerator)
	devLoanAccountEntity := processor.NewDevLoanAccountEntity(crdbLoanAccountDao)
	crdbTransactionLoanOffersDao := impl3.NewCrdbTransactionLoanOffersDao(gormDB, domainIdGenerator)
	devTransactionLoanOffersEntity := processor.NewDevTransactionLoanOffersEntity(crdbTransactionLoanOffersDao)
	creditCardBillDaoCRDB := impl4.NewCrdbCreditCardBillDao(gormDB, domainIdGenerator)
	devCreditCardBillsEntity := processor.NewDevCreditCardBillsEntity(creditCardBillDaoCRDB)
	creditCardPaymentDaoCRDB := impl4.NewCrdbCreditCardPaymentDao(gormDB, domainIdGenerator)
	devCreditCardPaymentEntity := processor.NewDevCreditCardPaymentEntity(creditCardPaymentDaoCRDB)
	cardAuditDao := impl.NewCrdbCardAudit(gormDB, domainIdGenerator)
	devCreditCardAuditEntity := processor.NewDevCreditCardAuditEntity(cardAuditDao)
	creditCardOfferCacheConfig := types2.CreditCardOfferCacheConfigProvider(cfg)
	crdbCreditCardOfferDao := impl.NewCrdbCreditCardOfferDao(gormDB, domainIdGenerator)
	creditCardOfferDaoCache := dao.NewCreditCardOfferDaoCache(ccCacheStorage, creditCardOfferCacheConfig, crdbCreditCardOfferDao)
	creditCardOffersDao := dao.ProvideCreditCardOfferDao(creditCardOfferDaoCache)
	devCreditCardOffersEntity := processor.NewDevCreditCardOffersEntity(creditCardOffersDao)
	crdbCreditCardOfferEligibilityCriteriaDao := impl.NewCrdbCreditCardOfferEligibilityCriteriaDao(gormDB, domainIdGenerator)
	devCreditCardOfferEligibilityCriteriaEntity := processor.NewDevCreditCardOfferEligibilityCriteriaEntity(crdbCreditCardOfferEligibilityCriteriaDao)
	creditCardSkuDao := impl.NewCrdbCreditCardSku(gormDB)
	devCreditCardSku := processor.NewDevCreditCardSku(creditCardSkuDao)
	creditCardSkuOverride := impl.NewCrdbCreditCardSkuOverride(gormDB)
	devCreditCardSkuOverridesEntity := processor.NewDevCreditCardSkuOverridesEntity(creditCardSkuOverride)
	disputedTransactionDaoCRDB := impl2.NewDisputedTransactionDaoCRDB(gormDB)
	devCreditCardDisputedTransactionsEntity := processor.NewDevCreditCardDisputedTransactionsEntity(disputedTransactionDaoCRDB)
	client := pinotClientProvider(cfg)
	ccTransactionDaoPinot := dao2.NewCcTransactionDaoPinot(client)
	devPinotCcTransactionsEntity := processor.NewDevPinotCcTransactionsEntity(ccTransactionDaoPinot)
	creditCardRecommendationInfoDao := impl.NewCrdbCreditCardRecommendationInfo(gormDB, domainIdGenerator)
	devCreditCardRecommendationInfoEntity := processor.NewDevCreditCardRecommendationInfoEntity(creditCardRecommendationInfoDao)
	implCardRequestDao := impl5.NewCardRequestDao(ccFederalDb, domainIdGenerator)
	devCardRequestV2Entity := processor.NewDevCardRequestV2Entity(implCardRequestDao)
	implCreditCardDao := impl5.NewCreditCardDao(ccFederalDb, domainIdGenerator)
	devCreditCardV2Entity := processor.NewDevCreditCardV2Entity(implCreditCardDao)
	creditCardOfferDao := impl5.NewCreditCardOfferDao(ccFederalDb, domainIdGenerator)
	devCreditCardOffersV2Entity := processor.NewDevCreditCardOffersV2Entity(creditCardOfferDao)
	devFactory := developer.NewDevFactory(devCreditCardEntity, devCardRequestEntity, devCreditAccountEntity, devCreditCardTransactionEntity, devTransactionAdditionalInfoEntity, devCardRequestStageEntity, devLoanAccountEntity, devTransactionLoanOffersEntity, devCreditCardBillsEntity, devCreditCardPaymentEntity, devCreditCardAuditEntity, devCreditCardOffersEntity, devCreditCardOfferEligibilityCriteriaEntity, devCreditCardSku, devCreditCardSkuOverridesEntity, devCreditCardDisputedTransactionsEntity, devPinotCcTransactionsEntity, devCreditCardRecommendationInfoEntity, devCardRequestV2Entity, devCreditCardV2Entity, devCreditCardOffersV2Entity)
	fireflyDevEntity := developer.NewFireflyDevEntity(devFactory, creditCardDao, cardAuditDao, daoCardRequestDao, creditCardOffersDao, creditCardSkuDao, creditCardSkuOverride, creditAccountDaoCRDB, creditCardTransactionDaoCRDB, transactionAdditionalInfoDaoCRDB, creditCardBillDaoCRDB, creditCardPaymentDaoCRDB, crdbLoanAccountDao, crdbTransactionLoanOffersDao, disputedTransactionDaoCRDB, cardRequestStageDao, crdbCreditCardOfferEligibilityCriteriaDao, ccTransactionDaoPinot, creditCardRecommendationInfoDao)
	return fireflyDevEntity
}

func InitialiseActivityProcessor(creditCardDb *gorm.DB, ccVgClient creditcard.CreditCardClient, userClient user.UsersClient, actorClient actor.ActorClient, txnExecutor storagev2.TxnExecutor, authClient orchestrator.OrchestratorClient, conf *worker.Config, vkycClient vkyc.VKYCClient, empClient employment.EmploymentClient, notification *common.Notification, notificationScheduleConfig *worker.NotificationSchedule, billingClient billing.BillingClient, accountingClient accounting.AccountingClient, commsClient comms.CommsClient, docsClient docs.DocsClient, celestialClient celestial.CelestialClient, bankCustClient bankcust.BankCustomerServiceClient, vgShipwayClient shipway.ShipwayClient, ffClient firefly2.FireflyClient, piClient paymentinstrument.PiClient, profValidationClient profilevalidation.ProfileValidationClient, orderClient order.OrderServiceClient, payClient pay.PayClient, creditLimitEstimatorClient credit_limit_estimator.CreditLimitEstimatorClient, ccBillGenerationEventPublisher types2.BillGeneratedEventPublisher, ccStageUpdateEventPublisher types2.StageUpdateEventPublisher, s3Client s3.S3Client, rewardsClient rewards.RewardsGeneratorClient, broker events.Broker, pinotClient pinot.TxnAggregatesClient, lmsClient lms.LoanManagementSystemClient, dcClient provisioning.CardProvisioningClient, creditReportV2ManagerClient creditreportv2.CreditReportManagerClient, obfuscatorClient obfuscator.ObfuscatorClient, creditLineVgClient creditline.CreditLineClient, panClient pan.PanClient, fireflyWorkerGenConfig *genconf2.Config, userGroupClient group.GroupClient, nudgeClient nudge.NudgeServiceClient, depositClient deposit.DepositClient, savingsClient savings.SavingsClient, nextActionProviderFactory *next_action_provider.NextActionProviderFactory, registerCustomerFactory *register_customer_provider.RegisterCustomerProviderFactory, createCardAndAccountFactory *create_card_and_account_provider.CreateCardAndAccountProviderFactory, userCommunicationFactory *user_communication_provider.UserCommunicationProviderFactory, creditReportManagerClient credit_report.CreditReportManagerClient, consentClient consent.ConsentClient, riskClient risk.RiskClient, autClient auth.AuthClient, onbClient onboarding.OnboardingClient, kycClient kyc.KycClient, breVgClient bre.BusinessRuleEngineClient, rewardsProjectionClient projector.ProjectorServiceClient, fennelClient fennel.FennelFeatureStoreClient, scienapticClient scienaptic.ScienapticClient, profileClient profile.ProfileClient, client bre2.BreClient, productClient product.ProductClient, panVgClient pan2.PANClient, FireflyRedisStore types2.FireflyRedisStore, acPiClient account_pi.AccountPIRelationClient) *activity.Processor {
	ccCacheStorage := types2.WorkerCcCacheStorageProvider(conf, FireflyRedisStore)
	cardRequestCacheConfig := types2.WorkerCardRequestCacheConfigProvider(conf)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	cardRequestDao := impl.NewCrdbCardRequest(creditCardDb, domainIdGenerator)
	doOnce := DoOnceProvider(creditCardDb)
	atmTxnSuccessRule := comms2.NewAtmTxnSuccessRule()
	atmTxnFailureRule := comms2.NewAtmTxnFailureRule()
	crossBorderTxnSuccessRule := comms2.NewCrossBorderTxnSuccessRule()
	txnSuccessRule := comms2.NewTxnSuccessRule()
	commsDataHelper := helper.NewCommsDataHelper(ffClient, ccVgClient, actorClient, userClient, accountingClient, billingClient, depositClient)
	txnFailureRule := comms2.NewTxnFailureRule(commsDataHelper)
	txnReversalSuccessRule := comms2.NewTxnReversalSuccessRule()
	onlineSettingsEnabledRule := comms2.NewOnlineSettingsEnabledRule()
	onlineSettingsDisabledRule := comms2.NewOnlineSettingsDisabledRule()
	posSettingsEnabledRule := comms2.NewPosSettingsEnabledRule()
	posSettingsDisabledRule := comms2.NewPosSettingsDisabledRule()
	internationalSettingsEnabledRule := comms2.NewInternationalSettingsEnabledRule()
	internationalSettingsDisabledRule := comms2.NewInternationalSettingsDisabledRule()
	contactlessSettingsEnabledRule := comms2.NewContactlessSettingsEnabledRule()
	contactlessSettingsDisabledRule := comms2.NewContactlessSettingsDisabledRule()
	cardUnfreezeSuccessRule := comms2.NewCardUnfreezeSuccessRule(commsDataHelper)
	creditCardPosPurchaseLimitChangedRule := comms2.NewCreditCardPosPurchaseLimitChangedRule(commsDataHelper)
	creditCardOnlinePurchaseLimitChangedRule := comms2.NewCreditCardOnlinePurchaseLimitChangedRule(commsDataHelper)
	cardFreezeSuccessRule := comms2.NewCardFreezeSuccessRule(commsDataHelper)
	creditCardCardUsageChangeFailureRule := comms2.NewCreditCardCardUsageChangeFailureRule(commsDataHelper)
	creditCardContactlessPurchaseLimitChangedRule := comms2.NewCreditCardContactlessPurchaseLimitChangedRule(commsDataHelper)
	cardFreezeFailureRule := comms2.NewCardFreezeFailureRule(commsDataHelper)
	creditCardLimitChangeFailureRule := comms2.NewCreditCardLimitChangeFailureRule(commsDataHelper)
	creditCardPinChangeSuccessRule := comms2.NewCreditCardPinChangeSuccessRule(commsDataHelper)
	cardUnFreezeFailureRule := comms2.NewCardUnFreezeFailureRule(commsDataHelper)
	cardActivationFailureRule := comms2.NewCardActivationFailureRule()
	cardActivationSuccessRule := comms2.NewCardActivationSuccessRule()
	cardReissueSuccessRule := comms2.NewCardReissueSuccessRule()
	issuePhysicalCardSuccessRule := comms2.NewIssuePhysicalCardSuccessRule()
	txnFailurePinRetriesExceededRule := comms2.NewTxnFailurePinRetriesExceededRule(commsDataHelper)
	paymentReminderWithInterestRule := comms2.NewPaymentReminderWithInterestRule(commsDataHelper)
	paymentReminderWithoutInterestRule := comms2.NewPaymentReminderWithoutInterestRule(commsDataHelper)
	paymentReminderBeforeDueDateRule := comms2.NewPaymentReminderBeforeDueDateRule(commsDataHelper)
	statementGeneratedRule := comms2.NewStatementGeneratedRule(commsDataHelper)
	cashWithdrawalFeeRule := comms2.NewCashWithdrawalFeeRule(commsDataHelper)
	successfulRepaymentRule := comms2.NewSuccessfulRepaymentRule(commsDataHelper)
	creditUtilisationReachingThresholdRule := comms2.NewCreditUtilisationReachingThresholdRule(commsDataHelper)
	unpaidDuesFeesRule := comms2.NewUnpaidDuesFeesRule(commsDataHelper)
	joiningFeeRule := comms2.NewJoiningFeeRule(commsDataHelper)
	genericCreditRule := comms2.NewGenericCreditRule(commsDataHelper)
	cardIssueSuccessWithLimitRule := comms2.NewCardIssueSuccessWithLimitRule(commsDataHelper)
	creditCardDispatchedRule := comms2.NewCreditCardDispatchedRule(commsDataHelper)
	welcomeOfferClaimRule := comms2.NewWelcomeOfferClaimRule(commsDataHelper)
	cardNotActivatedRule := comms2.NewCardNotActivatedRule(commsDataHelper)
	creditCardClosedRule := comms2.NewCreditCardClosedRule()
	securedCardFdCreationSuccessRule := comms2.NewSecuredCardFdCreationSuccessRule(commsDataHelper)
	securedCardFdLienMarkingSuccessSuccessRule := comms2.NewSecuredCardFdLienMarkingSuccessSuccessRule(commsDataHelper)
	ccProfileValidationSuccessRule := comms2.NewCCProfileValidationSuccessRule()
	ccProfileValidationFailureRule := comms2.NewCCProfileValidationFailureRule()
	creditCardPaymentReminderWhatsAppOnBillGenDayRule := comms2.NewCreditCardPaymentReminderWhatsAppOnBillGenDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppBeforeDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppBeforeDueDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppOnDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppOnDueDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppAfterDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppAfterDueDayRule(commsDataHelper)
	creditCardDeliveredRule := comms2.NewCreditCardDeliveredRule(commsDataHelper)
	creditCardReturnedRule := comms2.NewCreditCardReturnedRule(commsDataHelper)
	creditCardOutForDeliveryRule := comms2.NewCreditCardOutForDeliveryRule(commsDataHelper)
	emiCreatedRule := comms2.NewEmiCreatedRule(commsDataHelper)
	emiClosedRule := comms2.NewEmiClosedRule()
	emiPreClosedRule := comms2.NewEmiPreClosedRule()
	emiCancelledRule := comms2.NewEmiCancelledRule()
	webEligibilityDropOffSmsRule := comms2.NewWebEligibilityDropOffSmsRule(commsDataHelper)
	webEligibilityDropOffEmailRule := comms2.NewWebEligibilityDropOffEmailRule(commsDataHelper)
	webEligibilityDropOffWhatsappRule := comms2.NewWebEligibilityDropOffWhatsappRule(commsDataHelper)
	creditCardWelcomeRule := comms2.NewCreditCardWelcomeRule()
	magnifiWelcomeRule := comms2.NewMagnifiWelcomeRule()
	frmDeclineTxnRule := comms2.NewFrmDeclineTxnRule()
	ccDeactivateInXDaysRule := comms2.NewCcDeactivateInXDaysRule()
	asyncProcessor := comms2.NewAsyncProcessor(commsClient, actorClient, doOnce, atmTxnSuccessRule, atmTxnFailureRule, crossBorderTxnSuccessRule, txnSuccessRule, txnFailureRule, txnReversalSuccessRule, onlineSettingsEnabledRule, onlineSettingsDisabledRule, posSettingsEnabledRule, posSettingsDisabledRule, internationalSettingsEnabledRule, internationalSettingsDisabledRule, contactlessSettingsEnabledRule, contactlessSettingsDisabledRule, cardUnfreezeSuccessRule, creditCardPosPurchaseLimitChangedRule, creditCardOnlinePurchaseLimitChangedRule, cardFreezeSuccessRule, creditCardCardUsageChangeFailureRule, creditCardContactlessPurchaseLimitChangedRule, cardFreezeFailureRule, creditCardLimitChangeFailureRule, creditCardPinChangeSuccessRule, cardUnFreezeFailureRule, cardActivationFailureRule, cardActivationSuccessRule, cardReissueSuccessRule, issuePhysicalCardSuccessRule, txnFailurePinRetriesExceededRule, paymentReminderWithInterestRule, paymentReminderWithoutInterestRule, paymentReminderBeforeDueDateRule, statementGeneratedRule, cashWithdrawalFeeRule, successfulRepaymentRule, creditUtilisationReachingThresholdRule, unpaidDuesFeesRule, joiningFeeRule, genericCreditRule, cardIssueSuccessWithLimitRule, creditCardDispatchedRule, welcomeOfferClaimRule, cardNotActivatedRule, creditCardClosedRule, securedCardFdCreationSuccessRule, securedCardFdLienMarkingSuccessSuccessRule, ccProfileValidationSuccessRule, ccProfileValidationFailureRule, creditCardPaymentReminderWhatsAppOnBillGenDayRule, creditCardPaymentReminderWhatsAppBeforeDueDayRule, creditCardPaymentReminderWhatsAppOnDueDayRule, creditCardPaymentReminderWhatsAppAfterDueDayRule, creditCardDeliveredRule, creditCardReturnedRule, creditCardOutForDeliveryRule, emiCreatedRule, emiClosedRule, emiPreClosedRule, emiCancelledRule, webEligibilityDropOffSmsRule, webEligibilityDropOffEmailRule, webEligibilityDropOffWhatsappRule, creditCardWelcomeRule, magnifiWelcomeRule, frmDeclineTxnRule, ccDeactivateInXDaysRule)
	cardRequestDaoWithInstrumentation := dao.NewCardRequestDaoWithInstrumentation(ccCacheStorage, cardRequestCacheConfig, cardRequestDao, broker, asyncProcessor)
	daoCardRequestDao := dao.ProvideCardRequestDao(cardRequestDaoWithInstrumentation)
	cardRequestStageDao := impl.NewCrdbCardRequestStage(creditCardDb, domainIdGenerator)
	cardRequestStageDaoWithInstrumentation := impl.NewCardRequestStageDaoWithInstrumentation(cardRequestStageDao, broker, nudgeClient, conf, cardRequestDao)
	creditCardDao := impl.NewCrdbCreditCard(creditCardDb, domainIdGenerator)
	userProcessor := user2.NewUserProcessor(bankCustClient)
	actorProcessor := actor2.NewActorProcessor(actorClient)
	rpcHelper := helper.NewRpcHelper(ccVgClient, actorClient, userClient, celestialClient, bankCustClient, accountingClient, billingClient, docsClient, payClient, orderClient, rewardsClient, pinotClient, lmsClient, ffClient, depositClient, savingsClient, rewardsProjectionClient, profileClient)
	notificationHelper := helper.NewNotificationHelper(commsClient, notificationScheduleConfig, notification)
	crdbCreditCardOfferEligibilityCriteriaDao := impl.NewCrdbCreditCardOfferEligibilityCriteriaDao(creditCardDb, domainIdGenerator)
	creditCardOfferCacheConfig := types2.WorkerCreditCardOfferCacheConfigProvider(conf)
	crdbCreditCardOfferDao := impl.NewCrdbCreditCardOfferDao(creditCardDb, domainIdGenerator)
	creditCardOfferDaoCache := dao.NewCreditCardOfferDaoCache(ccCacheStorage, creditCardOfferCacheConfig, crdbCreditCardOfferDao)
	creditCardOffersDao := dao.ProvideCreditCardOfferDao(creditCardOfferDaoCache)
	featureReleaseConfig := WorkerReleaseConfigProvider(fireflyWorkerGenConfig)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	client2 := fennel2.NewFennelClient(fennelClient)
	scienapticFeatureStoreClient := scienaptic2.NewScienapticFeatureStoreClient(scienapticClient, userClient)
	factory := featurestore.NewFactory(client2, scienapticFeatureStoreClient)
	activityProcessor := activity.NewProcessor(ccVgClient, daoCardRequestDao, cardRequestStageDaoWithInstrumentation, creditCardDao, userProcessor, actorProcessor, txnExecutor, userClient, actorClient, authClient, conf, doOnce, vkycClient, accountingClient, empClient, rpcHelper, notificationHelper, piClient, vgShipwayClient, ffClient, profValidationClient, creditLimitEstimatorClient, ccBillGenerationEventPublisher, ccStageUpdateEventPublisher, s3Client, rewardsClient, bankCustClient, dcClient, creditReportV2ManagerClient, crdbCreditCardOfferEligibilityCriteriaDao, creditCardOffersDao, obfuscatorClient, creditLineVgClient, panClient, broker, fireflyWorkerGenConfig, evaluator, nextActionProviderFactory, registerCustomerFactory, createCardAndAccountFactory, userCommunicationFactory, creditReportManagerClient, consentClient, riskClient, autClient, onbClient, kycClient, nudgeClient, breVgClient, factory, asyncProcessor, client, productClient, orderClient, panVgClient, payClient, acPiClient)
	return activityProcessor
}

func InitializeFireflyCxService(db types.CreditCardPGDB, ccClient firefly2.FireflyClient, ccVgClient types2.CreditCardVgClientWithInterceptors, conf *config.Config, FireflyRedisStore types2.FireflyRedisStore, eventBroker events.Broker, commsClient comms.CommsClient, depositClient deposit.DepositClient, billingClient billing.BillingClient, accountingClient accounting.AccountingClient, userClient user.UsersClient, actorClient actor.ActorClient) *cx.Service {
	gormDB := types.CreditCardPGDBGormDBProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	creditCardDao := impl.NewCrdbCreditCard(gormDB, domainIdGenerator)
	ccCacheStorage := types2.CcCacheStorageProvider(conf, FireflyRedisStore)
	cardRequestCacheConfig := types2.CardRequestCacheConfigProvider(conf)
	cardRequestDao := impl.NewCrdbCardRequest(gormDB, domainIdGenerator)
	doOnce := DoOnceProvider(gormDB)
	atmTxnSuccessRule := comms2.NewAtmTxnSuccessRule()
	atmTxnFailureRule := comms2.NewAtmTxnFailureRule()
	crossBorderTxnSuccessRule := comms2.NewCrossBorderTxnSuccessRule()
	txnSuccessRule := comms2.NewTxnSuccessRule()
	creditCardClient := types2.CreditCardVgClientProvider(ccVgClient)
	commsDataHelper := helper.NewCommsDataHelper(ccClient, creditCardClient, actorClient, userClient, accountingClient, billingClient, depositClient)
	txnFailureRule := comms2.NewTxnFailureRule(commsDataHelper)
	txnReversalSuccessRule := comms2.NewTxnReversalSuccessRule()
	onlineSettingsEnabledRule := comms2.NewOnlineSettingsEnabledRule()
	onlineSettingsDisabledRule := comms2.NewOnlineSettingsDisabledRule()
	posSettingsEnabledRule := comms2.NewPosSettingsEnabledRule()
	posSettingsDisabledRule := comms2.NewPosSettingsDisabledRule()
	internationalSettingsEnabledRule := comms2.NewInternationalSettingsEnabledRule()
	internationalSettingsDisabledRule := comms2.NewInternationalSettingsDisabledRule()
	contactlessSettingsEnabledRule := comms2.NewContactlessSettingsEnabledRule()
	contactlessSettingsDisabledRule := comms2.NewContactlessSettingsDisabledRule()
	cardUnfreezeSuccessRule := comms2.NewCardUnfreezeSuccessRule(commsDataHelper)
	creditCardPosPurchaseLimitChangedRule := comms2.NewCreditCardPosPurchaseLimitChangedRule(commsDataHelper)
	creditCardOnlinePurchaseLimitChangedRule := comms2.NewCreditCardOnlinePurchaseLimitChangedRule(commsDataHelper)
	cardFreezeSuccessRule := comms2.NewCardFreezeSuccessRule(commsDataHelper)
	creditCardCardUsageChangeFailureRule := comms2.NewCreditCardCardUsageChangeFailureRule(commsDataHelper)
	creditCardContactlessPurchaseLimitChangedRule := comms2.NewCreditCardContactlessPurchaseLimitChangedRule(commsDataHelper)
	cardFreezeFailureRule := comms2.NewCardFreezeFailureRule(commsDataHelper)
	creditCardLimitChangeFailureRule := comms2.NewCreditCardLimitChangeFailureRule(commsDataHelper)
	creditCardPinChangeSuccessRule := comms2.NewCreditCardPinChangeSuccessRule(commsDataHelper)
	cardUnFreezeFailureRule := comms2.NewCardUnFreezeFailureRule(commsDataHelper)
	cardActivationFailureRule := comms2.NewCardActivationFailureRule()
	cardActivationSuccessRule := comms2.NewCardActivationSuccessRule()
	cardReissueSuccessRule := comms2.NewCardReissueSuccessRule()
	issuePhysicalCardSuccessRule := comms2.NewIssuePhysicalCardSuccessRule()
	txnFailurePinRetriesExceededRule := comms2.NewTxnFailurePinRetriesExceededRule(commsDataHelper)
	paymentReminderWithInterestRule := comms2.NewPaymentReminderWithInterestRule(commsDataHelper)
	paymentReminderWithoutInterestRule := comms2.NewPaymentReminderWithoutInterestRule(commsDataHelper)
	paymentReminderBeforeDueDateRule := comms2.NewPaymentReminderBeforeDueDateRule(commsDataHelper)
	statementGeneratedRule := comms2.NewStatementGeneratedRule(commsDataHelper)
	cashWithdrawalFeeRule := comms2.NewCashWithdrawalFeeRule(commsDataHelper)
	successfulRepaymentRule := comms2.NewSuccessfulRepaymentRule(commsDataHelper)
	creditUtilisationReachingThresholdRule := comms2.NewCreditUtilisationReachingThresholdRule(commsDataHelper)
	unpaidDuesFeesRule := comms2.NewUnpaidDuesFeesRule(commsDataHelper)
	joiningFeeRule := comms2.NewJoiningFeeRule(commsDataHelper)
	genericCreditRule := comms2.NewGenericCreditRule(commsDataHelper)
	cardIssueSuccessWithLimitRule := comms2.NewCardIssueSuccessWithLimitRule(commsDataHelper)
	creditCardDispatchedRule := comms2.NewCreditCardDispatchedRule(commsDataHelper)
	welcomeOfferClaimRule := comms2.NewWelcomeOfferClaimRule(commsDataHelper)
	cardNotActivatedRule := comms2.NewCardNotActivatedRule(commsDataHelper)
	creditCardClosedRule := comms2.NewCreditCardClosedRule()
	securedCardFdCreationSuccessRule := comms2.NewSecuredCardFdCreationSuccessRule(commsDataHelper)
	securedCardFdLienMarkingSuccessSuccessRule := comms2.NewSecuredCardFdLienMarkingSuccessSuccessRule(commsDataHelper)
	ccProfileValidationSuccessRule := comms2.NewCCProfileValidationSuccessRule()
	ccProfileValidationFailureRule := comms2.NewCCProfileValidationFailureRule()
	creditCardPaymentReminderWhatsAppOnBillGenDayRule := comms2.NewCreditCardPaymentReminderWhatsAppOnBillGenDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppBeforeDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppBeforeDueDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppOnDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppOnDueDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppAfterDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppAfterDueDayRule(commsDataHelper)
	creditCardDeliveredRule := comms2.NewCreditCardDeliveredRule(commsDataHelper)
	creditCardReturnedRule := comms2.NewCreditCardReturnedRule(commsDataHelper)
	creditCardOutForDeliveryRule := comms2.NewCreditCardOutForDeliveryRule(commsDataHelper)
	emiCreatedRule := comms2.NewEmiCreatedRule(commsDataHelper)
	emiClosedRule := comms2.NewEmiClosedRule()
	emiPreClosedRule := comms2.NewEmiPreClosedRule()
	emiCancelledRule := comms2.NewEmiCancelledRule()
	webEligibilityDropOffSmsRule := comms2.NewWebEligibilityDropOffSmsRule(commsDataHelper)
	webEligibilityDropOffEmailRule := comms2.NewWebEligibilityDropOffEmailRule(commsDataHelper)
	webEligibilityDropOffWhatsappRule := comms2.NewWebEligibilityDropOffWhatsappRule(commsDataHelper)
	creditCardWelcomeRule := comms2.NewCreditCardWelcomeRule()
	magnifiWelcomeRule := comms2.NewMagnifiWelcomeRule()
	frmDeclineTxnRule := comms2.NewFrmDeclineTxnRule()
	ccDeactivateInXDaysRule := comms2.NewCcDeactivateInXDaysRule()
	asyncProcessor := comms2.NewAsyncProcessor(commsClient, actorClient, doOnce, atmTxnSuccessRule, atmTxnFailureRule, crossBorderTxnSuccessRule, txnSuccessRule, txnFailureRule, txnReversalSuccessRule, onlineSettingsEnabledRule, onlineSettingsDisabledRule, posSettingsEnabledRule, posSettingsDisabledRule, internationalSettingsEnabledRule, internationalSettingsDisabledRule, contactlessSettingsEnabledRule, contactlessSettingsDisabledRule, cardUnfreezeSuccessRule, creditCardPosPurchaseLimitChangedRule, creditCardOnlinePurchaseLimitChangedRule, cardFreezeSuccessRule, creditCardCardUsageChangeFailureRule, creditCardContactlessPurchaseLimitChangedRule, cardFreezeFailureRule, creditCardLimitChangeFailureRule, creditCardPinChangeSuccessRule, cardUnFreezeFailureRule, cardActivationFailureRule, cardActivationSuccessRule, cardReissueSuccessRule, issuePhysicalCardSuccessRule, txnFailurePinRetriesExceededRule, paymentReminderWithInterestRule, paymentReminderWithoutInterestRule, paymentReminderBeforeDueDateRule, statementGeneratedRule, cashWithdrawalFeeRule, successfulRepaymentRule, creditUtilisationReachingThresholdRule, unpaidDuesFeesRule, joiningFeeRule, genericCreditRule, cardIssueSuccessWithLimitRule, creditCardDispatchedRule, welcomeOfferClaimRule, cardNotActivatedRule, creditCardClosedRule, securedCardFdCreationSuccessRule, securedCardFdLienMarkingSuccessSuccessRule, ccProfileValidationSuccessRule, ccProfileValidationFailureRule, creditCardPaymentReminderWhatsAppOnBillGenDayRule, creditCardPaymentReminderWhatsAppBeforeDueDayRule, creditCardPaymentReminderWhatsAppOnDueDayRule, creditCardPaymentReminderWhatsAppAfterDueDayRule, creditCardDeliveredRule, creditCardReturnedRule, creditCardOutForDeliveryRule, emiCreatedRule, emiClosedRule, emiPreClosedRule, emiCancelledRule, webEligibilityDropOffSmsRule, webEligibilityDropOffEmailRule, webEligibilityDropOffWhatsappRule, creditCardWelcomeRule, magnifiWelcomeRule, frmDeclineTxnRule, ccDeactivateInXDaysRule)
	cardRequestDaoWithInstrumentation := dao.NewCardRequestDaoWithInstrumentation(ccCacheStorage, cardRequestCacheConfig, cardRequestDao, eventBroker, asyncProcessor)
	daoCardRequestDao := dao.ProvideCardRequestDao(cardRequestDaoWithInstrumentation)
	creditAccountDaoCRDB := impl2.NewCreditAccountDaoCRDB(gormDB, domainIdGenerator)
	creditCardBillDaoCRDB := impl4.NewCrdbCreditCardBillDao(gormDB, domainIdGenerator)
	creditCardSkuOverride := impl.NewCrdbCreditCardSkuOverride(gormDB)
	cardRequestStageDao := impl.NewCrdbCardRequestStage(gormDB, domainIdGenerator)
	service := cx.NewService(creditCardDao, daoCardRequestDao, creditAccountDaoCRDB, creditCardBillDaoCRDB, creditCardClient, creditCardSkuOverride, ccClient, cardRequestStageDao)
	return service
}

func InitialiseConsumerServiceForPinot(ccDb types.CreditCardPGDB, merchantClient merchant.MerchantServiceClient, categorizerClient categorizer.TxnCategorizerClient, awsConf aws.Config, conf *config.Config) (*consumer.Consumer, error) {
	transactionsProducer, err := KinesisProducerProvider(awsConf, conf)
	if err != nil {
		return nil, err
	}
	db := types.CreditCardPGDBGormDBProvider(ccDb)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	creditCardTransactionDaoCRDB := impl2.NewCreditCardTransactionDaoCRDB(db, domainIdGenerator)
	transactionAdditionalInfoDaoCRDB := impl2.NewTransactionAdditionalInfoDaoCRDB(db)
	consumerConsumer := consumer.NewConsumer(merchantClient, categorizerClient, transactionsProducer, creditCardTransactionDaoCRDB, transactionAdditionalInfoDaoCRDB)
	return consumerConsumer, nil
}

func InitialiseTxnAggregatesService(conf *config.Config, categoryClient categorizer.TxnCategorizerClient, ccAccClient accounting.AccountingClient, ccFireflyClient firefly2.FireflyClient) *pinot3.Service {
	client := pinotClientProvider(conf)
	ccTransactionDaoPinot := dao2.NewCcTransactionDaoPinot(client)
	service := pinot3.NewService(ccTransactionDaoPinot, categoryClient, ccAccClient, ccFireflyClient)
	return service
}

func InitializeConsumerService(db types.CreditCardPGDB, awsConfig aws.Config, conf *config.Config, userClient user.UsersClient, bankCustClient bankcust.BankCustomerServiceClient, celestialClient celestial.CelestialClient, actorClient actor.ActorClient, creditCardVgClient types2.CreditCardVgClientWithInterceptors, accountingClient accounting.AccountingClient, billingClient billing.BillingClient, docsClient docs.DocsClient, orderClient order.OrderServiceClient, payClient pay.PayClient, rewardsClient rewards.RewardsGeneratorClient, cardsSentForPrintingS3Client consumer2.CardsSentForPrintingS3Client, cardsDispatchedS3Client consumer2.CardsDispatchedS3Client, pinotClient pinot.TxnAggregatesClient, lmsClient lms.LoanManagementSystemClient, commsClient comms.CommsClient, fireflyClient firefly2.FireflyClient, depositClient deposit.DepositClient, savingsClient savings.SavingsClient, rewardsProjectionClient projector.ProjectorServiceClient, profileClient profile.ProfileClient, FireflyRedisStore types2.FireflyRedisStore, eventBroker events.Broker, breClient bre2.BreClient, productClient product.ProductClient, crManagerClient creditreportv2.CreditReportManagerClient, ffCardRecommendationClient card_recommendation.CardRecommendationServiceClient, riskClient risk.RiskClient, segmentationClient segment.SegmentationServiceClient) (*consumer2.Service, error) {
	creditCardClient := types2.CreditCardVgClientProvider(creditCardVgClient)
	fireflyS3Client := fireflyS3ClientProvider(awsConfig, conf)
	rpcHelper := helper.NewRpcHelper(creditCardClient, actorClient, userClient, celestialClient, bankCustClient, accountingClient, billingClient, docsClient, payClient, orderClient, rewardsClient, pinotClient, lmsClient, fireflyClient, depositClient, savingsClient, rewardsProjectionClient, profileClient)
	ccCacheStorage := types2.CcCacheStorageProvider(conf, FireflyRedisStore)
	creditCardOfferCacheConfig := types2.CreditCardOfferCacheConfigProvider(conf)
	gormDB := types.CreditCardPGDBGormDBProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	crdbCreditCardOfferDao := impl.NewCrdbCreditCardOfferDao(gormDB, domainIdGenerator)
	creditCardOfferDaoCache := dao.NewCreditCardOfferDaoCache(ccCacheStorage, creditCardOfferCacheConfig, crdbCreditCardOfferDao)
	creditCardOffersDao := dao.ProvideCreditCardOfferDao(creditCardOfferDaoCache)
	crdbCreditCardOfferEligibilityCriteriaDao := impl.NewCrdbCreditCardOfferEligibilityCriteriaDao(gormDB, domainIdGenerator)
	creditCardDao := impl.NewCrdbCreditCard(gormDB, domainIdGenerator)
	cardRequestCacheConfig := types2.CardRequestCacheConfigProvider(conf)
	cardRequestDao := impl.NewCrdbCardRequest(gormDB, domainIdGenerator)
	doOnce := DoOnceProvider(gormDB)
	atmTxnSuccessRule := comms2.NewAtmTxnSuccessRule()
	atmTxnFailureRule := comms2.NewAtmTxnFailureRule()
	crossBorderTxnSuccessRule := comms2.NewCrossBorderTxnSuccessRule()
	txnSuccessRule := comms2.NewTxnSuccessRule()
	commsDataHelper := helper.NewCommsDataHelper(fireflyClient, creditCardClient, actorClient, userClient, accountingClient, billingClient, depositClient)
	txnFailureRule := comms2.NewTxnFailureRule(commsDataHelper)
	txnReversalSuccessRule := comms2.NewTxnReversalSuccessRule()
	onlineSettingsEnabledRule := comms2.NewOnlineSettingsEnabledRule()
	onlineSettingsDisabledRule := comms2.NewOnlineSettingsDisabledRule()
	posSettingsEnabledRule := comms2.NewPosSettingsEnabledRule()
	posSettingsDisabledRule := comms2.NewPosSettingsDisabledRule()
	internationalSettingsEnabledRule := comms2.NewInternationalSettingsEnabledRule()
	internationalSettingsDisabledRule := comms2.NewInternationalSettingsDisabledRule()
	contactlessSettingsEnabledRule := comms2.NewContactlessSettingsEnabledRule()
	contactlessSettingsDisabledRule := comms2.NewContactlessSettingsDisabledRule()
	cardUnfreezeSuccessRule := comms2.NewCardUnfreezeSuccessRule(commsDataHelper)
	creditCardPosPurchaseLimitChangedRule := comms2.NewCreditCardPosPurchaseLimitChangedRule(commsDataHelper)
	creditCardOnlinePurchaseLimitChangedRule := comms2.NewCreditCardOnlinePurchaseLimitChangedRule(commsDataHelper)
	cardFreezeSuccessRule := comms2.NewCardFreezeSuccessRule(commsDataHelper)
	creditCardCardUsageChangeFailureRule := comms2.NewCreditCardCardUsageChangeFailureRule(commsDataHelper)
	creditCardContactlessPurchaseLimitChangedRule := comms2.NewCreditCardContactlessPurchaseLimitChangedRule(commsDataHelper)
	cardFreezeFailureRule := comms2.NewCardFreezeFailureRule(commsDataHelper)
	creditCardLimitChangeFailureRule := comms2.NewCreditCardLimitChangeFailureRule(commsDataHelper)
	creditCardPinChangeSuccessRule := comms2.NewCreditCardPinChangeSuccessRule(commsDataHelper)
	cardUnFreezeFailureRule := comms2.NewCardUnFreezeFailureRule(commsDataHelper)
	cardActivationFailureRule := comms2.NewCardActivationFailureRule()
	cardActivationSuccessRule := comms2.NewCardActivationSuccessRule()
	cardReissueSuccessRule := comms2.NewCardReissueSuccessRule()
	issuePhysicalCardSuccessRule := comms2.NewIssuePhysicalCardSuccessRule()
	txnFailurePinRetriesExceededRule := comms2.NewTxnFailurePinRetriesExceededRule(commsDataHelper)
	paymentReminderWithInterestRule := comms2.NewPaymentReminderWithInterestRule(commsDataHelper)
	paymentReminderWithoutInterestRule := comms2.NewPaymentReminderWithoutInterestRule(commsDataHelper)
	paymentReminderBeforeDueDateRule := comms2.NewPaymentReminderBeforeDueDateRule(commsDataHelper)
	statementGeneratedRule := comms2.NewStatementGeneratedRule(commsDataHelper)
	cashWithdrawalFeeRule := comms2.NewCashWithdrawalFeeRule(commsDataHelper)
	successfulRepaymentRule := comms2.NewSuccessfulRepaymentRule(commsDataHelper)
	creditUtilisationReachingThresholdRule := comms2.NewCreditUtilisationReachingThresholdRule(commsDataHelper)
	unpaidDuesFeesRule := comms2.NewUnpaidDuesFeesRule(commsDataHelper)
	joiningFeeRule := comms2.NewJoiningFeeRule(commsDataHelper)
	genericCreditRule := comms2.NewGenericCreditRule(commsDataHelper)
	cardIssueSuccessWithLimitRule := comms2.NewCardIssueSuccessWithLimitRule(commsDataHelper)
	creditCardDispatchedRule := comms2.NewCreditCardDispatchedRule(commsDataHelper)
	welcomeOfferClaimRule := comms2.NewWelcomeOfferClaimRule(commsDataHelper)
	cardNotActivatedRule := comms2.NewCardNotActivatedRule(commsDataHelper)
	creditCardClosedRule := comms2.NewCreditCardClosedRule()
	securedCardFdCreationSuccessRule := comms2.NewSecuredCardFdCreationSuccessRule(commsDataHelper)
	securedCardFdLienMarkingSuccessSuccessRule := comms2.NewSecuredCardFdLienMarkingSuccessSuccessRule(commsDataHelper)
	ccProfileValidationSuccessRule := comms2.NewCCProfileValidationSuccessRule()
	ccProfileValidationFailureRule := comms2.NewCCProfileValidationFailureRule()
	creditCardPaymentReminderWhatsAppOnBillGenDayRule := comms2.NewCreditCardPaymentReminderWhatsAppOnBillGenDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppBeforeDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppBeforeDueDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppOnDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppOnDueDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppAfterDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppAfterDueDayRule(commsDataHelper)
	creditCardDeliveredRule := comms2.NewCreditCardDeliveredRule(commsDataHelper)
	creditCardReturnedRule := comms2.NewCreditCardReturnedRule(commsDataHelper)
	creditCardOutForDeliveryRule := comms2.NewCreditCardOutForDeliveryRule(commsDataHelper)
	emiCreatedRule := comms2.NewEmiCreatedRule(commsDataHelper)
	emiClosedRule := comms2.NewEmiClosedRule()
	emiPreClosedRule := comms2.NewEmiPreClosedRule()
	emiCancelledRule := comms2.NewEmiCancelledRule()
	webEligibilityDropOffSmsRule := comms2.NewWebEligibilityDropOffSmsRule(commsDataHelper)
	webEligibilityDropOffEmailRule := comms2.NewWebEligibilityDropOffEmailRule(commsDataHelper)
	webEligibilityDropOffWhatsappRule := comms2.NewWebEligibilityDropOffWhatsappRule(commsDataHelper)
	creditCardWelcomeRule := comms2.NewCreditCardWelcomeRule()
	magnifiWelcomeRule := comms2.NewMagnifiWelcomeRule()
	frmDeclineTxnRule := comms2.NewFrmDeclineTxnRule()
	ccDeactivateInXDaysRule := comms2.NewCcDeactivateInXDaysRule()
	asyncProcessor := comms2.NewAsyncProcessor(commsClient, actorClient, doOnce, atmTxnSuccessRule, atmTxnFailureRule, crossBorderTxnSuccessRule, txnSuccessRule, txnFailureRule, txnReversalSuccessRule, onlineSettingsEnabledRule, onlineSettingsDisabledRule, posSettingsEnabledRule, posSettingsDisabledRule, internationalSettingsEnabledRule, internationalSettingsDisabledRule, contactlessSettingsEnabledRule, contactlessSettingsDisabledRule, cardUnfreezeSuccessRule, creditCardPosPurchaseLimitChangedRule, creditCardOnlinePurchaseLimitChangedRule, cardFreezeSuccessRule, creditCardCardUsageChangeFailureRule, creditCardContactlessPurchaseLimitChangedRule, cardFreezeFailureRule, creditCardLimitChangeFailureRule, creditCardPinChangeSuccessRule, cardUnFreezeFailureRule, cardActivationFailureRule, cardActivationSuccessRule, cardReissueSuccessRule, issuePhysicalCardSuccessRule, txnFailurePinRetriesExceededRule, paymentReminderWithInterestRule, paymentReminderWithoutInterestRule, paymentReminderBeforeDueDateRule, statementGeneratedRule, cashWithdrawalFeeRule, successfulRepaymentRule, creditUtilisationReachingThresholdRule, unpaidDuesFeesRule, joiningFeeRule, genericCreditRule, cardIssueSuccessWithLimitRule, creditCardDispatchedRule, welcomeOfferClaimRule, cardNotActivatedRule, creditCardClosedRule, securedCardFdCreationSuccessRule, securedCardFdLienMarkingSuccessSuccessRule, ccProfileValidationSuccessRule, ccProfileValidationFailureRule, creditCardPaymentReminderWhatsAppOnBillGenDayRule, creditCardPaymentReminderWhatsAppBeforeDueDayRule, creditCardPaymentReminderWhatsAppOnDueDayRule, creditCardPaymentReminderWhatsAppAfterDueDayRule, creditCardDeliveredRule, creditCardReturnedRule, creditCardOutForDeliveryRule, emiCreatedRule, emiClosedRule, emiPreClosedRule, emiCancelledRule, webEligibilityDropOffSmsRule, webEligibilityDropOffEmailRule, webEligibilityDropOffWhatsappRule, creditCardWelcomeRule, magnifiWelcomeRule, frmDeclineTxnRule, ccDeactivateInXDaysRule)
	cardRequestDaoWithInstrumentation := dao.NewCardRequestDaoWithInstrumentation(ccCacheStorage, cardRequestCacheConfig, cardRequestDao, eventBroker, asyncProcessor)
	daoCardRequestDao := dao.ProvideCardRequestDao(cardRequestDaoWithInstrumentation)
	inMemoryCryptorStore, err := InitCryptors(conf)
	if err != nil {
		return nil, err
	}
	service := consumer2.NewService(actorClient, creditCardClient, fireflyS3Client, bankCustClient, rpcHelper, creditCardOffersDao, crdbCreditCardOfferEligibilityCriteriaDao, conf, creditCardDao, celestialClient, daoCardRequestDao, cardsSentForPrintingS3Client, cardsDispatchedS3Client, asyncProcessor, inMemoryCryptorStore, userClient, breClient, productClient, crManagerClient, ffCardRecommendationClient, eventBroker, accountingClient, riskClient, commsClient, segmentationClient, doOnce)
	return service, nil
}

func InitialiseCommonProcessor(creditCardDb *gorm.DB, ccVgClient creditcard.CreditCardClient, userClient user.UsersClient, actorClient actor.ActorClient, celestialClient celestial.CelestialClient, txnExecutor storagev2.TxnExecutor, billingClient billing.BillingClient, accountingClient accounting.AccountingClient, commsClient comms.CommsClient, docsClient docs.DocsClient, bankCustClient bankcust.BankCustomerServiceClient, orderClient order.OrderServiceClient, payClient pay.PayClient, rewardsClient rewards.RewardsGeneratorClient, pinotClient pinot.TxnAggregatesClient, lmsClient lms.LoanManagementSystemClient, fireflyClient firefly2.FireflyClient, depositClient deposit.DepositClient, savingsClient savings.SavingsClient, rewardsProjectionClient projector.ProjectorServiceClient, profileClient profile.ProfileClient, conf *worker.Config, FireflyRedisStore types2.FireflyRedisStore, eventBroker events.Broker) *common2.Processor {
	doOnce := DoOnceProvider(creditCardDb)
	atmTxnSuccessRule := comms2.NewAtmTxnSuccessRule()
	atmTxnFailureRule := comms2.NewAtmTxnFailureRule()
	crossBorderTxnSuccessRule := comms2.NewCrossBorderTxnSuccessRule()
	txnSuccessRule := comms2.NewTxnSuccessRule()
	commsDataHelper := helper.NewCommsDataHelper(fireflyClient, ccVgClient, actorClient, userClient, accountingClient, billingClient, depositClient)
	txnFailureRule := comms2.NewTxnFailureRule(commsDataHelper)
	txnReversalSuccessRule := comms2.NewTxnReversalSuccessRule()
	onlineSettingsEnabledRule := comms2.NewOnlineSettingsEnabledRule()
	onlineSettingsDisabledRule := comms2.NewOnlineSettingsDisabledRule()
	posSettingsEnabledRule := comms2.NewPosSettingsEnabledRule()
	posSettingsDisabledRule := comms2.NewPosSettingsDisabledRule()
	internationalSettingsEnabledRule := comms2.NewInternationalSettingsEnabledRule()
	internationalSettingsDisabledRule := comms2.NewInternationalSettingsDisabledRule()
	contactlessSettingsEnabledRule := comms2.NewContactlessSettingsEnabledRule()
	contactlessSettingsDisabledRule := comms2.NewContactlessSettingsDisabledRule()
	cardUnfreezeSuccessRule := comms2.NewCardUnfreezeSuccessRule(commsDataHelper)
	creditCardPosPurchaseLimitChangedRule := comms2.NewCreditCardPosPurchaseLimitChangedRule(commsDataHelper)
	creditCardOnlinePurchaseLimitChangedRule := comms2.NewCreditCardOnlinePurchaseLimitChangedRule(commsDataHelper)
	cardFreezeSuccessRule := comms2.NewCardFreezeSuccessRule(commsDataHelper)
	creditCardCardUsageChangeFailureRule := comms2.NewCreditCardCardUsageChangeFailureRule(commsDataHelper)
	creditCardContactlessPurchaseLimitChangedRule := comms2.NewCreditCardContactlessPurchaseLimitChangedRule(commsDataHelper)
	cardFreezeFailureRule := comms2.NewCardFreezeFailureRule(commsDataHelper)
	creditCardLimitChangeFailureRule := comms2.NewCreditCardLimitChangeFailureRule(commsDataHelper)
	creditCardPinChangeSuccessRule := comms2.NewCreditCardPinChangeSuccessRule(commsDataHelper)
	cardUnFreezeFailureRule := comms2.NewCardUnFreezeFailureRule(commsDataHelper)
	cardActivationFailureRule := comms2.NewCardActivationFailureRule()
	cardActivationSuccessRule := comms2.NewCardActivationSuccessRule()
	cardReissueSuccessRule := comms2.NewCardReissueSuccessRule()
	issuePhysicalCardSuccessRule := comms2.NewIssuePhysicalCardSuccessRule()
	txnFailurePinRetriesExceededRule := comms2.NewTxnFailurePinRetriesExceededRule(commsDataHelper)
	paymentReminderWithInterestRule := comms2.NewPaymentReminderWithInterestRule(commsDataHelper)
	paymentReminderWithoutInterestRule := comms2.NewPaymentReminderWithoutInterestRule(commsDataHelper)
	paymentReminderBeforeDueDateRule := comms2.NewPaymentReminderBeforeDueDateRule(commsDataHelper)
	statementGeneratedRule := comms2.NewStatementGeneratedRule(commsDataHelper)
	cashWithdrawalFeeRule := comms2.NewCashWithdrawalFeeRule(commsDataHelper)
	successfulRepaymentRule := comms2.NewSuccessfulRepaymentRule(commsDataHelper)
	creditUtilisationReachingThresholdRule := comms2.NewCreditUtilisationReachingThresholdRule(commsDataHelper)
	unpaidDuesFeesRule := comms2.NewUnpaidDuesFeesRule(commsDataHelper)
	joiningFeeRule := comms2.NewJoiningFeeRule(commsDataHelper)
	genericCreditRule := comms2.NewGenericCreditRule(commsDataHelper)
	cardIssueSuccessWithLimitRule := comms2.NewCardIssueSuccessWithLimitRule(commsDataHelper)
	creditCardDispatchedRule := comms2.NewCreditCardDispatchedRule(commsDataHelper)
	welcomeOfferClaimRule := comms2.NewWelcomeOfferClaimRule(commsDataHelper)
	cardNotActivatedRule := comms2.NewCardNotActivatedRule(commsDataHelper)
	creditCardClosedRule := comms2.NewCreditCardClosedRule()
	securedCardFdCreationSuccessRule := comms2.NewSecuredCardFdCreationSuccessRule(commsDataHelper)
	securedCardFdLienMarkingSuccessSuccessRule := comms2.NewSecuredCardFdLienMarkingSuccessSuccessRule(commsDataHelper)
	ccProfileValidationSuccessRule := comms2.NewCCProfileValidationSuccessRule()
	ccProfileValidationFailureRule := comms2.NewCCProfileValidationFailureRule()
	creditCardPaymentReminderWhatsAppOnBillGenDayRule := comms2.NewCreditCardPaymentReminderWhatsAppOnBillGenDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppBeforeDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppBeforeDueDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppOnDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppOnDueDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppAfterDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppAfterDueDayRule(commsDataHelper)
	creditCardDeliveredRule := comms2.NewCreditCardDeliveredRule(commsDataHelper)
	creditCardReturnedRule := comms2.NewCreditCardReturnedRule(commsDataHelper)
	creditCardOutForDeliveryRule := comms2.NewCreditCardOutForDeliveryRule(commsDataHelper)
	emiCreatedRule := comms2.NewEmiCreatedRule(commsDataHelper)
	emiClosedRule := comms2.NewEmiClosedRule()
	emiPreClosedRule := comms2.NewEmiPreClosedRule()
	emiCancelledRule := comms2.NewEmiCancelledRule()
	webEligibilityDropOffSmsRule := comms2.NewWebEligibilityDropOffSmsRule(commsDataHelper)
	webEligibilityDropOffEmailRule := comms2.NewWebEligibilityDropOffEmailRule(commsDataHelper)
	webEligibilityDropOffWhatsappRule := comms2.NewWebEligibilityDropOffWhatsappRule(commsDataHelper)
	creditCardWelcomeRule := comms2.NewCreditCardWelcomeRule()
	magnifiWelcomeRule := comms2.NewMagnifiWelcomeRule()
	frmDeclineTxnRule := comms2.NewFrmDeclineTxnRule()
	ccDeactivateInXDaysRule := comms2.NewCcDeactivateInXDaysRule()
	asyncProcessor := comms2.NewAsyncProcessor(commsClient, actorClient, doOnce, atmTxnSuccessRule, atmTxnFailureRule, crossBorderTxnSuccessRule, txnSuccessRule, txnFailureRule, txnReversalSuccessRule, onlineSettingsEnabledRule, onlineSettingsDisabledRule, posSettingsEnabledRule, posSettingsDisabledRule, internationalSettingsEnabledRule, internationalSettingsDisabledRule, contactlessSettingsEnabledRule, contactlessSettingsDisabledRule, cardUnfreezeSuccessRule, creditCardPosPurchaseLimitChangedRule, creditCardOnlinePurchaseLimitChangedRule, cardFreezeSuccessRule, creditCardCardUsageChangeFailureRule, creditCardContactlessPurchaseLimitChangedRule, cardFreezeFailureRule, creditCardLimitChangeFailureRule, creditCardPinChangeSuccessRule, cardUnFreezeFailureRule, cardActivationFailureRule, cardActivationSuccessRule, cardReissueSuccessRule, issuePhysicalCardSuccessRule, txnFailurePinRetriesExceededRule, paymentReminderWithInterestRule, paymentReminderWithoutInterestRule, paymentReminderBeforeDueDateRule, statementGeneratedRule, cashWithdrawalFeeRule, successfulRepaymentRule, creditUtilisationReachingThresholdRule, unpaidDuesFeesRule, joiningFeeRule, genericCreditRule, cardIssueSuccessWithLimitRule, creditCardDispatchedRule, welcomeOfferClaimRule, cardNotActivatedRule, creditCardClosedRule, securedCardFdCreationSuccessRule, securedCardFdLienMarkingSuccessSuccessRule, ccProfileValidationSuccessRule, ccProfileValidationFailureRule, creditCardPaymentReminderWhatsAppOnBillGenDayRule, creditCardPaymentReminderWhatsAppBeforeDueDayRule, creditCardPaymentReminderWhatsAppOnDueDayRule, creditCardPaymentReminderWhatsAppAfterDueDayRule, creditCardDeliveredRule, creditCardReturnedRule, creditCardOutForDeliveryRule, emiCreatedRule, emiClosedRule, emiPreClosedRule, emiCancelledRule, webEligibilityDropOffSmsRule, webEligibilityDropOffEmailRule, webEligibilityDropOffWhatsappRule, creditCardWelcomeRule, magnifiWelcomeRule, frmDeclineTxnRule, ccDeactivateInXDaysRule)
	ccCacheStorage := types2.WorkerCcCacheStorageProvider(conf, FireflyRedisStore)
	cardRequestCacheConfig := types2.WorkerCardRequestCacheConfigProvider(conf)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	cardRequestDao := impl.NewCrdbCardRequest(creditCardDb, domainIdGenerator)
	cardRequestDaoWithInstrumentation := dao.NewCardRequestDaoWithInstrumentation(ccCacheStorage, cardRequestCacheConfig, cardRequestDao, eventBroker, asyncProcessor)
	daoCardRequestDao := dao.ProvideCardRequestDao(cardRequestDaoWithInstrumentation)
	cardRequestStageDao := impl.NewCrdbCardRequestStage(creditCardDb, domainIdGenerator)
	creditCardDao := impl.NewCrdbCreditCard(creditCardDb, domainIdGenerator)
	rpcHelper := helper.NewRpcHelper(ccVgClient, actorClient, userClient, celestialClient, bankCustClient, accountingClient, billingClient, docsClient, payClient, orderClient, rewardsClient, pinotClient, lmsClient, fireflyClient, depositClient, savingsClient, rewardsProjectionClient, profileClient)
	commonProcessor := common2.NewProcessor(asyncProcessor, daoCardRequestDao, cardRequestStageDao, creditCardDao, rpcHelper, conf)
	return commonProcessor
}

func InitialiseNextActionProviderFactory(ffClient firefly2.FireflyClient, userClient user.UsersClient, workerGenConf *genconf2.Config, cardProvisioningClient provisioning.CardProvisioningClient, empFeClient employment.EmploymentFeClient, bcClient bankcust.BankCustomerServiceClient) *next_action_provider.NextActionProviderFactory {
	fdCreationStageNextActionProvider := next_action_provider.NewFdCreationStageNextActionProvider(ffClient, workerGenConf)
	addressCaptureStageNextActionProvider := next_action_provider.NewAddressCaptureStageNextActionProvider(ffClient, userClient, workerGenConf, cardProvisioningClient)
	createCardStageNextActionProvider := next_action_provider.NewCreateCardStageNextActionProvider(workerGenConf)
	employmentCollectionNextActionProvider := next_action_provider.NewEmploymentCollectionNextActionProvider(empFeClient)
	massunsecuredCreateCardStageNextActionProvider := massunsecured.NewCreateCardStageNextActionProvider(workerGenConf)
	consentStageNextActionProvider := next_action_provider.NewConsentStageNextActionProvider(ffClient, bcClient, workerGenConf)
	securedFiLiteFdCreationConsentNextActionProvider := fi_lite.NewSecuredFiLiteFdCreationConsentNextActionProvider(ffClient, workerGenConf)
	fiLiteSecuredCreateCardConsentStageNextActionProvider := fi_lite.NewFiLiteSecuredCreateCardConsentStageNextActionProvider(workerGenConf)
	cardConsentStageNextActionProvider := next_action_provider.NewCardConsentStageNextActionProvider(workerGenConf)
	createCardConsentStageNextActionProvider := unsecured.NewCreateCardConsentStageNextActionProvider(workerGenConf)
	nextActionProviderFactory := next_action_provider.NewNextActionProviderFactory(fdCreationStageNextActionProvider, addressCaptureStageNextActionProvider, createCardStageNextActionProvider, employmentCollectionNextActionProvider, massunsecuredCreateCardStageNextActionProvider, consentStageNextActionProvider, securedFiLiteFdCreationConsentNextActionProvider, fiLiteSecuredCreateCardConsentStageNextActionProvider, cardConsentStageNextActionProvider, createCardConsentStageNextActionProvider)
	return nextActionProviderFactory
}

func InitialiseUserCommunicationFactory(conf *worker.Config, creditCardDb *gorm.DB, FireflyRedisStore types2.FireflyRedisStore, commsClient comms.CommsClient, depositClient deposit.DepositClient, billingClient billing.BillingClient, accountingClient accounting.AccountingClient, userClient user.UsersClient, ccVgClient creditcard.CreditCardClient, ffClient firefly2.FireflyClient, actorClient actor.ActorClient, eventBroker events.Broker) *user_communication_provider.UserCommunicationProviderFactory {
	ccCacheStorage := types2.WorkerCcCacheStorageProvider(conf, FireflyRedisStore)
	cardRequestCacheConfig := types2.WorkerCardRequestCacheConfigProvider(conf)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	cardRequestDao := impl.NewCrdbCardRequest(creditCardDb, domainIdGenerator)
	doOnce := DoOnceProvider(creditCardDb)
	atmTxnSuccessRule := comms2.NewAtmTxnSuccessRule()
	atmTxnFailureRule := comms2.NewAtmTxnFailureRule()
	crossBorderTxnSuccessRule := comms2.NewCrossBorderTxnSuccessRule()
	txnSuccessRule := comms2.NewTxnSuccessRule()
	commsDataHelper := helper.NewCommsDataHelper(ffClient, ccVgClient, actorClient, userClient, accountingClient, billingClient, depositClient)
	txnFailureRule := comms2.NewTxnFailureRule(commsDataHelper)
	txnReversalSuccessRule := comms2.NewTxnReversalSuccessRule()
	onlineSettingsEnabledRule := comms2.NewOnlineSettingsEnabledRule()
	onlineSettingsDisabledRule := comms2.NewOnlineSettingsDisabledRule()
	posSettingsEnabledRule := comms2.NewPosSettingsEnabledRule()
	posSettingsDisabledRule := comms2.NewPosSettingsDisabledRule()
	internationalSettingsEnabledRule := comms2.NewInternationalSettingsEnabledRule()
	internationalSettingsDisabledRule := comms2.NewInternationalSettingsDisabledRule()
	contactlessSettingsEnabledRule := comms2.NewContactlessSettingsEnabledRule()
	contactlessSettingsDisabledRule := comms2.NewContactlessSettingsDisabledRule()
	cardUnfreezeSuccessRule := comms2.NewCardUnfreezeSuccessRule(commsDataHelper)
	creditCardPosPurchaseLimitChangedRule := comms2.NewCreditCardPosPurchaseLimitChangedRule(commsDataHelper)
	creditCardOnlinePurchaseLimitChangedRule := comms2.NewCreditCardOnlinePurchaseLimitChangedRule(commsDataHelper)
	cardFreezeSuccessRule := comms2.NewCardFreezeSuccessRule(commsDataHelper)
	creditCardCardUsageChangeFailureRule := comms2.NewCreditCardCardUsageChangeFailureRule(commsDataHelper)
	creditCardContactlessPurchaseLimitChangedRule := comms2.NewCreditCardContactlessPurchaseLimitChangedRule(commsDataHelper)
	cardFreezeFailureRule := comms2.NewCardFreezeFailureRule(commsDataHelper)
	creditCardLimitChangeFailureRule := comms2.NewCreditCardLimitChangeFailureRule(commsDataHelper)
	creditCardPinChangeSuccessRule := comms2.NewCreditCardPinChangeSuccessRule(commsDataHelper)
	cardUnFreezeFailureRule := comms2.NewCardUnFreezeFailureRule(commsDataHelper)
	cardActivationFailureRule := comms2.NewCardActivationFailureRule()
	cardActivationSuccessRule := comms2.NewCardActivationSuccessRule()
	cardReissueSuccessRule := comms2.NewCardReissueSuccessRule()
	issuePhysicalCardSuccessRule := comms2.NewIssuePhysicalCardSuccessRule()
	txnFailurePinRetriesExceededRule := comms2.NewTxnFailurePinRetriesExceededRule(commsDataHelper)
	paymentReminderWithInterestRule := comms2.NewPaymentReminderWithInterestRule(commsDataHelper)
	paymentReminderWithoutInterestRule := comms2.NewPaymentReminderWithoutInterestRule(commsDataHelper)
	paymentReminderBeforeDueDateRule := comms2.NewPaymentReminderBeforeDueDateRule(commsDataHelper)
	statementGeneratedRule := comms2.NewStatementGeneratedRule(commsDataHelper)
	cashWithdrawalFeeRule := comms2.NewCashWithdrawalFeeRule(commsDataHelper)
	successfulRepaymentRule := comms2.NewSuccessfulRepaymentRule(commsDataHelper)
	creditUtilisationReachingThresholdRule := comms2.NewCreditUtilisationReachingThresholdRule(commsDataHelper)
	unpaidDuesFeesRule := comms2.NewUnpaidDuesFeesRule(commsDataHelper)
	joiningFeeRule := comms2.NewJoiningFeeRule(commsDataHelper)
	genericCreditRule := comms2.NewGenericCreditRule(commsDataHelper)
	cardIssueSuccessWithLimitRule := comms2.NewCardIssueSuccessWithLimitRule(commsDataHelper)
	creditCardDispatchedRule := comms2.NewCreditCardDispatchedRule(commsDataHelper)
	welcomeOfferClaimRule := comms2.NewWelcomeOfferClaimRule(commsDataHelper)
	cardNotActivatedRule := comms2.NewCardNotActivatedRule(commsDataHelper)
	creditCardClosedRule := comms2.NewCreditCardClosedRule()
	securedCardFdCreationSuccessRule := comms2.NewSecuredCardFdCreationSuccessRule(commsDataHelper)
	securedCardFdLienMarkingSuccessSuccessRule := comms2.NewSecuredCardFdLienMarkingSuccessSuccessRule(commsDataHelper)
	ccProfileValidationSuccessRule := comms2.NewCCProfileValidationSuccessRule()
	ccProfileValidationFailureRule := comms2.NewCCProfileValidationFailureRule()
	creditCardPaymentReminderWhatsAppOnBillGenDayRule := comms2.NewCreditCardPaymentReminderWhatsAppOnBillGenDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppBeforeDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppBeforeDueDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppOnDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppOnDueDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppAfterDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppAfterDueDayRule(commsDataHelper)
	creditCardDeliveredRule := comms2.NewCreditCardDeliveredRule(commsDataHelper)
	creditCardReturnedRule := comms2.NewCreditCardReturnedRule(commsDataHelper)
	creditCardOutForDeliveryRule := comms2.NewCreditCardOutForDeliveryRule(commsDataHelper)
	emiCreatedRule := comms2.NewEmiCreatedRule(commsDataHelper)
	emiClosedRule := comms2.NewEmiClosedRule()
	emiPreClosedRule := comms2.NewEmiPreClosedRule()
	emiCancelledRule := comms2.NewEmiCancelledRule()
	webEligibilityDropOffSmsRule := comms2.NewWebEligibilityDropOffSmsRule(commsDataHelper)
	webEligibilityDropOffEmailRule := comms2.NewWebEligibilityDropOffEmailRule(commsDataHelper)
	webEligibilityDropOffWhatsappRule := comms2.NewWebEligibilityDropOffWhatsappRule(commsDataHelper)
	creditCardWelcomeRule := comms2.NewCreditCardWelcomeRule()
	magnifiWelcomeRule := comms2.NewMagnifiWelcomeRule()
	frmDeclineTxnRule := comms2.NewFrmDeclineTxnRule()
	ccDeactivateInXDaysRule := comms2.NewCcDeactivateInXDaysRule()
	asyncProcessor := comms2.NewAsyncProcessor(commsClient, actorClient, doOnce, atmTxnSuccessRule, atmTxnFailureRule, crossBorderTxnSuccessRule, txnSuccessRule, txnFailureRule, txnReversalSuccessRule, onlineSettingsEnabledRule, onlineSettingsDisabledRule, posSettingsEnabledRule, posSettingsDisabledRule, internationalSettingsEnabledRule, internationalSettingsDisabledRule, contactlessSettingsEnabledRule, contactlessSettingsDisabledRule, cardUnfreezeSuccessRule, creditCardPosPurchaseLimitChangedRule, creditCardOnlinePurchaseLimitChangedRule, cardFreezeSuccessRule, creditCardCardUsageChangeFailureRule, creditCardContactlessPurchaseLimitChangedRule, cardFreezeFailureRule, creditCardLimitChangeFailureRule, creditCardPinChangeSuccessRule, cardUnFreezeFailureRule, cardActivationFailureRule, cardActivationSuccessRule, cardReissueSuccessRule, issuePhysicalCardSuccessRule, txnFailurePinRetriesExceededRule, paymentReminderWithInterestRule, paymentReminderWithoutInterestRule, paymentReminderBeforeDueDateRule, statementGeneratedRule, cashWithdrawalFeeRule, successfulRepaymentRule, creditUtilisationReachingThresholdRule, unpaidDuesFeesRule, joiningFeeRule, genericCreditRule, cardIssueSuccessWithLimitRule, creditCardDispatchedRule, welcomeOfferClaimRule, cardNotActivatedRule, creditCardClosedRule, securedCardFdCreationSuccessRule, securedCardFdLienMarkingSuccessSuccessRule, ccProfileValidationSuccessRule, ccProfileValidationFailureRule, creditCardPaymentReminderWhatsAppOnBillGenDayRule, creditCardPaymentReminderWhatsAppBeforeDueDayRule, creditCardPaymentReminderWhatsAppOnDueDayRule, creditCardPaymentReminderWhatsAppAfterDueDayRule, creditCardDeliveredRule, creditCardReturnedRule, creditCardOutForDeliveryRule, emiCreatedRule, emiClosedRule, emiPreClosedRule, emiCancelledRule, webEligibilityDropOffSmsRule, webEligibilityDropOffEmailRule, webEligibilityDropOffWhatsappRule, creditCardWelcomeRule, magnifiWelcomeRule, frmDeclineTxnRule, ccDeactivateInXDaysRule)
	cardRequestDaoWithInstrumentation := dao.NewCardRequestDaoWithInstrumentation(ccCacheStorage, cardRequestCacheConfig, cardRequestDao, eventBroker, asyncProcessor)
	daoCardRequestDao := dao.ProvideCardRequestDao(cardRequestDaoWithInstrumentation)
	webFlowDropOffUserCommunicationProvider := user_communication_provider.NewWebFlowDropOffUserCommunicationProvider(daoCardRequestDao, conf)
	userCommunicationProviderFactory := user_communication_provider.NewUserCommunicationProviderFactory(webFlowDropOffUserCommunicationProvider)
	return userCommunicationProviderFactory
}

func InitialiseRegisterCustomerFactory(ccVgClient creditcard.CreditCardClient, actorClient actor.ActorClient, usersClient user.UsersClient, celestialClient celestial.CelestialClient, bankCustClient bankcust.BankCustomerServiceClient, accountingClient accounting.AccountingClient, billingClient billing.BillingClient, docsClient docs.DocsClient, payClient pay.PayClient, orderClient order.OrderServiceClient, rewardsClient rewards.RewardsGeneratorClient, ccPinotClient pinot.TxnAggregatesClient, lmsClient lms.LoanManagementSystemClient, fireflyClient firefly2.FireflyClient, depositClient deposit.DepositClient, savingsClient savings.SavingsClient, rewardsProjectionClient projector.ProjectorServiceClient, profileClient profile.ProfileClient, onboardingClient onboarding.OnboardingClient) *register_customer_provider.RegisterCustomerProviderFactory {
	rpcHelper := helper.NewRpcHelper(ccVgClient, actorClient, usersClient, celestialClient, bankCustClient, accountingClient, billingClient, docsClient, payClient, orderClient, rewardsClient, ccPinotClient, lmsClient, fireflyClient, depositClient, savingsClient, rewardsProjectionClient, profileClient)
	vendorRequestComponents := register_customer_provider.NewVendorRequestComponents(onboardingClient, rpcHelper)
	securedRegisterCustomerProvider := register_customer_provider.NewSecuredRegisterCustomerProvider(vendorRequestComponents, rpcHelper)
	registerCustomerProviderFactory := register_customer_provider.NewRegisterCustomerProviderFactory(securedRegisterCustomerProvider)
	return registerCustomerProviderFactory
}

func InitialiseCreateCardAndAccountFactory(db types.CreditCardPGDB, ccVgClient creditcard.CreditCardClient, actorClient actor.ActorClient, usersClient user.UsersClient, celestialClient celestial.CelestialClient, bankCustClient bankcust.BankCustomerServiceClient, accountingClient accounting.AccountingClient, billingClient billing.BillingClient, docsClient docs.DocsClient, payClient pay.PayClient, orderClient order.OrderServiceClient, rewardsClient rewards.RewardsGeneratorClient, ccPinotClient pinot.TxnAggregatesClient, lmsClient lms.LoanManagementSystemClient, fireflyClient firefly2.FireflyClient, depositClient deposit.DepositClient, savingsClient savings.SavingsClient, rewardsProjectionClient projector.ProjectorServiceClient, profileClient profile.ProfileClient, workerConf *worker.Config, FireflyRedisStore types2.FireflyRedisStore) *create_card_and_account_provider.CreateCardAndAccountProviderFactory {
	rpcHelper := helper.NewRpcHelper(ccVgClient, actorClient, usersClient, celestialClient, bankCustClient, accountingClient, billingClient, docsClient, payClient, orderClient, rewardsClient, ccPinotClient, lmsClient, fireflyClient, depositClient, savingsClient, rewardsProjectionClient, profileClient)
	gormDB := types.CreditCardPGDBGormDBProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	creditCardDao := impl.NewCrdbCreditCard(gormDB, domainIdGenerator)
	ccCacheStorage := types2.WorkerCcCacheStorageProvider(workerConf, FireflyRedisStore)
	creditCardOfferCacheConfig := types2.WorkerCreditCardOfferCacheConfigProvider(workerConf)
	crdbCreditCardOfferDao := impl.NewCrdbCreditCardOfferDao(gormDB, domainIdGenerator)
	creditCardOfferDaoCache := dao.NewCreditCardOfferDaoCache(ccCacheStorage, creditCardOfferCacheConfig, crdbCreditCardOfferDao)
	creditCardOffersDao := dao.ProvideCreditCardOfferDao(creditCardOfferDaoCache)
	securedCardAndAccountCreationProvider := create_card_and_account_provider.NewSecuredCardAndAccountCreationProvider(rpcHelper, accountingClient, creditCardDao, creditCardOffersDao, workerConf)
	createCardAndAccountProviderFactory := create_card_and_account_provider.NewCardAndAccountCreationProviderFactory(securedCardAndAccountCreationProvider)
	return createCardAndAccountProviderFactory
}

func InitializeRecommendationSvc(db types.CreditCardPGDB, fireflyConf *config.Config, fireflyGenConf *genconf.Config, ffClient firefly2.FireflyClient, segmentSrvClient segment.SegmentationServiceClient, eventBroker events.Broker, FireflyRedisStore types2.FireflyRedisStore, userGroupClient group.GroupClient, userClient user.UsersClient, actorClient actor.ActorClient) *card_recommendation2.Service {
	gormDB := types.CreditCardPGDBGormDBProvider(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	creditCardRecommendationInfoDao := impl.NewCrdbCreditCardRecommendationInfo(gormDB, domainIdGenerator)
	ccCacheStorage := types2.CcCacheStorageProvider(fireflyConf, FireflyRedisStore)
	creditCardOfferCacheConfig := types2.CreditCardOfferCacheConfigProvider(fireflyConf)
	crdbCreditCardOfferDao := impl.NewCrdbCreditCardOfferDao(gormDB, domainIdGenerator)
	creditCardOfferDaoCache := dao.NewCreditCardOfferDaoCache(ccCacheStorage, creditCardOfferCacheConfig, crdbCreditCardOfferDao)
	creditCardOffersDao := dao.ProvideCreditCardOfferDao(creditCardOfferDaoCache)
	featureReleaseConfig := ReleaseConfigProvider(fireflyGenConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	service := card_recommendation2.NewCardRecommendationService(fireflyConf, fireflyGenConf, ffClient, creditCardRecommendationInfoDao, creditCardOffersDao, segmentSrvClient, eventBroker, evaluator, userClient)
	return service
}

// wire.go:

func RateLimiterConfigProvider(conf *genconf.Config) *genconf3.RateLimitConfig {
	return conf.RateLimiterConfig()
}

func DummyRateLimiterOptionsProvider() []ratelimiter.Option {
	return nil
}

func ReleaseConfigProvider(conf *genconf.Config) *genconf4.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}

func WorkerReleaseConfigProvider(conf *genconf2.Config) *genconf4.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}

func pinotClientProvider(fireflyCfg *config.Config) pinot4.Client {
	env, _ := cfg.GetEnvironment()
	client, _ := pinot4.NewPinotClient(fireflyCfg.PinotConfig, env)
	return client
}

func DoOnceProvider(ccDb *gorm.DB) once.DoOnce {
	return once.NewDoOnce(ccDb)
}

func KinesisProducerProvider(awsV2Config aws.Config, conf *config.Config) (types2.TransactionsProducer, error) {
	kinesisClient := kinesis.NewKinesisClient(&awsV2Config)

	var transactionsProducer types2.TransactionsProducer
	transactionsProducer, err := kinesis.NewKinesisProducer(context.Background(), conf.TransactionsProducer, kinesisClient)
	if err != nil {
		err = fmt.Errorf("failed to initialise producer: %w", err)
	}
	return transactionsProducer, err
}

func fireflyS3ClientProvider(awsConfig aws.Config, conf *config.Config) types2.FireflyS3Client {
	var bucket string
	env := conf.Application.Environment
	if cfg.IsQaEnv(env) || cfg.IsStagingEnv(env) || cfg.IsUatEnv(env) {
		bucket = "epifi-" + env + "-docs"
		if cfg.IsTestTenantEnabled() {
			bucket += "-" + cfg.GetTestTenant()
		}
	} else {
		bucket = "epifi-docs"
	}
	return s3.NewClient(awsConfig, bucket)
}

func InitCryptors(conf *config.Config) (*cryptormap.InMemoryCryptorStore, error) {

	pgpCryptor := pgp.New(conf.Secrets.Ids[config.FederalPgpPublicKey],
		conf.Secrets.Ids[config.EpifiFederalPgpPrivateKey], conf.Secrets.Ids[config.EpifiFederalPgpPassphrase])
	if pgpCryptor == nil {
		return nil, errors.New("failed to create PGP cryptor")
	}
	cryptorStore := &cryptormap.InMemoryCryptorStore{}
	cryptorStore.AddCryptor(vendorgateway.Vendor_FEDERAL_BANK, vendorgateway.CryptorType_PGP, pgpCryptor)

	ffPgpCryptor := pgp.New(conf.Secrets.Ids[config.SeshaasaiPgpPublicKey],
		conf.Secrets.Ids[config.EpifiSeshaasaiPgpPrivateKey], conf.Secrets.Ids[config.EpifiSeshaasaiPgpPassphrase])
	if pgpCryptor == nil {
		return nil, errors.New("failed to create PGP cryptor")
	}

	cryptorStore.AddCryptor(vendorgateway.Vendor_SESHAASAI, vendorgateway.CryptorType_PGP, ffPgpCryptor)

	return cryptorStore, nil
}
