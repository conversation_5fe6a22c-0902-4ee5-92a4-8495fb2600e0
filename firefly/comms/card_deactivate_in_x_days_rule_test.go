package comms

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"

	commsPb "github.com/epifi/gamma/api/comms"
	ffPb "github.com/epifi/gamma/api/firefly"
	ffCommsPb "github.com/epifi/gamma/api/firefly/comms"
)

func TestCcDeactivateInXDaysRule_GetComms(t *testing.T) {
	type args struct {
		in0  context.Context
		data IActionData
	}
	tests := []struct {
		name    string
		args    args
		wantRes []commsPb.CommMessage
		wantErr bool
	}{
		{
			name: "success case",
			args: args{
				in0: context.Background(),
				data: &ffCommsPb.ActionData_CardClosureReminderActionData{
					CardClosureReminderActionData: &ffCommsPb.CardClosureReminderActionData{
						CreditCard: &ffPb.CreditCard{
							Id: "id-1",
							BasicInfo: &ffPb.BasicInfo{
								MaskedCardNumber: "xxxxxxxxxxxx1234",
							},
						},
						DaysLeftBeforeClosure: 1,
					},
				},
			},
			wantRes: []commsPb.CommMessage{
				&commsPb.SendMessageRequest_Sms{
					Sms: &commsPb.SMSMessage{
						SmsOption: &commsPb.SmsOption{
							Option: &commsPb.SmsOption_CcClosureInXDaysSmsOption{
								CcClosureInXDaysSmsOption: &commsPb.CcClosureInXDaysSmsOption{
									SmsType: commsPb.SmsType_CC_CLOSURE_INXDAYS_SMS,
									Option: &commsPb.CcClosureInXDaysSmsOption_CcClosureInXDaysSmsOptionV1{
										CcClosureInXDaysSmsOptionV1: &commsPb.CcClosureInXDaysSmsOptionV1{
											TemplateVersion:             commsPb.TemplateVersion_VERSION_V1,
											CardLastFourDigits:          "1234",
											NumberDaysLeftBeforeClosure: 1,
											AppDownloadUrl:              " ",
										},
									},
								},
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "invalid card number length",
			args: args{
				in0: context.Background(),
				data: &ffCommsPb.ActionData_CardClosureReminderActionData{
					CardClosureReminderActionData: &ffCommsPb.CardClosureReminderActionData{
						CreditCard: &ffPb.CreditCard{
							Id: "id-1",
							BasicInfo: &ffPb.BasicInfo{
								MaskedCardNumber: "123",
							},
						},
						DaysLeftBeforeClosure: 1,
					},
				},
			},
			wantRes: nil,
			wantErr: false,
		},
		{
			name: "invalid value for DaysLeftBeforeClosure",
			args: args{
				in0: context.Background(),
				data: &ffCommsPb.ActionData_CardClosureReminderActionData{
					CardClosureReminderActionData: &ffCommsPb.CardClosureReminderActionData{
						CreditCard: &ffPb.CreditCard{
							Id: "id-1",
							BasicInfo: &ffPb.BasicInfo{
								MaskedCardNumber: "xxxxxxxxxxxx1234",
							},
						},
						DaysLeftBeforeClosure: 0,
					},
				},
			},
			wantRes: nil,
			wantErr: false,
		},
		{
			name: "invalid action data type mismatch",
			args: args{
				in0: context.Background(),
				data: &ffCommsPb.ActionData_CardClosureActionData{
					CardClosureActionData: &ffCommsPb.CardClosureActionData{
						CreditCard: &ffPb.CreditCard{
							Id: "id-1",
							BasicInfo: &ffPb.BasicInfo{
								MaskedCardNumber: "xxxxxxxxxxxx1234",
							},
						},
					},
				},
			},
			wantRes: nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			gotRes, err := NewCcDeactivateInXDaysRule().GetComms(tt.args.in0, tt.args.data)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetComms() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			require.Equal(t, tt.wantRes, gotRes)
		})
	}
}
