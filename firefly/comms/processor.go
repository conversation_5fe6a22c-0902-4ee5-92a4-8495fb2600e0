// nolint
package comms

import (
	"context"
	"fmt"

	"github.com/google/wire"
	pkgErrors "github.com/pkg/errors"
	"go.uber.org/zap"

	onceV2 "github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"

	actorPb "github.com/epifi/gamma/api/actor"
	commsPb "github.com/epifi/gamma/api/comms"
	ffCommsPb "github.com/epifi/gamma/api/firefly/comms"
)

var (
	AsyncProcessorWireSet = wire.NewSet(
		NewTxnSuccessRule,
		NewTxnFailureRule,
		NewAtmTxnFailureRule,
		NewAtmTxnSuccessRule,
		NewCrossBorderTxnSuccessRule,
		NewTxnReversalSuccessRule,
		NewContactlessSettingsDisabledRule,
		NewContactlessSettingsEnabledRule,
		NewInternationalSettingsEnabledRule,
		NewInternationalSettingsDisabledRule,
		NewOnlineSettingsDisabledRule,
		NewOnlineSettingsEnabledRule,
		NewPosSettingsDisabledRule,
		NewPosSettingsEnabledRule,
		NewCardUnfreezeSuccessRule,
		NewCreditCardPosPurchaseLimitChangedRule,
		NewCreditCardOnlinePurchaseLimitChangedRule,
		NewCardFreezeSuccessRule,
		NewCreditCardCardUsageChangeFailureRule,
		NewCreditCardContactlessPurchaseLimitChangedRule,
		NewCardFreezeFailureRule,
		NewCreditCardLimitChangeFailureRule,
		NewCreditCardPinChangeSuccessRule,
		NewCardUnFreezeFailureRule,
		NewCardActivationFailureRule,
		NewCardActivationSuccessRule,
		NewCardReissueSuccessRule,
		NewIssuePhysicalCardSuccessRule,
		NewTxnFailurePinRetriesExceededRule,
		NewPaymentReminderWithInterestRule,
		NewPaymentReminderWithoutInterestRule,
		NewPaymentReminderBeforeDueDateRule,
		NewStatementGeneratedRule,
		NewCashWithdrawalFeeRule,
		NewSuccessfulRepaymentRule,
		NewCreditUtilisationReachingThresholdRule,
		NewUnpaidDuesFeesRule,
		NewJoiningFeeRule,
		NewGenericCreditRule,
		NewCardIssueSuccessWithLimitRule,
		NewCreditCardDispatchedRule,
		NewWelcomeOfferClaimRule,
		NewCardNotActivatedRule,
		NewCreditCardClosedRule,
		NewSecuredCardFdCreationSuccessRule,
		NewSecuredCardFdLienMarkingSuccessSuccessRule,
		NewCCProfileValidationFailureRule,
		NewCCProfileValidationSuccessRule,
		NewCreditCardPaymentReminderWhatsAppOnBillGenDayRule,
		NewCreditCardPaymentReminderWhatsAppBeforeDueDayRule,
		NewCreditCardPaymentReminderWhatsAppOnDueDayRule,
		NewCreditCardPaymentReminderWhatsAppAfterDueDayRule,
		NewCreditCardOutForDeliveryRule,
		NewCreditCardDeliveredRule,
		NewCreditCardReturnedRule,
		NewEmiCreatedRule,
		NewEmiPreClosedRule,
		NewEmiClosedRule,
		NewEmiCancelledRule,
		NewWebEligibilityDropOffSmsRule,
		NewWebEligibilityDropOffEmailRule,
		NewWebEligibilityDropOffWhatsappRule,
		NewCreditCardWelcomeRule,
		NewMagnifiWelcomeRule,
		NewFrmDeclineTxnRule,
		NewCcDeactivateInXDaysRule,
		wire.NewSet(NewAsyncProcessor, wire.Bind(new(Processor), new(*AsyncProcessor))),
	)
)

type Processor interface {
	ProcessAction(ctx context.Context, actionData *ffCommsPb.ActionData) error
}

type AsyncProcessor struct {
	commsClient   commsPb.CommsClient
	actorClient   actorPb.ActorClient
	sendCommsList []Rule
	doOnce        onceV2.DoOnce
}

func NewAsyncProcessor(
	commsClient commsPb.CommsClient,
	actorClient actorPb.ActorClient,
	doOnceMgr onceV2.DoOnce,
	atmTxnSuccessRule *AtmTxnSuccessRule,
	atmTxnFailureRule *AtmTxnFailureRule,
	crossBorderTxnSuccessRule *CrossBorderTxnSuccessRule,
	txnSuccessRule *TxnSuccessRule,
	txnFailureRule *TxnFailureRule,
	txnReversalSuccessRule *TxnReversalSuccessRule,
	onlineSettingsEnabledRule *OnlineSettingsEnabledRule,
	onlineSettingsDisabledRule *OnlineSettingsDisabledRule,
	posSettingsEnabledRule *PosSettingsEnabledRule,
	posSettingsDisabledRule *PosSettingsDisabledRule,
	internationalSettingsEnabledRule *InternationalSettingsEnabledRule,
	internationalSettingsDisabledRule *InternationalSettingsDisabledRule,
	contactlessSettingsEnabledRule *ContactlessSettingsEnabledRule,
	contactlessSettingsDisabledRule *ContactlessSettingsDisabledRule,
	cardUnfreezeSuccess *CardUnfreezeSuccessRule,
	creditCardPosPurchaseLimitChangedRule *CreditCardPosPurchaseLimitChangedRule,
	creditCardOnlinePurchaseLimitChangedRule *CreditCardOnlinePurchaseLimitChangedRule,
	cardFreezeSuccessRule *CardFreezeSuccessRule,
	creditCardCardUsageChangeFailureRule *CreditCardCardUsageChangeFailureRule,
	creditCardContactlessPurchaseLimitChangedRule *CreditCardContactlessPurchaseLimitChangedRule,
	cardFreezeFailureRule *CardFreezeFailureRule,
	creditCardLimitChangeFailureRule *CreditCardLimitChangeFailureRule,
	creditCardPinChangeSuccessRule *CreditCardPinChangeSuccessRule,
	cardUnFreezeFailureRule *CardUnFreezeFailureRule,
	cardActivationFailureRule *CardActivationFailureRule,
	cardActivationSuccessRule *CardActivationSuccessRule,
	cardReissueSuccessRule *CardReissueSuccessRule,
	issuePhysicalCardSuccessRule *IssuePhysicalCardSuccessRule,
	txnFailurePinRetriesExceededRule *TxnFailurePinRetriesExceededRule,
	paymentReminderWithInterestRule *PaymentReminderWithInterestRule,
	paymentReminderWithoutInterestRule *PaymentReminderWithoutInterestRule,
	paymentReminderBeforeDueDateRule *PaymentReminderBeforeDueDateRule,
	statementGeneratedRule *StatementGeneratedRule,
	cashWithdrawalFeeRule *CashWithdrawalFeeRule,
	successfulRepaymentRule *SuccessfulRepaymentRule,
	creditUtilisationReachingThresholdRule *CreditUtilisationReachingThresholdRule,
	unpaidDuesFeesRule *UnpaidDuesFeesRule,
	joiningFeeRule *JoiningFeeRule,
	genericCreditRule *GenericCreditRule,
	cardIssueSuccessWithLimitRule *CardIssueSuccessWithLimitRule,
	creditCardDispatchedRule *CreditCardDispatchedRule,
	welcomeOfferClaimRule *WelcomeOfferClaimRule,
	cardNotActivatedRule *CardNotActivatedRule,
	creditCardClosedRule *CreditCardClosedRule,
	securedCardFdCreationSuccessRule *SecuredCardFdCreationSuccessRule,
	securedCardFdLienMarkingSuccessSuccessRule *SecuredCardFdLienMarkingSuccessSuccessRule,
	cCProfileValidationSuccessRule *CCProfileValidationSuccessRule,
	cCProfileValidationFailureRule *CCProfileValidationFailureRule,
	creditCardPaymentReminderWhatsAppOnBillGenDayRule *CreditCardPaymentReminderWhatsAppOnBillGenDayRule,
	creditCardPaymentReminderWhatsAppBeforeDueDayRule *CreditCardPaymentReminderWhatsAppBeforeDueDayRule,
	creditCardPaymentReminderWhatsAppOnDueDayRule *CreditCardPaymentReminderWhatsAppOnDueDayRule,
	creditCardPaymentReminderWhatsAppAfterDueDayRule *CreditCardPaymentReminderWhatsAppAfterDueDayRule,
	creditCardDeliveredRule *CreditCardDeliveredRule,
	creditCardReturnedRule *CreditCardReturnedRule,
	creditCardOutForDeliveryRule *CreditCardOutForDeliveryRule,
	emiCreatedRule *EmiCreatedRule,
	emiClosedRule *EmiClosedRule,
	emiPreClosedRule *EmiPreClosedRule,
	emiCancelledRule *EmiCancelledRule,
	webEligibilityDropOffSmsRule *WebEligibilityDropOffSmsRule,
	webEligibilityDropOffEmailRule *WebEligibilityDropOffEmailRule,
	webEligibilityDropOffWhatsappRule *WebEligibilityDropOffWhatsappRule,
	creditCardWelcomeRule *CreditCardWelcomeRule,
	magnifiWelcomeRule *MagnifiWelcomeRule,
	frmDeclineTxnRule *FrmDeclineTxnRule,
	ccDeactivateInXDaysRule *CcDeactivateInXDaysRule,
) *AsyncProcessor {
	var scl []Rule
	scl = append(scl, atmTxnSuccessRule, atmTxnFailureRule, crossBorderTxnSuccessRule, txnSuccessRule, txnFailureRule,
		txnReversalSuccessRule, onlineSettingsEnabledRule, onlineSettingsDisabledRule, posSettingsEnabledRule, posSettingsDisabledRule,
		internationalSettingsDisabledRule, internationalSettingsEnabledRule, contactlessSettingsDisabledRule, contactlessSettingsEnabledRule,
		cardUnfreezeSuccess, creditCardPosPurchaseLimitChangedRule, creditCardOnlinePurchaseLimitChangedRule, cardFreezeSuccessRule,
		creditCardCardUsageChangeFailureRule, creditCardContactlessPurchaseLimitChangedRule, cardFreezeFailureRule, creditCardLimitChangeFailureRule,
		creditCardPinChangeSuccessRule, cardUnFreezeFailureRule, cardActivationFailureRule, cardActivationSuccessRule, cardReissueSuccessRule, paymentReminderWithInterestRule,
		paymentReminderWithoutInterestRule, paymentReminderBeforeDueDateRule, statementGeneratedRule,
		issuePhysicalCardSuccessRule, txnFailurePinRetriesExceededRule, cashWithdrawalFeeRule, successfulRepaymentRule, creditUtilisationReachingThresholdRule, unpaidDuesFeesRule, joiningFeeRule, genericCreditRule,
		cardIssueSuccessWithLimitRule, creditCardDispatchedRule, welcomeOfferClaimRule, cardNotActivatedRule, creditCardClosedRule,
		securedCardFdCreationSuccessRule, securedCardFdLienMarkingSuccessSuccessRule, cCProfileValidationSuccessRule, cCProfileValidationFailureRule,
		creditCardPaymentReminderWhatsAppOnBillGenDayRule, creditCardPaymentReminderWhatsAppBeforeDueDayRule, creditCardPaymentReminderWhatsAppOnDueDayRule,
		creditCardPaymentReminderWhatsAppAfterDueDayRule, creditCardDeliveredRule, creditCardReturnedRule, creditCardOutForDeliveryRule,
		emiCreatedRule, emiClosedRule, emiPreClosedRule, emiCancelledRule,
		webEligibilityDropOffSmsRule, webEligibilityDropOffEmailRule, webEligibilityDropOffWhatsappRule,
		creditCardWelcomeRule, magnifiWelcomeRule, frmDeclineTxnRule, ccDeactivateInXDaysRule,
	)
	return &AsyncProcessor{
		commsClient:   commsClient,
		actorClient:   actorClient,
		sendCommsList: scl,
		doOnce:        doOnceMgr,
	}
}

func (p *AsyncProcessor) ProcessAction(ctx context.Context, actionData *ffCommsPb.ActionData) error {
	data, ok := actionData.GetData().(IActionData)
	if !ok {
		return pkgErrors.New("Given action data does not implement IActionData interface")
	}
	actorRes, err := p.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: data.GetActorId()})
	if te := epifigrpc.RPCError(actorRes, err); te != nil {
		return pkgErrors.Wrap(te, "failed to GetActorById")
	}
	commsChan := make(chan *SendCommsMessageAttributes)
	sendCommsMasterGrp, _ := errgroup.WithContext(ctx)
	sendCommsGrp, sendCommsGrpCtx := errgroup.WithContext(ctx)
	// init receivers on channel
	sendCommsMasterGrp.Go(func() error {
		for c := range commsChan {
			sm := c
			sendCommsGrp.Go(func() error {
				medium, err := GetCommsMedium(sm.Message)
				if err != nil {
					logger.Error(sendCommsGrpCtx, "error in GetCommsMedium", zap.Error(err))
				}
				externalMessageId := data.GetExternalMessageId()
				sendCommsMessage := &commsPb.SendMessageRequest{
					Type:   sm.QualityOfService,
					Medium: medium,
					UserIdentifier: &commsPb.SendMessageRequest_UserId{
						UserId: actorRes.GetActor().GetEntityId(),
					},
					Message: sm.Message,
					// TODO(Vikas): update campaign once available
					CampaignName:        commsPb.CampaignName_CAMPAIGN_NAME_UNSPECIFIED,
					ExternalReferenceId: externalMessageId,
				}
				var sendCommsErr error
				switch {
				case sm.DoOnceTaskId == "":
					sendCommsErr = p.SendCommsWithoutDoOnceCheck(sendCommsGrpCtx, sendCommsMessage, externalMessageId)
				default:
					sendCommsErr = p.SendCommsWithDoOnceCheck(sendCommsGrpCtx, sendCommsMessage, sm.DoOnceTaskId, externalMessageId)
				}
				return sendCommsErr
			})
		}
		return nil
	})
	getCommsGrp, getCommsGrpCtx := errgroup.WithContext(ctx)
	getCommsGrp.SetLimit(3)
	for i := range p.sendCommsList {
		sc := p.sendCommsList[i]
		getCommsGrp.Go(func() error {
			cs, err := sc.GetComms(getCommsGrpCtx, data)
			if err != nil {
				logger.Error(getCommsGrpCtx, fmt.Sprintf("error in GetComms for: %T", sc), zap.Error(err))
				return nil
			}
			for _, c := range cs {
				commsMedium, err := GetCommsMedium(c)
				if err != nil {
					logger.Error(getCommsGrpCtx, "error in GetCommsMedium", zap.Error(err))
					continue
				}
				doOnceRule, ok := sc.(DoOnceRule)
				doOnceId := ""
				if ok {
					doOnceId = doOnceRule.GetDoOnceTaskId(ctx, data, commsMedium)
				}
				qos := commsPb.QoS_BEST_EFFORT
				qosRule, ok := sc.(QualityOfServiceRule)
				if ok {
					qos = qosRule.GetQualityOfService(ctx, commsMedium)
				}
				commsChan <- &SendCommsMessageAttributes{
					Message:          c,
					DoOnceTaskId:     doOnceId,
					QualityOfService: qos,
				}
			}
			return nil
		})
	}
	if err := getCommsGrp.Wait(); err != nil {
		return pkgErrors.Wrap(err, "error in getting GetComms for rules")
	}
	close(commsChan)
	if err := sendCommsMasterGrp.Wait(); err != nil {
		logger.Error(ctx, "error in sendComms for comms messages", zap.Error(err))
	}
	if err := sendCommsGrp.Wait(); err != nil {
		logger.Error(ctx, "error in sendComms for comms messages", zap.Error(err))
	}
	return nil
}

func (p *AsyncProcessor) SendCommsWithDoOnceCheck(ctx context.Context, message *commsPb.SendMessageRequest, doOnceTaskId string, externalMessageId string) error {
	sendCommsFunc := func() error {
		res, err := p.commsClient.SendMessage(ctx, message)
		if te := epifigrpc.RPCError(res, err); te != nil {
			logger.Error(ctx, "error in SendMessage", zap.Error(te), zap.String(logger.COMMS_EXTERNAL_REF_ID, externalMessageId))
			return te
		} else {
			logger.Info(ctx, "successfully sent comms: ", zap.String(logger.COMMS_EXTERNAL_REF_ID, externalMessageId), zap.String(logger.COMMS_EXTERNAL_REF_ID, externalMessageId),
				zap.String(logger.COMMS_MSG_ID, res.GetMessageId()))
		}
		return nil
	}
	isDone, err := p.doOnce.IsDone(ctx, doOnceTaskId)
	switch {
	case err != nil:
		logger.Error(ctx, "error determining if task is done from do once dao", zap.Error(err))
		return err
	case isDone:
		logger.Info(ctx, "comms already sent for given task id: ", zap.String(logger.TASK_ID, doOnceTaskId))
		return nil
	default:
	}
	doOnceTaskError := p.doOnce.DoOnceFn(ctx, doOnceTaskId, sendCommsFunc)
	if doOnceTaskError != nil {
		logger.Error(ctx, "error sending comms: ", zap.Error(doOnceTaskError))
		return doOnceTaskError
	}
	return nil
}
func (p *AsyncProcessor) SendCommsWithoutDoOnceCheck(ctx context.Context, message *commsPb.SendMessageRequest, externalMessageId string) error {
	res, err := p.commsClient.SendMessage(ctx, message)
	if te := epifigrpc.RPCError(res, err); te != nil {
		logger.Error(ctx, "error in SendMessage", zap.Error(te), zap.String(logger.COMMS_EXTERNAL_REF_ID, externalMessageId))
		return te
	} else {
		logger.Info(ctx, "successfully sent comms: ", zap.String(logger.COMMS_EXTERNAL_REF_ID, externalMessageId), zap.String(logger.COMMS_EXTERNAL_REF_ID, externalMessageId),
			zap.String(logger.COMMS_MSG_ID, res.GetMessageId()))
	}
	return nil
}
func GetCommsMedium(msg commsPb.CommMessage) (commsPb.Medium, error) {
	switch msg.(type) {
	case *commsPb.SendMessageRequest_Email:
		return commsPb.Medium_EMAIL, nil
	case *commsPb.SendMessageRequest_Sms:
		return commsPb.Medium_SMS, nil
	case *commsPb.SendMessageRequest_Notification:
		return commsPb.Medium_NOTIFICATION, nil
	case *commsPb.SendMessageRequest_Whatsapp:
		return commsPb.Medium_WHATSAPP, nil
	default:
		return commsPb.Medium_MEDIUM_UNSPECIFIED, pkgErrors.New("unidentified comms medium")
	}
}
