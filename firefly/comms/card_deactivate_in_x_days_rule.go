package comms

import (
	"context"

	commsPb "github.com/epifi/gamma/api/comms"
	ffCommsPb "github.com/epifi/gamma/api/firefly/comms"
)

type CcDeactivateInXDaysRule struct {
}

func NewCcDeactivateInXDaysRule() *CcDeactivateInXDaysRule {
	return &CcDeactivateInXDaysRule{}
}

func (s *CcDeactivateInXDaysRule) GetComms(_ context.Context, data IActionData) (res []commsPb.CommMessage, resErr error) {
	cardClosureReminderActionData, ok := data.(*ffCommsPb.ActionData_CardClosureReminderActionData)
	if !ok {
		return
	}
	maskedCardNumber := cardClosureReminderActionData.CardClosureReminderActionData.GetCreditCard().GetBasicInfo().GetMaskedCardNumber()
	if len(maskedCardNumber) < 4 || cardClosureReminderActionData.CardClosureReminderActionData.GetDaysLeftBeforeClosure() <= 0 {
		return
	}

	// sms comms
	res = append(res, &commsPb.SendMessageRequest_Sms{
		Sms: &commsPb.SMSMessage{
			SmsOption: &commsPb.SmsOption{
				Option: &commsPb.SmsOption_CcClosureInXDaysSmsOption{
					CcClosureInXDaysSmsOption: &commsPb.CcClosureInXDaysSmsOption{
						SmsType: commsPb.SmsType_CC_CLOSURE_INXDAYS_SMS,
						Option: &commsPb.CcClosureInXDaysSmsOption_CcClosureInXDaysSmsOptionV1{
							CcClosureInXDaysSmsOptionV1: &commsPb.CcClosureInXDaysSmsOptionV1{
								TemplateVersion:             commsPb.TemplateVersion_VERSION_V1,
								CardLastFourDigits:          maskedCardNumber[len(maskedCardNumber)-4:],
								NumberDaysLeftBeforeClosure: cardClosureReminderActionData.CardClosureReminderActionData.GetDaysLeftBeforeClosure(),
								// NOTE: passing a single space in URL since comms are not working when passing actual URL.
								// Passing single space here is necessary, since if any of the variables in the template is empty, request ends up failing in validation at vendor.
								// This could be due to the character limit at vendor or URL not being whitelisted.
								// Behaviour with actual URL:
								//           Message is being sent to vendor(KALEYRA) but not getting delivered to end users,
								//           also we're not receiving callbacks for terminal status which includes both failure and success.
								AppDownloadUrl: " ",
							},
						},
					},
				},
			},
		},
	})
	return
}
