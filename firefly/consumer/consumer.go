//nolint:funlen,dupl
package consumer

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"bytes"
	"context"
	"encoding/csv"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	celestialPb "github.com/epifi/be-common/api/celestial"
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	doOnceV2 "github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/crypto/cryptormap"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"

	actorPb "github.com/epifi/gamma/api/actor"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/bre"
	"github.com/epifi/gamma/api/comms"
	creditReportPbv2 "github.com/epifi/gamma/api/creditreportv2"
	ccAccountingPb "github.com/epifi/gamma/api/firefly/accounting"
	ffRePb "github.com/epifi/gamma/api/firefly/card_recommendation"
	ffCommsPb "github.com/epifi/gamma/api/firefly/comms"
	creditcardConsumerPb "github.com/epifi/gamma/api/firefly/consumer"
	ffEnums "github.com/epifi/gamma/api/firefly/enums"
	"github.com/epifi/gamma/api/product"
	riskPb "github.com/epifi/gamma/api/risk"
	"github.com/epifi/gamma/api/segment"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	creditCardVgPb "github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	ffComms "github.com/epifi/gamma/firefly/comms"
	"github.com/epifi/gamma/firefly/config"
	"github.com/epifi/gamma/firefly/dao"
	ccEvents "github.com/epifi/gamma/firefly/events"
	"github.com/epifi/gamma/firefly/helper"
	"github.com/epifi/gamma/firefly/wire/types"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
)

const (
	dateLayout   = "2006-01-02" // YYYY-MM-DD
	dpdSegmentId = "65ea3cc4-87b1-4c45-9d7e-0901c5d4c530"
)

var (
	csvHeader          = []string{"customer_id"}
	amplifiCardProgram = &typesPb.CardProgram{
		CardProgramVendor: typesPb.CardProgramVendor_CARD_PROGRAM_VENDOR_FEDERAL,
		CardProgramSource: typesPb.CardProgramSource_CARD_PROGRAM_SOURCE_PREAPPROVED,
		CardProgramType:   typesPb.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED,
		CardProgramOrigin: typesPb.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI,
	}
	magnifiCardProgram = &typesPb.CardProgram{
		CardProgramVendor: typesPb.CardProgramVendor_CARD_PROGRAM_VENDOR_FEDERAL,
		CardProgramSource: typesPb.CardProgramSource_CARD_PROGRAM_SOURCE_PREAPPROVED,
		CardProgramType:   typesPb.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED,
		CardProgramOrigin: typesPb.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI,
	}
)

type CardsSentForPrintingS3Client s3.S3Client
type CardsDispatchedS3Client s3.S3Client

type Service struct {
	creditcardConsumerPb.UnimplementedCreditCardConsumerServer
	actorClient                  actorPb.ActorClient
	creditCardVgClient           creditCardVgPb.CreditCardClient
	s3Client                     s3.S3Client
	bankCustClient               bankCustPb.BankCustomerServiceClient
	rpcHelper                    *helper.RpcHelper
	ccOffersDao                  dao.CreditCardOffersDao
	ccEligibilityDao             dao.CreditCardOfferEligibilityCriteriaDao
	conf                         *config.Config
	creditCardDao                dao.CreditCardDao
	celestialClient              celestialPb.CelestialClient
	cardRequestDao               dao.CardRequestDao
	cardsSentForPrintingS3Client CardsSentForPrintingS3Client
	cardsDispatchedS3Client      CardsDispatchedS3Client
	commsProcessor               ffComms.Processor
	cryptorStore                 *cryptormap.InMemoryCryptorStore
	userClient                   user.UsersClient
	breClient                    bre.BreClient
	productClient                product.ProductClient
	crManagerClient              creditReportPbv2.CreditReportManagerClient
	recommendationEngineClient   ffRePb.CardRecommendationServiceClient
	eventBroker                  events.Broker
	ccAccountingClient           ccAccountingPb.AccountingClient
	riskClient                   riskPb.RiskClient
	commsClient                  comms.CommsClient
	segmentationClient           segment.SegmentationServiceClient
	doOnceClient                 doOnceV2.DoOnce
}

func NewService(actorClient actorPb.ActorClient,
	creditCardVgClient creditCardVgPb.CreditCardClient,
	s3Client types.FireflyS3Client,
	bankCustClient bankCustPb.BankCustomerServiceClient,
	rpcHelper *helper.RpcHelper,
	ccOffersDao dao.CreditCardOffersDao,
	ccEligibilityDao dao.CreditCardOfferEligibilityCriteriaDao,
	conf *config.Config,
	creditCardDao dao.CreditCardDao,
	celestialClient celestialPb.CelestialClient,
	cardRequestDao dao.CardRequestDao,
	cardsSentForPrintingS3Client CardsSentForPrintingS3Client,
	cardsDispatchedS3Client CardsDispatchedS3Client,
	commsProcessor ffComms.Processor,
	cryptorStore *cryptormap.InMemoryCryptorStore,
	userClient user.UsersClient,
	breClient bre.BreClient,
	productClient product.ProductClient,
	crManagerClient creditReportPbv2.CreditReportManagerClient,
	recommendationEngineClient ffRePb.CardRecommendationServiceClient,
	eventBroker events.Broker,
	ccAccountingClient ccAccountingPb.AccountingClient,
	riskClient riskPb.RiskClient,
	commsClient comms.CommsClient,
	segmentationClient segment.SegmentationServiceClient,
	doOnceClient doOnceV2.DoOnce,
) *Service {
	return &Service{
		actorClient:                  actorClient,
		creditCardVgClient:           creditCardVgClient,
		s3Client:                     s3Client,
		bankCustClient:               bankCustClient,
		rpcHelper:                    rpcHelper,
		ccOffersDao:                  ccOffersDao,
		ccEligibilityDao:             ccEligibilityDao,
		conf:                         conf,
		creditCardDao:                creditCardDao,
		celestialClient:              celestialClient,
		cardRequestDao:               cardRequestDao,
		cardsSentForPrintingS3Client: cardsSentForPrintingS3Client,
		cardsDispatchedS3Client:      cardsDispatchedS3Client,
		commsProcessor:               commsProcessor,
		cryptorStore:                 cryptorStore,
		userClient:                   userClient,
		breClient:                    breClient,
		productClient:                productClient,
		crManagerClient:              crManagerClient,
		recommendationEngineClient:   recommendationEngineClient,
		eventBroker:                  eventBroker,
		ccAccountingClient:           ccAccountingClient,
		riskClient:                   riskClient,
		commsClient:                  commsClient,
		segmentationClient:           segmentationClient,
		doOnceClient:                 doOnceClient,
	}
}

func (s *Service) ProcessCreditCardEligibleUsersFile(ctx context.Context, req *creditcardConsumerPb.ProcessEligibleUsersFileRequest) (*creditcardConsumerPb.ProcessEligibleUsersFileResponse, error) {
	if len(req.GetRecords()) == 0 {
		logger.Info(ctx, "Processing of eligible users file: expected a few records, received: 0")
		return &creditcardConsumerPb.ProcessEligibleUsersFileResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_SUCCESS,
			},
		}, nil
	}

	for _, record := range req.GetRecords() {
		// sample s3RecordPath := "epifi_eligible_users/sample-file.csv"
		s3RecordPath := record.GetS3().GetObject().GetKey()

		if returnStatus, returnError := s.processS3RecordAndUploadFile(ctx, s3RecordPath); returnStatus != queuePb.MessageConsumptionStatus_SUCCESS {
			logger.Error(ctx, returnError.Error())
			return &creditcardConsumerPb.ProcessEligibleUsersFileResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: returnStatus,
				},
			}, nil
		}
	}

	return &creditcardConsumerPb.ProcessEligibleUsersFileResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status: queuePb.MessageConsumptionStatus_SUCCESS,
		},
	}, nil
}

// vendor needs to be provided in case isDecryptionNeeded param is true to initialize cryptor for that specific vendor
func (s *Service) readCsvFromS3(ctx context.Context, s3RecordPath string, isDecryptionNeeded bool, vendor commonvgpb.Vendor) (*[][]string, error) {
	csvFileContents, readError := s.s3Client.Read(ctx, s3RecordPath)
	if readError != nil {
		return nil, errors.Wrap(readError, "read from s3Client failed")
	}

	if isDecryptionNeeded {
		decryptedData, decryptionError := decryptCsvFile(ctx, s.cryptorStore, csvFileContents, vendor)
		if decryptionError != nil {
			return nil, decryptionError
		}
		csvFileContents = decryptedData
	}

	csvReader := csv.NewReader(bytes.NewReader(csvFileContents))
	csvRecords, csvReadError := csvReader.ReadAll()
	if csvReadError != nil {
		return nil, errors.Wrap(csvReadError, "read from csvReader failed")
	}

	return &csvRecords, nil
}

func decryptCsvFile(ctx context.Context, cryptorMapStore *cryptormap.InMemoryCryptorStore, csvFileContents []byte, vendor commonvgpb.Vendor) ([]byte, error) {
	pgpCryptor, err := cryptorMapStore.GetCryptor(vendor, commonvgpb.CryptorType_PGP)
	if err != nil {
		return nil, err
	}

	decryptedData, decryptionError := pgpCryptor.DecryptAndVerify(ctx, csvFileContents, "")
	if decryptionError != nil {
		return nil, errors.Wrap(decryptionError, "decryption of csv file failed")
	}

	return decryptedData, nil
}

func (s *Service) encryptAndWriteCsvToS3(ctx context.Context, filePath string, customerIdMappings *[][]string) (string, error) {
	buf := new(bytes.Buffer)
	csvWriter := csv.NewWriter(buf)
	if csvWriterError := csvWriter.WriteAll(*customerIdMappings); csvWriterError != nil {
		return "", errors.Wrap(csvWriterError, "read from csvWriter failed")
	}

	pgpCryptor, err := s.cryptorStore.GetCryptor(commonvgpb.Vendor_FEDERAL_BANK, commonvgpb.CryptorType_PGP)
	if err != nil {
		return "", err
	}

	encryptedCsvData, encryptionError := pgpCryptor.EncryptAndSign(ctx, buf.Bytes(), "")
	if encryptionError != nil {
		return "", errors.Wrap(encryptionError, "encryption of csv file failed")
	}

	s3FilePath, writeToS3error := s.s3Client.WriteAndGetPreSignedUrl(ctx, filePath, encryptedCsvData, 1800)
	if writeToS3error != nil {
		return "", errors.Wrap(writeToS3error, "write to s3Client failed")
	}

	return s3FilePath, nil
}

func (s *Service) getCustomerIdMappingsFromCsvRecords(ctx context.Context, csvHeader []string, csvRecords *[][]string) (*[][]string, error) {
	var customerIdMappings [][]string
	customerIdMappings = append(customerIdMappings, csvHeader)
	for _, actorId := range (*csvRecords)[1:] { // Skip the header of csv
		customerId, err := s.rpcHelper.FetchCustomerIdByActorId(ctx, commonvgpb.Vendor_FEDERAL_BANK, actorId[0])
		if err != nil {
			return nil, errors.Wrap(err, fmt.Sprint("error while fetching bank customer by actor id"))
		}

		customerIdMappings = append(customerIdMappings, []string{customerId})
	}

	return &customerIdMappings, nil
}

func (s *Service) processS3RecordAndUploadFile(ctx context.Context, s3RecordPath string) (queuePb.MessageConsumptionStatus, error) {
	// File extension check, expecting only csv file
	fileExtension := strings.Split(s3RecordPath, ".")[1]
	if fileExtension != "csv" {
		return queuePb.MessageConsumptionStatus_PERMANENT_FAILURE, errors.New(fmt.Sprintf("incorrect extension for file, expected .csv received: %s", fileExtension))
	}

	// Read csv file from s3 bucket
	csvRecords, csvReadError := s.readCsvFromS3(ctx, s3RecordPath, false, commonvgpb.Vendor_VENDOR_UNSPECIFIED)
	if csvReadError != nil {
		return queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE, errors.Wrap(csvReadError, "read csv file from s3 bucket failed")
	}

	// Process received csv records, Create actor id to customer id mapping
	customerIdMappings, customerIdMappingsError := s.getCustomerIdMappingsFromCsvRecords(ctx, csvHeader, csvRecords)
	if customerIdMappingsError != nil {
		return queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE, errors.Wrap(customerIdMappingsError, "actorid to customerid mapping failed")
	}

	// Sample processedFilePath: "eligible_users/yyyy-mm-dd/uuid.csv"
	currentDate := time.Now().In(datetime.IST).Format(dateLayout)
	processedFilePath := "eligible_users/" + currentDate + "/" + uuid.New().String() + ".csv"

	// Write the processed csv file to s3 bucket
	s3FilePath, writeToS3error := s.encryptAndWriteCsvToS3(ctx, processedFilePath, customerIdMappings)
	if writeToS3error != nil {
		return queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE, errors.Wrap(writeToS3error, "write csv file to s3 bucket failed")
	}

	// Upload the processed csv file to vendor sftp
	uploadFileResponse, uploadFileError := s.creditCardVgClient.UploadFile(ctx, &creditCardVgPb.UploadFileRequest{
		// Sftp remote path must end with file extension
		RemotePath: s.conf.Vendor.SftpUploadRemotePath,
		FileUrl:    s3FilePath,
	})
	if rpcError := epifigrpc.RPCError(uploadFileResponse, uploadFileError); rpcError != nil {
		return queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE, errors.Wrap(rpcError, "upload to vendor sftp failed")
	}

	return queuePb.MessageConsumptionStatus_SUCCESS, nil
}

func (s *Service) sendDpdNotification(ctx context.Context, actorId, entityId string) error {
	// check if user is in dpd segment
	segmentResp, segErr := s.segmentationClient.IsMember(ctx, &segment.IsMemberRequest{
		ActorId:    actorId,
		SegmentIds: []string{dpdSegmentId},
	})
	if grpcErr := epifigrpc.RPCError(segmentResp, segErr); grpcErr != nil {
		return errors.Wrap(grpcErr, "error in isMember")
	}

	// return if user is not part of dpd30 segment
	if !segmentResp.GetSegmentMembershipMap()[dpdSegmentId].GetIsActorMember() {
		return nil
	}

	// check for doOnce
	isDone, err := s.doOnceClient.IsDone(ctx, getDpdNotificationTaskString(actorId))
	if err != nil {
		return errors.Wrap(err, "error in isDone")
	}

	// return if notification already sent
	if isDone {
		return nil
	}

	// marking task as done first - we shouldn't send notification if this is not success as notification will be sent again in that case
	doneErr := s.doOnceClient.Do(ctx, getDpdNotificationTaskString(actorId))
	if doneErr != nil {
		return errors.Wrap(doneErr, "erorr in do call")
	}

	s.eventBroker.AddToBatch(ctx, ccEvents.NewCardNotifyDPDEvent(actorId, entityId, 30))
	return nil
}

func getDpdNotificationTaskString(actorId string) string {
	return "DPD_30_NOTIFICATION:" + actorId
}

func (s *Service) ProcessNonFinancialNotificationEvent(ctx context.Context, req *creditcardConsumerPb.ProcessNonFinancialNotificationEventRequest) (*creditcardConsumerPb.ProcessNonFinancialNotificationEventResponse, error) {
	crAcc, err := s.ccAccountingClient.GetAccount(ctx, &ccAccountingPb.GetAccountRequest{
		GetBy: &ccAccountingPb.GetAccountRequest_ReferenceId{
			ReferenceId: req.GetEntityId(),
		},
	})
	if grpcErr := epifigrpc.RPCError(crAcc, err); grpcErr != nil {
		logger.Error(ctx, "error in Get Credit account", zap.Error(grpcErr))
		return &creditcardConsumerPb.ProcessNonFinancialNotificationEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
			},
		}, nil
	}
	ctx = epificontext.CtxWithActorId(ctx, crAcc.GetAccount().GetActorId())

	switch req.GetNotificationType() {
	case ffEnums.NonFinancialNotificationType_NON_FINANCIAL_NOTIFICATION_TYPE_CARD_STATUS_UPDATE:
		s.triggerCardStatusUpdate(ctx, crAcc.GetAccount())
	case ffEnums.NonFinancialNotificationType_NON_FINANCIAL_NOTIFICATION_TYPE_DPD_NOTIFY:
		if notifErr := s.sendDpdNotification(ctx, crAcc.GetAccount().GetActorId(), req.GetEntityId()); notifErr != nil {
			logger.Error(ctx, "error in sendDpdNotification", zap.Error(notifErr))
			return &creditcardConsumerPb.ProcessNonFinancialNotificationEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			}, nil
		}
	case ffEnums.NonFinancialNotificationType_NON_FINANCIAL_NOTIFICATION_TYPE_CLOSURE_DUE_TO_TXN_INACTIVITY:
		updateErr := s.closeCCWithClosureReason(ctx, crAcc.GetAccount(), ffEnums.CardClosureReason_CARD_CLOSURE_REASON_TRANSACTION_INACTIVITY)
		if updateErr != nil {
			logger.Error(ctx, "error in UpdateCardStatusWithClosureReason", zap.Error(updateErr))
			return &creditcardConsumerPb.ProcessNonFinancialNotificationEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			}, nil
		}

	case ffEnums.NonFinancialNotificationType_NON_FINANCIAL_NOTIFICATION_TYPE_CLOSURE_DUE_TO_TXN_INACTIVITY_REMINDER:
		s.eventBroker.AddToBatch(ctx, ccEvents.NewCcClosureDueToFinancialInactivityReminderEvent(crAcc.GetAccount().GetActorId(), crAcc.GetAccount().GetId()))
		sendCommsErr := s.sendClosureReminderComms(ctx, req.GetClosureReminderSequenceNumber(), crAcc.GetAccount().GetId())
		if sendCommsErr != nil {
			logger.Error(ctx, "error while sending card closure reminder comms to user",
				zap.String(logger.ACTOR_ID_V2, crAcc.GetAccount().GetActorId()),
				zap.Int32(logger.SEQ_NUM, req.GetClosureReminderSequenceNumber()),
				zap.String(logger.ACCOUNT_ID, crAcc.GetAccount().GetId()),
				zap.Error(sendCommsErr))
			return &creditcardConsumerPb.ProcessNonFinancialNotificationEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			}, nil
		}

	default:
		logger.Error(ctx, fmt.Sprintf("unexpected notification type received: %v", req.GetNotificationType().String()))
		return &creditcardConsumerPb.ProcessNonFinancialNotificationEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
			},
		}, nil
	}

	return &creditcardConsumerPb.ProcessNonFinancialNotificationEventResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status: queuePb.MessageConsumptionStatus_SUCCESS,
		},
	}, nil
}

func (s *Service) closeCCWithClosureReason(ctx context.Context, creditAccount *ccAccountingPb.CreditAccount, reason ffEnums.CardClosureReason) error {
	var (
		newState ffEnums.CardState
	)
	cards, err := s.creditCardDao.GetByAccountId(ctx, creditAccount.GetId(), nil)
	if err != nil {
		return err
	}
	latestCard := cards[0]
	currentState := latestCard.GetCardState()
	latestCard.CardState = ffEnums.CardState_CARD_STATE_CLOSED
	latestCard.BasicInfo.CardClosureReason = reason

	err = s.creditCardDao.Update(ctx, latestCard, []ffEnums.CreditCardFieldMask{ffEnums.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_STATE, ffEnums.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_CARD_INFO})
	if err != nil {
		return err
	}

	s.eventBroker.AddToBatch(ctx, ccEvents.NewCardStatusUpdateEvent(creditAccount.GetActorId(), latestCard.GetId(),
		creditAccount.GetId(), currentState.String(), newState.String()))

	return nil
}

func (s *Service) sendClosureReminderComms(ctx context.Context, notificationSequenceNumber int32, ccAccountId string) error {
	cards, err := s.creditCardDao.GetByAccountId(ctx, ccAccountId, nil)
	if err != nil {
		return err
	}
	return s.commsProcessor.ProcessAction(ctx, &ffCommsPb.ActionData{
		Data: &ffCommsPb.ActionData_CardClosureReminderActionData{
			CardClosureReminderActionData: &ffCommsPb.CardClosureReminderActionData{
				CreditCard:            cards[0],
				DaysLeftBeforeClosure: 31 - notificationSequenceNumber,
			},
		},
	})
}

func (s *Service) triggerCardStatusUpdate(ctx context.Context, creditAccount *ccAccountingPb.CreditAccount) {
	var (
		newState ffEnums.CardState
	)
	cards, err := s.creditCardDao.GetByAccountId(ctx, creditAccount.GetId(), nil)
	if err != nil {
		logger.Error(ctx, "error in fetching cards", zap.Error(err))
		return
	}
	latestCard := cards[0]
	currentState := latestCard.GetCardState()
	getCardListRes, err := s.rpcHelper.GetCardList(ctx, &creditCardVgPb.GetCardListRequest{
		Header:     &commonvgpb.RequestHeader{Vendor: latestCard.GetVendor()},
		CustomerId: creditAccount.GetReferenceId(),
	})
	if err != nil {
		logger.Error(ctx, "error in fetching card details from vendor", zap.Error(err))
		return
	}
	for _, cardDetails := range getCardListRes.GetCardDetails() {
		if cardDetails.GetKitNumber() == latestCard.GetVendorIdentifier() {
			newStatus := cardDetails.GetCardStatus()
			newState, err = ffPkg.GetCardStateFromStatus(newStatus)
			if err != nil {
				logger.Error(ctx, "error in fetching card status", zap.String(logger.STATUS, newStatus.String()))
				return
			}
		}
	}
	latestCard.CardState = newState
	err = s.creditCardDao.Update(ctx, latestCard, []ffEnums.CreditCardFieldMask{ffEnums.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_STATE})
	if err != nil {
		logger.Error(ctx, "error in updating card state", zap.Error(err))
		return
	}
	s.eventBroker.AddToBatch(ctx, ccEvents.NewCardStatusUpdateEvent(creditAccount.GetActorId(), latestCard.GetId(),
		creditAccount.GetId(), currentState.String(), newState.String()))
	return
}
