// protolint:disable MAX_LINE_LENGTH

// A service to send messages to a user through a specified medium and with a provided Quality of Service
syntax = "proto3";

package comms;

import "api/comms/enums.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/deposit.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/comms";
option java_package = "com.github.epifi.gamma.api.comms";

// All sms templates will be defined here which the clients can use to specify what they want to use
// Each template will have corresponding options to set the values
enum SmsType {
  SMS_TYPE_UNSPECIFIED = 0;

  // sms sent on first login to app that is the otp verification step
  ONBOARDING_OTP = 1;

  // sms sent for waitlist otp
  WAITLIST_OTP = 2;

  // message sent when a debit card is blocked
  DEBIT_CARD_BLOCK = 3;

  // For Cards Issued with Soft Pin Mandate. Triggered on next day of card issue date
  DEBIT_CARD_NEW_CARD_ISSUANCE_SOFT_PIN = 4;

  // 2nd SMS  send after 4 days of first message, with details regarding activation of card by Soft pin.
  // 4 days after card issue date
  DEBIT_CARD_NEW_CARD_ISSUANCE_SOFT_PIN_RE = 5;

  // Message sent saying that card is ready and active to be used
  DEBIT_CARD_ON = 6;

  // Message sent when the debit card is turned off
  DEBIT_CARD_OFF = 7;

  // Message sent when request to turn on/off card failed
  DEBIT_CARD_ON_OFF_FAILURE = 8;

  // message sent when internation limit is turned on for card
  DEBIT_CARD_INTERNATIONAL_ON = 9;

  // message sent when internation limit is turned off for card
  DEBIT_CARD_INTERNATIONAL_OFF = 10;

  // Message sent when request to turn on/off card failed for international usage
  DEBIT_CARD_INTERNATIONAL_ON_OFF_FAILURE = 11;

  // message sent when ecom is turned on for card
  DEBIT_CARD_ECOM_ON = 12;

  // message sent when ecom is turned off for card
  DEBIT_CARD_ECOM_OFF = 13;

  // message sent when the card is activated by the user
  DEBIT_CARD_ACTIVATE = 14;

  // message sent when request to block card failed
  DEBIT_CARD_BLOCK_FAILURE = 15;

  // message sent when debit card is dispatched
  DEBIT_CARD_DISPATCH = 16;

  // message sent on incorrect pin attempts on a card
  DEBIT_CARD_INCORRECT_PIN_RETRIES = 17;

  // message sent on freeze request for a card
  DEBIT_CARD_FREEZE = 18;

  // message sent on unfreeze request for a card
  DEBIT_CARD_UNFREEZE = 19;

  // message sent on changing atm pin of card
  DEBIT_CARD_CHANGE_ATM_PIN = 20;

  // SMS as described by NPCI : Alert when someone activates their device, example
  UPI_REGISTRATION = 21;

  // sms sent on withdrawing cash from atm
  CASH_WITHDRAWAL_ATM = 22;

  // message sent when debit from account using neft
  NEFT_DEBIT = 23;

  // message to sent on receiving neft credit
  NEFT_CREDIT = 24;

  // message sent on receiving credit from other bank
  NEFT_CREDIT_OTHER_BANK = 25;

  // NEFT Credit Confirmation to originator of NEFT
  NEFT_CREDIT_CONFIRMATION = 26;

  // message sent on debit via POS
  POS_DEBIT = 27;

  // message sent on reversal of credit via pos
  POS_REVERSAL_CREDIT = 28;

  // message sent on reversal of credit via failed atm txn
  UNSUCCESSFUL_ATM_REVERSAL_CREDIT = 29;

  // message sent as confirmation to user that rtgs has been credited
  RTGS_CREDIT_CONFIRMATION = 30;

  // message sent to user on rtgs debit
  RTGS_DEBIT = 31;

  CREDIT_CASH_DEPOSIT_MACHINE = 32;

  UPI_CREDIT = 33;

  UPI_DEBIT = 34;

  COLLECT_REQUEST = 35;

  FAILED_TRANSACTION = 36;

  GENERIC_PI_CREDIT = 37;

  GENERIC_PI_DEBIT = 38;

  FD_OPEN = 39;

  SD_OPEN = 40;

  FD_SD_X_DAYS_BEFORE_MATURITY = 41;

  ADD_FUNDS_SD = 42;

  FD_SD_CLOSURE = 43;

  INTEREST_PAID_IN_SB = 44;

  MOBILE_NUMBER_ADD = 45;

  MOBILE_NUMBER_MODIFY = 46;

  CARD_CONTROL_ON = 47;

  CARD_CONTROL_OFF = 48;

  VKYC_APPROVED = 49;

  // Sms sent to users who have waitlist access
  // we have two versions of this sms
  WAITLIST_ACCESS = 50;

  // Sms sent to users to let them know that they are eligible for limit upgrade of account post VKYC
  VKYC_WITH_LIMIT = 51;

  // Sms sent to users with a finite code, to give them early access to the Fi app
  FINITE_CODE = 52;

  // Sms sent to cbo users to give them app access with finite code and app link
  CBO_FINITE_CODE = 53;

  // Sms sent to users six weeks before the account freezing date, to prompt them to complete VKYC
  VKYC_SIX_WEEKS_BEFORE = 54;

  // Sms sent to users four weeks before the account freezing date, to prompt them to complete VKYC
  VKYC_FOUR_WEEKS_BEFORE = 55;

  // Sms sent to users ten days before the account freezing date, to prompt them to complete VKYC
  VKYC_TEN_DAYS_BEFORE = 56;

  // Sms to be sent to user stuck at stage where PAN is not provided during onboarding.
  PAN_REMINDER = 57;

  // Sms to be sent to user stuck at Aadhar verification during onboarding.
  EKYC_REMINDER = 58;

  // Sms to be sent to user stuck due to Name mismatch issue during onboarding.
  NAME_MISMATCH_UPDATE = 59;

  // Sms to be sent to user stuck at Liveness during onboarding.
  LIVENESS_REMINDER = 60;
  // reminder sms sent to cbo users to give them app access with finite code and app link
  CBO_FINITE_CODE_REMINDER = 61;
  // Sms to be sent in case of user stuck due to failure in kyc validation.
  KYC_VALIDATION_FAILURE = 62;
  NON_CBO_REMINDER_SMS = 63;
  // Sms to be sent once debit card is moved to in transit state
  DEBIT_CARD_DELIVERY = 64;
  // Sms to be sent to payer upon payment to generic pi where, beneficiary couldn't be resolved
  GENERIC_PI_DEBIT_UNCLEAR_BENEFICARY_DETAILS = 65;
  // SMS to send community login/signup otp
  COMMUNITY_LOGIN_OTP = 66;
  // Sms to be sent once debit card is moved to out for delivery state
  CARD_OUT_FOR_DELIVERY = 67;
  // Sms to be sent to users if there is delay in card delivery
  CARD_DELIVERY_DELAY = 68;
  // Sms to be sent after user onboards successfully regarding expected card dispatch timelines
  CARD_DISPATCH_TIMELINE_INFO = 69;
  // Sms to be sent when KYC is complete
  ONBOARDING_KYC_COMPLETE = 70;
  // SMS for one or more add funds operations to smart deposit in FIT rules
  FIT_SMART_DEPOSIT_ADD_FUNDS = 71;
  // Following 6 templates are fallback for already existing cases when balance api is down
  CASH_WITHDRAWAL_ATM_FALLBACK = 72;

  POS_DEBIT_FALLBACK = 73;

  NEFT_DEBIT_FALLBACK = 74;

  NEFT_CREDIT_FALLBACK = 75;

  RTGS_DEBIT_FALLBACK = 76;

  INTEREST_PAID_IN_SB_FALLBACK = 77;
  // Sms to be sent for receiving Mandate request
  MANDATE_RECEIVED = 78;
  // Sms to be sent for approving Mandate request
  MANDATE_APPROVED = 79;
  // Sms to be sent for declining Mandate request
  MANDATE_DECLINED = 80;
  // Sms to be sent when mandate gets created
  MANDATE_CREATED = 81;
  // Sms to be sent for successful execution of Mandate request
  MANDATE_EXECUTION_SUCCESSFUL = 82;
  // Sms to be sent for failed execution of Mandate request
  MANDATE_EXECUTION_FAILED = 83;
  // Sms to be sent when a mandate gets revoked
  MANDATE_REVOKED = 84;
  // Sms to be sent when a mandate gets modified
  MANDATE_MODIFIED = 85;
  // Sms to be sent when manually liveness passed
  MANUAL_LIVENESS_PASSED = 86;
  // Sms to be sent for reversed transaction
  TRANSACTION_REVERSED = 87;
  // Sms to be sent when SI gets created
  SI_CREATED = 88;
  // Sms to be sent when SI creation fails
  SI_DECLINED = 89;
  // Sms to be sent for successful execution of SI request
  SI_EXECUTION_SUCCESSFUL = 90;
  // Sms to be sent for failed execution of SI request
  SI_EXECUTION_FAILED = 91;
  // Sms to be sent for authorisation of a mandate payment for payer
  MANDATE_AUTHORISED = 92;
  // Sms to be sent for acceptance of a mandate payment for payee
  MANDATE_ACCEPTANCE = 93;
  // Sms to be sent for pausing mandates
  MANDATE_PAUSED = 94;
  // Sms to be sent for unpausing mandates
  MANDATE_UNPAUSED = 95;
  // otp sms sent to authenticate user before placing mutual fund withdrawal order
  MUTUAL_FUND_WITHDRAWAL_OTP = 96;
  // first vkyc reminder during onboarding
  OB_VKYC_REMINDER_ONE = 97;
  // second vkyc reminder during onboarding
  OB_VKYC_REMINDER_TWO = 98;
  // Sms to be sent for TOD charges debited
  TOD_CHARGES_DEBIT = 99;
  // screener verification reminder during onboarding
  OB_SCREENER_VERIFICATION_REMINDER_ONE = 100;
  // screener verification reminder during onboarding
  OB_SCREENER_VERIFICATION_REMINDER_TWO = 101;
  // Sms to be sent to user for ECS return related charges
  ECS_RETURN_CHARGES = 102;
  // Sms to be sent to user for ATM decline fee
  ATM_DECLINE_FEES = 103;
  // Sms to be sent to user for duplicate card fee
  DUPLICATE_CARD_FEE = 104;
  // Sms to be sent to user for ATM penalty to be paid to them
  ATM_WITHDRAWAL_COMPLAINT_PENALTY = 105;
  // Sms to be sent to user for International ATM withdrawal txns charges
  INTERNATIONAL_ATM_CHARGES = 106;
  // Sms to be sent to user for Charges related to crossing ATM Withdrawal free limit
  OTHER_BANK_ATM_USAGE_CHARGES = 107;
  // Sms to be sent 45 days before expiry of account and user having more then zero balance
  VKYC_ACCOUNT_CLOSURE_MORE_THEN_ZERO_BALANCE_FORTY_FIVE_DAYS = 108;
  // Sms to be sent 30days before expiry of account and user having more then zero balance
  VKYC_ACCOUNT_CLOSURE_MORE_THEN_ZERO_BALANCE_THIRTY_DAYS = 109;
  // Sms to be sent 11days before expiry of account and user having more then zero balance
  VKYC_ACCOUNT_CLOSURE_MORE_THEN_ZERO_BALANCE_ELEVEN_DAYS = 110;
  // Sms to be sent 5days before expiry of account and user having more then zero balance
  VKYC_ACCOUNT_CLOSURE_MORE_THEN_ZERO_BALANCE_FIVE_DAYS = 111;
  // Sms to be sent 3days before expiry of account and user having more then zero balance
  VKYC_ACCOUNT_CLOSURE_MORE_THEN_ZERO_BALANCE_THREE_DAYS = 112;
  // Sms to be sent 1days before expiry of account and user having more then zero balance
  VKYC_ACCOUNT_CLOSURE_MORE_THEN_ZERO_BALANCE_ONE_DAY = 113;
  // Sms that is sent on day 0 when the account is blocked/disabled
  ONBOARDING_ACCOUNT_BLOCK_DAY_ZERO = 114;
  // Sms that is sent when account is unblocked
  ONBOARDING_ACCOUNT_UNBLOCK = 115;

  // OTP SMS for wealth account nominee opt-in/opt-out
  WEALTH_ACCOUNT_NOMINEE_DECLARATION_OTP = 116;

  // SMS to be sent before x days of deposit maturity date
  SD_X_DAYS_BEFORE_MATURITY = 117;

  // SMS to be initiate after 6hrs of user drop off from pan dob screen
  ONBOARDING_DOB_AND_PAN_DROP_OFF = 118;

  // SMS to send OTP required for verifying phone number in order to download credit report.
  CREDIT_REPORT_DOWNLOAD_OTP_VERIFICATION = 119;

  // SMS to send credit card cross border txn success message to the user.
  CREDIT_CARD_CROSS_BORDER_TRANSACTION_SUCCESS = 120;
  // SMS to send credit card atm txn failure message to the user.
  CREDIT_CARD_ATM_TRANSACTION_FAILURE = 121;
  // SMS to send credit card atm txn success message to the user.
  CREDIT_CARD_ATM_TRANSACTION_SUCCESS = 122;
  // SMS to send credit card successful reversal of a failed txn.
  CREDIT_CARD_FAILED_TRANSACTION_REVERSAL_SUCCESS = 123;
  // SMS to send credit card txn declined message.
  CREDIT_CARD_TRANSACTION_DECLINED = 124;
  // SMS to send credit card txn success message.
  CREDIT_CARD_TRANSACTION_SUCCESS = 125;
  // SMS to send credit card txn international txn disabled message.
  CREDIT_CARD_INTERNATIONAL_TRANSACTIONS_DISABLED = 126;
  // SMS to send credit card international txn enabled message.
  CREDIT_CARD_INTERNATIONAL_TRANSACTIONS_ENABLED = 127;
  // SMS to send credit card pos txn disabled message.
  CREDIT_CARD_POS_TRANSACTIONS_DISABLED = 128;
  // SMS to send credit card pos txn enabled message.
  CREDIT_CARD_POS_TRANSACTIONS_ENABLED = 129;
  // SMS to send credit card contactless txn disabled message.
  CREDIT_CARD_CONTACTLESS_TRANSACTIONS_DISABLED = 130;
  // SMS to send credit card contactless txn enabled message.
  CREDIT_CARD_CONTACTLESS_TRANSACTIONS_ENABLED = 131;
  // SMS to send credit card online txn disabled message.
  CREDIT_CARD_ONLINE_TRANSACTIONS_DISABLED = 132;
  // SMS to send acknowledgement of online transactions enabled for the credit card to the user.
  CREDIT_CARD_ONLINE_TRANSACTIONS_ENABLED = 133;
  // SMS to send OTP for mutual-fund one time buy orders.
  MUTUAL_FUND_ONE_TIME_BUY_OTP = 134;
  // SMS to send OTP for mutual-fund sip registration.
  MUTUAL_FUND_REGISTER_SIP_OTP = 135;
  // SMS to send in case spends on category exceed above limit
  CATEGORY_SPENDS_EXCEEDED_REMINDER_SMS = 136;
  // SMS to send in case total spends exceed above limit
  AMOUNT_SPENDS_EXCEEDED_REMINDER_SMS = 137;
  // SMS to send a reminder for due date of credit card bill payment
  CREDIT_CARD_BILL_PAYMENT_DUE_DATE_REMINDER_SMS = 138;
  // SMS to send on upi pin set / reset
  UPI_PIN_SET_RESET = 139;
  // SMS to send credit card pin tries exceeded for transactions message.
  CREDIT_CARD_PIN_TRIES_EXCEEDED_FOR_TRANSACTIONS = 140;
  // SMS to send credit card payment not done reminder with interest charge message.
  CREDIT_CARD_PAYMENT_NOT_DONE_REMINDER_WITH_INTEREST_CHARGE = 141;
  // SMS to send credit card payment not done reminder message.
  CREDIT_CARD_PAYMENT_NOT_DONE_REMINDER = 142;
  // SMS to send credit card bill successful repayment message.
  CREDIT_CARD_BILL_SUCCESSFUL_REPAYMENT = 143;
  // SMS to send credit card bill repayment due message.
  CREDIT_CARD_BILL_REPAYMENT_DUE = 144;
  // SMS to send credit card statement generation message.
  CREDIT_CARD_STATEMENT_GENERATION = 145;
  // SMS to send credit card communicate transaction charges message.
  CREDIT_CARD_COMMUNICATE_TRANSACTION_CHARGES = 146;
  // SMS to send credit card replacement message.
  CREDIT_CARD_REPLACEMENT = 147;
  // SMS to send credit card limit change failure message.
  CREDIT_CARD_LIMIT_CHANGE_FAILURE = 148;
  // SMS to send credit card card usage change failure message.
  CREDIT_CARD_CARD_USAGE_CHANGE_FAILURE = 149;
  // SMS to send credit card contactless purchase limit changed message.
  CREDIT_CARD_CONTACTLESS_PURCHASE_LIMIT_CHANGED = 150;
  // SMS to send credit card online purchase limit changed message.
  CREDIT_CARD_ONLINE_PURCHASE_LIMIT_CHANGED = 151;
  // SMS to send credit card pos purchase limit changed message.
  CREDIT_CARD_POS_PURCHASE_LIMIT_CHANGED = 152;
  // SMS to send credit card unfreezing failure message.
  CREDIT_CARD_UNFREEZING_FAILURE = 153;
  // SMS to send credit card freezing failure message.
  CREDIT_CARD_FREEZING_FAILURE = 154;
  // SMS to send credit card unfreezing success message.
  CREDIT_CARD_UNFREEZING_SUCCESS = 155;
  // SMS to send credit card freezing success message.
  CREDIT_CARD_FREEZING_SUCCESS = 156;
  // SMS to send credit card pin change success message.
  CREDIT_CARD_PIN_CHANGE_SUCCESS = 157;
  // SMS to send credit card activation failure message.
  CREDIT_CARD_ACTIVATION_FAILURE = 158;
  // SMS to send credit card physical card activation success message.
  CREDIT_CARD_PHYSICAL_CARD_ACTIVATION_SUCCESS = 159;
  // SMS to send credit card digital card activation success message.
  CREDIT_CARD_DIGITAL_CARD_ACTIVATION_SUCCESS = 160;
  // SMS to send credit card activation information message.
  CREDIT_CARD_ACTIVATION_INFORMATION = 161;
  // SMS to send credit card shipment delay message.
  CREDIT_CARD_SHIPMENT_DELAY = 162;
  // SMS to send credit card disptach delay message.
  CREDIT_CARD_DISPTACH_DELAY = 163;
  // SMS to send credit card disptached with tracking number message.
  CREDIT_CARD_DISPTACHED_WITH_TRACKING_NUMBER = 164;
  // SMS to send credit card issued with credit limit message.
  CREDIT_CARD_ISSUED_WITH_CREDIT_LIMIT = 165;
  // SMS to send credit card issued message.
  CREDIT_CARD_ISSUED = 166;
  // SMS to send credit card incomplete application process message.
  CREDIT_CARD_INCOMPLETE_APPLICATION_PROCESS = 167;
  // SMS to send credit card complete video kyc for credit card message.
  CREDIT_CARD_COMPLETE_VIDEO_KYC_FOR_CREDIT_CARD = 168;
  // SMS to send credit card otp for changing card pin message.
  CREDIT_CARD_OTP_FOR_CHANGING_CARD_PIN = 169;
  // SMS to send credit card reward points credited message.
  CREDIT_CARD_REWARD_POINTS_CREDITED = 170;
  // SMS to send cc limit reaching a threshold comms
  CREDIT_CARD_LIMIT_REACHING_THRESHOLD = 171;
  // SMS to send credit card joining fees
  CREDIT_CARD_JOINING_FEES = 172;
  // SMS to send credit card unpaid dues fees
  CREDIT_CARD_UNPAID_DUE_FEES = 173;
  // SMS to send credit card generic credit comms
  CREDIT_CARD_GENERIC_CREDIT = 174;
  // SMS to send credit card txn declined with reason message. This is sent if the reason is
  // available, else we send CREDIT_CARD_TRANSACTION_DECLINED sms
  CREDIT_CARD_TRANSACTION_DECLINED_WITH_REASON = 175;
  // SMS to be sent when user signs a loan agreement during loan application
  PL_LOAN_AGREEMENT_OTP = 176;
  // SMS to be sent when a credit card is not activated for x days.
  CREDIT_CARD_NOT_ACTIVATED_SMS = 177;
  // SMS to be sent when a credit card is closed.
  CREDIT_CARD_CLOSURE_CONFIRMATION_SMS = 178;
  // SMS for credit card successful opening of FD
  SECURED_CREDIT_CARD_SUCCESSFUL_FD_CREATION = 179;
  // SMS for credit card FD lien marking
  SECURED_CREDIT_CARD_FD_LIEN_MARKING_INTIMATION = 180;
  // SMS for secured cc fd closing confirmation
  SECURED_CREDIT_CARD_FD_CLOSURE_CONFIRMATION = 181;
  // SMS for secured cc fd closure warning
  SECURED_CREDIT_CARD_FD_CLOSURE_WARNING = 182;
  // SMS for OTP regarding the credit card web eligibility check
  CREDIT_CARD_WEB_ELIGIBILITY_CHECK_LOGIN_OTP = 183;
  // SMS for user getting approved in the in house profile validation
  CREDIT_CARD_WEB_ELIGIBILITY_APPROVED_SMS = 184;
  // SMS for user getting rejected in the in house profile validation
  CREDIT_CARD_WEB_ELIGIBILITY_REJECTED_SMS = 185;
  // SMS for user account freeze initiated by risk
  RISK_ACCOUNT_FREEZE_SMS = 186;
  // SMS to be sent in cse we receive a forex markup refund txn
  DEBIT_CARD_FOREX_MARKUP_REFUND_RECEIVED = 187;

  // SMS to be sent when debit card txn was not processed
  DEBIT_CARD_UNABLE_TO_PROCESS_TRANSACTION = 188;
  // SMS to send wrong pin alert message
  DEBIT_CARD_INCORRECT_PIN = 189;
  // SMS to be sent when txn is not authorized
  DEBIT_CARD_UNABLE_TO_AUTHORIZE_TRANSACTION = 190;
  // SMS to send debit card expiry message
  DEBIT_CARD_CARD_EXPIRED = 191;
  // SMS to send ECOM txn disabled message
  DEBIT_CARD_ECOM_TRANSACTIONS_NOT_ENABLED = 192;
  // SMS to send daily amount txn limit reached
  DEBIT_CARD_DAILY_TRANSACTIONS_AMT_LIMIT_REACHED = 193;
  // SMS to send debit card POS not supported message
  DEBIT_CARD_POS_NOT_SUPPORTED = 194;
  // SMS to send pin retries exhausted message
  DEBIT_CARD_PIN_TRIES_EXCEEDED_FOR_TRANSACTIONS = 195;
  // SMS to send debit card duplicate txn message
  DEBIT_CARD_DUPLICATE_TRANSACTION = 196;
  // SMS to send debit card txn declined message
  DEBIT_CARD_TRANSACTION_DECLINED = 197;
  // SMS to send txn not supported in debit card message
  DEBIT_CARD_TRANSACTION_TYPE_NOT_SUPPORTED = 198;
  // SMS to send txn invalid message
  DEBIT_CARD_INVALID_TRANSACTION = 199;
  // SMS to send international txn disabled message
  DEBIT_CARD_INTERNATIONAL_TRANSACTIONS_NOT_ENABLED = 200;
  // SMS to send contactless txn disabled message
  DEBIT_CARD_CONTACTLESS_CARD_USAGE_NOT_ENABLED = 201;
  // SMS to send insufficient funds for txn message
  DEBIT_CARD_INSUFFICIENT_FUNDS_FOR_TRANSACTION = 202;
  // SMS to send daily withdrawal limit on debit card reached message
  DEBIT_CARD_DAILY_WITHDRAWAL_LIMIT_REACHED = 203;
  // SMS to send low funds to process txn message
  DEBIT_CARD_LOW_FUNDS_FOR_TRANSACTION = 204;
  // SMS to send invalid expiry date message
  DEBIT_CARD_INVALID_EXPIRY_DATE = 205;
  // SMS to send NFC not enabled message
  DEBIT_CARD_NFC_NOT_ENABLED = 206;
  // SMS to send PRM declined message
  DEBIT_CARD_PRM_DECLINED = 207;
  // SMS to send wrong CVV entered message
  DEBIT_CARD_CVV_ERROR = 208;
  // SMS to send daily contactless payment limit exceed massage
  DEBIT_CARD_DAILY_CONTACTLESS_PAYMENTS_LIMIT_EXCEEDED = 209;
  // SMS to send card off (transactions not enabled in card controls) massage
  DEBIT_CARD_CARD_OFF_FOR_TRANSACTIONS = 210;
  // SMS to send on txn failure due to technical error
  DEBIT_CARD_HOST_DOWN = 211;
  // SMS to send domestic txns off massage
  DEBIT_CARD_DOMESTIC_TRANSACTIONS_NOT_ENABLED = 212;
  // SMS to send after playing call recording for risk use case
  CALL_RECORDING_POST_RISK_USE_CASE = 213;

  // SMS to send after lien marking is successful
  LAMF_LIEN_MARK_SUCCESS = 214;
  // SMS to send after loan is successfully disbursed
  LAMF_LOAN_DISBURSED = 215;
  // SMS to send after all emi are paid and loan closure is initiated
  LAMF_ALL_EMI_PAID_LOAN_CLOSURE_INITIATED = 216;
  // SMS to send after EMI is auto recovered by Bajaj
  LAMF_REPAYMENT_AUTO_RECOVERED_BY_BAJAJ = 217;
  // SMS to send after EMI is auto recovered by Fi
  LAMF_REPAYMENT_AUTO_RECOVERED_BY_FI = 218;
  // SMS to send for reminding about upcoming EMI
  LAMF_REPAYMENT_UPCOMING_EMI = 219;
  // SMS to send for alerting about low balance in case of upcoming EMI
  LAMF_REPAYMENT_UPCOMING_EMI_LOW_BALANCE = 220;
  // SMS to send in case of emandate getting bounced
  LAMF_REPAYMENT_EMANDATE_BOUNCE = 221;
  // SMS to send after prepayment is successful
  LAMF_REPAYMENT_PREPAYMENT_SUCCESS = 222;
  // SMS to send after 2 hours of user drop off from pan dob stage
  CC_FI_LITE_PAN_DOB_DROP_OFF_2_HRS = 223;
  // SMS to send after 120 hours of user drop off from pan dob stage
  CC_FI_LITE_PAN_DOB_DROP_OFF_120_HRS = 224;
  // SMS to be send after 2 hours of user drop off from EKYC stage
  CC_FI_LITE_EKYC_DROP_OFF = 225;
  // SMS to be send after 24 hours of user drop off from VKYC stage
  CC_VKYC_DROP_OFF = 226;
  // SMS to send when user has successfully created an EMI
  CREDIT_CARD_EMI_CREATED = 227;
  // SMS to sent when user has successfully repaid all the EMIs and closed the cc loan account
  CREDIT_CARD_EMI_CLOSED = 228;
  // SMS to be sent when a user precloses an EMI; a pre-closure fees apply
  CREDIT_CARD_EMI_PRE_CLOSED = 229;
  // SMS to be sent when a user cancels an EMI; NO closure fees apply
  CREDIT_CARD_EMI_CANCELLED = 230;
  // SMS to be sent after user is eligible for cc through web flow
  CREDIT_CARD_ELIGIBLE_WEB_FLOW_START_APPLICATION_SMS = 231;
  // SMS to be sent after user is eligible for cc through web flow but not started/completed application for long time
  CREDIT_CARD_ELIGIBLE_WEB_FLOW_COMPLETE_APPLICATION_SMS = 232;
  // SMS to be sent for user once order is out for delivery & we receive order tracking link.
  FI_STORE_ORDER_DELIVERY_STATUS_UPDATE_SMS = 233;
  // OTP sms for auth in risk outcall form.
  RISK_OUTCALL_FORM_LOGIN_OTP = 234;
  // OTP for user to view the cibil report once downloaded
  CIBIL_REPORT_SMS = 235;
  // SMS to be sent after the B2B user is whitelisted
  SALARY_PROGRAM_B2B_USER_WHITELISTED_SMS = 236;
  // OTP sent for verification of alternate number
  LOAN_CONTACTABILITY_ALTERNATE_PHONE_NUMBER_SMS = 237;
  // Login OTP sent for verification of non-resident mobile number
  NON_RESIDENT_ONBOARDING_OTP = 238;
  // Informative SMS to be sent on notify the daily international atm withdrawal limits in a particular country
  DEBIT_CARD_INTERNATIONAL_ATM_WITHDRAWAL_LIMIT_SMS = 239;
  // SMS to be sent for esigning the document from TSP.
  STOCKGUARDIAN_LOAN_APPLICATION_ESIGN_SMS = 240;
  // SMS to be sent when user call is dropped by playing some pre-recorded message
  CX_USER_CALL_DROPPED_SMS = 241;
  // SMS to be sent on dc txn decline due to CAF not found error
  DEBIT_CARD_CAF_NOT_FOUND = 242;
  // SMS to be sent on atm withdrawal failure due to atm flag off
  DEBIT_CARD_ATM_USAGE_NOT_ENABLED = 243;
  // SMS to be sent on dc txn failures due to host unavailable error
  DEBIT_CARD_HOST_NOT_AVAILABLE = 244;
  // SMS to be sent when a dc txn is attempted using a lost or stolen card
  DEBIT_CARD_LOST_OR_STOLEN_CARD = 245;
  // SMS to be sent to when dc txn failed due daily txn limit(set by bank) exceeding
  DEBIT_CARD_AMOUNT_OVER_DAILY_MAX = 246;
  // SMS to sent on dc txn failure due to account of card user being ineligible for the txn
  DEBIT_CARD_INELIGIBLE_ACCOUNT = 247;
  // SMS to be sent due to txn amount entered being higher than threshold(enter lesser amount)
  DEBIT_CARD_ELA = 248;
  // SMS to be sent on dc txn failure due pos no being enabled
  DEBIT_CARD_POS_USAGE_NOT_ENABLED = 249;
  // SMS to be sent on unauthorized dc txn attempt
  DEBIT_CARD_UNAUTHORIZED_USAGE = 250;
  // SMS to be sent when dc txn was decline on switch interface hub
  DEBIT_CARD_SI_HUB_DECLINE = 251;
  // SMS to be sent on dc pos(swipe) txn decline due to limit exceeding
  DEBIT_CARD_AMOUNT_OVER_WITHDRAWAL_LIMIT_POS = 252;
  // SMS to be send on dc txn failure due to caf status decline(trying to use old card for txn)
  DEBIT_CARD_CAF_STATUS_DECLINE = 253;
  // SMS to be sent on dc txn decline due to any issue with magnetic chip
  DEBIT_CARD_FALLBACK_DECLINE = 254;
  DEBIT_CARD_MESSAGE_EDIT_ERROR = 255;
  DEBIT_CARD_DEST_NOT_AVAILABLE = 256;
  DEBIT_CARD_ATC_CHECK_FAILURE = 257;
  DEBIT_CARD_TOKEN_IN_APP_FLAG_OFF = 258;
  DEBIT_CARD_NO_IDF_ERROR = 259;
  DEBIT_CARD_SYSTEM_ERROR = 260;
  DEBIT_CARD_ARQC_FAILURE = 261;
  DEBIT_CARD_APPROVED_NO_BALANCES = 262;
  DEBIT_CARD_INVALID_TXN_DATE = 263;
  DEBIT_CARD_TO_BE_CAPTURED_IN_CAF = 264;
  DEBIT_CARD_BAD_CARD_STATUS = 265;
  DEBIT_CARD_RESERVED_B24_CODE = 266;
  DEBIT_CARD_HSM_PARAM_ERROR = 267;
  DEBIT_CARD_MAX_CREDIT_PER_REFUND = 268;
  DEBIT_CARD_USAGE_LIMIT_EXCEEDED = 269;
  DEBIT_CARD_TOKEN_NFC_FLAG_OFF = 270;
  // SMS to be sent for risk credit freeze operations
  RISKOPS_CF_SMS = 271;
  DEBIT_CARD_FOREX_MARKUP_TXN = 272;
  // SMS related to Risk LEA complaints
  RISK_UNIFIED_LEA_DEBIT_FREEZE = 273;
  RISK_UNIFIED_LEA_CREDIT_FREEZE = 274;
  RISK_UNIFIED_LEA_TOTAL_FREEZE = 275;
  RISK_UNIFIED_LEA_LIEN = 276;
  // SMS to be sent when credit freeze is applied by risk
  RISK_CREDIT_FREEZE_APPLIED_SMS = 277;
  // SMS to be sent to those user whose limit revised
  CREDIT_CARD_REVISED_LIMIT_SMS = 278;
  // SMS to be sent for cases when the bank has received the cheque and the processing has started
  CHEQUE_CREDIT_PROCESSING_FINT = 279;
  // SMS to be sent for cases when the cheque credit processing has failed. For such scenario we receive a debit notification.
  CHEQUE_CREDIT_PROCESSING_FAILED_FINT = 280;
  // SMS to be sent to users whose ticket is resolved and CSAT is to be collected
  CX_TICKET_RESOLUTION_CSAT_SMS = 281;
  // SMS to be sent to credit card users whose KYC has expired and all cc transaction are blocked for that reason
  CREDIT_CARD_BLOCK_KYC_EXPIRY = 282;
  // SMS to be sent to credit card users who have completed their KYC and CC transactions are not to be unblocked
  CREDIT_CARD_UNBLOCK_KYC_COMPLETED = 283;
  // SMS to be sent to credit card users for conveying CX migration.
  CREDIT_CARD_CX_SUPPORT_DETAILS_UPDATE_SMS = 284;
  // SMS to be sent to credit card users to notify them on the cc closure due to financial inactivity
  CC_CLOSURE_INXDAYS_SMS = 285;
}

message SmsOption {
  // template options which will replace the variables in the template
  oneof option {
    OnboardingOtpSmsOption onboarding_otp_sms_option = 1;
    WaitlistOtpSmsOption waitlist_otp_sms_option = 2;
    DebitCardBlockSmsOption debit_card_block_sms_option = 3;
    DebitCardNewCardIssuanceSoftPin debit_card_new_issuance_soft_pin_sms_option = 4;
    DebitCardNewCardIssuanceSoftPinRe debit_card_new_issuance_soft_pin_re_sms_option = 5;
    DebitCardOnSmsOption debit_card_on_sms_option = 6;
    DebitCardOffSmsOption debit_card_off_sms_option = 7;
    DebitCardOnOffFailureSmsOption debit_card_on_off_failure_sms_option = 8;
    DebitCardInternationalOnSmsOption debit_card_international_on_sms_option = 9;
    DebitCardInternationalOffSmsOption debit_card_international_off_sms_option = 10;
    DebitCardInternationalOnOffFailureSmsOption debit_card_international_on_off_failure_sms_option = 11;
    DebitCardEcomOnSmsOption debit_card_ecom_on_sms_option = 12;
    DebitCardEcomOffSmsOption debit_card_ecom_off_sms_option = 13;
    DebitCardActivateSmsOption debit_card_activate_sms_option = 14;
    DebitCardBlockFailureSmsOption debit_card_block_failure_sms_option = 15;
    DebitCardDispatchSmsOption debit_card_dispatch_sms_option = 16;
    DebitCardInCorrectPinRetriesSmsOption debit_card_incorrect_pin_retries_sms_option = 17;
    DebitCardFreezeSmsOption debit_card_freeze_sms_option = 18;
    DebitCardUnFreezeSmsOption debit_card_unfreeze_sms_option = 19;
    DebitCardChangeAtmPinSmsOption debit_card_change_atm_pin_sms_option = 20;
    UpiRegistrationSmsOption upi_registration_sms_option = 21;
    CashWithdrawalAtmSmsOption cash_withdrawal_atm_sms_option = 22;
    NeftDebitSmsOption neft_debit_sms_option = 23;
    NeftCreditSmsOption neft_credit_sms_option = 24;
    NeftCreditOtherBankSmsOption neft_credit_other_bank_sms_option = 25;
    NeftCreditConfirmationSmsOption neft_credit_confirmation_sms_option = 26;
    PosDebitSmsOption pos_debit_sms_option = 27;
    PosReversalCreditSmsOption pos_reversal_credit_sms_option = 28;
    UnsuccessfulAtmReversalCreditSmsOption unsuccessful_atm_reversal_credit_sms_option = 29;
    RtgsCreditConfirmationSmsOption rtgs_credit_confirmation_sms_option = 30;
    RtgsDebitSmsOption rtgs_debit_sms_option = 31;
    CreditCashDepositMachineSmsOption credit_cash_deposit_machine_sms_option = 32;
    UpiCreditSmsOption upi_credit_sms_option = 33;
    UpiDebitSmsOption upi_debit_sms_option = 34;
    CollectRequestSmsOption collect_request_sms_option = 35;
    FailedTransactionSmsOption failed_transacton_sms_option = 36;
    GenericPiCreditSmsOption generic_pi_credit_sms_option = 37;
    GenericPiDebitSmsOption generic_pi_debit_sms_option = 38;
    FdOpenSmsOption fd_open_sms_option = 39;
    SdOpenSmsOption sd_open_sms_option = 40;
    FdSdXDaysBeforeMatuaritySmsOption fd_sd_x_days_before_matuarity_sms_option = 41;
    AddFundsSdSmsOption add_funds_sd_sms_option = 42;
    FdSdClosureSmsOption fd_sd_closure_sms_option = 43;
    InterestPaidInSBSmsOption interest_paid_in_sb_sms_option = 44;
    MobileNumberAddSmsOption mobile_number_add_sms_option = 45;
    MobileNumberModifySmsOption mobile_number_modify_sms_option = 46;
    CardControlOnSmsOption card_control_on_sms_option = 47;
    CardControlOffSmsOption card_control_off_sms_option = 48;
    VkycApprovedSmsOption vkyc_approved_sms_option = 49;
    WaitlistAccessSmsOption waitlist_access_sms_option = 50;
    VkycWithLimitSmsOption vkyc_with_limit_sms_option = 51;
    FiniteCodeSmsOption finite_code_sms_option = 52;
    CboFiniteCodeSmsOption cbo_finite_code_sms_option = 53;
    VkycSixWeeksBeforeSmsOption vkyc_six_weeks_before_sms_option = 54;
    VkycFourWeeksBeforeSmsOption vkyc_four_weeks_before_sms_option = 55;
    VkycTenDaysBeforeSmsOption vkyc_ten_days_before_sms_option = 56;
    PanReminderSmsOption pan_reminder_sms_option = 57;
    EKYCReminderSmsOption ekyc_reminder_sms_option = 58;
    NameMismatchUpdateSmsOption name_mismatch_update_sms_option = 59;
    LivenessReminderSmsOption liveness_reminder_sms_option = 60;
    CboFiniteCodeReminderSmsOption cbo_finite_code_reminder_sms_option = 61;
    KYCValidationFailureSmsOption kyc_validation_failure_sms_option = 62;
    NonCBOReminderSmsOption non_cbo_reminder_sms_option = 63;
    DebitCardDeliverySmsOption debit_card_delivery_sms_option = 64;
    GenericPiDebitUnclearBeneficiaryDetailsSmsOption generic_pi_debit_unclear_beneficary_details_sms_option = 65;
    CommunityLoginOtpSmsOption community_login_otp_sms_option = 66;
    CardOutForDeliverySmsOption card_out_for_delivery_sms_option = 67;
    CardDeliveryDelaySmsOption card_delivery_delay_sms_option = 68;
    CardDispatchTimelineInfoSmsOption card_dispatch_timeline_info_sms_option = 69;
    OnboardingKYCCompleteSmsOption onboarding_kyc_complete_sms_option = 70;
    FITSmartDepositAddFundsSMSOption fit_smart_deposit_add_funds_sms_option = 71;
    CashWithdrawalAtmFallbackSmsOption cash_withdrawal_atm_fallback_sms_option = 72;
    PosDebitFallbackSmsOption pos_debit_fallback_sms_option = 73;
    NeftDebitFallbackSmsOption neft_debit_fallback_sms_option = 74;
    NeftCreditFallbackSmsOption neft_credit_fallback_sms_option = 75;
    RtgsDebitFallbackSmsOption rtgs_debit_fallback_sms_option = 76;
    InterestPaidInSBFallbackSmsOption interest_paid_in_sb_fallback_sms_option = 77;
    MandateReceivedSmsOption mandate_received_sms_option = 78;
    MandateApprovedSmsOption mandate_approved_sms_option = 79;
    MandateDeclinedSmsOption mandate_declined_sms_option = 80;
    MandateCreatedSmsOption mandate_created_sms_option = 81;
    MandateExecutionSuccessfulSmsOption mandate_execution_successful_sms_option = 82;
    MandateExecutionFailedSmsOption mandate_execution_failed_sms_option = 83;
    MandateRevokedSmsOption mandate_revoked_sms_option = 84;
    MandateModifiedSmsOption mandate_modified_sms_option = 85;
    ManualLivenessPassedSmsOption manual_liveness_passed_sms_option = 86;
    TransactionReversedSmsOption transaction_reversed_sms_option = 87;
    SICreatedSmsOption si_created_sms_option = 88;
    SIDeclinedSmsOption si_declined_sms_option = 89;
    SIExecutionSuccessfulSmsOption si_execution_successful_sms_option = 90;
    SIExecutionFailedSmsOption si_execution_failed_sms_option = 91;
    MandateAuthorisedSmsOption mandate_authorised_sms_option = 92;
    MandateAcceptanceSmsOption mandate_acceptance_sms_option = 93;
    MandatePausedSmsOption mandate_paused_sms_option = 94;
    MandateUnpausedSmsOption mandate_unpaused_sms_option = 95;
    MutualFundWithdrawalOtpOption mutual_fund_withdrawal_otp_option = 96;
    OBVkycReminderOneSmsOption ob_vkyc_reminder_one_sms_option = 97;
    OBVkycReminderTwoSmsOption ob_vkyc_reminder_two_sms_option = 98;
    TodChargesDebitOption tod_charges_debit_option = 99;
    OBScreenerVerficationReminderOneOption ob_screener_verification_reminder_one_option = 100;
    OBScreenerVerficationReminderTwoOption ob_screener_verification_reminder_two_option = 101;
    EcsReturnChargesSmsOption ecs_return_charges_sms_option = 102;
    AtmDeclineFeesSmsOption atm_decline_fees_sms_option = 103;
    DuplicateCardFeeSmsOption duplicate_card_fee_sms_option = 104;
    AtmWithdrawalComplaintPenaltySmsOption atm_withdrawal_complaint_penalty_sms_option = 105;
    InternationAtmChargesSmsOption internation_atm_charges_sms_option = 106;
    OtherBankAtmUsageChargesSmsOption other_bank_atm_usage_charges_sms_option = 107;
    VkycAccountClosureMoreThenZeroBalanceFortyFiveDaysSmsOption vkyc_account_closure_more_then_zero_balance_forty_five_days_sms_option = 108;
    VkycAccountClosureMoreThenZeroBalanceThirtyDaysSmsOption vkyc_account_closure_more_then_zero_balance_thirty_days_sms_option = 109;
    VkycAccountClosureMoreThenZeroBalanceElevenDaysSmsOption vkyc_account_closure_more_then_zero_balance_eleven_days_sms_option = 110;
    VkycAccountClosureMoreThenZeroBalanceFiveDaysSmsOption vkyc_account_closure_more_then_zero_balance_five_days_sms_option = 111;
    VkycAccountClosureMoreThenZeroBalanceThreeDaysSmsOption vkyc_account_closure_more_then_zero_balance_three_days_sms_option = 112;
    VkycAccountClosureMoreThenZeroBalanceOneDaySmsOption vkyc_account_closure_more_then_zero_balance_one_day_sms_option = 113;
    OnboardingAccountBlockDayZeroSmsOption onboarding_account_block_day_zero_sms_option = 114;
    OnboardingAccountUnblockSmsOption onboarding_account_unblock_sms_option = 115;
    WealthAccountNomineeDeclarationOTPOption wealth_account_nominee_declaration_otp_option = 116;
    SdXDaysBeforeMaturitySmsOption sd_x_days_before_maturity_sms_option = 117;
    OnboardingDobAndPanDropOffSmsOption onboarding_dob_and_pan_drop_off_sms_option = 118;
    CreditReportDownloadOtpSmsOption credit_report_download_otp_sms_option = 119;
    CreditCardCrossBorderTransactionSuccessSmsOption credit_card_cross_border_transaction_success_sms_option = 120;
    CreditCardAtmTransactionFailureSmsOption credit_card_atm_transaction_failure_sms_option = 121;
    CreditCardAtmTransactionSuccessSmsOption credit_card_atm_transaction_success_sms_option = 122;
    CreditCardFailedTransactionReversalSuccessSmsOption credit_card_failed_transaction_reversal_success_sms_option = 123;
    CreditCardTransactionDeclinedSmsOption credit_card_transaction_declined_sms_option = 124;
    CreditCardTransactionSuccessSmsOption credit_card_transaction_success_sms_option = 125;
    CreditCardInternationalTransactionsDisabledSmsOption credit_card_international_transactions_disabled_sms_option = 126;
    CreditCardInternationalTransactionsEnabledSmsOption credit_card_international_transactions_enabled_sms_option = 127;
    CreditCardPosTransactionsDisabledSmsOption credit_card_pos_transactions_disabled_sms_option = 128;
    CreditCardPosTransactionsEnabledSmsOption credit_card_pos_transactions_enabled_sms_option = 129;
    CreditCardContactlessTransactionsDisabledSmsOption credit_card_contactless_transactions_disabled_sms_option = 130;
    CreditCardContactlessTransactionsEnabledSmsOption credit_card_contactless_transactions_enabled_sms_option = 131;
    CreditCardOnlineTransactionsDisabledSmsOption credit_card_online_transactions_disabled_sms_option = 132;
    CreditCardOnlineTransactionsEnabledSmsOption credit_card_online_transactions_enabled_sms_option = 133;
    MutualFundOneTimeBuyOtpOption mf_one_time_buy_option = 134;
    MutualFundRegisterSIPOtpOption mf_register_sip_option = 135;
    CategorySpendsExceededReminderSmsOption category_spends_exceeded_reminder_sms_option = 136;
    AmountSpendsExceededReminderSmsOption amount_spends_exceeded_reminder_sms_option = 137;
    CreditCardBillPaymentDueDateReminderSmsOption credit_card_bill_payment_due_date_reminder_sms_option = 138;
    UpiPinSetResetSmsOption upi_pin_set_reset_sms_option = 139;
    CreditCardPinTriesExceededForTransactionsSmsOption credit_card_pin_tries_exceeded_for_transactions_sms_option = 140;
    CreditCardPaymentNotDoneReminderWithInterestChargeSmsOption credit_card_payment_not_done_reminder_with_interest_charge_sms_option = 141;
    CreditCardPaymentNotDoneReminderSmsOption credit_card_payment_not_done_reminder_sms_option = 142;
    CreditCardBillSuccessfulRepaymentSmsOption credit_card_bill_successful_repayment_sms_option = 143;
    CreditCardBillRepaymentDueSmsOption credit_card_bill_repayment_due_sms_option = 144;
    CreditCardStatementGenerationSmsOption credit_card_statement_generation_sms_option = 145;
    CreditCardCommunicateTransactionChargesSmsOption credit_card_communicate_transaction_charges_sms_option = 146;
    CreditCardReplacementSmsOption credit_card_replacement_sms_option = 147;
    CreditCardLimitChangeFailureSmsOption credit_card_limit_change_failure_sms_option = 148;
    CreditCardCardUsageChangeFailureSmsOption credit_card_card_usage_change_failure_sms_option = 149;
    CreditCardContactlessPurchaseLimitChangedSmsOption credit_card_contactless_purchase_limit_changed_sms_option = 150;
    CreditCardOnlinePurchaseLimitChangedSmsOption credit_card_online_purchase_limit_changed_sms_option = 151;
    CreditCardPosPurchaseLimitChangedSmsOption credit_card_pos_purchase_limit_changed_sms_option = 152;
    CreditCardUnfreezingFailureSmsOption credit_card_unfreezing_failure_sms_option = 153;
    CreditCardFreezingFailureSmsOption credit_card_freezing_failure_sms_option = 154;
    CreditCardUnfreezingSuccessSmsOption credit_card_unfreezing_success_sms_option = 155;
    CreditCardFreezingSuccessSmsOption credit_card_freezing_success_sms_option = 156;
    CreditCardPinChangeSuccessSmsOption credit_card_pin_change_success_sms_option = 157;
    CreditCardActivationFailureSmsOption credit_card_activation_failure_sms_option = 158;
    CreditCardPhysicalCardActivationSuccessSmsOption credit_card_physical_card_activation_success_sms_option = 159;
    CreditCardDigitalCardActivationSuccessSmsOption credit_card_digital_card_activation_success_sms_option = 160;
    CreditCardActivationInformationSmsOption credit_card_activation_information_sms_option = 161;
    CreditCardShipmentDelaySmsOption credit_card_shipment_delay_sms_option = 162;
    CreditCardDisptachDelaySmsOption credit_card_disptach_delay_sms_option = 163;
    CreditCardDisptachedWithTrackingNumberSmsOption credit_card_disptached_with_tracking_number_sms_option = 164;
    CreditCardIssuedWithCreditLimitSmsOption credit_card_issued_with_credit_limit_sms_option = 165;
    CreditCardIssuedSmsOption credit_card_issued_sms_option = 166;
    CreditCardIncompleteApplicationProcessSmsOption credit_card_incomplete_application_process_sms_option = 167;
    CreditCardCompleteVideoKycForCreditCardSmsOption credit_card_complete_video_kyc_for_credit_card_sms_option = 168;
    CreditCardOtpForChangingCardPinSmsOption credit_card_otp_for_changing_card_pin_sms_option = 169;
    CreditCardRewardPointsCreditedSmsOption credit_card_reward_points_credited_sms_option = 170;
    CreditCardLimitReachingThresholdSmsOption credit_card_limit_reaching_threshold_sms_option = 171;
    CreditCardJoiningFeeSmsOption credit_card_joining_fee_sms_option = 172;
    CreditCardUnpaidDueFeesSmsOption credit_card_unpaid_due_fees_sms_option = 173;
    CreditCardGenericCreditSmsOption credit_card_generic_credit_sms_option = 174;
    CreditCardTransactionDeclinedWithReasonSmsOption credit_card_transaction_declined_with_reason_sms_option = 175;
    PlLoanAgreementOtpSmsOption pl_loan_agreement_otp_sms_option = 176;
    CreditCardNotActivatedSmsOption credit_card_not_activated_sms_option = 177;
    CreditCardClosureConfirmationSmsOption credit_card_closure_confirmation_sms_option = 178;
    SecuredCreditCardSuccessfulFdCreationSmsOption secured_credit_card_successful_fd_creation_sms_option = 179;
    SecuredCreditCardFdLienMarkingIntimationSmsOption secured_credit_card_fd_lien_marking_intimation_sms_option = 180;
    SecuredCreditCardFdClosureConfirmationSmsOption secured_credit_card_fd_closure_confirmation_sms_option = 181;
    SecuredCreditCardFdClosureWarningSmsOption secured_credit_card_fd_closure_warning_sms_option = 182;
    CreditCardWebEligibilityLoginOtpSmsOption credit_card_web_eligibility_login_otp_sms_option = 183;
    CreditCardWebEligibilityApprovedSmsOption credit_card_web_eligibility_approved_sms_option = 184;
    CreditCardWebEligibilityRejectedSmsOption credit_card_web_eligibility_rejected_sms_option = 185;
    RiskAccountFreezeSmsOption risk_account_freeze_sms_option = 186;
    DebitCardForexRefundReceivedSmsOption debit_card_forex_refund_received_sms_option = 187;
    DebitCardUnableToProcessTransactionSmsOption debit_card_unable_to_process_transaction_sms_option = 188;
    DebitCardIncorrectPinSmsOption debit_card_incorrect_pin_sms_option = 189;
    DebitCardUnableToAuthorizeTransactionSmsOption debit_card_unable_to_authorize_transaction_sms_option = 190;
    DebitCardExpiredSmsOption debit_card_expired_sms_option = 191;
    DebitCardEcomTransactionsNotEnabledSmsOption debit_card_ecom_transactions_not_enabled_sms_option = 192;
    DebitCardDailyTransactionsAmtLimitReachedSmsOption debit_card_daily_transactions_amt_limit_reached_sms_option = 193;
    DebitCardPosNotSupportedSmsOption debit_card_pos_not_supported_sms_option = 194;
    DebitCardPinTriesExceededForTransactionSmsOption debit_card_pin_tries_exceeded_for_transaction_sms_option = 195;
    DebitCardDuplicateTransactionSmsOption debit_card_duplicate_transaction_sms_option = 196;
    DebitCardTransactionDeclinedSmsOption debit_card_transaction_declined_sms_option = 197;
    DebitCardTransactionTypeNotSupportedSmsOption debit_card_transaction_type_not_supported_sms_option = 198;
    DebitCardInvalidTransactionSmsOption debit_card_invalid_transaction_sms_option = 199;
    DebitCardInternationalTransactionsNotEnabledSmsOption debit_card_international_transactions_not_enabled_sms_option = 200;
    DebitCardContactlessCardUsageNotEnabledSmsOption debit_card_contactless_card_usage_not_enabled_sms_option = 201;
    DebitCardInsufficientFundsForTransactionSmsOption debit_card_insufficient_funds_for_transaction_sms_option = 202;
    DebitCardDailyWithdrawalLimitReachedSmsOption debit_card_daily_withdrawal_limit_reached_sms_option = 203;
    DebitCardLowFundsForTransactionSmsOption debit_card_low_funds_for_transaction_sms_option = 204;
    DebitCardInvalidExpiryDateSmsOption debit_card_invalid_expiry_date_sms_option = 205;
    DebitCardNfcNotEnabledSmsOption debit_card_nfc_not_enabled_sms_option = 206;
    DebitCardPrmDeclinedSmsOption debit_card_prm_declined_sms_option = 207;
    DebitCardCvvErrorSmsOption debit_card_cvv_error_sms_option = 208;
    DebitCardDailyContactlessPaymentsLimitExceededSmsOption debit_card_daily_contactless_payments_limit_exceeded_sms_option = 209;
    DebitCardCardOffForTransactionsSmsOption debit_card_card_off_for_transactions_sms_option = 210;
    DebitCardHostDownSmsOption debit_card_host_down_sms_option = 211;
    DebitCardDomesticTransactionsNotEnabledSmsOption debit_card_domestic_transactions_not_enabled_sms_option = 212;
    CallRecordingPostRiskUseCaseSmsOption call_recording_post_risk_use_case_sms_option = 213;
    LamfLienMarkSuccessSmsOption lamf_lien_mark_success_sms_option = 214;
    LamfLoanDisbursedSmsOption lamf_loan_disbursed_sms_option = 215;
    LamfAllEmiPaidLoanClosureInitiatedSmsOption lamf_all_emi_paid_loan_closure_initiated_sms_option = 216;
    LamfRepaymentAutoRecoveredByBajajSmsOption lamf_repayment_auto_recovered_by_bajaj_sms_option = 217;
    LamfRepaymentAutoRecoveredByFiSmsOption lamf_repayment_auto_recovered_by_fi_sms_option = 218;
    LamfRepaymentUpcomingEmiSmsOption lamf_repayment_upcoming_emi_sms_option = 219;
    LamfRepaymentUpcomingEmiLowBalanceSmsOption lamf_repayment_upcoming_emi_low_balance_sms_option = 220;
    LamfRepaymentEmandateBounceSmsOption lamf_repayment_emandate_bounce_sms_option = 221;
    LamfRepaymentPrepaymentSuccessSmsOption lamf_repayment_prepayment_success_sms_option = 222;
    CcFilitePanDob2HrDropOffSmsOption cc_filite_pan_dob2_hr_drop_off_sms_option = 223;
    CcFilitePanDob120HrDropOffSmsOption cc_filite_pan_dob120_hr_drop_off_sms_option = 224;
    CcFiLiteEKYCDropOffSmsOption cc_fi_lite_ekyc_drop_off_sms_option = 225;
    CcFiLiteVKYCDropOffSmsOption cc_cc_fi_lite_vkyc_drop_off_sms_option = 226;
    CreditCardEmiCreatedSmsOption credit_card_emi_created_sms_option = 227;
    CreditCardEmiClosedSmsOption credit_card_emi_closed_sms_option = 228;
    CreditCardEmiPreClosedSmsOption credit_card_emi_pre_closed_sms_option = 229;
    CreditCardEmiCancelledSmsOption credit_card_emi_cancelled_sms_option = 230;
    CreditCardEligibleWebFlowStartApplicationSmsOption credit_card_eligible_web_flow_start_application_sms_option = 231;
    CreditCardEligibleWebFlowCompleteApplicationSmsOption credit_card_eligible_web_flow_complete_application_sms_option = 232;
    FiStoreOrderDeliveryStatusUpdateSmsOption fi_store_order_delivery_status_update_sms_option = 233;
    RiskOutcallFormLoginOtpSmsOption risk_outcall_form_login_otp_sms_option = 234;
    CibilReportSmsOption cibil_report_sms_option = 235;
    SalaryProgramB2BUserWhitelistedSmsOption salary_program_b2b_user_whitelisted_sms_option = 236;
    AlternateContactFlowOtpSmsOption alternate_contact_flow_otp_sms_option = 237;
    NonResidentOnboardingOtpSmsOption non_resident_onboarding_otp_sms_option = 238;
    DebitCardInternationalAtmWithdrawalLimitSmsOption debit_card_international_atm_withdrawal_limit_sms_option = 239;
    StockguardianLoanApplicationEsignSms stockguardian_loan_application_esign_sms = 240;
    CxUserCallDroppedSmsOption cx_user_call_dropped_sms_option = 241;
    DebitCardCafNotFoundSmsOption debit_card_caf_not_found_sms_option = 242;
    DebitCardAtmUsageNotEnabledSmsOption debit_card_atm_usage_not_enabled_sms_option = 243;
    DebitCardHostNotAvailableSmsOption debit_card_host_not_available_sms_option = 244;
    DebitCardAmountOverDailyMaxSmsOption debit_card_amount_over_daily_max_sms_option = 245;
    DebitCardLostOrStolenCardSmsOption debit_card_lost_or_stolen_card_sms_option = 246;
    DebitCardIneligibleAccountSmsOption debit_card_ineligible_account_sms_option = 247;
    DebitCardElaSmsOption debit_card_ela_sms_option = 248;
    DebitCardPosUsageNotEnabledSmsOption debit_card_pos_usage_not_enabled_sms_option = 249;
    DebitCardUnauthorizedUsageSmsOption debit_card_unauthorized_usage_sms_option = 250;
    DebitCardSiHibDeclineSmsOption debit_card_si_hib_decline_sms_option = 251;
    DebitCardAmountOverWithdrawalLimitPosSmsOption debit_card_amount_over_withdrawal_limit_pos_sms_option = 252;
    DebitCardCafStatusDeclineSmsOption debit_card_caf_status_decline_sms_option = 253;
    DebitCardFallBackDeclineSmsOption debit_card_fall_back_decline_sms_option = 254;
    DebitCardMessageEditErrorSmsOption debit_card_message_edit_error_sms_option = 255;
    DebitCardDestNotAvailableSmsOption debit_card_dest_not_available_sms_option = 256;
    DebitCardAtcCheckFailureSmsOption debit_card_atc_check_failure_sms_option = 257;
    DebitCardTokenInAppFlagOffSmsOption debit_card_token_in_app_flag_off_sms_option = 258;
    DebitCardNoIdfErrorSmsOption debit_card_no_idf_error_sms_option = 259;
    DebitCardSystemErrorSmsOption debit_card_system_error_sms_option = 260;
    DebitCardArqcFailureSmsOption debit_card_arqc_failure_sms_option = 261;
    DebitCardApprovedNoBalancesSmsOption debit_card_approved_no_balances_sms_option = 262;
    DebitCardInvalidTxnDateSmsOption debit_card_invalid_txn_date_sms_option = 263;
    DebitCardToBeCapturedInCafSmsOption debit_card_to_be_captured_in_caf_sms_option = 264;
    DebitCardBadCardStatusSmsOption debit_card_bad_card_status_sms_option = 265;
    DebitCardReservedB24CodeSmsOption debit_card_reserved_b24_code_sms_option = 266;
    DebitCardHsmParamErrorSmsOption debit_card_hsm_param_error_sms_option = 267;
    DebitCardMaxCreditPerRefundSmsOption debit_card_max_credit_per_refund_sms_option = 268;
    DebitCardUsageLimitExceededSmsOption debit_card_usage_limit_exceeded_sms_option = 269;
    DebitCardTokenNfcFlagOffSmsOption debit_card_token_nfc_flag_off_sms_option = 270;
    RiskOpsCFSmsOption risk_ops_cf_sms_option = 271;
    DebitCardForexMarkupTxnSmsOption debit_card_forex_markup_txn_sms_option = 272;
    RiskUnifiedLeaDebitFreezeSmsOption risk_unified_lea_debit_freeze_sms_option = 273;
    RiskUnifiedLeaCreditFreezeSmsOption risk_unified_lea_credit_freeze_sms_option = 274;
    RiskUnifiedLeaTotalFreezeSmsOption risk_unified_lea_total_freeze_sms_option = 275;
    RiskUnifiedLeaLienSmsOption risk_unified_lea_lien_sms_option = 276;
    RiskCreditFreezeAppliedSmsOption risk_credit_freeze_applied_sms_option = 277;
    CreditCardRevisedLimitSmsOption credit_card_revised_limit_applied_sms_option = 278;
    ChequeCreditProcessingFint cheque_credit_processing_fint = 279;
    ChequeCreditProcessingFailedFint cheque_credit_processing_failed_fint = 280;
    CxTicketResolutionCsatSmsOption cx_ticket_resolution_csat_sms_option = 281;
    CreditCardBlockKycExpirySmsOption credit_card_block_kyc_expiry_sms_option = 282;
    CreditCardUnblockKycCompletedSmOption credit_card_unblock_kyc_completed_sm_option = 283;
    CreditCardCxSupportDetailsUpdateSmsOption credit_card_cx_support_details_update_sms_option = 284;
    CcClosureInXDaysSmsOption cc_closure_in_x_days_sms_option = 285;
  }
}

message CcClosureInXDaysSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 285];
  oneof option {
    CcClosureInXDaysSmsOptionV1 cc_closure_in_x_days_sms_option_v1 = 2;
  }
}

message CcClosureInXDaysSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string card_last_four_digits = 2;
  int32 number_days_left_before_closure = 3;
  string app_download_url = 4;
}

message CreditCardCxSupportDetailsUpdateSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 284];
  oneof option {
    CreditCardCxSupportDetailsUpdateSmsOptionV1 credit_card_cx_support_details_update_sms_option_v1 = 2;
  }
}

message CreditCardCxSupportDetailsUpdateSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.protobuf.Timestamp date = 2;
  string contact_number = 3;
  string email_user_name = 4;
  string email_domain_name = 5;
}

message CreditCardBlockKycExpirySmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 282];
  oneof option {
    CreditCardBlockKycExpirySmsOptionV1 credit_card_block_kyc_expiry_sms_option_v1 = 2;
  }
}

message CreditCardBlockKycExpirySmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string card_last_four_digits = 2;
  string complete_kyc_link_url = 3;
}

message CreditCardUnblockKycCompletedSmOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 283];
  oneof option {
    CreditCardUnblockKycCompletedSmOptionV1 credit_card_unblock_kyc_completed_sm_option_v1 = 2;
  }
}

message CreditCardUnblockKycCompletedSmOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string card_last_four_digits = 2;
}

message DebitCardForexMarkupTxnSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 272];
  oneof option {
    DebitCardForexMarkupTxnSmsOptionV1 debit_card_forex_markup_txn_sms_option_v1 = 2;
  }
}

message DebitCardForexMarkupTxnSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string name = 2;
  string txn_date = 3;
  string txn_time = 4;
  string txn_amount = 5;
}

message DebitCardTokenNfcFlagOffSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message DebitCardTokenNfcFlagOffSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 270];
  oneof option {
    DebitCardTokenNfcFlagOffSmsOptionV1 debit_card_token_nfc_flag_off_sms_option_v1 = 2;
  }
}

message DebitCardUsageLimitExceededSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message DebitCardUsageLimitExceededSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 269];
  oneof option {
    DebitCardUsageLimitExceededSmsOptionV1 debit_card_usage_limit_exceeded_sms_option_v1 = 2;
  }
}

message DebitCardMaxCreditPerRefundSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string merchant = 2;
  string last_four_digit = 3;
}

message DebitCardMaxCreditPerRefundSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 268];
  oneof option {
    DebitCardMaxCreditPerRefundSmsOptionV1 debit_card_max_credit_per_refund_sms_option_v1 = 2;
  }
}

message DebitCardHsmParamErrorSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string merchant = 2;
  string last_four_digit = 3;
}

message DebitCardHsmParamErrorSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 267];
  oneof option {
    DebitCardHsmParamErrorSmsOptionV1 debit_card_hsm_param_error_sms_option_v1 = 2;
  }
}

message DebitCardReservedB24CodeSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string merchant = 2;
  string last_four_digit = 3;
}

message DebitCardReservedB24CodeSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 266];
  oneof option {
    DebitCardReservedB24CodeSmsOptionV1 debit_card_reserved_b24_code_sms_option_v1 = 2;
  }
}

message DebitCardBadCardStatusSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string last_four_digit = 2;
  string download_link = 3;
}

message DebitCardBadCardStatusSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 265];
  oneof option {
    DebitCardBadCardStatusSmsOptionV1 debit_card_bad_card_status_sms_option_v1 = 2;
  }
}

message DebitCardToBeCapturedInCafSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string merchant = 2;
  string last_four_digit = 3;
}

message DebitCardToBeCapturedInCafSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 264];
  oneof option {
    DebitCardToBeCapturedInCafSmsOptionV1 debit_card_to_be_captured_in_caf_sms_option_v1 = 2;
  }
}

message DebitCardInvalidTxnDateSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string merchant = 2;
  string last_four_digit = 3;
}

message DebitCardInvalidTxnDateSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 263];
  oneof option {
    DebitCardInvalidTxnDateSmsOptionV1 debit_card_invalid_txn_date_sms_option_v1 = 2;
  }
}

message DebitCardApprovedNoBalancesSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string merchant = 2;
  string last_four_digit = 3;
}

message DebitCardApprovedNoBalancesSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 262];
  oneof option {
    DebitCardApprovedNoBalancesSmsOptionV1 debit_card_approved_no_balances_sms_option_v1 = 2;
  }
}

message DebitCardArqcFailureSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string merchant = 2;
  string last_four_digit = 3;
}

message DebitCardArqcFailureSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 261];
  oneof option {
    DebitCardArqcFailureSmsOptionV1 debit_card_arqc_failure_sms_option_v1 = 2;
  }
}

message DebitCardSystemErrorSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string merchant = 2;
  string last_four_digit = 3;
}

message DebitCardSystemErrorSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 260];
  oneof option {
    DebitCardSystemErrorSmsOptionV1 debit_card_system_error_sms_option_v1 = 2;
  }
}

message DebitCardNoIdfErrorSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string merchant = 2;
  string last_four_digit = 3;
}

message DebitCardNoIdfErrorSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 259];
  oneof option {
    DebitCardNoIdfErrorSmsOptionV1 debit_card_no_idf_error_sms_option_v1 = 2;
  }
}

message DebitCardTokenInAppFlagOffSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string merchant = 2;
  string last_four_digit = 3;
}

message DebitCardTokenInAppFlagOffSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 258];
  oneof option {
    DebitCardTokenInAppFlagOffSmsOptionV1 debit_card_token_in_app_flag_off_sms_option_v1 = 2;
  }
}

message DebitCardAtcCheckFailureSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string last_four_digit = 2;
}

message DebitCardAtcCheckFailureSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 257];
  oneof option {
    DebitCardAtcCheckFailureSmsOptionV1 debit_card_atc_check_failure_sms_option_v1 = 2;
  }
}

message DebitCardDestNotAvailableSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string merchant = 2;
  string last_four_digit = 3;
}

message DebitCardDestNotAvailableSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 256];
  oneof option {
    DebitCardDestNotAvailableSmsOptionV1 debit_card_dest_not_available_sms_option_v1 = 2;
  }
}

message DebitCardMessageEditErrorSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name customer_name = 2;
  string merchant = 3;
  string last_four_digit = 4;
}

message DebitCardMessageEditErrorSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 255];
  oneof option {
    DebitCardMessageEditErrorSmsOptionV1 debit_card_message_edit_error_sms_option_v1 = 2;
  }
}

message DebitCardFallBackDeclineSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name customer_name = 2;
  string merchant = 3;
  string last_four_digit = 4;
}

message DebitCardFallBackDeclineSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 254];
  oneof option {
    DebitCardFallBackDeclineSmsOptionV1 debit_card_fall_back_decline_sms_option_v1 = 2;
  }
}

message DebitCardCafStatusDeclineSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message DebitCardCafStatusDeclineSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 253];
  oneof option {
    DebitCardCafStatusDeclineSmsOptionV1 debit_card_caf_status_decline_sms_option_v1 = 2;
  }
}

message DebitCardAmountOverWithdrawalLimitPosSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string merchant = 2;
  string last_four_digit = 3;
  string download_link = 4;
}

message DebitCardAmountOverWithdrawalLimitPosSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 252];
  oneof option {
    DebitCardAmountOverWithdrawalLimitPosSmsOptionV1 debit_card_amount_over_withdrawal_limit_pos_sms_option_v1 = 2;
  }
}

message DebitCardSiHibDeclineSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string merchant = 2;
  google.type.Money txn_amount = 3;
  string last_four_digit = 4;
}

message DebitCardSiHibDeclineSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 251];
  oneof option {
    DebitCardSiHibDeclineSmsOptionV1 debit_card_si_hib_decline_sms_option_v1 = 2;
  }
}

message DebitCardUnauthorizedUsageSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string merchant = 2;
  google.type.Money txn_amount = 3;
  string last_four_digit = 4;
}

message DebitCardUnauthorizedUsageSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 250];
  oneof option {
    DebitCardUnauthorizedUsageSmsOptionV1 debit_card_unauthorized_usage_sms_option_v1 = 2;
  }
}

message DebitCardPosUsageNotEnabledSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string merchant = 2;
  google.type.Money txn_amount = 3;
  string last_four_digit = 4;
  string download_link = 5;
}

message DebitCardPosUsageNotEnabledSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 249];
  oneof option {
    DebitCardPosUsageNotEnabledSmsOptionV1 debit_card_pos_usage_not_enabled_sms_option_v1 = 2;
  }
}

message DebitCardElaSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string merchant = 2;
  google.type.Money txn_amount = 3;
  string last_four_digit = 4;
  string download_link = 5;
}

message DebitCardElaSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 248];
  oneof option {
    DebitCardElaSmsOptionV1 debit_card_ela_sms_option_v1 = 2;
  }
}

message DebitCardIneligibleAccountSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string download_link = 2;
}

message DebitCardIneligibleAccountSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 247];
  oneof option {
    DebitCardIneligibleAccountSmsOptionV1 debit_card_ineligible_account_sms_option_v1 = 2;
  }
}

message DebitCardAmountOverDailyMaxSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string last_four_digit = 2;
  google.protobuf.Timestamp txn_time = 3;
  string download_link = 4;
}

message DebitCardAmountOverDailyMaxSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 246];
  oneof option {
    DebitCardAmountOverDailyMaxSmsOptionV1 debit_card_amount_over_daily_max_sms_option_v1 = 2;
  }
}

message DebitCardLostOrStolenCardSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string last_four_digit = 2;
  string download_link = 3;
}

message DebitCardLostOrStolenCardSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 245];
  oneof option {
    DebitCardLostOrStolenCardSmsOptionV1 debit_card_lost_or_stolen_card_sms_option_v1 = 2;
  }
}

message DebitCardHostNotAvailableSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string merchant = 2;
  string last_four_digit = 3;
}

message DebitCardHostNotAvailableSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 244];
  oneof option {
    DebitCardHostNotAvailableSmsOptionV1 debit_card_host_not_available_sms_option_v1 = 2;
  }
}

message DebitCardAtmUsageNotEnabledSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string last_four_digit = 2;
  google.protobuf.Timestamp txn_time = 3;
  string download_link = 4;
}

message DebitCardAtmUsageNotEnabledSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 243];
  oneof option {
    DebitCardAtmUsageNotEnabledSmsOptionV1 debit_card_atm_usage_not_enabled_sms_option_v1 = 2;
  }
}


message DebitCardCafNotFoundSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string merchant = 2;
  string last_four_digit = 3;
  string download_link = 4;
}

message DebitCardCafNotFoundSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 242];
  oneof option {
    DebitCardCafNotFoundSmsOptionV1 debit_card_caf_not_found_sms_option_v1 = 2;
  }
}

message StockguardianLoanApplicationEsignSms {
  SmsType sms_type = 1 [(validate.rules).enum.const = 240];
  oneof option {
    StockguardianLoanApplicationEsignSmsV1 stockguardian_loan_application_esign_sms_v1 = 2;
  }
}

message StockguardianLoanApplicationEsignSmsV1 {
  string otp = 1;
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message DebitCardInternationalAtmWithdrawalLimitSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 239];
  oneof option {
    DebitCardInternationalAtmWithdrawalLimitSmsOptionV1 debit_card_international_atm_withdrawal_limit_sms_option_v1 = 2;
  }
}

message DebitCardInternationalAtmWithdrawalLimitSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string name = 2;
  string country = 3;
  google.type.Money withdrawal_amount_limit = 4;
}

message CreditCardEmiCreatedSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 227];
  oneof option {
    CreditCardEmiCreatedSmsOptionV1 credit_card_emi_created_sms_option_v1 = 2;
  }
}

message CreditCardEmiCreatedSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name customer_name = 2;
}

message CreditCardEmiClosedSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 228];
  oneof option {
    CreditCardEmiClosedSmsOptionV1 credit_card_emi_closed_sms_option_v1 = 2;
  }
}

message CreditCardEmiClosedSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string merchant_name = 2;
}

message CreditCardEmiPreClosedSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 229];
  oneof option {
    CreditCardEmiPreClosedSmsOptionV1 credit_card_emi_pre_closed_sms_option_v1 = 2;
  }
}

message CreditCardEmiPreClosedSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.protobuf.Timestamp current_timestamp = 2 [(validate.rules).timestamp.required = true];
  string merchant_name = 3;
  google.type.Money due_amount = 4;
  google.type.Money pre_closure_fee = 5;
}

message CreditCardEmiCancelledSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 230];
  oneof option {
    CreditCardEmiCancelledSmsOptionV1 credit_card_emi_cancelled_sms_option_v1 = 2;
  }
}

message CreditCardEmiCancelledSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string merchant_name = 2;
}

message CreditCardEligibleWebFlowStartApplicationSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 231];
  oneof option {
    CreditCardEligibleWebFlowStartApplicationSmsOptionV1 credit_card_eligible_web_flow_start_application_sms_option_v1 = 2;
  }
}

message CreditCardEligibleWebFlowStartApplicationSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string name = 2;
  string app_download_url = 3;
}

message CreditCardEligibleWebFlowCompleteApplicationSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 232];
  oneof option {
    CreditCardEligibleWebFlowCompleteApplicationSmsOptionV1 credit_card_eligible_web_flow_complete_application_sms_option_v1 = 2;
  }
}

message CreditCardEligibleWebFlowCompleteApplicationSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string name = 2;
  string app_download_url = 3;
}

message FiStoreOrderDeliveryStatusUpdateSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 233];
  oneof option {
    FiStoreOrderDeliveryStatusUpdateSmsOptionV1 fi_store_order_delivery_status_update_sms_option_v1 = 2;
  }
}

message FiStoreOrderDeliveryStatusUpdateSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string order_tracking_link = 2;
  string order_id = 3;
}

message CcFilitePanDob2HrDropOffSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 223];

  oneof option {
    CcFilitePanDob2HrDropOffSmsOptionV1 cc_filite_pan_dob2_hr_drop_off_sms_option_v1 = 2;
  }
}

message CcFilitePanDob2HrDropOffSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string deep_link = 2 [(validate.rules).string.min_len = 1];
}

message CcFilitePanDob120HrDropOffSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 224];

  oneof option {
    CcFilitePanDob120HrDropOffSmsOptionV1 cc_filite_pan_dob120_hr_drop_off_sms_option_v1 = 2;
  }
}

message CcFilitePanDob120HrDropOffSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string deep_link = 2 [(validate.rules).string.min_len = 1];
}

message CcFiLiteEKYCDropOffSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 225];

  oneof option {
    CcFiLiteEKYCDropOffSmsOptionV1 cc_fi_lite_ekyc_drop_off_sms_option_v1 = 2;
  }
}

message CcFiLiteEKYCDropOffSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string deep_link = 2 [(validate.rules).string.min_len = 1];
}

message CcFiLiteVKYCDropOffSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 226];

  oneof option {
    CcFiLiteVKYCDropOffSmsOptionV1 cc_fi_lite_vkyc_drop_off_sms_option_v1 = 2;
  }
}

message CcFiLiteVKYCDropOffSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string name = 2;
  string link = 3;
}

message LamfLienMarkSuccessSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string investor_name = 2;
  string continue_link = 3 [(validate.rules).string.min_len = 1];
}

message LamfLienMarkSuccessSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 214];

  oneof option {
    LamfLienMarkSuccessSmsOptionV1 lamf_lien_mark_success_sms_option_v1 = 2;
  }
}

message LamfLoanDisbursedSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string investor_name = 2;
  string status_link = 3 [(validate.rules).string.min_len = 1];
}

message LamfLoanDisbursedSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 215];

  oneof option {
    LamfLoanDisbursedSmsOptionV1 lamf_loan_disbursed_sms_option_v1 = 2;
  }
}

message LamfAllEmiPaidLoanClosureInitiatedSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string investor_name = 2;
}

message LamfAllEmiPaidLoanClosureInitiatedSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 216];

  oneof option {
    LamfAllEmiPaidLoanClosureInitiatedSmsOptionV1 lamf_all_emi_paid_loan_closure_initiated_sms_option_v1 = 2;
  }
}

message LamfRepaymentAutoRecoveredByBajajSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string investor_name = 2;
  string emi_amount = 3;
  string account_last_four_digits = 4;
}

message LamfRepaymentAutoRecoveredByBajajSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 217];

  oneof option {
    LamfRepaymentAutoRecoveredByBajajSmsOptionV1 lamf_repayment_auto_recovered_by_bajaj_sms_option_v1 = 2;
  }
}

message LamfRepaymentAutoRecoveredByFiSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string investor_name = 2;
  string emi_amount = 3;
  string account_last_four_digits = 4;
}

message LamfRepaymentAutoRecoveredByFiSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 218];

  oneof option {
    LamfRepaymentAutoRecoveredByFiSmsOptionV1 lamf_repayment_auto_recovered_by_fi_sms_option_v1 = 2;
  }
}

message LamfRepaymentUpcomingEmiSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string investor_name = 2;
  string emi_amount = 3;
  string due_date = 4;
}

message LamfRepaymentUpcomingEmiSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 219];

  oneof option {
    LamfRepaymentUpcomingEmiSmsOptionV1 lamf_repayment_upcoming_emi_sms_option_v1 = 2;
  }
}

message LamfRepaymentUpcomingEmiLowBalanceSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string investor_name = 2;
  string emi_amount = 3;
  string due_date = 4;
}

message LamfRepaymentUpcomingEmiLowBalanceSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 220];

  oneof option {
    LamfRepaymentUpcomingEmiLowBalanceSmsOptionV1 lamf_repayment_upcoming_emi_low_balance_sms_option_v1 = 2;
  }
}

message LamfRepaymentEmandateBounceSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string investor_name = 2;
  string account_last_four_digits = 3;
}

message LamfRepaymentEmandateBounceSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 221];

  oneof option {
    LamfRepaymentEmandateBounceSmsOptionV1 lamf_repayment_emandate_bounce_sms_option_v1 = 2;
  }
}

message LamfRepaymentPrepaymentSuccessSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string investor_name = 2;
}

message LamfRepaymentPrepaymentSuccessSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 222];

  oneof option {
    LamfRepaymentPrepaymentSuccessSmsOptionV1 lamf_repayment_prepayment_success_sms_option_v1 = 2;
  }
}

message CallRecordingPostRiskUseCaseSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 213];
  oneof option {
    CallRecordingPostRiskUseCaseSmsOptionV1 call_recording_post_risk_use_case_sms_option_v1 = 2;
  }
}

message CallRecordingPostRiskUseCaseSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message DebitCardForexRefundReceivedSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 187];
  oneof option {
    DebitCardForexRefundReceivedSmsOptionV1 debit_card_forex_refund_received_sms_option_v1 = 2;
  }
}

message DebitCardForexRefundReceivedSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  // forex refund that has been received
  google.type.Money refund_amount = 2;
  // account number of the user for whom the refund has come
  string account_number = 3;
}

message CreditCardWebEligibilityLoginOtpSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 183];
  oneof option {
    CreditCardWebEligibilityLoginOtpSmsOptionV1 credit_card_web_eligibility_login_otp_sms_option_v1 = 2;
  }
}

message CreditCardWebEligibilityLoginOtpSmsOptionV1 {
  string otp = 1;
  TemplateVersion template_version = 3 [(validate.rules).enum.const = 1];
}

message CreditCardWebEligibilityRejectedSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 185];
  oneof option {
    CreditCardWebEligibilityRejectedSmsOptionV1 credit_card_web_eligibility_rejected_sms_option_v1 = 2;
  }
}

message CreditCardWebEligibilityRejectedSmsOptionV1 {
  string download_link = 1;
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message CreditCardWebEligibilityApprovedSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 184];
  oneof option {
    CreditCardWebEligibilityApprovedSmsOptionV1 credit_card_web_eligibility_approved_sms_option_v1 = 2;
  }
}

message CreditCardWebEligibilityApprovedSmsOptionV1 {
  // name of the user to whom the sms is being sent
  api.typesv2.common.Name name = 1;
  string download_link = 2;
  TemplateVersion template_version = 3 [(validate.rules).enum.const = 1];
}

message OnboardingOtpSmsOptionV1 {
  // actual otp to be sent
  string otp = 1 [(validate.rules).string.min_len = 1];
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];

  // android client signature to be set in message
  // This is env specific based on which client auto reads OTP
  string android_client_signature = 3 [(validate.rules).string.min_len = 1];
}

// each template will have certain options which are the variables that will be filled in the template
message OnboardingOtpSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 1];

  oneof option {
    OnboardingOtpSmsOptionV1 onboarding_otp_sms_option_v1 = 2;
  }
}

message CardOutForDeliverySmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];
}

message CardOutForDeliverySmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 67];
  oneof option {
    CardOutForDeliverySmsOptionV1 card_out_for_delivery_sms_option_v1 = 2;
  }
}

message CardDeliveryDelaySmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];
}

message CardDeliveryDelaySmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 68];
  oneof option {
    CardDeliveryDelaySmsOptionV1 card_delivery_delay_sms_option_v1 = 2;
  }
}

message CardDispatchTimelineInfoSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message CardDispatchTimelineInfoSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 69];
  oneof option {
    CardDispatchTimelineInfoSmsOptionV1 card_dispatch_timeline_info_sms_option_v1 = 2;
  }
}

message PanReminderSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message PanReminderSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 57];
  oneof option {
    PanReminderSmsOptionV1 pan_reminder_sms_option_v1 = 2;
  }
}

message EKYCReminderSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message EKYCReminderSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 58];

  oneof option {
    EKYCReminderSmsOptionV1 ekyc_reminder_sms_option_v1 = 2;
  }
}

message NameMismatchUpdateSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name first_name = 2 [(validate.rules).message.required = true];
}

message NameMismatchUpdateSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 59];

  oneof option {
    NameMismatchUpdateSmsOptionV1 name_mismatch_update_sms_option_v1 = 2;
  }
}

message KYCValidationFailureSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message KYCValidationFailureSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 62];

  oneof option {
    KYCValidationFailureSmsOptionV1 kyc_validation_failure_sms_option_v1 = 2;
  }
}

message DebitCardDeliverySmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  string carrier_partner = 3 [(validate.rules).string.min_len = 1];

  string awb = 4 [(validate.rules).string.min_len = 1];
}

message DebitCardDeliverySmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 64];

  oneof option {
    DebitCardDeliverySmsOptionV1 debit_card_delivery_sms_option_v1 = 2;
  }
}

message LivenessReminderSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message LivenessReminderSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 60];

  oneof option {
    LivenessReminderSmsOptionV1 liveness_reminder_sms_option_v1 = 2;
  }
}

message WaitlistOtpSmsOptionV1 {
  // actual otp to be sent
  string otp = 1 [(validate.rules).string.min_len = 1];

  // to be set as VERSION_V1
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message WaitlistOtpSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 2];

  oneof option {
    WaitlistOtpSmsOptionV1 waitlist_otp_sms_option_v1 = 2;
  }
}

message DebitCardBlockSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  string card_last_four_digits = 2 [(validate.rules).string.min_len = 4];
}

message DebitCardBlockSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 3];

  oneof option {
    DebitCardBlockSmsOptionV1 debit_card_block_sms_option_v1 = 2;
  }
}

message DebitCardNewCardIssuanceSoftPinV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];
}

message DebitCardNewCardIssuanceSoftPin {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 4];

  oneof option {
    DebitCardNewCardIssuanceSoftPinV1 debit_card_new_card_issuance_soft_pin_sms_option_v1 = 2;
  }
}

message DebitCardNewCardIssuanceSoftPinReV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message DebitCardNewCardIssuanceSoftPinRe {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 5];

  oneof option {
    DebitCardNewCardIssuanceSoftPinReV1 debit_card_new_card_issuance_soft_pin_re_sms_option_v1 = 2;
  }
}

message DebitCardOnSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  string card_last_four_digits = 3 [(validate.rules).string.min_len = 4];
}

message DebitCardOnSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 6];

  oneof option {
    DebitCardOnSmsOptionV1 debit_card_on_sms_option_v1 = 2;
  }
}

message DebitCardOffSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  string card_last_four_digits = 2 [(validate.rules).string.min_len = 4];
}

message DebitCardOffSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 7];

  oneof option {
    DebitCardOffSmsOptionV1 debit_card_off_sms_option_v1 = 2;
  }
}

message DebitCardOnOffFailureSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message DebitCardOnOffFailureSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 8];

  oneof option {
    DebitCardOnOffFailureSmsOptionV1 debit_card_on_off_failure_sms_option_v1 = 2;
  }
}

message DebitCardInternationalOnSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  string card_last_four_digits = 3 [(validate.rules).string.min_len = 4];
}

message DebitCardInternationalOnSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 9];

  oneof option {
    DebitCardInternationalOnSmsOptionV1 debit_card_international_on_sms_option_v1 = 2;
  }
}

message DebitCardInternationalOffSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  string card_last_four_digits = 2 [(validate.rules).string.min_len = 4];
}

message DebitCardInternationalOffSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 10];

  oneof option {
    DebitCardInternationalOffSmsOptionV1 debit_card_international_off_sms_option_v1 = 2;
  }
}

message DebitCardInternationalOnOffFailureSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message DebitCardInternationalOnOffFailureSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 11];

  oneof option {
    DebitCardInternationalOnOffFailureSmsOptionV1 debit_card_international_on_off_failure_sms_option_v1 = 2;
  }
}

message DebitCardEcomOnSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  string card_last_four_digits = 3 [(validate.rules).string.min_len = 4];
}

message DebitCardEcomOnSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 12];

  oneof option {
    DebitCardEcomOnSmsOptionV1 debit_card_ecom_on_sms_option_v1 = 2;
  }
}

message DebitCardEcomOffSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  string card_last_four_digits = 3 [(validate.rules).string.min_len = 4];
}

message DebitCardEcomOffSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 13];

  oneof option {
    DebitCardEcomOffSmsOptionV1 debit_card_ecom_off_sms_option_v1 = 2;
  }
}

message DebitCardActivateSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];
}

message DebitCardActivateSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 14];

  oneof option {
    DebitCardActivateSmsOptionV1 debit_card_activate_sms_option_v1 = 2;
  }
}

message DebitCardBlockFailureSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];
}

message DebitCardBlockFailureSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 15];

  oneof option {
    DebitCardBlockFailureSmsOptionV1 debit_card_block_failure_sms_option_v1 = 2;
  }
}

message DebitCardDispatchSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  string card_last_four_digits = 3 [(validate.rules).string.min_len = 4];

  string logistics_partner = 4 [(validate.rules).string.min_len = 1];

  google.protobuf.Timestamp dispatch_time = 5 [(validate.rules).timestamp.required = true];

  string tracking_number = 6 [(validate.rules).string.min_len = 1];
}

message DebitCardDispatchSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 16];

  oneof option {
    DebitCardDispatchSmsOptionV1 debit_card_dispatch_sms_option_v1 = 2;
  }
}

message DebitCardInCorrectPinRetriesSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  string card_last_four_digits = 2 [(validate.rules).string.min_len = 4];
}

message DebitCardInCorrectPinRetriesSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 17];

  oneof option {
    DebitCardInCorrectPinRetriesSmsOptionV1 debit_card_incorrect_pin_retries_sms_option_v1 = 2;
  }
}

message DebitCardFreezeSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  string card_last_four_digits = 3 [(validate.rules).string.min_len = 4];
}

message DebitCardFreezeSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 18];

  oneof option {
    DebitCardFreezeSmsOptionV1 debit_card_freeze_sms_option_v1 = 2;
  }
}

message DebitCardUnFreezeSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  string card_last_four_digits = 3 [(validate.rules).string.min_len = 4];
}

message DebitCardUnFreezeSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 19];

  oneof option {
    DebitCardUnFreezeSmsOptionV1 debit_card_unfreeze_sms_option_v1 = 2;
  }
}

message DebitCardChangeAtmPinSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];
}

message DebitCardChangeAtmPinSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 20];

  oneof option {
    DebitCardChangeAtmPinSmsOptionV1 debit_card_change_atm_pin_sms_option_v1 = 2;
  }
}

//<First NameXXXXXXXXX>, you're now registered on UPI. When you sign-up on our app, we create a fresh UPI ID for you.
//Questions? Call us on 080-68971063. -Federal
message UpiRegistrationSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];
}

message UpiRegistrationSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 21];

  oneof option {
    UpiRegistrationSmsOptionV1 upi_registration_sms_option_v1 = 2;
  }
}

// Withdrawn: Rs.13,000 Balance: Rs.1,30,000.81
//This transaction occurred on Apr 11, 2020 at 6.49 PM.If it wasn't done by you, ping us on the Fi app.-Federal
message CashWithdrawalAtmSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money withdrawn_amount = 2 [(validate.rules).message.required = true];

  google.type.Money balance_amount = 3 [(validate.rules).message.required = true];

  google.protobuf.Timestamp transaction_timestamp = 4 [(validate.rules).timestamp.required = true];
}

message CashWithdrawalAtmSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 22];

  oneof option {
    CashWithdrawalAtmSmsOptionV1 cash_withdrawal_atm_sms_option_v1 = 2;
  }
}

// <First NameXXXXXXXXX>, you've sent Rs.1,00,00,000 to Metro Cera.
//Mode: NEFT | Apr 5, 2020 | Ref: FDRLM7303006233.
//Balance: Rs.2,00,00,000
//-Federal
message NeftDebitSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  google.type.Money transaction_amount = 3 [(validate.rules).message.required = true];

  api.typesv2.common.Name receiver_name = 4 [(validate.rules).message.required = true];

  string payment_mode = 5 [(validate.rules).string.min_len = 1];

  google.type.Money balance_amount = 6 [(validate.rules).message.required = true];

  google.protobuf.Timestamp transaction_timestamp = 7 [(validate.rules).timestamp.required = true];

  string reference_number = 8 [(validate.rules).string.min_len = 1];
}

message NeftDebitSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 23];

  oneof option {
    NeftDebitSmsOptionV1 neft_debit_sms_option_v1 = 2;
  }
}

message NeftCreditSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  google.type.Money transaction_amount = 3 [(validate.rules).message.required = true];

  api.typesv2.common.Name sender_name = 4 [(validate.rules).message.required = true];

  string payment_mode = 5 [(validate.rules).string.min_len = 1];

  google.protobuf.Timestamp transaction_timestamp = 6 [(validate.rules).timestamp.required = true];

  string reference_number = 7 [(validate.rules).string.min_len = 1];

  google.type.Money balance_amount = 8 [(validate.rules).message.required = true];
}

message NeftCreditSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 24];

  oneof option {
    NeftCreditSmsOptionV1 neft_credit_sms_option_v1 = 2;
  }
}

message NeftCreditOtherBankSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money transaction_amount = 2 [(validate.rules).message.required = true];

  google.type.Money balance_amount = 3 [(validate.rules).message.required = true];

  string account_number_last_four_digits = 4 [(validate.rules).string.min_len = 4];

  string payment_mode = 5 [(validate.rules).string.min_len = 1];

  google.protobuf.Timestamp transaction_timestamp = 6 [(validate.rules).timestamp.required = true];
}

message NeftCreditOtherBankSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 25];

  oneof option {
    NeftCreditOtherBankSmsOptionV1 neft_credit_other_bank_sms_option_v1 = 2;
  }
}

//<First NameXXXXXXXXX>, Shree Krishna Granites has received Rs.10,00,000 from your account.
//Mode: NEFT | Apr 26, 2020 | Ref No. SFB51189154.
//-Federal
message NeftCreditConfirmationSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  api.typesv2.common.Name receiver_name = 3 [(validate.rules).message.required = true];

  google.type.Money transaction_amount = 4 [(validate.rules).message.required = true];

  string payment_mode = 5 [(validate.rules).string.min_len = 1];

  google.protobuf.Timestamp transaction_timestamp = 6 [(validate.rules).timestamp.required = true];

  string reference_number = 7 [(validate.rules).string.min_len = 1];
}

message NeftCreditConfirmationSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 26];

  oneof option {
    NeftCreditConfirmationSmsOptionV1 neft_credit_confirmation_sms_option_v1 = 2;
  }
}

message PosDebitSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money transaction_amount = 2 [(validate.rules).message.required = true];

  api.typesv2.common.Name receiver_name = 3 [(validate.rules).message.required = true];

  google.protobuf.Timestamp transaction_timestamp = 4 [(validate.rules).timestamp.required = true];

  google.type.Money balance_amount = 5 [(validate.rules).message.required = true];
}

message PosDebitSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 27];

  oneof option {
    PosDebitSmsOptionV1 pos_debit_sms_option_v1 = 2;
  }
}

message PosReversalCreditSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money transaction_amount = 2 [(validate.rules).message.required = true];

  string account_number_last_four_digits = 3 [(validate.rules).string.min_len = 4];

  google.protobuf.Timestamp transaction_timestamp = 4 [(validate.rules).timestamp.required = true];

  google.type.Money balance_amount = 5 [(validate.rules).message.required = true];
}

message PosReversalCreditSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 28];

  oneof option {
    PosReversalCreditSmsOptionV1 pos_reversal_credit_sms_option_v1 = 2;
  }
}

message UnsuccessfulAtmReversalCreditSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money transaction_amount = 2 [(validate.rules).message.required = true];

  google.protobuf.Timestamp transaction_timestamp = 3 [(validate.rules).timestamp.required = true];

  google.type.Money balance_amount = 4 [(validate.rules).message.required = true];
}

message UnsuccessfulAtmReversalCreditSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 29];

  oneof option {
    UnsuccessfulAtmReversalCreditSmsOptionV1 unsuccessful_atm_reversal_credit_sms_option_v1 = 2;
  }
}

message RtgsCreditConfirmationSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  api.typesv2.common.Name receiver_name = 3 [(validate.rules).message.required = true];

  google.type.Money transaction_amount = 4 [(validate.rules).message.required = true];

  google.protobuf.Timestamp transaction_timestamp = 5 [(validate.rules).timestamp.required = true];

  string reference_number = 6 [(validate.rules).string.min_len = 1];
}

message RtgsCreditConfirmationSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 30];

  oneof option {
    RtgsCreditConfirmationSmsOptionV1 rtgs_credit_confirmation_sms_option_v1 = 2;
  }
}

// "{#first_name#}, you've sent Rs.{#txn_amount#} to {#reciever_name#}.
//Mode: {#payment_mode#} | {# %b %e, %Y#} |Ref: {#reference_no#}.
//Balance: Rs.{#balance_amount#}
//-Federal"
message RtgsDebitSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  api.typesv2.common.Name receiver_name = 3 [(validate.rules).message.required = true];

  google.type.Money transaction_amount = 4 [(validate.rules).message.required = true];

  google.protobuf.Timestamp transaction_timestamp = 5 [(validate.rules).timestamp.required = true];

  string reference_number = 6 [(validate.rules).string.min_len = 1];

  string payment_mode = 7 [(validate.rules).string.min_len = 1];

  google.type.Money balance_amount = 8 [(validate.rules).message.required = true];
}

message RtgsDebitSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 31];

  oneof option {
    RtgsDebitSmsOptionV1 rtgs_debit_sms_option_v1 = 2;
  }
}

message CreditCashDepositMachineSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  google.type.Money transaction_amount = 3 [(validate.rules).message.required = true];

  google.type.Money balance_amount = 4 [(validate.rules).message.required = true];

  string location = 5 [(validate.rules).string.min_len = 1];
}

message CreditCashDepositMachineSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 32];

  oneof option {
    CreditCashDepositMachineSmsOptionV1 credit_cash_deposit_machine_sms_option_v1 = 2;
  }
}

message UpiCreditSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  google.type.Money transaction_amount = 3 [(validate.rules).message.required = true];

  string upi_id = 4;

  string account_number_last_four_digits = 5 [(validate.rules).string.min_len = 4];

  google.protobuf.Timestamp transaction_timestamp = 6 [(validate.rules).timestamp.required = true];

  api.typesv2.common.Name sender_name = 7 [(validate.rules).message.required = true];

  string sender_upi_id = 8 [(validate.rules).string.min_len = 1];
}

message UpiCreditSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];

  google.type.Money transaction_amount = 2 [(validate.rules).message.required = true];

  string account_number_last_four_digits = 3 [(validate.rules).string.min_len = 4];

  google.protobuf.Timestamp transaction_timestamp = 4 [(validate.rules).timestamp.required = true];

  api.typesv2.common.Name sender_name = 5 [(validate.rules).message.required = true];

  string sender_upi_id = 6 [(validate.rules).string.min_len = 1];
}

message UpiCreditSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 33];

  oneof option {
    UpiCreditSmsOptionV1 upi_credit_sms_option_v1 = 2;
    UpiCreditSmsOptionV2 upi_credit_sms_option_v2 = 3;
  }
}

message UpiDebitSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  google.type.Money transaction_amount = 3 [(validate.rules).message.required = true];

  string sender_pi = 4 [(validate.rules).string.min_len = 1];

  string account_number_last_four_digits = 5 [(validate.rules).string.min_len = 4];

  google.protobuf.Timestamp transaction_timestamp = 6 [(validate.rules).timestamp.required = true];
}

message UpiDebitSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];

  google.type.Money transaction_amount = 2 [(validate.rules).message.required = true];

  string sender_pi = 3 [(validate.rules).string.min_len = 1];

  string account_number_last_four_digits = 4 [(validate.rules).string.min_len = 4];

  google.protobuf.Timestamp transaction_timestamp = 5 [(validate.rules).timestamp.required = true];
}

message UpiDebitSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 34];

  oneof option {
    UpiDebitSmsOptionV1 upi_debit_sms_option_v1 = 2;
    UpiDebitSmsOptionV2 upi_debit_sms_option_v2 = 3;
  }
}

message CollectRequestSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  google.type.Money transaction_amount = 3 [(validate.rules).message.required = true];

  api.typesv2.common.Name sender_name = 4 [(validate.rules).message.required = true];
}

message CollectRequestSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 35];

  oneof option {
    CollectRequestSmsOptionV1 collect_request_sms_option_v1 = 2;
  }
}

message FailedTransactionSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money transaction_amount = 2 [(validate.rules).message.required = true];
}

message FailedTransactionSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 36];

  oneof option {
    FailedTransactionSmsOptionV1 failed_transaction_sms_option_v1 = 2;
  }
}

message GenericPiCreditSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  google.type.Money transaction_amount = 3 [(validate.rules).message.required = true];

  string account_number_last_four_digits = 4 [(validate.rules).string.min_len = 4];

  google.protobuf.Timestamp transaction_timestamp = 5 [(validate.rules).timestamp.required = true];

  api.typesv2.common.Name sender_name = 6 [deprecated = true];
}

message GenericPiCreditSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  google.type.Money transaction_amount = 3 [(validate.rules).message.required = true];

  string account_number_last_four_digits = 4 [(validate.rules).string.min_len = 4];

  google.protobuf.Timestamp transaction_timestamp = 5 [(validate.rules).timestamp.required = true];

  string sender_pi = 6 [(validate.rules).string.min_len = 1];
}

message GenericPiCreditSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 37];

  oneof option {
    GenericPiCreditSmsOptionV1 generic_pi_credit_sms_option_v1 = 2;
    GenericPiCreditSmsOptionV2 generic_pi_credit_sms_option_v2 = 3;
  }
}

message GenericPiDebitSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  google.type.Money transaction_amount = 3 [(validate.rules).message.required = true];

  string account_number_last_four_digits = 4 [(validate.rules).string.min_len = 4];

  google.protobuf.Timestamp transaction_timestamp = 5 [(validate.rules).timestamp.required = true];

  api.typesv2.common.Name sender_name = 6 [(validate.rules).message.required = true];

  string sender_pi = 7 [(validate.rules).string.min_len = 1];
}

message GenericPiDebitSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];

  google.type.Money transaction_amount = 2 [(validate.rules).message.required = true];

  string account_number_last_four_digits = 3 [(validate.rules).string.min_len = 4];

  google.protobuf.Timestamp transaction_timestamp = 4 [(validate.rules).timestamp.required = true];

  string sender_pi = 5 [(validate.rules).string.min_len = 1];
}

message GenericPiDebitSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 38];

  oneof option {
    GenericPiDebitSmsOptionV1 generic_pi_debit_sms_option_v1 = 2;
    GenericPiDebitSmsOptionV2 generic_pi_debit_sms_option_v2 = 3;
  }
}

message FdOpenSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  string fd_name = 2 [(validate.rules).string.min_len = 1];

  google.type.Money fd_amount = 3 [(validate.rules).message.required = true];

  string interest_rate = 4 [(validate.rules).string.min_len = 1];

  // duration should be passed as display string
  // for Ex. 7 days or 6 months or 2 year etc
  // should be same as that shown in app
  string duration = 5 [(validate.rules).string.min_len = 1];

  google.protobuf.Timestamp maturity_date = 6 [(validate.rules).timestamp.required = true];

  google.type.Money maturity_amount = 7 [(validate.rules).message.required = true];

  string renewal_instruction = 8 [(validate.rules).string.min_len = 1];
}

message FdOpenSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 39];

  oneof option {
    FdOpenSmsOptionV1 fd_open_sms_option_v1 = 2;
  }
}

message SdOpenSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  string sd_name = 2 [(validate.rules).string.min_len = 1];

  google.type.Money sd_amount = 3 [(validate.rules).message.required = true];

  string interest_rate = 4 [(validate.rules).string.min_len = 1];

  // duration should be passed as display string
  // for Ex. 7 days or 6 months or 2 year etc
  // should be same as that shown in app
  string duration = 5 [(validate.rules).string.min_len = 1];

  google.protobuf.Timestamp maturity_date = 6 [(validate.rules).timestamp.required = true];

  google.type.Money maturity_amount = 7 [(validate.rules).message.required = true];
}

message SdOpenSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];

  string sd_name = 2 [(validate.rules).string.min_len = 1];

  google.type.Money sd_amount = 3 [(validate.rules).message.required = true];

  string interest_rate = 4 [(validate.rules).string.min_len = 1];

  // duration should be passed as display string
  // for Ex. 7 days or 6 months or 2 year etc
  // should be same as that shown in app
  string duration = 5 [(validate.rules).string.min_len = 1];

  google.protobuf.Timestamp maturity_date = 6 [(validate.rules).timestamp.required = true];
}

message SdOpenSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 40];

  oneof option {
    SdOpenSmsOptionV1 sd_open_sms_option_v1 = 2;
    SdOpenSmsOptionV2 sd_open_sms_option_v2 = 3;
  }
}

message FdSdXDaysBeforeMatuaritySmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  // pass it as string
  // possible values
  // 1. Fixed (for FIXED_DEPOSIT)
  // 2. Smart (for SMART_DEPOSIT)
  string deposit_type = 2 [(validate.rules).string.min_len = 1];

  string deposit_number_last_four_digits = 3 [(validate.rules).string.min_len = 4];

  google.protobuf.Timestamp maturity_date = 4 [(validate.rules).timestamp.required = true];

  google.protobuf.Timestamp opening_date = 5 [(validate.rules).timestamp.required = true];

  // duration should be passed as display string
  // for Ex. 7 days or 6 months or 2 year etc
  // should be same as that shown in app
  string duration = 6 [(validate.rules).string.min_len = 1];

  string renewal_instruction = 7 [(validate.rules).string.min_len = 1];

  string renewal_period = 8 [(validate.rules).string.min_len = 1];
}

message SdXDaysBeforeMaturitySmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  string deposit_name = 2 [(validate.rules).string.min_len = 1];

  // optional, right now we are not displaying the masked number in the sms
  string masked_deposit_number = 3;

  google.protobuf.Timestamp maturity_date = 4 [(validate.rules).timestamp.required = true];
  google.protobuf.Timestamp opening_date = 5 [(validate.rules).timestamp.required = true];

  // duration should be passed as display string
  // for Ex. 7 days or 6 months or 2 year etc
  // should be same as that shown in app
  string duration = 6 [(validate.rules).string.min_len = 1];

  // total invested deposit amount
  google.type.Money amount = 7 [(validate.rules).message.required = true];
}

message SdXDaysBeforeMaturitySmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 117];

  oneof option {
    SdXDaysBeforeMaturitySmsOptionV1 sd_x_days_before_maturity_sms_option_v1 = 2;
  }
}

message FdSdXDaysBeforeMatuaritySmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 41];

  oneof option {
    FdSdXDaysBeforeMatuaritySmsOptionV1 fd_sd_x_days_before_matuarity_sms_option_v1 = 2;
  }
}

message AddFundsSdSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  string sd_name = 2 [(validate.rules).string.min_len = 1];

  string sd_account_number_last_four_digits = 3 [(validate.rules).string.min_len = 4];

  google.type.Money sd_add_amount = 4 [(validate.rules).message.required = true];
}

message AddFundsSdSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 42];

  oneof option {
    AddFundsSdSmsOptionV1 add_funds_sd_sms_option_v1 = 2;
  }
}

message FdSdClosureSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  // pass it as string
  // possible values
  // 1. Fixed (for FIXED_DEPOSIT)
  // 2. Smart (for SMART_DEPOSIT)
  string deposit_type = 2 [(validate.rules).string.min_len = 1];

  string deposit_number_last_four_digits = 3 [(validate.rules).string.min_len = 4];

  google.protobuf.Timestamp maturity_date = 4 [(validate.rules).timestamp.required = true];

  google.type.Money maturity_amount = 5 [(validate.rules).message.required = true];

  string saving_account_number_last_four_digits = 6 [(validate.rules).string.min_len = 4];
}

message FdSdClosureSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 43];

  oneof option {
    FdSdClosureSmsOptionV1 fd_sd_closure_sms_option_v1 = 2;
  }
}

message InterestPaidInSBSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money interest_amount = 2 [(validate.rules).message.required = true];

  string account_number_last_four_digits = 3 [(validate.rules).string.min_len = 4];

  google.protobuf.Timestamp timestamp = 4 [(validate.rules).timestamp.required = true];

  google.type.Money balance = 5 [(validate.rules).message.required = true];
}

message InterestPaidInSBSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 44];

  oneof option {
    InterestPaidInSBSmsOptionV1 interest_paid_in_sb_sms_option_v1 = 2;
  }
}

message MobileNumberAddSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  // mobile number that was added
  api.typesv2.common.PhoneNumber new_mobile_number = 2 [(validate.rules).message.required = true];

  // timestamp at which request was accepted
  google.protobuf.Timestamp request_accepted_timestamp = 3 [(validate.rules).timestamp.required = true];

  string customer_id_last_four_digits = 4 [(validate.rules).string.min_len = 4];
}

message MobileNumberAddSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 45];

  oneof option {
    MobileNumberAddSmsOptionV1 mobile_number_add_sms_option_v1 = 2;
  }
}


message MobileNumberModifySmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  // old mobile number
  api.typesv2.common.PhoneNumber old_mobile_number = 2 [(validate.rules).message.required = true];

  // new mobile number
  api.typesv2.common.PhoneNumber new_mobile_number = 3 [(validate.rules).message.required = true];

  // timestamp at which request was accepted
  google.protobuf.Timestamp request_accepted_timestamp = 4 [(validate.rules).timestamp.required = true];

  string customer_id_last_four_digits = 5 [(validate.rules).string.min_len = 4];
}

message MobileNumberModifySmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 46];

  oneof option {
    MobileNumberModifySmsOptionV1 mobile_number_modify_sms_option_v1 = 2;
  }
}

message CardControlOnSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  // old mobile number
  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  string debit_card_last_four_digits = 3 [(validate.rules).string.min_len = 4];

  string card_mode = 4 [(validate.rules).string.min_len = 1];
}

message CardControlOnSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 47];

  oneof option {
    CardControlOnSmsOptionV1 card_control_on_sms_option_v1 = 2;
  }
}

message CardControlOffSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  // old mobile number
  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  string debit_card_last_four_digits = 3 [(validate.rules).string.min_len = 4];

  string card_mode = 4 [(validate.rules).string.min_len = 1];
}

message CardControlOffSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 48];

  oneof option {
    CardControlOffSmsOptionV1 card_control_off_sms_option_v1 = 2;
  }
}

message VkycApprovedSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];
}

message VkycApprovedSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 49];

  oneof option {
    VkycApprovedSmsOptionV1 vkyc_approved_sms_option_v1 = 2;
  }
}

message WaitlistAccessSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];
}

message WaitlistAccessSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];
}

message WaitlistAccessSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 50];

  oneof option {
    WaitlistAccessSmsOptionV1 waitlist_access_sms_option_v1 = 2;
    WaitlistAccessSmsOptionV2 waitlist_access_sms_option_v2 = 3;
  }
}

message VkycWithLimitSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];
}

message VkycWithLimitSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 51];

  oneof option {
    VkycWithLimitSmsOptionV1 vkyc_with_limit_sms_option_v1 = 2;
  }
}

message FiniteCodeSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  string finite_code = 3 [(validate.rules).string.min_len = 1];
}

message FiniteCodeSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 52];

  oneof option {
    FiniteCodeSmsOptionV1 finite_code_sms_option_v1 = 2;
  }
}

message CboFiniteCodeSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string finite_code = 2 [(validate.rules).string.min_len = 1];
}

message CboFiniteCodeSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 53];

  oneof option {
    CboFiniteCodeSmsOptionV1 cbo_finite_code_sms_option_v1 = 2;
  }
}

message VkycSixWeeksBeforeSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message VkycSixWeeksBeforeSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 54];

  oneof option {
    VkycSixWeeksBeforeSmsOptionV1 vkyc_six_weeks_before_sms_option_v1 = 2;
  }
}

message VkycFourWeeksBeforeSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  google.protobuf.Timestamp account_freeze_date = 3 [(validate.rules).timestamp.required = true];
}

message VkycFourWeeksBeforeSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 55];

  oneof option {
    VkycFourWeeksBeforeSmsOptionV1 vkyc_four_weeks_before_sms_option_v1 = 2;
  }
}

message VkycTenDaysBeforeSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.protobuf.Timestamp account_freeze_date = 2 [(validate.rules).timestamp.required = true];
}

message VkycTenDaysBeforeSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 56];

  oneof option {
    VkycTenDaysBeforeSmsOptionV1 vkyc_ten_days_before_sms_option_v1 = 2;
  }
}

message CboFiniteCodeReminderSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  string finite_code = 2 [(validate.rules).string.min_len = 1];
}

message CboFiniteCodeReminderSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 61];

  oneof option {
    CboFiniteCodeReminderSmsOptionV1 cbo_finite_code_reminder_sms_option_v1 = 2;
  }
}

message NonCBOReminderSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  string finite_code = 2 [(validate.rules).string.min_len = 1];

  string name = 3 [(validate.rules).string.min_len = 1];
}

message NonCBOReminderSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 63];

  oneof option {
    NonCBOReminderSmsOptionV1 non_cbo_reminder_sms_option_v1 = 2;
  }
}

message GenericPiDebitUnclearBeneficiaryDetailsSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money transaction_amount = 2 [(validate.rules).message.required = true];

  string account_number_last_four_digits = 3 [(validate.rules).string.min_len = 4];

  google.protobuf.Timestamp transaction_timestamp = 4 [(validate.rules).timestamp.required = true];
}

message GenericPiDebitUnclearBeneficiaryDetailsSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 65];

  oneof option {
    GenericPiDebitUnclearBeneficiaryDetailsSmsOptionV1 generic_pi_debit_unclear_beneficary_details_sms_option_v1 = 2;
  }
}

message CommunityLoginOtpSmsOptionV1 {
  string otp = 1 [(validate.rules).string.min_len = 1];
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message CommunityLoginOtpSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 66];

  oneof option {
    CommunityLoginOtpSmsOptionV1 community_login_otp_sms_option_v1 = 2;
  }
}

message OnboardingKYCCompleteSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message OnboardingKYCCompleteSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 70];

  oneof option {
    OnboardingKYCCompleteSmsOptionV1 onboarding_kyc_complete_sms_option_v1 = 2;
  }
}

message FITSmartDepositAddFundsSMSOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 71];

  oneof option {
    FITSmartDepositAddFundsSMSOptionV1 option_v1 = 2;
  }
}

message FITSmartDepositAddFundsSMSOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string smart_deposit_acc_no = 2 [(validate.rules).string.min_len = 1];
  string savings_acc_no = 3 [(validate.rules).string.min_len = 1];
  string rule_name = 4 [(validate.rules).string.min_len = 1];
  uint32 exec_count = 5 [(validate.rules).uint32.gt = 0];
  google.protobuf.Timestamp exec_date = 6 [(validate.rules).timestamp.required = true];
  google.type.Money deposit_amount = 7 [(validate.rules).message.required = true];
}

message CashWithdrawalAtmFallbackSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money withdrawn_amount = 2 [(validate.rules).message.required = true];

  google.protobuf.Timestamp transaction_timestamp = 3 [(validate.rules).timestamp.required = true];
}

message CashWithdrawalAtmFallbackSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 72];

  oneof option {
    CashWithdrawalAtmFallbackSmsOptionV1 cash_withdrawal_atm_sms_option_v1 = 2;
  }
}

message PosDebitFallbackSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 73];

  oneof option {
    PosDebitFallbackSmsOptionV1 pos_debit_sms_option_v1 = 2;
  }
}

message PosDebitFallbackSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money transaction_amount = 2 [(validate.rules).message.required = true];

  google.protobuf.Timestamp transaction_timestamp = 3 [(validate.rules).timestamp.required = true];
}

message NeftDebitFallbackSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  google.type.Money transaction_amount = 3 [(validate.rules).message.required = true];

  api.typesv2.common.Name receiver_name = 4 [(validate.rules).message.required = true];

  string payment_mode = 5 [(validate.rules).string.min_len = 1];

  google.protobuf.Timestamp transaction_timestamp = 6 [(validate.rules).timestamp.required = true];

  string reference_number = 7 [(validate.rules).string.min_len = 1];
}

message NeftDebitFallbackSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 74];

  oneof option {
    NeftDebitFallbackSmsOptionV1 neft_debit_sms_option_v1 = 2;
  }
}

message NeftCreditFallbackSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  google.type.Money transaction_amount = 3 [(validate.rules).message.required = true];

  api.typesv2.common.Name sender_name = 4 [(validate.rules).message.required = true];

  string payment_mode = 5 [(validate.rules).string.min_len = 1];

  google.protobuf.Timestamp transaction_timestamp = 6 [(validate.rules).timestamp.required = true];

  string reference_number = 7 [(validate.rules).string.min_len = 1];
}

message NeftCreditFallbackSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 75];

  oneof option {
    NeftCreditFallbackSmsOptionV1 neft_credit_sms_option_v1 = 2;
  }
}

message RtgsDebitFallbackSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

  api.typesv2.common.Name receiver_name = 3 [(validate.rules).message.required = true];

  google.type.Money transaction_amount = 4 [(validate.rules).message.required = true];

  google.protobuf.Timestamp transaction_timestamp = 5 [(validate.rules).timestamp.required = true];

  string reference_number = 6 [(validate.rules).string.min_len = 1];

  string payment_mode = 7 [(validate.rules).string.min_len = 1];
}

message RtgsDebitFallbackSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 76];

  oneof option {
    RtgsDebitFallbackSmsOptionV1 rtgs_debit_sms_option_v1 = 2;
  }
}

message InterestPaidInSBFallbackSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money interest_amount = 2 [(validate.rules).message.required = true];

  string account_number_last_four_digits = 3 [(validate.rules).string.min_len = 4];

  google.protobuf.Timestamp timestamp = 4 [(validate.rules).timestamp.required = true];
}

message InterestPaidInSBFallbackSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 77];

  oneof option {
    InterestPaidInSBFallbackSmsOptionV1 interest_paid_in_sb_sms_option_v1 = 2;
  }
}

message MandateReceivedSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 78];

  oneof option {
    MandateReceivedSmsOptionV1 mandate_received_v1 = 2;
  }
}

message MandateReceivedSmsOptionV1 {

  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money mandate_amount = 2 [(validate.rules).message.required = true];

  api.typesv2.common.Name payee_name = 3 [(validate.rules).message.required = true];
}

message MandateApprovedSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 79];

  oneof option {
    MandateApprovedSmsOptionV1 mandate_approved_v1 = 2;
  }
}

message MandateApprovedSmsOptionV1 {

  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money mandate_amount = 2 [(validate.rules).message.required = true];

  string mandate_start_date = 3 [(validate.rules).string.min_len = 1];

  //  [deprecated in favour of payee_name]
  string payee_id = 4 [deprecated = true];

  string mandate_frequency = 5 [(validate.rules).string.min_len = 1];

  api.typesv2.common.Name payee_name = 6 [(validate.rules).message.required = true];
}

message MandateDeclinedSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 80];

  oneof option {
    MandateDeclinedSmsOptionV1 mandate_declined_v1 = 2;
  }
}

message MandateDeclinedSmsOptionV1 {

  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money mandate_amount = 2 [(validate.rules).message.required = true];

  string mandate_execution_date = 3 [(validate.rules).string.min_len = 1];

  //  [deprecated in favour of payee_name]
  string payee_id = 4 [deprecated = true];

  string mandate_frequency = 5 [(validate.rules).string.min_len = 1];

  api.typesv2.common.Name payee_name = 6 [(validate.rules).message.required = true];
}

message MandateCreatedSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 81];

  oneof option {
    MandateCreatedSmsOptionV1 mandate_created_v1 = 2;
  }
}

message MandateCreatedSmsOptionV1 {

  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money mandate_amount = 2 [(validate.rules).message.required = true];

  api.typesv2.common.Name payer_name = 3 [(validate.rules).message.required = true];

  api.typesv2.common.Name payee_name = 4 [(validate.rules).message.required = true];

  string mandate_frequency = 5 [(validate.rules).string.min_len = 1];
}

message MandateExecutionSuccessfulSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 82];

  oneof option {
    MandateExecutionSuccessfulSmsOptionV1 mandate_execution_successful_v1 = 2;
  }
}

message MandateExecutionSuccessfulSmsOptionV1 {

  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money mandate_amount = 2 [(validate.rules).message.required = true];

  string mandate_execution_date = 3 [(validate.rules).string.min_len = 1];

  //  [deprecated in favour of payee_name]
  string payee_id = 4 [deprecated = true];

  api.typesv2.common.Name payee_name = 5 [(validate.rules).message.required = true];
}

message MandateExecutionFailedSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 83];

  oneof option {
    MandateExecutionFailedSmsOptionV1 mandate_execution_failed_v1 = 2;
  }
}

message MandateExecutionFailedSmsOptionV1 {

  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money mandate_amount = 2 [(validate.rules).message.required = true];

  string mandate_execution_date = 3 [(validate.rules).string.min_len = 1];

  //  [deprecated in favour of payee_name]
  string payee_id = 4 [deprecated = true];

  api.typesv2.common.Name payee_name = 5 [(validate.rules).message.required = true];
}


message MandateRevokedSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 84];

  oneof option {
    MandateRevokedSmsOptionV1 mandate_revoked_v1 = 2;
  }
}

message MandateRevokedSmsOptionV1 {

  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money mandate_amount = 2 [(validate.rules).message.required = true];

  api.typesv2.common.Name payee_name = 3 [(validate.rules).message.required = true];
}

message MandateModifiedSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 85];

  oneof option {
    MandateModifiedSmsOptionV1 mandate_modified_v1 = 2;
  }
}

message MandateModifiedSmsOptionV1 {

  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money mandate_amount = 2 [(validate.rules).message.required = true];

  api.typesv2.common.Name payee_name = 3 [(validate.rules).message.required = true];
}

message ManualLivenessPassedSmsOption {

  SmsType sms_type = 1 [(validate.rules).enum.const = 86];

  oneof option {
    ManualLivenessPassedSmsOptionV1 manual_liveness_passed_v1 = 2;
  }

}

message ManualLivenessPassedSmsOptionV1 {

  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];

}


message TransactionReversedSmsOption {

  SmsType sms_type = 1 [(validate.rules).enum.const = 87];

  oneof option {
    TransactionReversedSmsOptionV1 transaction_reversed_v1 = 2;
  }
}

message TransactionReversedSmsOptionV1 {

  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money amount = 2 [(validate.rules).message.required = true];
}

message SICreatedSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 88];

  oneof option {
    SICreatedSmsOptionV1 si_created_v1 = 2;
  }
}

message SICreatedSmsOptionV1 {

  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money si_amount = 2 [(validate.rules).message.required = true];
  string si_frequency = 3 [(validate.rules).string.min_len = 1];
  api.typesv2.common.Name payee_name = 4 [(validate.rules).message.required = true];

}

message SIDeclinedSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 89];

  oneof option {
    SIDeclinedSmsOptionV1 si_declined_v1 = 2;
  }
}

message SIDeclinedSmsOptionV1 {

  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money si_amount = 2 [(validate.rules).message.required = true];
  api.typesv2.common.Name payee_name = 3 [(validate.rules).message.required = true];

}

message SIExecutionSuccessfulSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 90];

  oneof option {
    SIExecutionSuccessfulSmsOptionV1 si_execution_successful_v1 = 2;
  }
}

message SIExecutionSuccessfulSmsOptionV1 {

  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money si_amount = 2 [(validate.rules).message.required = true];
  string si_execution_date = 3 [(validate.rules).string.min_len = 1];
  api.typesv2.common.Name payee_name = 4 [(validate.rules).message.required = true];

}

message SIExecutionFailedSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 91];

  oneof option {
    SIExecutionFailedSmsOptionV1 si_execution_failed_v1 = 2;
  }
}

message SIExecutionFailedSmsOptionV1 {

  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money si_amount = 2 [(validate.rules).message.required = true];
  api.typesv2.common.Name payee_name = 3 [(validate.rules).message.required = true];

}

message MandateAuthorisedSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 92];
  oneof option {
    MandateAuthorisedSmsOptionV1 mandate_authorised_v1 = 2;
  }
}

message MandateAuthorisedSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money amount = 2 [(validate.rules).message.required = true];
  api.typesv2.common.Name payee_name = 3 [(validate.rules).message.required = true];
}

message MandateAcceptanceSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 93];
  oneof option {
    MandateAcceptanceSmsOptionV1 mandate_acceptance_v1 = 2;
  }
}

message MandateAcceptanceSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money amount = 2 [(validate.rules).message.required = true];
  api.typesv2.common.Name payer_name = 3 [(validate.rules).message.required = true];
}

message MandatePausedSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 94];
  oneof option {
    MandatePausedSmsOptionV1 mandate_paused_v1 = 2;
  }
}

message MandatePausedSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name payer_name = 2 [(validate.rules).message.required = true];
  api.typesv2.common.Name payee_name = 3 [(validate.rules).message.required = true];
}

message MandateUnpausedSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 95];
  oneof option {
    MandateUnpausedSmsOptionV1 mandate_unpaused_v1 = 2;
  }
}

message MandateUnpausedSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name payee_name = 2 [(validate.rules).message.required = true];
}

message MutualFundWithdrawalOtpOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 96];
  oneof option {
    MutualFundWithdrawalOtpOptionV1 mutual_fund_withdrawal_otp_v1 = 2;
  }
}


message MutualFundOneTimeBuyOtpOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 134];
  oneof option {
    MutualFundOneTimeBuyOtpOptionV1 mutual_fund_one_time_buy_otp_v1 = 2;
  }
}


// {#otp#} is the OTP to confirm your lump sum investment in {#fund_name#}.
// Amount: INR <>. 82275JXpmM1 - Fi
message MutualFundOneTimeBuyOtpOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  // actual otp to be sent
  string otp = 2 [(validate.rules).string.min_len = 1];
  // name of mutual fund from which amount to be withdrawn
  string mutual_fund_name = 3 [(validate.rules).string.min_len = 1];

  google.type.Money amount = 4 [(validate.rules).message.required = true];
}

message MutualFundRegisterSIPOtpOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 135];
  oneof option {
    MutualFundRegisterSIPOtpOptionV1 mutual_fund_register_sip_otp_v1 = 2;
  }
}

// {#otp#} is the OTP to confirm your {#frequency#} investment in {#fund_name#}.
// Amount: INR <>. 82275JXpmM1 - Fi
message MutualFundRegisterSIPOtpOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  // actual otp to be sent
  string otp = 2 [(validate.rules).string.min_len = 1];
  // name of mutual fund from which amount to be withdrawn
  string mutual_fund_name = 3 [(validate.rules).string.min_len = 1];
  // amount to be withdrawn
  google.type.Money amount = 4 [(validate.rules).message.required = true];
  // frequency of the investment
  string frequency = 5;
}


// {#otp#} is the OTP to confirm your withdrawal from {#mutual_fund_name#}.
//  Amount: {#withdrawal_amount#}. 82275JXpmM1 -Fi
message MutualFundWithdrawalOtpOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  // actual otp to be sent
  string otp = 2 [(validate.rules).string.min_len = 1];
  // name of mutual fund from which amount to be withdrawn
  string mutual_fund_name = 3 [(validate.rules).string.min_len = 1];
  // amount to be withdrawn
  google.type.Money withdrawn_amount = 4 [(validate.rules).message.required = true];
}

message OBVkycReminderOneSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 97];
  oneof option {
    OBVkycReminderOneSmsOptionV1 ob_vkyc_reminder_one_sms_option_v1 = 2;
  }
}

message OBVkycReminderOneSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money reward_amount = 2;
}

message OBVkycReminderTwoSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 98];
  oneof option {
    OBVkycReminderTwoSmsOptionV1 ob_vkyc_reminder_two_sms_option_v1 = 2;
  }
}

message OBVkycReminderTwoSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message TodChargesDebitOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 99];

  oneof option {
    TodChargesDebitOptionV1 tod_charges_debit_option_v1 = 2;
  }
}

message TodChargesDebitOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money transaction_amount = 2 [(validate.rules).message.required = true];

  google.protobuf.Timestamp transaction_timestamp = 3 [(validate.rules).timestamp.required = true];
}

message OBScreenerVerficationReminderOneOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 100];
  oneof option {
    OBScreenerVerficationReminderOneOptionV1 ob_screener_verification_reminder_one_option_v1 = 2;
  }
}

message OBScreenerVerficationReminderOneOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message OBScreenerVerficationReminderTwoOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 101];
  oneof option {
    OBScreenerVerficationReminderTwoOptionV1 ob_screener_verification_reminder_two_option_v1 = 2;
  }
}

message OBScreenerVerficationReminderTwoOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message EcsReturnChargesSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money amount = 2 [(validate.rules).message.required = true];
}

message EcsReturnChargesSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 102];
  oneof option {
    EcsReturnChargesSmsOptionV1 ecs_return_charges_sms_option_v1 = 2;
  }
}

message AtmDeclineFeesSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money amount = 2 [(validate.rules).message.required = true];
}

message AtmDeclineFeesSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 103];
  oneof option {
    AtmDeclineFeesSmsOptionV1 atm_decline_fees_sms_option_v1 = 2;
  }
}

message DuplicateCardFeeSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money amount = 2 [(validate.rules).message.required = true];
}

message DuplicateCardFeeSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 104];
  oneof option {
    DuplicateCardFeeSmsOptionV1 duplicate_card_fee_sms_option_v1 = 2;
  }
}
message AtmWithdrawalComplaintPenaltySmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money amount = 2 [(validate.rules).message.required = true];
}

message AtmWithdrawalComplaintPenaltySmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 105];
  oneof option {
    AtmWithdrawalComplaintPenaltySmsOptionV1 atm_withdrawal_complaint_penalty_sms_option_v1 = 2;
  }
}

message InternationAtmChargesSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money amount = 2 [(validate.rules).message.required = true];
}

message InternationAtmChargesSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 106];
  oneof option {
    InternationAtmChargesSmsOptionV1 internation_atm_charges_sms_option_v1 = 2;
  }
}

message OtherBankAtmUsageChargesSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money amount = 2 [(validate.rules).message.required = true];
}

message OtherBankAtmUsageChargesSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 107];
  oneof option {
    OtherBankAtmUsageChargesSmsOptionV1 other_bank_atm_usage_charges_sms_option_v1 = 2;
  }
}

message VkycAccountClosureMoreThenZeroBalanceFortyFiveDaysSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 108];
  oneof option {
    VkycAccountClosureMoreThenZeroBalanceFortyFiveDaysSmsOptionV1 vkyc_account_closure_more_then_zero_balance_forty_five_days_sms_option_v1 = 2;
  }
}

message VkycAccountClosureMoreThenZeroBalanceFortyFiveDaysSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string amount = 2 [(validate.rules).string.min_len = 1];
  string remaining_days = 3 [(validate.rules).string.min_len = 1];
  string link = 4 [(validate.rules).string.min_len = 1];
}

message VkycAccountClosureMoreThenZeroBalanceThirtyDaysSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 109];
  oneof option {
    VkycAccountClosureMoreThenZeroBalanceThirtyDaysSmsOptionV1 vkyc_account_closure_more_then_zero_balance_thirty_days_sms_option_v1 = 2;
  }
}

message VkycAccountClosureMoreThenZeroBalanceThirtyDaysSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string amount = 2 [(validate.rules).string.min_len = 1];
  string remaining_days = 3 [(validate.rules).string.min_len = 1];
  string link = 4 [(validate.rules).string.min_len = 1];
}

message VkycAccountClosureMoreThenZeroBalanceElevenDaysSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 110];
  oneof option {
    VkycAccountClosureMoreThenZeroBalanceElevenDaysSmsOptionV1 vkyc_account_closure_more_then_zero_balance_eleven_days_sms_option_v1 = 2;
  }
}

message VkycAccountClosureMoreThenZeroBalanceElevenDaysSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string amount = 2 [(validate.rules).string.min_len = 1];
  string remaining_days = 3 [(validate.rules).string.min_len = 1];
  string link = 4 [(validate.rules).string.min_len = 1];
}

message VkycAccountClosureMoreThenZeroBalanceFiveDaysSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 111];
  oneof option {
    VkycAccountClosureMoreThenZeroBalanceFiveDaysSmsOptionV1 vkyc_account_closure_more_then_zero_balance_five_days_sms_option_v1 = 2;
  }
}

message VkycAccountClosureMoreThenZeroBalanceFiveDaysSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string amount = 2 [(validate.rules).string.min_len = 1];
  string remaining_days = 3 [(validate.rules).string.min_len = 1];
  string link = 4 [(validate.rules).string.min_len = 1];
}

message VkycAccountClosureMoreThenZeroBalanceThreeDaysSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 112];
  oneof option {
    VkycAccountClosureMoreThenZeroBalanceThreeDaysSmsOptionV1 vkyc_account_closure_more_then_zero_balance_three_days_sms_option_v1 = 2;
  }
}

message VkycAccountClosureMoreThenZeroBalanceThreeDaysSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string amount = 2 [(validate.rules).string.min_len = 1];
  string remaining_days = 3 [(validate.rules).string.min_len = 1];
  string link = 4 [(validate.rules).string.min_len = 1];
}

message VkycAccountClosureMoreThenZeroBalanceOneDaySmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 113];
  oneof option {
    VkycAccountClosureMoreThenZeroBalanceOneDaySmsOptionV1 vkyc_account_closure_more_then_zero_balance_one_day_sms_option_v1 = 2;
  }
}

message VkycAccountClosureMoreThenZeroBalanceOneDaySmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message OnboardingAccountBlockDayZeroSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 114];
  oneof option {
    OnboardingAccountBlockDayZeroSmsOptionV1 onboarding_account_block_day_zero_sms_option_v1 = 2;
  }
}

message OnboardingAccountBlockDayZeroSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string user_name = 2 [(validate.rules).string.min_len = 1];
  string email = 3 [(validate.rules).string.min_len = 1];
}

message OnboardingAccountUnblockSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 115];
  oneof option {
    OnboardingAccountUnblockSmsOptionV1 onboarding_account_unblock_sms_option_v1 = 2;
  }
}

message OnboardingAccountUnblockSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string user_name = 2 [(validate.rules).string.min_len = 1];
}

message WealthAccountNomineeDeclarationOTPOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 116];
  oneof option {
    WealthAccountNomineeDeclarationOTPOptionV1 wealth_account_nominee_declaration_otp_v1 = 2;
  }
}

// <#>Your OTP code is <564739>. Regarding the Nomination Declaration for all
// upcoming mutual fund investments on Fi, you have: <Opted Out/ Opted In>. 82275JXpmM1 - Fi
message WealthAccountNomineeDeclarationOTPOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  // actual otp to be sent
  string otp = 2 [(validate.rules).string.min_len = 1];

  // can be "Opted In" or "Opted Out"
  string choice = 3 [(validate.rules).string.min_len = 1];
}

message OnboardingDobAndPanDropOffSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 118];
  oneof option {
    OnboardingDobAndPanDropOffSmsOptionV1 onboarding_dob_and_pan_drop_off_sms_option_v1 = 2;
  }
}

message OnboardingDobAndPanDropOffSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string user_name = 2 [(validate.rules).string.min_len = 1];
  string deep_link = 3 [(validate.rules).string.min_len = 1];
}

message CreditReportDownloadOtpSmsOptionV1 {
  // actual otp to be sent
  string otp = 1 [(validate.rules).string.min_len = 1];
  // client signature - required by client to auto-read OTP
  string hash = 2;
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 3 [(validate.rules).enum.const = 1];
}

// each template will have certain options which are the variables that will be filled in the template
message CreditReportDownloadOtpSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 119];

  oneof option {
    CreditReportDownloadOtpSmsOptionV1 credit_report_download_otp_sms_option_v1 = 2;
  }
}

message CreditCardCrossBorderTransactionSuccessSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money amount = 2;
  string beneficiary_name = 3;
  float currency_exchange_rate = 5;
  google.type.Money markup_fee = 6;
}

message CreditCardCrossBorderTransactionSuccessSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 120];

  oneof option {
    CreditCardCrossBorderTransactionSuccessSmsOptionV1 credit_card_cross_border_transaction_success_sms_option_v1 = 2;
  }
}

message CreditCardAtmTransactionFailureSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money amount = 2;
}

message CreditCardAtmTransactionFailureSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 121];

  oneof option {
    CreditCardAtmTransactionFailureSmsOptionV1 credit_card_atm_transaction_failure_sms_option_v1 = 2;
  }
}

message CreditCardAtmTransactionSuccessSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string first_name = 2;
  google.type.Money amount = 3;
  google.type.Money available_account_balance = 4;
  string helpline_number = 5;
}

message CreditCardAtmTransactionSuccessSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 122];

  oneof option {
    CreditCardAtmTransactionSuccessSmsOptionV1 credit_card_atm_transaction_success_sms_option_v1 = 2;
  }
}

message CreditCardFailedTransactionReversalSuccessSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money amount = 2;
  google.type.Money available_account_balance = 3;
  string account_number = 5;
  google.type.Date reversal_date = 6;
}

message CreditCardFailedTransactionReversalSuccessSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  google.type.Money amount = 2;
  google.type.Money available_account_balance = 3;
  string cc_ending_digits = 4;
  google.type.Date reversal_date = 5;
}

message CreditCardFailedTransactionReversalSuccessSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 123];

  oneof option {
    CreditCardFailedTransactionReversalSuccessSmsOptionV1 credit_card_failed_transaction_reversal_success_sms_option_v1 = 2;
    CreditCardFailedTransactionReversalSuccessSmsOptionV2 credit_card_failed_transaction_reversal_success_sms_option_v2 = 3;
  }
}

message CreditCardTransactionDeclinedSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money amount = 2;
  string beneficiary = 3;
  google.type.Date txn_date = 4;
}

message CreditCardTransactionDeclinedSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  google.type.Money amount = 2;
  string beneficiary = 3;
  google.type.Date txn_date = 4;
  string cc_ending_digits = 5;
}

message CreditCardTransactionDeclinedSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 124];

  oneof option {
    CreditCardTransactionDeclinedSmsOptionV1 credit_card_transaction_declined_sms_option_v1 = 2;
    CreditCardTransactionDeclinedSmsOptionV2 credit_card_transaction_declined_sms_option_v2 = 3;
  }
}

message CreditCardTransactionSuccessSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money amount = 2;
  string beneficiary = 3;
  google.type.Date txn_date = 4;
  string helpline_number = 5;
}

message CreditCardTransactionSuccessSmsOptionV2 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  google.type.Money amount = 2;
  string beneficiary = 3;
  google.protobuf.Timestamp txn_timestamp = 4;
  string helpline_number = 5;
  string cc_ending_digits = 6;
}

message CreditCardTransactionSuccessSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 125];

  oneof option {
    CreditCardTransactionSuccessSmsOptionV1 credit_card_transaction_success_sms_option_v1 = 2;
    CreditCardTransactionSuccessSmsOptionV2 credit_card_transaction_success_sms_option_v2 = 3;
  }
}

message CreditCardControlsSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string customer_first_name = 2;
  // credit card type can be different for different rewards construct. For ex: 5x
  string credit_card_type = 3;
  // last four digits of the credit card.
  string last_four_digits = 4;
}

message CreditCardInternationalTransactionsDisabledSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 126];

  oneof option {
    CreditCardControlsSmsOptionV1 credit_card_controls_sms_option_v1 = 2;
  }
}

message CreditCardInternationalTransactionsEnabledSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 127];
  oneof option {
    CreditCardControlsSmsOptionV1 credit_card_controls_sms_option_v1 = 2;
  }
}

message CreditCardPosTransactionsDisabledSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 128];
  oneof option {
    CreditCardControlsSmsOptionV1 credit_card_controls_sms_option_v1 = 2;
  }
}

message CreditCardPosTransactionsEnabledSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 129];
  oneof option {
    CreditCardControlsSmsOptionV1 credit_card_controls_sms_option_v1 = 2;
  }
}

message CreditCardContactlessTransactionsDisabledSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 130];
  oneof option {
    CreditCardControlsSmsOptionV1 credit_card_controls_sms_option_v1 = 2;
  }
}

message CreditCardContactlessTransactionsEnabledSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 131];
  oneof option {
    CreditCardControlsSmsOptionV1 credit_card_controls_sms_option_v1 = 2;
  }
}

message CreditCardOnlineTransactionsDisabledSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 132];
  oneof option {
    CreditCardControlsSmsOptionV1 credit_card_controls_sms_option_v1 = 2;
  }
}

message CreditCardOnlineTransactionsEnabledSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 133];
  oneof option {
    CreditCardControlsSmsOptionV1 credit_card_controls_sms_option_v1 = 2;
  }
}

message ReminderSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string amount = 2;
  string category = 3;
  string merchant = 4;
  google.protobuf.Timestamp date = 5;
  string deeplink = 6 [(validate.rules).string.min_len = 1];
}

message CategorySpendsExceededReminderSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 136];
  oneof option {
    ReminderSmsOptionV1 reminder_sms_option_v1 = 2;
    ReminderCategorySpendsSmsOption reminder_category_spends_sms_option = 3;
  }
}

message ReminderCategorySpendsSmsOption {
  // to be set as VERSION_V2, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  string amount = 2;
  string category = 3;
  // denotes the link to be redirected to
  string deeplink = 4;
  string configured_amount = 5;
}


message AmountSpendsExceededReminderSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 137];
  oneof option {
    ReminderSmsOptionV1 reminder_sms_option_v1 = 2;
    ReminderAmountSpendsSmsOption reminder_amount_spends_sms_option = 3;
  }
}

message ReminderAmountSpendsSmsOption {
  // to be set as VERSION_V2, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  string amount = 2;
  // denotes the link to be redirected to
  string deeplink = 3;
  string configured_amount = 4;
}


message CreditCardBillPaymentDueDateReminderSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 138];
  oneof option {
    ReminderSmsOptionV1 reminder_sms_option_v1 = 2;
    ReminderCcDueDateSmsOption reminder_cc_due_date_sms_option = 3;
  }
}

message ReminderCcDueDateSmsOption {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.protobuf.Timestamp date = 2;
  // denotes the link to be redirected to
  string deeplink = 3;
  string amount = 4;
}

// Your UPI PIN has been successfully generated via Fi. If you didn’t do this, inform Fi Care immediately at ***********.
// - Fi
// Gentle reminder: For the next 24 hours, there’s a cooling period limit of ₹5000 on your account. P.S Do not share UPI PIN/Card info/OTP/CVV with anyone!
// - Fi
message UpiPinSetResetSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message UpiPinSetResetSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 139];
  oneof option {
    UpiPinSetResetSmsOptionV1 upi_pin_set_reset_sms_option_v1 = 2;
  }
}

message CreditCardPinTriesExceededForTransactionsSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 140];

  oneof option {
    CreditCardPinTriesExceededForTransactionsSmsOptionV1 credit_card_pin_tries_exceeded_for_transactions_sms_option_v1 = 2;
  }
}
message CreditCardPaymentNotDoneReminderWithInterestChargeSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 141];

  oneof option {
    CreditCardPaymentNotDoneReminderWithInterestChargeSmsOptionV1 credit_card_payment_not_done_reminder_with_interest_charge_sms_option_v1 = 2;
  }
}
message CreditCardPaymentNotDoneReminderSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 142];

  oneof option {
    CreditCardPaymentNotDoneReminderSmsOptionV1 credit_card_payment_not_done_reminder_sms_option_v1 = 2;
  }
}
message CreditCardBillSuccessfulRepaymentSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 143];

  oneof option {
    CreditCardBillSuccessfulRepaymentSmsOptionV1 credit_card_bill_successful_repayment_sms_option_v1 = 2;
  }
}
message CreditCardBillRepaymentDueSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 144];

  oneof option {
    CreditCardBillRepaymentDueSmsOptionV1 credit_card_bill_repayment_due_sms_option_v1 = 2;
  }
}
message CreditCardStatementGenerationSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 145];

  oneof option {
    CreditCardStatementGenerationSmsOptionV1 credit_card_statement_generation_sms_option_v1 = 2;
  }
}
message CreditCardCommunicateTransactionChargesSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 146];

  oneof option {
    CreditCardCommunicateTransactionChargesSmsOptionV1 credit_card_communicate_transaction_charges_sms_option_v1 = 2;
  }
}
message CreditCardReplacementSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 147];

  oneof option {
    CreditCardReplacementSmsOptionV1 credit_card_replacement_sms_option_v1 = 2;
  }
}
message CreditCardLimitChangeFailureSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 148];

  oneof option {
    CreditCardLimitChangeFailureSmsOptionV1 credit_card_limit_change_failure_sms_option_v1 = 2;
  }
}
message CreditCardCardUsageChangeFailureSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 149];

  oneof option {
    CreditCardCardUsageChangeFailureSmsOptionV1 credit_card_card_usage_change_failure_sms_option_v1 = 2;
  }
}
message CreditCardContactlessPurchaseLimitChangedSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 150];

  oneof option {
    CreditCardContactlessPurchaseLimitChangedSmsOptionV1 credit_card_contactless_purchase_limit_changed_sms_option_v1 = 2;
  }
}
message CreditCardOnlinePurchaseLimitChangedSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 151];

  oneof option {
    CreditCardOnlinePurchaseLimitChangedSmsOptionV1 credit_card_online_purchase_limit_changed_sms_option_v1 = 2;
  }
}
message CreditCardPosPurchaseLimitChangedSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 152];

  oneof option {
    CreditCardPosPurchaseLimitChangedSmsOptionV1 credit_card_pos_purchase_limit_changed_sms_option_v1 = 2;
  }
}
message CreditCardUnfreezingFailureSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 153];

  oneof option {
    CreditCardControlsWithHelplineSmsOptionV1 credit_card_controls_with_helpline_sms_option_v1 = 2;
  }
}

message CreditCardFreezingFailureSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 154];

  oneof option {
    CreditCardControlsWithHelplineSmsOptionV1 credit_card_controls_with_helpline_sms_option_v1 = 2;
  }
}

message CreditCardUnfreezingSuccessSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 155];

  oneof option {
    CreditCardControlsSmsOptionV1 credit_card_controls_sms_option_v1 = 2;
  }
}

message CreditCardFreezingSuccessSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 156];

  oneof option {
    CreditCardFreezingSuccessSmsOptionV1 credit_card_freezing_success_sms_option_v1 = 2;
  }
}
message CreditCardPinChangeSuccessSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 157];

  oneof option {
    CreditCardPinChangeSuccessSmsOptionV1 credit_card_pin_change_success_sms_option_v1 = 2;
  }
}
message CreditCardActivationFailureSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 158];

  oneof option {
    CreditCardActivationFailureSmsOptionV1 credit_card_activation_failure_sms_option_v1 = 2;
  }
}
message CreditCardPhysicalCardActivationSuccessSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 159];

  oneof option {
    CreditCardPhysicalCardActivationSuccessSmsOptionV1 credit_card_physical_card_activation_success_sms_option_v1 = 2;
  }
}
message CreditCardDigitalCardActivationSuccessSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 160];

  oneof option {
    CreditCardDigitalCardActivationSuccessSmsOptionV1 credit_card_digital_card_activation_success_sms_option_v1 = 2;
  }
}
message CreditCardActivationInformationSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 161];

  oneof option {
    CreditCardActivationInformationSmsOptionV1 credit_card_activation_information_sms_option_v1 = 2;
  }
}
message CreditCardShipmentDelaySmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 162];

  oneof option {
    CreditCardShipmentDelaySmsOptionV1 credit_card_shipment_delay_sms_option_v1 = 2;
  }
}
message CreditCardDisptachDelaySmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 163];

  oneof option {
    CreditCardDisptachDelaySmsOptionV1 credit_card_disptach_delay_sms_option_v1 = 2;
  }
}
message CreditCardDisptachedWithTrackingNumberSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 164];

  oneof option {
    CreditCardDisptachedWithTrackingNumberSmsOptionV1 credit_card_disptached_with_tracking_number_sms_option_v1 = 2;
  }
}
message CreditCardIssuedWithCreditLimitSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 165];

  oneof option {
    CreditCardIssuedWithCreditLimitSmsOptionV1 credit_card_issued_with_credit_limit_sms_option_v1 = 2;
  }
}
message CreditCardIssuedSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 166];

  oneof option {
    CreditCardIssuedSmsOptionV1 credit_card_issued_sms_option_v1 = 2;
  }
}
message CreditCardIncompleteApplicationProcessSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 167];

  oneof option {
    CreditCardIncompleteApplicationProcessSmsOptionV1 credit_card_incomplete_application_process_sms_option_v1 = 2;
  }
}
message CreditCardCompleteVideoKycForCreditCardSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 168];

  oneof option {
    CreditCardCompleteVideoKycForCreditCardSmsOptionV1 credit_card_complete_video_kyc_for_credit_card_sms_option_v1 = 2;
  }
}
message CreditCardOtpForChangingCardPinSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 169];

  oneof option {
    CreditCardOtpForChangingCardPinSmsOptionV1 credit_card_otp_for_changing_card_pin_sms_option_v1 = 2;
  }
}
message CreditCardRewardPointsCreditedSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 170];

  oneof option {
    CreditCardRewardPointsCreditedSmsOptionV1 credit_card_reward_points_credited_sms_option_v1 = 2;
  }
}

message CreditCardLimitReachingThresholdSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 171];

  oneof option {
    CreditCardLimitReachingThresholdSmsOptionV1 credit_card_limit_reaching_threshold_sms_option_v1 = 2;
  }
}

message CreditCardPinTriesExceededForTransactionsSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string card_last_four_digits = 2 [(validate.rules).string.min_len = 4];
  string helpline_number = 3;
}

message CreditCardPaymentNotDoneReminderWithInterestChargeSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  // credit card type can be different for different rewards construct. For ex: 5x
  string credit_card_type = 2;
}

message CreditCardPaymentNotDoneReminderSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name first_name = 2 [(validate.rules).message.required = true];
  string masked_card_number = 3;
}

message CreditCardBillSuccessfulRepaymentSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name first_name = 2 [(validate.rules).message.required = true];
  string masked_card_number = 3;
  // credit card type can be different for different rewards construct. For ex: 5x
  string credit_card_type = 4;
}


message CreditCardBillRepaymentDueSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name first_name = 2 [(validate.rules).message.required = true];
  string days_left = 3;
  string payment_link = 4;
}

message CreditCardStatementGenerationSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name first_name = 2 [(validate.rules).message.required = true];
  string masked_card_number = 3;
}

message CreditCardCommunicateTransactionChargesSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money withdrawn_amount = 2 [(validate.rules).message.required = true];
}

message CreditCardReplacementSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message CreditCardLimitChangeFailureSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name first_name = 2 [(validate.rules).message.required = true];
}

message CreditCardCardUsageChangeFailureSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message CreditCardContactlessPurchaseLimitChangedSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name first_name = 2 [(validate.rules).message.required = true];
  string helpline_number = 3;
}

message CreditCardOnlinePurchaseLimitChangedSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name first_name = 2 [(validate.rules).message.required = true];
  string helpline_number = 3;
}

message CreditCardPosPurchaseLimitChangedSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string helpline_number = 2;
}


message CreditCardControlsWithHelplineSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string customer_first_name = 2;
  // credit card type can be different for different rewards construct. For ex: 5x
  string credit_card_type = 3;
  // last four digits of the credit card.
  string last_four_digits = 4;
  string helpline_number = 5;
}

message CreditCardFreezingSuccessSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  // credit card type can be different for different rewards construct. For ex: 5x
  string credit_card_type = 2;
  // last four digits of the credit card.
  string last_four_digits = 3;
}

message CreditCardPinChangeSuccessSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name first_name = 2 [(validate.rules).message.required = true];
  string helpline_number = 3;
}

message CreditCardActivationFailureSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string helpline_number = 2;
}


message CreditCardPhysicalCardActivationSuccessSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message CreditCardDigitalCardActivationSuccessSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message CreditCardActivationInformationSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message CreditCardShipmentDelaySmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name first_name = 2 [(validate.rules).message.required = true];
  string tracking_id = 3;
  string tracking_link = 4;
}

message CreditCardDisptachDelaySmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message CreditCardDisptachedWithTrackingNumberSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name first_name = 2 [(validate.rules).message.required = true];
  // credit card type can be different for different rewards construct. For ex: 5x
  string credit_card_type = 3;
  string tracking_name = 4;
}

message CreditCardIssuedWithCreditLimitSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money credit_limit = 2 [(validate.rules).message.required = true];
}

message CreditCardIssuedSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string credit_card_type = 3;
}

message CreditCardIncompleteApplicationProcessSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money credit_limit = 2 [(validate.rules).message.required = true];
  string complete_application_link = 3;
}

message CreditCardCompleteVideoKycForCreditCardSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name first_name = 2 [(validate.rules).message.required = true];
  google.type.Money credit_limit = 3 [(validate.rules).message.required = true];
  string complete_application_link = 4;
}

message CreditCardOtpForChangingCardPinSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  // actual otp to be sent
  string otp = 2 [(validate.rules).string.min_len = 1];
}

message CreditCardRewardPointsCreditedSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name first_name = 2 [(validate.rules).message.required = true];
  string coins = 3;
}

message CreditCardLimitReachingThresholdSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  int32 threshold_percentage = 2;
  google.type.Money available_limit = 3;
}

message CreditCardTransactionDeclinedWithReasonSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money amount = 2;
  string beneficiary = 3;
  google.protobuf.Timestamp txn_time = 4;
  string cc_ending_digits = 5;
  string reason = 6;
}

message CreditCardTransactionDeclinedWithReasonSmsOptionV2 {
  // to be set as VERSION_V2, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  google.type.Money amount = 2;
  string beneficiary = 3;
  google.protobuf.Timestamp txn_time = 4;
  string cc_ending_digits = 5;
  string reason = 6;
  // Action item contains what a customer should do along with a link if any.
  string action_item = 7;
}

message CreditCardTransactionDeclinedWithReasonSmsOptionV3 {
  // to be set as VERSION_V3, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 3];
  // The detailed reason for failure of the transaction. This will be the first line of the SMS
  string txn_failure_reason = 2;
  // This contains an explanation about what the user can do. Also will contain an action item link(if applicable). This will be the second line of the SMS
  string txn_failure_fix = 3;
}

message CreditCardTransactionDeclinedWithReasonSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 175];

  oneof option {
    CreditCardTransactionDeclinedWithReasonSmsOptionV1 credit_card_transaction_declined_with_reason_sms_option_v1 = 2;
    CreditCardTransactionDeclinedWithReasonSmsOptionV2 credit_card_transaction_declined_with_reason_sms_option_v2 = 3;
    CreditCardTransactionDeclinedWithReasonSmsOptionV3 credit_card_transaction_declined_with_reason_sms_option_v3 = 4;
  }
}

message CreditCardFeeSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money amount = 2;
  google.protobuf.Timestamp txn_time = 3;
  string cc_ending_digits = 4;
}

message CreditCardJoiningFeeSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 172];

  oneof option {
    CreditCardFeeSmsOptionV1 credit_card_joining_fee_sms_option_v1 = 2;
  }
}

message CreditCardUnpaidDueFeesSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 173];

  oneof option {
    CreditCardFeeSmsOptionV1 credit_card_unpaid_due_fees_sms_option_v1 = 2;
  }
}


message CreditCardGenericCreditSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money amount = 2;
  google.protobuf.Timestamp txn_time = 4;
  string cc_ending_digits = 5;
}

message CreditCardGenericCreditSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 174];

  oneof option {
    CreditCardGenericCreditSmsOptionV1 credit_card_generic_credit_sms_option_v1 = 2;
  }
}

message PlLoanAgreementOtpSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  // actual otp to be sent
  string otp = 2 [(validate.rules).string.min_len = 1];
  // TODO(harish): add more items once clarity on SMS template
}

message PlLoanAgreementOtpSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 176];

  oneof option {
    PlLoanAgreementOtpSmsOptionV1 pl_loan_agreement_otp_sms_option_v1 = 2;
  }
}

message CreditCardNotActivatedSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name customer_name = 2;
  string credit_card_last_four_digits = 3;
  int32 days_left_for_card_activation = 4;
  int32 total_inactive_days = 5;
}

message CreditCardNotActivatedSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 177];

  oneof option {
    CreditCardNotActivatedSmsOptionV1 credit_card_not_activated_sms_option_v1 = 2;
  }
}

message CreditCardClosureConfirmationSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name customer_name = 2;
  string cc_last_four_digits = 3;
}

message CreditCardClosureConfirmationSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 178];

  oneof option {
    CreditCardClosureConfirmationSmsOptionV1 credit_card_closure_confirmation_sms_option_v1 = 2;
  }
}

message SecuredCreditCardSuccessfulFdCreationSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money deposit_amount = 2;
  string interest_rate = 3;
  google.type.Date maturity_date = 4;
  api.typesv2.DepositTerm deposit_term = 5;
  string deposit_identifier = 6;
}

message SecuredCreditCardSuccessfulFdCreationSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 179];

  oneof option {
    SecuredCreditCardSuccessfulFdCreationSmsOptionV1 secured_credit_card_successful_fd_creation_sms_option_v1 = 2;
  }
}

message SecuredCreditCardFdLienMarkingIntimationSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string deposit_identifier = 2;
}

message SecuredCreditCardFdLienMarkingIntimationSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 180];

  oneof option {
    SecuredCreditCardFdLienMarkingIntimationSmsOptionV1 secured_credit_card_fd_lien_marking_intimation_sms_option_v1 = 2;
  }
}


message SecuredCreditCardFdClosureConfirmationSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string deposit_identifier = 2;
  google.type.Date fd_closure_date = 3;
  google.type.Money maturity_amount = 4;
  string masked_savings_acc_no = 5;
}

message SecuredCreditCardFdClosureConfirmationSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 181];

  oneof option {
    SecuredCreditCardFdClosureConfirmationSmsOptionV1 secured_credit_card_fd_closure_confirmation_sms_option_v1 = 2;
  }
}

message SecuredCreditCardFdClosureWarningSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name customer_name = 2;
  string cc_last_four_digits = 3;
}

message SecuredCreditCardFdClosureWarningSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 182];

  oneof option {
    SecuredCreditCardFdClosureWarningSmsOptionV1 secured_credit_card_fd_closure_warning_sms_option_v1 = 2;
  }
}

message RiskAccountFreezeSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string fi_contact_email = 2;
}

message RiskAccountFreezeSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 186];
  oneof option {
    RiskAccountFreezeSmsOptionV1 risk_account_freeze_sms_option_v1 = 2;
  }
}


message DebitCardUnableToProcessTransactionSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string download_link = 2;
}

message DebitCardUnableToProcessTransactionSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 188];

  oneof option {
    DebitCardUnableToProcessTransactionSmsOptionV1 debit_card_unable_to_process_transaction_sms_option_v1 = 2;
  }
}

message DebitCardIncorrectPinSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string download_link = 2;
}

message DebitCardIncorrectPinSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  string merchant = 2;
  string last_four_digit = 3;
  string download_link = 4;
}

message DebitCardIncorrectPinSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 189];

  oneof option {
    DebitCardIncorrectPinSmsOptionV1 debit_card_incorrect_pin_sms_option_v1 = 2;
    DebitCardIncorrectPinSmsOptionV2 debit_card_incorrect_pin_sms_option_v2 = 3;
  }
}

message DebitCardUnableToAuthorizeTransactionSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string download_link = 2;
}

message DebitCardUnableToAuthorizeTransactionSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  string merchant = 2;
  google.type.Money txn_amount = 3;
}

message DebitCardUnableToAuthorizeTransactionSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 190];

  oneof option {
    DebitCardUnableToAuthorizeTransactionSmsOptionV1 debit_card_unable_to_authorize_transaction_sms_option_v1 = 2;
    DebitCardUnableToAuthorizeTransactionSmsOptionV2 debit_card_unable_to_authorize_transaction_sms_option_v2 = 3;
  }
}

message DebitCardExpiredSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string download_link = 2;
}

message DebitCardExpiredSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 191];

  oneof option {
    DebitCardExpiredSmsOptionV1 debit_card_expired_sms_option_v1 = 2;
  }
}

message DebitCardEcomTransactionsNotEnabledSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string download_link = 2;
}

message DebitCardEcomTransactionsNotEnabledSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  string merchant = 2;
  string last_four_digit = 3;
  string download_link = 4;
}

message DebitCardEcomTransactionsNotEnabledSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 192];

  oneof option {
    DebitCardEcomTransactionsNotEnabledSmsOptionV1 debit_card_ecom_transactions_not_enabled_sms_option_v1 = 2;
    DebitCardEcomTransactionsNotEnabledSmsOptionV2 debit_card_ecom_transactions_not_enabled_sms_option_v2 = 3;
  }
}

message DebitCardDailyTransactionsAmtLimitReachedSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string download_link = 2;
}

message DebitCardDailyTransactionsAmtLimitReachedSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 193];

  oneof option {
    DebitCardDailyTransactionsAmtLimitReachedSmsOptionV1 debit_card_daily_transactions_amt_limit_reached_sms_option_v1 = 2;
  }
}

message DebitCardPosNotSupportedSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string download_link = 2;
}

message DebitCardPosNotSupportedSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 194];

  oneof option {
    DebitCardPosNotSupportedSmsOptionV1 debit_card_pos_not_supported_sms_option_v1 = 2;
  }
}

message DebitCardPinTriesExceededForTransactionSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string download_link = 2;
}

message DebitCardPinTriesExceededForTransactionSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  string last_four_digit = 2;
}

message DebitCardPinTriesExceededForTransactionSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 195];

  oneof option {
    DebitCardPinTriesExceededForTransactionSmsOptionV1 debit_card_pin_tries_exceeded_for_transaction_sms_option_v1 = 2;
    DebitCardPinTriesExceededForTransactionSmsOptionV2 debit_card_pin_tries_exceeded_for_transaction_sms_option_v2 = 3;
  }
}

message DebitCardDuplicateTransactionSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money amount = 2;
  string download_link = 3;
}

message DebitCardDuplicateTransactionSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
}


message DebitCardDuplicateTransactionSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 196];

  oneof option {
    DebitCardDuplicateTransactionSmsOptionV1 debit_card_duplicate_transaction_sms_option_v1 = 2;
    DebitCardDuplicateTransactionSmsOptionV2 debit_card_duplicate_transaction_sms_option_v2 = 3;
  }
}

message DebitCardTransactionDeclinedSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money amount = 2;
  string download_link = 3;
}

message DebitCardTransactionDeclinedSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 197];

  oneof option {
    DebitCardTransactionDeclinedSmsOptionV1 debit_card_transaction_declined_sms_option_v1 = 2;
  }
}

message DebitCardTransactionTypeNotSupportedSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string download_link = 2;
}

message DebitCardTransactionTypeNotSupportedSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 198];

  oneof option {
    DebitCardTransactionTypeNotSupportedSmsOptionV1 debit_card_transaction_type_not_supported_sms_option_v1 = 2;
  }
}

message DebitCardInvalidTransactionSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money amount = 2;
  string download_link = 3;
}

message DebitCardInvalidTransactionSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  string merchant = 2;
  string last_four_digit = 3;
}

message DebitCardInvalidTransactionSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 199];

  oneof option {
    DebitCardInvalidTransactionSmsOptionV1 debit_card_invalid_transaction_sms_option_v1 = 2;
    DebitCardInvalidTransactionSmsOptionV2 debit_card_invalid_transaction_sms_option_v2 = 3;
  }
}

message DebitCardInternationalTransactionsNotEnabledSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string download_link = 2;
}

message DebitCardInternationalTransactionsNotEnabledSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  string last_four_digit = 2;
  string download_link = 3;
}

message DebitCardInternationalTransactionsNotEnabledSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 200];

  oneof option {
    DebitCardInternationalTransactionsNotEnabledSmsOptionV1 debit_card_international_transactions_not_enabled_sms_option_v1 = 2;
    DebitCardInternationalTransactionsNotEnabledSmsOptionV2 debit_card_international_transactions_not_enabled_sms_option_v2 = 3;
  }
}

message DebitCardContactlessCardUsageNotEnabledSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string download_link = 2;
}

message DebitCardContactlessCardUsageNotEnabledSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  string merchant = 2;
  string last_four_digit = 3;
  string download_link = 4;
}

message DebitCardContactlessCardUsageNotEnabledSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 201];

  oneof option {
    DebitCardContactlessCardUsageNotEnabledSmsOptionV1 debit_card_contactless_card_usage_not_enabled_sms_option_v1 = 2;
    DebitCardContactlessCardUsageNotEnabledSmsOptionV2 debit_card_contactless_card_usage_not_enabled_sms_option_v2 = 3;
  }
}

message DebitCardInsufficientFundsForTransactionSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string download_link = 2;
}

message DebitCardInsufficientFundsForTransactionSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  string merchant = 2;
  string last_four_digits = 3;
  string download_link = 4;
}

message DebitCardInsufficientFundsForTransactionSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 202];

  oneof option {
    DebitCardInsufficientFundsForTransactionSmsOptionV1 debit_card_insufficient_funds_for_transaction_sms_option_v1 = 2;
    DebitCardInsufficientFundsForTransactionSmsOptionV2 debit_card_insufficient_funds_for_transaction_sms_option_v2 = 3;
  }
}

message DebitCardDailyWithdrawalLimitReachedSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string download_link = 2;
}

message DebitCardDailyWithdrawalLimitReachedSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  string last_four_digit = 2;
  google.protobuf.Timestamp txn_time = 3;
  string download_link = 4;
}

message DebitCardDailyWithdrawalLimitReachedSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 203];

  oneof option {
    DebitCardDailyWithdrawalLimitReachedSmsOptionV1 debit_card_daily_withdrawal_limit_reached_sms_option_v1 = 2;
    DebitCardDailyWithdrawalLimitReachedSmsOptionV2 debit_card_daily_withdrawal_limit_reached_sms_option_v2 = 3;
  }
}

message DebitCardLowFundsForTransactionSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string download_link = 2;
}

message DebitCardLowFundsForTransactionSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  string merchant = 2;
  string last_four_digits = 3;
  string download_link = 4;
}

message DebitCardLowFundsForTransactionSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 204];

  oneof option {
    DebitCardLowFundsForTransactionSmsOptionV1 debit_card_low_funds_for_transaction_sms_option_v1 = 2;
    DebitCardLowFundsForTransactionSmsOptionV2 debit_card_low_funds_for_transaction_sms_option_v2 = 3;
  }
}

message DebitCardInvalidExpiryDateSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string download_link = 2;
}

message DebitCardInvalidExpiryDateSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  string merchant = 2;
  google.type.Money txn_amount = 3;
  string last_for_digit = 4;
}

message DebitCardInvalidExpiryDateSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 205];

  oneof option {
    DebitCardInvalidExpiryDateSmsOptionV1 debit_card_invalid_expiry_date_sms_option_v1 = 2;
    DebitCardInvalidExpiryDateSmsOptionV2 debit_card_invalid_expiry_date_sms_option_v2 = 3;
  }
}

message DebitCardNfcNotEnabledSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string download_link = 2;
}

message DebitCardNfcNotEnabledSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 206];

  oneof option {
    DebitCardNfcNotEnabledSmsOptionV1 debit_card_nfc_not_enabled_sms_option_v1 = 2;
  }
}

message DebitCardPrmDeclinedSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string download_link = 2;
}

message DebitCardPrmDeclinedSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  string country = 2;
  string last_four_digit = 3;
  google.protobuf.Timestamp txn_time = 4;
  string download_link = 5;
}

message DebitCardPrmDeclinedSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 207];

  oneof option {
    DebitCardPrmDeclinedSmsOptionV1 debit_card_prm_declined_sms_option_v1 = 2;
    DebitCardPrmDeclinedSmsOptionV2 debit_card_prm_declined_sms_option_v2 = 3;
  }
}

message DebitCardCvvErrorSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string download_link = 2;
}

message DebitCardCvvErrorSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  string merchant = 2;
  google.type.Money txn_amount = 3;
  string last_four_digit = 4;
}

message DebitCardCvvErrorSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 208];

  oneof option {
    DebitCardCvvErrorSmsOptionV1 debit_card_cvv_error_sms_option_v1 = 2;
    DebitCardCvvErrorSmsOptionV2 debit_card_cvv_error_sms_option_v2 = 3;
  }
}

message DebitCardDailyContactlessPaymentsLimitExceededSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string download_link = 2;
}

message DebitCardDailyContactlessPaymentsLimitExceededSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  string merchant = 2;
  string last_four_digit = 3;
  string download_link = 4;
}

message DebitCardDailyContactlessPaymentsLimitExceededSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 209];

  oneof option {
    DebitCardDailyContactlessPaymentsLimitExceededSmsOptionV1 debit_card_daily_contactless_payments_limit_exceeded_sms_option_v1 = 2;
    DebitCardDailyContactlessPaymentsLimitExceededSmsOptionV2 debit_card_daily_contactless_payments_limit_exceeded_sms_option_v2 = 3;
  }
}

message DebitCardCardOffForTransactionsSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string download_link = 2;
}

message DebitCardCardOffForTransactionsSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  string last_four_digit = 2;
  string download_link = 3;
}

message DebitCardCardOffForTransactionsSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 210];

  oneof option {
    DebitCardCardOffForTransactionsSmsOptionV1 debit_card_card_off_for_transactions_sms_option_v1 = 2;
    DebitCardCardOffForTransactionsSmsOptionV2 debit_card_card_off_for_transactions_sms_option_v2 = 3;
  }
}

message DebitCardHostDownSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string download_link = 2;
}

message DebitCardHostDownSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  string merchant = 2;
  string download_link = 3;
}

message DebitCardHostDownSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 211];

  oneof option {
    DebitCardHostDownSmsOptionV1 debit_card_host_down_sms_option_v1 = 2;
    DebitCardHostDownSmsOptionV2 debit_card_host_down_sms_option_v2 = 3;
  }
}

message DebitCardDomesticTransactionsNotEnabledSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string download_link = 2;
}

message DebitCardDomesticTransactionsNotEnabledSmsOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  string last_four_digit = 2;
  string download_link = 3;
}

message DebitCardDomesticTransactionsNotEnabledSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 212];

  oneof option {
    DebitCardDomesticTransactionsNotEnabledSmsOptionV1 debit_card_domestic_transactions_not_enabled_sms_option_v1 = 2;
    DebitCardDomesticTransactionsNotEnabledSmsOptionV2 debit_card_domestic_transactions_not_enabled_sms_option_v2 = 3;
  }
}

message RiskOutcallFormLoginOtpSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 234];

  oneof option {
    RiskOutcallFormLoginOtpSmsOptionV1 risk_outcall_form_login_otp_sms_option_v1 = 2;
  }
}

message RiskOutcallFormLoginOtpSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string otp = 2 [(validate.rules).string.min_len = 1];
}

message CibilReportSmsOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message CibilReportSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 235];

  oneof option {
    CibilReportSmsOptionV1 cibil_report_option_v1 = 2;
  }
}

message SalaryProgramB2BUserWhitelistedSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message SalaryProgramB2BUserWhitelistedSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 236];

  oneof option {
    SalaryProgramB2BUserWhitelistedSmsOptionV1 salary_program_b2b_user_whitelisted_sms_option_v1 = 2;
  }
}

message AlternateContactFlowOtpSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 237];

  oneof option {
    AlternateContactOtpSmsOptionV1 alternate_contact_otp_sms_option_v1 = 2;
  }
}

message AlternateContactOtpSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  // actual otp to be sent
  string otp = 2 [(validate.rules).string.min_len = 1];
}

message NonResidentOnboardingOtpSmsOption {
  // type of template
  SmsType sms_type = 1 [(validate.rules).enum.const = 238];

  oneof option {
    NonResidentOnboardingOtpSmsOptionV1 non_resident_onboarding_otp_sms_option_v1 = 2;
  }
}

message NonResidentOnboardingOtpSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  // actual otp to be sent
  string otp = 2 [(validate.rules).string.min_len = 1];
}

message CxUserCallDroppedSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 241];
  oneof option {
    CxUserCallDroppedSmsOptionV1 cx_user_call_dropped_sms_option_v1 = 2;
  }
}

message CxUserCallDroppedSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string contact_us_flow_link = 2;
  string app_download_link = 3;

}

message RiskOpsCFSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string freeze_reason = 2;
  string freeze_type = 3;
}

message RiskOpsCFSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 271];
  oneof option {
    RiskOpsCFSmsOptionV1 risk_account_freeze_sms_option_v1 = 2;
  }
}

message RiskUnifiedLeaDebitFreezeSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 273];

  oneof option {
    RiskUnifiedLeaDebitFreezeOptionV1 risk_unified_lea_debit_freeze_option_v1 = 2;
  }
}

message RiskUnifiedLeaDebitFreezeOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string reminder_text = 2;
}

message RiskUnifiedLeaCreditFreezeSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 274];

  oneof option {
    RiskUnifiedLeaCreditFreezeOptionV1 risk_unified_lea_credit_freeze_option_v1 = 2;
  }
}

message RiskUnifiedLeaCreditFreezeOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string reminder_text = 2;
}

message RiskUnifiedLeaTotalFreezeSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 275];

  oneof option {
    RiskUnifiedLeaTotalFreezeOptionV1 risk_unified_lea_total_freeze_option_v1 = 2;
  }
}

message RiskUnifiedLeaTotalFreezeOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string reminder_text = 2;
}

message RiskUnifiedLeaLienSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 276];

  oneof option {
    RiskUnifiedLeaLienOptionV1 risk_unified_lea_lien_option_v1 = 2;
  }
}

message RiskUnifiedLeaLienOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string reminder_text = 2;
  string amount = 3;
}

message RiskCreditFreezeAppliedSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 277];

  oneof option {
    RiskCreditFreezeAppliedOptionV1 risk_ops_credit_freeze_option_v1 = 2;
  }
}

message RiskCreditFreezeAppliedOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string reminder_heading = 2;
  string freeze_type = 3;
  string freeze_reason = 4;
}

message CreditCardRevisedLimitSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 278];

  oneof option {
    CreditCardRevisedLimitSmsOptionV1 credit_card_revised_limit_sms_option_v1 = 2;
  }
}

message CreditCardRevisedLimitSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  google.type.Money credit_limit = 3;
  api.typesv2.common.Name customer_name = 2;
}

message ChequeCreditProcessingFint {
  SmsType sms_type = 1 [(validate.rules).enum.const = 279];

  oneof option {
    ChequeCreditProcessingFintSmsOptionV1 cheque_credit_processing_fint_sms_option_v1 = 2;
  }
}

message ChequeCreditProcessingFintSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money transaction_amount = 2 [(validate.rules).message.required = true];
}

message ChequeCreditProcessingFailedFint {
  SmsType sms_type = 1  [(validate.rules).enum.const = 280];

  oneof option {
    ChequeCreditProcessingFailedFintSmsOptionV1 cheque_credit_processing_failed_fint_sms_option_v1 = 2;
  }
}

message ChequeCreditProcessingFailedFintSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  google.type.Money transaction_amount = 2 [(validate.rules).message.required = true];
}

message CxTicketResolutionCsatSmsOption {
  SmsType sms_type = 1 [(validate.rules).enum.const = 281];
  oneof option {
    CxTicketResolutionCsatSmsOptionV1 cx_ticket_resolution_csat_sms_option_v1 = 2;
  }
}

message CxTicketResolutionCsatSmsOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string first_name = 2;
  string ticket_id = 3;
  string csat_link = 4;
}
