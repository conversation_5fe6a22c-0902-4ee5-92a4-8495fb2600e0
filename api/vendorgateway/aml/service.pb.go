// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/aml/service.proto

package aml

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	aml "github.com/epifi/gamma/api/aml"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// represents whether a match is found or not after screening is done
type MatchStatus int32

const (
	MatchStatus_MATCH_STATUS_UNSPECIFIED MatchStatus = 0
	// Match found in screening
	MatchStatus_MATCH_STATUS_MATCHED MatchStatus = 1
	// Match not found in screening
	MatchStatus_MATCH_STATUS_NOT_MATCHED MatchStatus = 2
	// Error from vendor in screening attempt
	MatchStatus_MATCH_STATUS_ERROR MatchStatus = 3
)

// Enum value maps for MatchStatus.
var (
	MatchStatus_name = map[int32]string{
		0: "MATCH_STATUS_UNSPECIFIED",
		1: "MATCH_STATUS_MATCHED",
		2: "MATCH_STATUS_NOT_MATCHED",
		3: "MATCH_STATUS_ERROR",
	}
	MatchStatus_value = map[string]int32{
		"MATCH_STATUS_UNSPECIFIED": 0,
		"MATCH_STATUS_MATCHED":     1,
		"MATCH_STATUS_NOT_MATCHED": 2,
		"MATCH_STATUS_ERROR":       3,
	}
)

func (x MatchStatus) Enum() *MatchStatus {
	p := new(MatchStatus)
	*p = x
	return p
}

func (x MatchStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MatchStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_aml_service_proto_enumTypes[0].Descriptor()
}

func (MatchStatus) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_aml_service_proto_enumTypes[0]
}

func (x MatchStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MatchStatus.Descriptor instead.
func (MatchStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_service_proto_rawDescGZIP(), []int{0}
}

// Product is a set of pre-configured watchlists and screening parameters used for screening a user's details against.
// This is useful for clients to use for common financial products instead of sending individual watchlists and parameters.
type Product int32

const (
	Product_PRODUCT_UNSPECIFIED  Product = 0
	Product_PRODUCT_MUTUAL_FUND  Product = 1
	Product_PRODUCT_LOAN         Product = 2
	Product_PRODUCT_US_STOCKS    Product = 3
	Product_PRODUCT_BANK_ACCOUNT Product = 4
)

// Enum value maps for Product.
var (
	Product_name = map[int32]string{
		0: "PRODUCT_UNSPECIFIED",
		1: "PRODUCT_MUTUAL_FUND",
		2: "PRODUCT_LOAN",
		3: "PRODUCT_US_STOCKS",
		4: "PRODUCT_BANK_ACCOUNT",
	}
	Product_value = map[string]int32{
		"PRODUCT_UNSPECIFIED":  0,
		"PRODUCT_MUTUAL_FUND":  1,
		"PRODUCT_LOAN":         2,
		"PRODUCT_US_STOCKS":    3,
		"PRODUCT_BANK_ACCOUNT": 4,
	}
)

func (x Product) Enum() *Product {
	p := new(Product)
	*p = x
	return p
}

func (x Product) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Product) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_aml_service_proto_enumTypes[1].Descriptor()
}

func (Product) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_aml_service_proto_enumTypes[1]
}

func (x Product) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Product.Descriptor instead.
func (Product) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_service_proto_rawDescGZIP(), []int{1}
}

// Purpose is used to decide which type of screening to perform
type Purpose int32

const (
	Purpose_PURPOSE_UNSPECIFIED Purpose = 0
	// To screen a user with the given details once
	Purpose_PURPOSE_INITIAL_SCREENING Purpose = 1
	// To keep screening the user not just now, but also in future if his details match
	// any new entries in the watchlists as the watchlists keep getting updated periodically
	Purpose_PURPOSE_CONTINUOUS_SCREENING Purpose = 2
)

// Enum value maps for Purpose.
var (
	Purpose_name = map[int32]string{
		0: "PURPOSE_UNSPECIFIED",
		1: "PURPOSE_INITIAL_SCREENING",
		2: "PURPOSE_CONTINUOUS_SCREENING",
	}
	Purpose_value = map[string]int32{
		"PURPOSE_UNSPECIFIED":          0,
		"PURPOSE_INITIAL_SCREENING":    1,
		"PURPOSE_CONTINUOUS_SCREENING": 2,
	}
)

func (x Purpose) Enum() *Purpose {
	p := new(Purpose)
	*p = x
	return p
}

func (x Purpose) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Purpose) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_aml_service_proto_enumTypes[2].Descriptor()
}

func (Purpose) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_aml_service_proto_enumTypes[2]
}

func (x Purpose) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Purpose.Descriptor instead.
func (Purpose) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_service_proto_rawDescGZIP(), []int{2}
}

type OverallStatus int32

const (
	OverallStatus_OVERALL_STATUS_UNSPECIFIED OverallStatus = 0
	// The screening request was accepted and the AML check process was started.
	// In case no hit is found for the user details against any watchlist, then the process ends here.
	// If hits are found, then a case will be created for this request for manual review.
	// Some details of hits are also provided in the response in case of hits.
	OverallStatus_OVERALL_STATUS_ACCEPTED OverallStatus = 1
	// The screening request was rejected, most likely due to some error validating request parameters.
	OverallStatus_OVERALL_STATUS_REJECTED OverallStatus = 2
)

// Enum value maps for OverallStatus.
var (
	OverallStatus_name = map[int32]string{
		0: "OVERALL_STATUS_UNSPECIFIED",
		1: "OVERALL_STATUS_ACCEPTED",
		2: "OVERALL_STATUS_REJECTED",
	}
	OverallStatus_value = map[string]int32{
		"OVERALL_STATUS_UNSPECIFIED": 0,
		"OVERALL_STATUS_ACCEPTED":    1,
		"OVERALL_STATUS_REJECTED":    2,
	}
)

func (x OverallStatus) Enum() *OverallStatus {
	p := new(OverallStatus)
	*p = x
	return p
}

func (x OverallStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OverallStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_aml_service_proto_enumTypes[3].Descriptor()
}

func (OverallStatus) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_aml_service_proto_enumTypes[3]
}

func (x OverallStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OverallStatus.Descriptor instead.
func (OverallStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_service_proto_rawDescGZIP(), []int{3}
}

type ValidationOutcome int32

const (
	ValidationOutcome_VALIDATION_OUTCOME_UNSPECIFIED ValidationOutcome = 0
	ValidationOutcome_VALIDATION_OUTCOME_SUCCESS     ValidationOutcome = 1
	ValidationOutcome_VALIDATION_OUTCOME_FAILURE     ValidationOutcome = 2
)

// Enum value maps for ValidationOutcome.
var (
	ValidationOutcome_name = map[int32]string{
		0: "VALIDATION_OUTCOME_UNSPECIFIED",
		1: "VALIDATION_OUTCOME_SUCCESS",
		2: "VALIDATION_OUTCOME_FAILURE",
	}
	ValidationOutcome_value = map[string]int32{
		"VALIDATION_OUTCOME_UNSPECIFIED": 0,
		"VALIDATION_OUTCOME_SUCCESS":     1,
		"VALIDATION_OUTCOME_FAILURE":     2,
	}
)

func (x ValidationOutcome) Enum() *ValidationOutcome {
	p := new(ValidationOutcome)
	*p = x
	return p
}

func (x ValidationOutcome) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ValidationOutcome) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_aml_service_proto_enumTypes[4].Descriptor()
}

func (ValidationOutcome) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_aml_service_proto_enumTypes[4]
}

func (x ValidationOutcome) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ValidationOutcome.Descriptor instead.
func (ValidationOutcome) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_service_proto_rawDescGZIP(), []int{4}
}

// Suggested action based on screening results
type SuggestedAction int32

const (
	SuggestedAction_SUGGESTED_ACTION_UNSPECIFIED SuggestedAction = 0
	// Proceed with the user's onboarding (no hits found)
	SuggestedAction_SUGGESTED_ACTION_PROCEED SuggestedAction = 1
	// Review the user (probable hits found)
	SuggestedAction_SUGGESTED_ACTION_REVIEW SuggestedAction = 2
	// Stop the user onboarding (confirmed hits found)
	SuggestedAction_SUGGESTED_ACTION_STOP SuggestedAction = 3
)

// Enum value maps for SuggestedAction.
var (
	SuggestedAction_name = map[int32]string{
		0: "SUGGESTED_ACTION_UNSPECIFIED",
		1: "SUGGESTED_ACTION_PROCEED",
		2: "SUGGESTED_ACTION_REVIEW",
		3: "SUGGESTED_ACTION_STOP",
	}
	SuggestedAction_value = map[string]int32{
		"SUGGESTED_ACTION_UNSPECIFIED": 0,
		"SUGGESTED_ACTION_PROCEED":     1,
		"SUGGESTED_ACTION_REVIEW":      2,
		"SUGGESTED_ACTION_STOP":        3,
	}
)

func (x SuggestedAction) Enum() *SuggestedAction {
	p := new(SuggestedAction)
	*p = x
	return p
}

func (x SuggestedAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SuggestedAction) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_aml_service_proto_enumTypes[5].Descriptor()
}

func (SuggestedAction) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_aml_service_proto_enumTypes[5]
}

func (x SuggestedAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SuggestedAction.Descriptor instead.
func (SuggestedAction) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_service_proto_rawDescGZIP(), []int{5}
}

// Enum for match type in hit response
type MatchType int32

const (
	MatchType_MATCH_TYPE_UNSPECIFIED MatchType = 0
	// Confirmed hit/match
	MatchType_MATCH_TYPE_CONFIRMED MatchType = 1
	// Probable hit/match
	MatchType_MATCH_TYPE_PROBABLE MatchType = 2
)

// Enum value maps for MatchType.
var (
	MatchType_name = map[int32]string{
		0: "MATCH_TYPE_UNSPECIFIED",
		1: "MATCH_TYPE_CONFIRMED",
		2: "MATCH_TYPE_PROBABLE",
	}
	MatchType_value = map[string]int32{
		"MATCH_TYPE_UNSPECIFIED": 0,
		"MATCH_TYPE_CONFIRMED":   1,
		"MATCH_TYPE_PROBABLE":    2,
	}
)

func (x MatchType) Enum() *MatchType {
	p := new(MatchType)
	*p = x
	return p
}

func (x MatchType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MatchType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_aml_service_proto_enumTypes[6].Descriptor()
}

func (MatchType) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_aml_service_proto_enumTypes[6]
}

func (x MatchType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MatchType.Descriptor instead.
func (MatchType) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_service_proto_rawDescGZIP(), []int{6}
}

type ScreenCustomerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// unique for each actor
	RecordIdentifier string `protobuf:"bytes,2,opt,name=record_identifier,json=recordIdentifier,proto3" json:"record_identifier,omitempty"`
	// request id to be passed to vendor
	VendorRequestId string `protobuf:"bytes,3,opt,name=vendor_request_id,json=vendorRequestId,proto3" json:"vendor_request_id,omitempty"`
	// deprecated since AML screening rules are configured based on AmlProduct and this is of no use
	//
	// Deprecated: Marked as deprecated in api/vendorgateway/aml/service.proto.
	Entity          aml.AmlEntity    `protobuf:"varint,4,opt,name=entity,proto3,enum=aml.AmlEntity" json:"entity,omitempty"`
	Product         aml.AmlProduct   `protobuf:"varint,5,opt,name=product,proto3,enum=aml.AmlProduct" json:"product,omitempty"`
	CustomerDetails *CustomerDetails `protobuf:"bytes,6,opt,name=customer_details,json=customerDetails,proto3" json:"customer_details,omitempty"`
	// Owner of the screening request.
	// Determines the correct API URL and configuration needed for the screening request with the vendor.
	// The API URL and configuration may differ by owner to comply with regulations,
	// ensuring only the owner’s designated case reviewer can access and review potential matches on money launderer lists.
	Owner common.Owner `protobuf:"varint,7,opt,name=owner,proto3,enum=api.typesv2.common.Owner" json:"owner,omitempty"`
}

func (x *ScreenCustomerRequest) Reset() {
	*x = ScreenCustomerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aml_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScreenCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScreenCustomerRequest) ProtoMessage() {}

func (x *ScreenCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aml_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScreenCustomerRequest.ProtoReflect.Descriptor instead.
func (*ScreenCustomerRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_service_proto_rawDescGZIP(), []int{0}
}

func (x *ScreenCustomerRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ScreenCustomerRequest) GetRecordIdentifier() string {
	if x != nil {
		return x.RecordIdentifier
	}
	return ""
}

func (x *ScreenCustomerRequest) GetVendorRequestId() string {
	if x != nil {
		return x.VendorRequestId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/vendorgateway/aml/service.proto.
func (x *ScreenCustomerRequest) GetEntity() aml.AmlEntity {
	if x != nil {
		return x.Entity
	}
	return aml.AmlEntity(0)
}

func (x *ScreenCustomerRequest) GetProduct() aml.AmlProduct {
	if x != nil {
		return x.Product
	}
	return aml.AmlProduct(0)
}

func (x *ScreenCustomerRequest) GetCustomerDetails() *CustomerDetails {
	if x != nil {
		return x.CustomerDetails
	}
	return nil
}

func (x *ScreenCustomerRequest) GetOwner() common.Owner {
	if x != nil {
		return x.Owner
	}
	return common.Owner(0)
}

type ScreenCustomerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// rejection message if the request is rejected
	RejectionMessage string `protobuf:"bytes,2,opt,name=rejection_message,json=rejectionMessage,proto3" json:"rejection_message,omitempty"`
	// rejection code if the request is rejected
	RejectionCode aml.RejectionCode `protobuf:"varint,3,opt,name=rejection_code,json=rejectionCode,proto3,enum=aml.RejectionCode" json:"rejection_code,omitempty"`
	// record identifier passed in the request
	RecordIdentifier string      `protobuf:"bytes,4,opt,name=record_identifier,json=recordIdentifier,proto3" json:"record_identifier,omitempty"`
	MatchStatus      MatchStatus `protobuf:"varint,5,opt,name=match_status,json=matchStatus,proto3,enum=vendorgateway.aml.MatchStatus" json:"match_status,omitempty"`
	// case id if matched and case is generated
	CaseId string `protobuf:"bytes,6,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	// case link if matched and case is generated
	CaseLink string `protobuf:"bytes,7,opt,name=case_link,json=caseLink,proto3" json:"case_link,omitempty"`
	// count of matches found
	AlertCount uint64 `protobuf:"varint,8,opt,name=alert_count,json=alertCount,proto3" json:"alert_count,omitempty"`
	// details of all the matches
	MatchDetails []*aml.MatchDetails `protobuf:"bytes,9,rep,name=match_details,json=matchDetails,proto3" json:"match_details,omitempty"`
}

func (x *ScreenCustomerResponse) Reset() {
	*x = ScreenCustomerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aml_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScreenCustomerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScreenCustomerResponse) ProtoMessage() {}

func (x *ScreenCustomerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aml_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScreenCustomerResponse.ProtoReflect.Descriptor instead.
func (*ScreenCustomerResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_service_proto_rawDescGZIP(), []int{1}
}

func (x *ScreenCustomerResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ScreenCustomerResponse) GetRejectionMessage() string {
	if x != nil {
		return x.RejectionMessage
	}
	return ""
}

func (x *ScreenCustomerResponse) GetRejectionCode() aml.RejectionCode {
	if x != nil {
		return x.RejectionCode
	}
	return aml.RejectionCode(0)
}

func (x *ScreenCustomerResponse) GetRecordIdentifier() string {
	if x != nil {
		return x.RecordIdentifier
	}
	return ""
}

func (x *ScreenCustomerResponse) GetMatchStatus() MatchStatus {
	if x != nil {
		return x.MatchStatus
	}
	return MatchStatus_MATCH_STATUS_UNSPECIFIED
}

func (x *ScreenCustomerResponse) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *ScreenCustomerResponse) GetCaseLink() string {
	if x != nil {
		return x.CaseLink
	}
	return ""
}

func (x *ScreenCustomerResponse) GetAlertCount() uint64 {
	if x != nil {
		return x.AlertCount
	}
	return 0
}

func (x *ScreenCustomerResponse) GetMatchDetails() []*aml.MatchDetails {
	if x != nil {
		return x.MatchDetails
	}
	return nil
}

type CustomerDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name of the customer - Mandatory
	Name *common.Name `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// father name - NOT Mandatory
	FatherName *common.Name `protobuf:"bytes,2,opt,name=father_name,json=fatherName,proto3" json:"father_name,omitempty"`
	// mother name - NOT Mandatory
	MotherName *common.Name `protobuf:"bytes,3,opt,name=mother_name,json=motherName,proto3" json:"mother_name,omitempty"`
	// gender - NOT Mandatory
	Gender common.Gender `protobuf:"varint,4,opt,name=gender,proto3,enum=api.typesv2.common.Gender" json:"gender,omitempty"`
	// marital status - NOT Mandatory
	MaritalStatus common.MaritalStatus `protobuf:"varint,5,opt,name=marital_status,json=maritalStatus,proto3,enum=api.typesv2.common.MaritalStatus" json:"marital_status,omitempty"`
	// income slab - NOT Mandatory
	IncomeSlab common.IncomeSlab `protobuf:"varint,6,opt,name=income_slab,json=incomeSlab,proto3,enum=api.typesv2.common.IncomeSlab" json:"income_slab,omitempty"`
	// pan number - NOT Mandatory
	PanNumber string `protobuf:"bytes,7,opt,name=pan_number,json=panNumber,proto3" json:"pan_number,omitempty"`
	// nationality - Mandatory
	Nationality common.Nationality `protobuf:"varint,8,opt,name=nationality,proto3,enum=api.typesv2.common.Nationality" json:"nationality,omitempty"`
	// passport id number - NOT Mandatory
	PassportNumber string `protobuf:"bytes,9,opt,name=passport_number,json=passportNumber,proto3" json:"passport_number,omitempty"`
	// passport expiry date - Mandatory if passport provided
	PassportExpiryDate *date.Date `protobuf:"bytes,10,opt,name=passport_expiry_date,json=passportExpiryDate,proto3" json:"passport_expiry_date,omitempty"`
	// driving license id number - NOT Mandatory
	DrivingLicenseNumber string `protobuf:"bytes,11,opt,name=driving_license_number,json=drivingLicenseNumber,proto3" json:"driving_license_number,omitempty"`
	// passport expiry date - Mandatory if driving license provided
	DrivingLicenseExpiryDate *date.Date `protobuf:"bytes,12,opt,name=driving_license_expiry_date,json=drivingLicenseExpiryDate,proto3" json:"driving_license_expiry_date,omitempty"`
	// voter id number - NOT Mandatory
	VoterId string `protobuf:"bytes,13,opt,name=voter_id,json=voterId,proto3" json:"voter_id,omitempty"`
	// document type of proof of address provided - NOT Mandatory
	PoaType common.DocumentProofType `protobuf:"varint,14,opt,name=poa_type,json=poaType,proto3,enum=api.typesv2.common.DocumentProofType" json:"poa_type,omitempty"`
	// phone number - NOT Mandatory
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,15,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// email - NOT Mandatory
	Email string `protobuf:"bytes,16,opt,name=email,proto3" json:"email,omitempty"`
	// date of birth - NOT Mandatory
	DateOfBirth *date.Date `protobuf:"bytes,17,opt,name=date_of_birth,json=dateOfBirth,proto3" json:"date_of_birth,omitempty"`
	// permanent address - NOT Mandatory
	PermanentAddress *common.PostalAddress `protobuf:"bytes,18,opt,name=permanent_address,json=permanentAddress,proto3" json:"permanent_address,omitempty"`
	// correspondence address - NOT Mandatory
	CorrespondenceAddress *common.PostalAddress `protobuf:"bytes,19,opt,name=correspondence_address,json=correspondenceAddress,proto3" json:"correspondence_address,omitempty"`
	// politically exposed status - NOT Mandatory
	PoliticallyExposedStatus common.PoliticallyExposedStatus `protobuf:"varint,20,opt,name=politically_exposed_status,json=politicallyExposedStatus,proto3,enum=api.typesv2.common.PoliticallyExposedStatus" json:"politically_exposed_status,omitempty"`
	// employment type - NOT Mandatory
	EmploymentType common.EmploymentType `protobuf:"varint,21,opt,name=employment_type,json=employmentType,proto3,enum=api.typesv2.common.EmploymentType" json:"employment_type,omitempty"`
}

func (x *CustomerDetails) Reset() {
	*x = CustomerDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aml_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerDetails) ProtoMessage() {}

func (x *CustomerDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aml_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerDetails.ProtoReflect.Descriptor instead.
func (*CustomerDetails) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_service_proto_rawDescGZIP(), []int{2}
}

func (x *CustomerDetails) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *CustomerDetails) GetFatherName() *common.Name {
	if x != nil {
		return x.FatherName
	}
	return nil
}

func (x *CustomerDetails) GetMotherName() *common.Name {
	if x != nil {
		return x.MotherName
	}
	return nil
}

func (x *CustomerDetails) GetGender() common.Gender {
	if x != nil {
		return x.Gender
	}
	return common.Gender(0)
}

func (x *CustomerDetails) GetMaritalStatus() common.MaritalStatus {
	if x != nil {
		return x.MaritalStatus
	}
	return common.MaritalStatus(0)
}

func (x *CustomerDetails) GetIncomeSlab() common.IncomeSlab {
	if x != nil {
		return x.IncomeSlab
	}
	return common.IncomeSlab(0)
}

func (x *CustomerDetails) GetPanNumber() string {
	if x != nil {
		return x.PanNumber
	}
	return ""
}

func (x *CustomerDetails) GetNationality() common.Nationality {
	if x != nil {
		return x.Nationality
	}
	return common.Nationality(0)
}

func (x *CustomerDetails) GetPassportNumber() string {
	if x != nil {
		return x.PassportNumber
	}
	return ""
}

func (x *CustomerDetails) GetPassportExpiryDate() *date.Date {
	if x != nil {
		return x.PassportExpiryDate
	}
	return nil
}

func (x *CustomerDetails) GetDrivingLicenseNumber() string {
	if x != nil {
		return x.DrivingLicenseNumber
	}
	return ""
}

func (x *CustomerDetails) GetDrivingLicenseExpiryDate() *date.Date {
	if x != nil {
		return x.DrivingLicenseExpiryDate
	}
	return nil
}

func (x *CustomerDetails) GetVoterId() string {
	if x != nil {
		return x.VoterId
	}
	return ""
}

func (x *CustomerDetails) GetPoaType() common.DocumentProofType {
	if x != nil {
		return x.PoaType
	}
	return common.DocumentProofType(0)
}

func (x *CustomerDetails) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *CustomerDetails) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CustomerDetails) GetDateOfBirth() *date.Date {
	if x != nil {
		return x.DateOfBirth
	}
	return nil
}

func (x *CustomerDetails) GetPermanentAddress() *common.PostalAddress {
	if x != nil {
		return x.PermanentAddress
	}
	return nil
}

func (x *CustomerDetails) GetCorrespondenceAddress() *common.PostalAddress {
	if x != nil {
		return x.CorrespondenceAddress
	}
	return nil
}

func (x *CustomerDetails) GetPoliticallyExposedStatus() common.PoliticallyExposedStatus {
	if x != nil {
		return x.PoliticallyExposedStatus
	}
	return common.PoliticallyExposedStatus(0)
}

func (x *CustomerDetails) GetEmploymentType() common.EmploymentType {
	if x != nil {
		return x.EmploymentType
	}
	return common.EmploymentType(0)
}

type InitiateScreeningRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Owner  common.Owner                 `protobuf:"varint,2,opt,name=owner,proto3,enum=api.typesv2.common.Owner" json:"owner,omitempty"`
	// Unique identifier of a user in caller's system to refer later during case reviews
	UserId string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// Unique request id to be passed to vendor
	VendorRequestId string           `protobuf:"bytes,4,opt,name=vendor_request_id,json=vendorRequestId,proto3" json:"vendor_request_id,omitempty"`
	Product         Product          `protobuf:"varint,5,opt,name=product,proto3,enum=vendorgateway.aml.Product" json:"product,omitempty"`
	Purpose         Purpose          `protobuf:"varint,6,opt,name=purpose,proto3,enum=vendorgateway.aml.Purpose" json:"purpose,omitempty"`
	UserDetails     *CustomerDetails `protobuf:"bytes,7,opt,name=user_details,json=userDetails,proto3" json:"user_details,omitempty"`
}

func (x *InitiateScreeningRequest) Reset() {
	*x = InitiateScreeningRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aml_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateScreeningRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateScreeningRequest) ProtoMessage() {}

func (x *InitiateScreeningRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aml_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateScreeningRequest.ProtoReflect.Descriptor instead.
func (*InitiateScreeningRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_service_proto_rawDescGZIP(), []int{3}
}

func (x *InitiateScreeningRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *InitiateScreeningRequest) GetOwner() common.Owner {
	if x != nil {
		return x.Owner
	}
	return common.Owner(0)
}

func (x *InitiateScreeningRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *InitiateScreeningRequest) GetVendorRequestId() string {
	if x != nil {
		return x.VendorRequestId
	}
	return ""
}

func (x *InitiateScreeningRequest) GetProduct() Product {
	if x != nil {
		return x.Product
	}
	return Product_PRODUCT_UNSPECIFIED
}

func (x *InitiateScreeningRequest) GetPurpose() Purpose {
	if x != nil {
		return x.Purpose
	}
	return Purpose_PURPOSE_UNSPECIFIED
}

func (x *InitiateScreeningRequest) GetUserDetails() *CustomerDetails {
	if x != nil {
		return x.UserDetails
	}
	return nil
}

type InitiateScreeningResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Request ID from the original request
	RequestId string `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Overall status of the request
	OverallStatus OverallStatus `protobuf:"varint,3,opt,name=overall_status,json=overallStatus,proto3,enum=vendorgateway.aml.OverallStatus" json:"overall_status,omitempty"`
	// Main validation code (if any)
	ValidationCode string `protobuf:"bytes,4,opt,name=validation_code,json=validationCode,proto3" json:"validation_code,omitempty"`
	// Main validation description (if any)
	ValidationDescription string               `protobuf:"bytes,5,opt,name=validation_description,json=validationDescription,proto3" json:"validation_description,omitempty"`
	UserScreeningResult   *UserScreeningResult `protobuf:"bytes,6,opt,name=user_screening_result,json=userScreeningResult,proto3" json:"user_screening_result,omitempty"`
}

func (x *InitiateScreeningResponse) Reset() {
	*x = InitiateScreeningResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aml_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateScreeningResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateScreeningResponse) ProtoMessage() {}

func (x *InitiateScreeningResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aml_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateScreeningResponse.ProtoReflect.Descriptor instead.
func (*InitiateScreeningResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_service_proto_rawDescGZIP(), []int{4}
}

func (x *InitiateScreeningResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *InitiateScreeningResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *InitiateScreeningResponse) GetOverallStatus() OverallStatus {
	if x != nil {
		return x.OverallStatus
	}
	return OverallStatus_OVERALL_STATUS_UNSPECIFIED
}

func (x *InitiateScreeningResponse) GetValidationCode() string {
	if x != nil {
		return x.ValidationCode
	}
	return ""
}

func (x *InitiateScreeningResponse) GetValidationDescription() string {
	if x != nil {
		return x.ValidationDescription
	}
	return ""
}

func (x *InitiateScreeningResponse) GetUserScreeningResult() *UserScreeningResult {
	if x != nil {
		return x.UserScreeningResult
	}
	return nil
}

type UserScreeningResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique identifier of a user in caller's system to refer later during case reviews
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// Outcome of validation of user's details
	ValidationOutcome ValidationOutcome `protobuf:"varint,2,opt,name=validation_outcome,json=validationOutcome,proto3,enum=vendorgateway.aml.ValidationOutcome" json:"validation_outcome,omitempty"`
	// Overall suggested action for this user
	SuggestedAction SuggestedAction `protobuf:"varint,3,opt,name=suggested_action,json=suggestedAction,proto3,enum=vendorgateway.aml.SuggestedAction" json:"suggested_action,omitempty"`
	// Response for a purpose requested
	PurposeScreeningResult *PurposeScreeningResult `protobuf:"bytes,4,opt,name=purpose_screening_result,json=purposeScreeningResult,proto3" json:"purpose_screening_result,omitempty"`
	// Validation code for any validation failures in user's details
	ValidationCode string `protobuf:"bytes,5,opt,name=validation_code,json=validationCode,proto3" json:"validation_code,omitempty"`
	// Validation description for any validation failures in user's details
	ValidationDescription string `protobuf:"bytes,6,opt,name=validation_description,json=validationDescription,proto3" json:"validation_description,omitempty"`
	// Number of validation failures for this user
	ValidationFailureCount uint32 `protobuf:"varint,7,opt,name=validation_failure_count,json=validationFailureCount,proto3" json:"validation_failure_count,omitempty"`
}

func (x *UserScreeningResult) Reset() {
	*x = UserScreeningResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aml_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserScreeningResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserScreeningResult) ProtoMessage() {}

func (x *UserScreeningResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aml_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserScreeningResult.ProtoReflect.Descriptor instead.
func (*UserScreeningResult) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_service_proto_rawDescGZIP(), []int{5}
}

func (x *UserScreeningResult) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserScreeningResult) GetValidationOutcome() ValidationOutcome {
	if x != nil {
		return x.ValidationOutcome
	}
	return ValidationOutcome_VALIDATION_OUTCOME_UNSPECIFIED
}

func (x *UserScreeningResult) GetSuggestedAction() SuggestedAction {
	if x != nil {
		return x.SuggestedAction
	}
	return SuggestedAction_SUGGESTED_ACTION_UNSPECIFIED
}

func (x *UserScreeningResult) GetPurposeScreeningResult() *PurposeScreeningResult {
	if x != nil {
		return x.PurposeScreeningResult
	}
	return nil
}

func (x *UserScreeningResult) GetValidationCode() string {
	if x != nil {
		return x.ValidationCode
	}
	return ""
}

func (x *UserScreeningResult) GetValidationDescription() string {
	if x != nil {
		return x.ValidationDescription
	}
	return ""
}

func (x *UserScreeningResult) GetValidationFailureCount() uint32 {
	if x != nil {
		return x.ValidationFailureCount
	}
	return 0
}

type PurposeScreeningResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Name/description of the purpose
	Purpose string `protobuf:"bytes,1,opt,name=purpose,proto3" json:"purpose,omitempty"`
	// Purpose code (01, 02, 03, 04, 05)
	PurposeCode string `protobuf:"bytes,2,opt,name=purpose_code,json=purposeCode,proto3" json:"purpose_code,omitempty"`
	// Validation code for this purpose
	ValidationCode string `protobuf:"bytes,3,opt,name=validation_code,json=validationCode,proto3" json:"validation_code,omitempty"`
	// Validation description for this purpose
	ValidationDescription string `protobuf:"bytes,4,opt,name=validation_description,json=validationDescription,proto3" json:"validation_description,omitempty"`
	// Number of validation failures for this purpose
	ValidationFailureCount uint32 `protobuf:"varint,5,opt,name=validation_failure_count,json=validationFailureCount,proto3" json:"validation_failure_count,omitempty"`
	// Results of screening for this purpose
	ScreeningResult *ScreeningResult `protobuf:"bytes,6,opt,name=screening_result,json=screeningResult,proto3" json:"screening_result,omitempty"`
}

func (x *PurposeScreeningResult) Reset() {
	*x = PurposeScreeningResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aml_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PurposeScreeningResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PurposeScreeningResult) ProtoMessage() {}

func (x *PurposeScreeningResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aml_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PurposeScreeningResult.ProtoReflect.Descriptor instead.
func (*PurposeScreeningResult) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_service_proto_rawDescGZIP(), []int{6}
}

func (x *PurposeScreeningResult) GetPurpose() string {
	if x != nil {
		return x.Purpose
	}
	return ""
}

func (x *PurposeScreeningResult) GetPurposeCode() string {
	if x != nil {
		return x.PurposeCode
	}
	return ""
}

func (x *PurposeScreeningResult) GetValidationCode() string {
	if x != nil {
		return x.ValidationCode
	}
	return ""
}

func (x *PurposeScreeningResult) GetValidationDescription() string {
	if x != nil {
		return x.ValidationDescription
	}
	return ""
}

func (x *PurposeScreeningResult) GetValidationFailureCount() uint32 {
	if x != nil {
		return x.ValidationFailureCount
	}
	return 0
}

func (x *PurposeScreeningResult) GetScreeningResult() *ScreeningResult {
	if x != nil {
		return x.ScreeningResult
	}
	return nil
}

// Data specific to screening purpose
type ScreeningResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether hits were detected (Yes/No)
	HitsDetected bool `protobuf:"varint,1,opt,name=hits_detected,json=hitsDetected,proto3" json:"hits_detected,omitempty"`
	// Total number of hits detected
	HitsCount uint32 `protobuf:"varint,2,opt,name=hits_count,json=hitsCount,proto3" json:"hits_count,omitempty"`
	// Whether any confirmed hits were found
	ConfirmedHit bool `protobuf:"varint,3,opt,name=confirmed_hit,json=confirmedHit,proto3" json:"confirmed_hit,omitempty"`
	// Case ID generated if hits are found
	CaseId string `protobuf:"bytes,4,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	// Case URL for reviewing the hits
	CaseUrl string `protobuf:"bytes,5,opt,name=case_url,json=caseUrl,proto3" json:"case_url,omitempty"`
	// Base64 encoded report data
	ReportData string `protobuf:"bytes,6,opt,name=report_data,json=reportData,proto3" json:"report_data,omitempty"`
	// List of individual hits/matches
	Hits []*Hit `protobuf:"bytes,7,rep,name=hits,proto3" json:"hits,omitempty"`
}

func (x *ScreeningResult) Reset() {
	*x = ScreeningResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aml_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScreeningResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScreeningResult) ProtoMessage() {}

func (x *ScreeningResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aml_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScreeningResult.ProtoReflect.Descriptor instead.
func (*ScreeningResult) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_service_proto_rawDescGZIP(), []int{7}
}

func (x *ScreeningResult) GetHitsDetected() bool {
	if x != nil {
		return x.HitsDetected
	}
	return false
}

func (x *ScreeningResult) GetHitsCount() uint32 {
	if x != nil {
		return x.HitsCount
	}
	return 0
}

func (x *ScreeningResult) GetConfirmedHit() bool {
	if x != nil {
		return x.ConfirmedHit
	}
	return false
}

func (x *ScreeningResult) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *ScreeningResult) GetCaseUrl() string {
	if x != nil {
		return x.CaseUrl
	}
	return ""
}

func (x *ScreeningResult) GetReportData() string {
	if x != nil {
		return x.ReportData
	}
	return ""
}

func (x *ScreeningResult) GetHits() []*Hit {
	if x != nil {
		return x.Hits
	}
	return nil
}

// Represents a single hit/match found during screening
type Hit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Source of the watchlist where the hit was found
	Source string `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	// TrackWizz ID for the watchlist record
	WatchlistSourceId string `protobuf:"bytes,2,opt,name=watchlist_source_id,json=watchlistSourceId,proto3" json:"watchlist_source_id,omitempty"`
	// Type of match (Confirmed, Probable, etc.)
	MatchType MatchType `protobuf:"varint,3,opt,name=match_type,json=matchType,proto3,enum=vendorgateway.aml.MatchType" json:"match_type,omitempty"`
	// Confidence score assigned to the match
	Score float64 `protobuf:"fixed64,4,opt,name=score,proto3" json:"score,omitempty"`
	// Attributes that confirmed the match
	ConfirmedMatchingAttributes string `protobuf:"bytes,5,opt,name=confirmed_matching_attributes,json=confirmedMatchingAttributes,proto3" json:"confirmed_matching_attributes,omitempty"`
}

func (x *Hit) Reset() {
	*x = Hit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_aml_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Hit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Hit) ProtoMessage() {}

func (x *Hit) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_aml_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Hit.ProtoReflect.Descriptor instead.
func (*Hit) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_aml_service_proto_rawDescGZIP(), []int{8}
}

func (x *Hit) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *Hit) GetWatchlistSourceId() string {
	if x != nil {
		return x.WatchlistSourceId
	}
	return ""
}

func (x *Hit) GetMatchType() MatchType {
	if x != nil {
		return x.MatchType
	}
	return MatchType_MATCH_TYPE_UNSPECIFIED
}

func (x *Hit) GetScore() float64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *Hit) GetConfirmedMatchingAttributes() string {
	if x != nil {
		return x.ConfirmedMatchingAttributes
	}
	return ""
}

var File_api_vendorgateway_aml_service_proto protoreflect.FileDescriptor

var file_api_vendorgateway_aml_service_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x61, 0x6d, 0x6c, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x1a, 0x12, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6d,
	0x6c, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70,
	0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x70, 0x72, 0x6f, 0x6f, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x67, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x69, 0x6e, 0x63,
	0x6f, 0x6d, 0x65, 0x5f, 0x73, 0x6c, 0x61, 0x62, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x6d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x6f, 0x6c, 0x69,
	0x74, 0x69, 0x63, 0x61, 0x6c, 0x6c, 0x79, 0x5f, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x64, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x87, 0x03, 0x0a, 0x15, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2a,
	0x0a, 0x06, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e,
	0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x41, 0x6d, 0x6c, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x06, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x29, 0x0a, 0x07, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x61, 0x6d,
	0x6c, 0x2e, 0x41, 0x6d, 0x6c, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x52, 0x07, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x57, 0x0a, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x61, 0x6d, 0x6c, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2f,
	0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x22,
	0xa4, 0x03, 0x0a, 0x16, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x2b, 0x0a, 0x11, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x6a, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x39, 0x0a, 0x0e,
	0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x52, 0x65, 0x6a, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x0d, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x12, 0x41, 0x0a, 0x0c, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x73, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x1f, 0x0a,
	0x0b, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0a, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x36,
	0x0a, 0x0d, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0c, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xb1, 0x0a, 0x0a, 0x0f, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x36, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61,
	0x6d, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d,
	0x65, 0x52, 0x0a, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a,
	0x0b, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0a, 0x6d, 0x6f,
	0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x48, 0x0a, 0x0e,
	0x6d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x61, 0x72, 0x69, 0x74, 0x61,
	0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x6d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3f, 0x0a, 0x0b, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65,
	0x5f, 0x73, 0x6c, 0x61, 0x62, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x53, 0x6c, 0x61, 0x62, 0x52, 0x0a, 0x69, 0x6e, 0x63,
	0x6f, 0x6d, 0x65, 0x53, 0x6c, 0x61, 0x62, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x6e, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x6e,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x4b, 0x0a, 0x0b, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x4e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x69, 0x74, 0x79, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x61,
	0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x14,
	0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x12, 0x70,
	0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x34, 0x0a, 0x16, 0x64, 0x72, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x69, 0x63,
	0x65, 0x6e, 0x73, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x14, 0x64, 0x72, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73,
	0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x50, 0x0a, 0x1b, 0x64, 0x72, 0x69, 0x76, 0x69,
	0x6e, 0x67, 0x5f, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52,
	0x18, 0x64, 0x72, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x45,
	0x78, 0x70, 0x69, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x6f, 0x74,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x6f, 0x74,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x08, 0x70, 0x6f, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x6f, 0x66, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x70,
	0x6f, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x35, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x62, 0x69, 0x72, 0x74,
	0x68, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x65,
	0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x12, 0x4e, 0x0a, 0x11, 0x70, 0x65, 0x72, 0x6d, 0x61,
	0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x10, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x58, 0x0a, 0x16, 0x63, 0x6f, 0x72, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x73,
	0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x15, 0x63, 0x6f, 0x72, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x6a, 0x0a, 0x1a, 0x70, 0x6f, 0x6c, 0x69, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x6c, 0x79,
	0x5f, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x74,
	0x69, 0x63, 0x61, 0x6c, 0x6c, 0x79, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x64, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x18, 0x70, 0x6f, 0x6c, 0x69, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x6c, 0x79,
	0x45, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4b, 0x0a,
	0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x65, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x83, 0x03, 0x0a, 0x18, 0x49,
	0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2f, 0x0a,
	0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x17,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x52, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x34, 0x0a, 0x07, 0x70, 0x75, 0x72,
	0x70, 0x6f, 0x73, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x50,
	0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x52, 0x07, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x12,
	0x4f, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x22, 0xe4, 0x02, 0x0a, 0x19, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x47, 0x0a, 0x0e, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x4f,
	0x76, 0x65, 0x72, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x6f, 0x76,
	0x65, 0x72, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x35, 0x0a, 0x16, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5a, 0x0a, 0x15, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x13, 0x75, 0x73, 0x65, 0x72, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xd1, 0x03, 0x0a, 0x13, 0x55, 0x73, 0x65, 0x72,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x53, 0x0a, 0x12, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65, 0x52, 0x11, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x4d, 0x0a,
	0x10, 0x73, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x53, 0x75, 0x67, 0x67,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x73, 0x75, 0x67,
	0x67, 0x65, 0x73, 0x74, 0x65, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x63, 0x0a, 0x18,
	0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e,
	0x67, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61,
	0x6d, 0x6c, 0x2e, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x16, 0x70, 0x75, 0x72, 0x70, 0x6f,
	0x73, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x27, 0x0a, 0x0f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x35, 0x0a, 0x16, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x38, 0x0a, 0x18, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x16, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46,
	0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xbe, 0x02, 0x0a, 0x16,
	0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x35, 0x0a, 0x16,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x38, 0x0a, 0x18, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x16, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4d, 0x0a,
	0x10, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xfb, 0x01, 0x0a,
	0x0f, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x23, 0x0a, 0x0d, 0x68, 0x69, 0x74, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x68, 0x69, 0x74, 0x73, 0x44, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x69, 0x74, 0x73, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x68, 0x69, 0x74, 0x73, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65,
	0x64, 0x5f, 0x68, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x48, 0x69, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65,
	0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x73, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a,
	0x0b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2a,
	0x0a, 0x04, 0x68, 0x69, 0x74, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c,
	0x2e, 0x48, 0x69, 0x74, 0x52, 0x04, 0x68, 0x69, 0x74, 0x73, 0x22, 0xe4, 0x01, 0x0a, 0x03, 0x48,
	0x69, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x77, 0x61,
	0x74, 0x63, 0x68, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x77, 0x61, 0x74, 0x63, 0x68, 0x6c, 0x69,
	0x73, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0a, 0x6d, 0x61,
	0x74, 0x63, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61,
	0x6d, 0x6c, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6d, 0x61,
	0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x42, 0x0a,
	0x1d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x69, 0x6e, 0x67, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x1b, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x73, 0x2a, 0x7b, 0x0a, 0x0b, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1c, 0x0a, 0x18, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18,
	0x0a, 0x14, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d,
	0x41, 0x54, 0x43, 0x48, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x4d, 0x41, 0x54, 0x43,
	0x48, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4d, 0x41, 0x54,
	0x43, 0x48, 0x45, 0x44, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x03, 0x2a, 0x7e,
	0x0a, 0x07, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x52, 0x4f,
	0x44, 0x55, 0x43, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x4d, 0x55,
	0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x50,
	0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x02, 0x12, 0x15, 0x0a,
	0x11, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43,
	0x4b, 0x53, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f,
	0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x04, 0x2a, 0x63,
	0x0a, 0x07, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x55, 0x52,
	0x50, 0x4f, 0x53, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x50, 0x55, 0x52, 0x50, 0x4f, 0x53, 0x45, 0x5f, 0x49, 0x4e,
	0x49, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x49, 0x4e, 0x47, 0x10,
	0x01, 0x12, 0x20, 0x0a, 0x1c, 0x50, 0x55, 0x52, 0x50, 0x4f, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x4e,
	0x54, 0x49, 0x4e, 0x55, 0x4f, 0x55, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x49, 0x4e,
	0x47, 0x10, 0x02, 0x2a, 0x69, 0x0a, 0x0d, 0x4f, 0x76, 0x65, 0x72, 0x61, 0x6c, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x1a, 0x4f, 0x56, 0x45, 0x52, 0x41, 0x4c, 0x4c, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x4f, 0x56, 0x45, 0x52, 0x41, 0x4c, 0x4c, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x45, 0x44, 0x10,
	0x01, 0x12, 0x1b, 0x0a, 0x17, 0x4f, 0x56, 0x45, 0x52, 0x41, 0x4c, 0x4c, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x02, 0x2a, 0x77,
	0x0a, 0x11, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x75, 0x74, 0x63,
	0x6f, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x1e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x4f, 0x55, 0x54, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4f, 0x55, 0x54, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x53, 0x55,
	0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4f, 0x55, 0x54, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x02, 0x2a, 0x89, 0x01, 0x0a, 0x0f, 0x53, 0x75, 0x67, 0x67,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x1c, 0x53,
	0x55, 0x47, 0x47, 0x45, 0x53, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c, 0x0a,
	0x18, 0x53, 0x55, 0x47, 0x47, 0x45, 0x53, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x53,
	0x55, 0x47, 0x47, 0x45, 0x53, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x55, 0x47, 0x47,
	0x45, 0x53, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x4f,
	0x50, 0x10, 0x03, 0x2a, 0x5a, 0x0a, 0x09, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1a, 0x0a, 0x16, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14,
	0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49,
	0x52, 0x4d, 0x45, 0x44, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x42, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x02, 0x32,
	0xdc, 0x01, 0x0a, 0x03, 0x41, 0x6d, 0x6c, 0x12, 0x65, 0x0a, 0x0e, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x28, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e,
	0x0a, 0x11, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x69, 0x6e, 0x67, 0x12, 0x2b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x5c,
	0x0a, 0x2c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x6d, 0x6c, 0x5a, 0x2c,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x61, 0x6d, 0x6c, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_aml_service_proto_rawDescOnce sync.Once
	file_api_vendorgateway_aml_service_proto_rawDescData = file_api_vendorgateway_aml_service_proto_rawDesc
)

func file_api_vendorgateway_aml_service_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_aml_service_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_aml_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_aml_service_proto_rawDescData)
	})
	return file_api_vendorgateway_aml_service_proto_rawDescData
}

var file_api_vendorgateway_aml_service_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_api_vendorgateway_aml_service_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_api_vendorgateway_aml_service_proto_goTypes = []interface{}{
	(MatchStatus)(0),                     // 0: vendorgateway.aml.MatchStatus
	(Product)(0),                         // 1: vendorgateway.aml.Product
	(Purpose)(0),                         // 2: vendorgateway.aml.Purpose
	(OverallStatus)(0),                   // 3: vendorgateway.aml.OverallStatus
	(ValidationOutcome)(0),               // 4: vendorgateway.aml.ValidationOutcome
	(SuggestedAction)(0),                 // 5: vendorgateway.aml.SuggestedAction
	(MatchType)(0),                       // 6: vendorgateway.aml.MatchType
	(*ScreenCustomerRequest)(nil),        // 7: vendorgateway.aml.ScreenCustomerRequest
	(*ScreenCustomerResponse)(nil),       // 8: vendorgateway.aml.ScreenCustomerResponse
	(*CustomerDetails)(nil),              // 9: vendorgateway.aml.CustomerDetails
	(*InitiateScreeningRequest)(nil),     // 10: vendorgateway.aml.InitiateScreeningRequest
	(*InitiateScreeningResponse)(nil),    // 11: vendorgateway.aml.InitiateScreeningResponse
	(*UserScreeningResult)(nil),          // 12: vendorgateway.aml.UserScreeningResult
	(*PurposeScreeningResult)(nil),       // 13: vendorgateway.aml.PurposeScreeningResult
	(*ScreeningResult)(nil),              // 14: vendorgateway.aml.ScreeningResult
	(*Hit)(nil),                          // 15: vendorgateway.aml.Hit
	(*vendorgateway.RequestHeader)(nil),  // 16: vendorgateway.RequestHeader
	(aml.AmlEntity)(0),                   // 17: aml.AmlEntity
	(aml.AmlProduct)(0),                  // 18: aml.AmlProduct
	(common.Owner)(0),                    // 19: api.typesv2.common.Owner
	(*rpc.Status)(nil),                   // 20: rpc.Status
	(aml.RejectionCode)(0),               // 21: aml.RejectionCode
	(*aml.MatchDetails)(nil),             // 22: aml.MatchDetails
	(*common.Name)(nil),                  // 23: api.typesv2.common.Name
	(common.Gender)(0),                   // 24: api.typesv2.common.Gender
	(common.MaritalStatus)(0),            // 25: api.typesv2.common.MaritalStatus
	(common.IncomeSlab)(0),               // 26: api.typesv2.common.IncomeSlab
	(common.Nationality)(0),              // 27: api.typesv2.common.Nationality
	(*date.Date)(nil),                    // 28: google.type.Date
	(common.DocumentProofType)(0),        // 29: api.typesv2.common.DocumentProofType
	(*common.PhoneNumber)(nil),           // 30: api.typesv2.common.PhoneNumber
	(*common.PostalAddress)(nil),         // 31: api.typesv2.common.PostalAddress
	(common.PoliticallyExposedStatus)(0), // 32: api.typesv2.common.PoliticallyExposedStatus
	(common.EmploymentType)(0),           // 33: api.typesv2.common.EmploymentType
}
var file_api_vendorgateway_aml_service_proto_depIdxs = []int32{
	16, // 0: vendorgateway.aml.ScreenCustomerRequest.header:type_name -> vendorgateway.RequestHeader
	17, // 1: vendorgateway.aml.ScreenCustomerRequest.entity:type_name -> aml.AmlEntity
	18, // 2: vendorgateway.aml.ScreenCustomerRequest.product:type_name -> aml.AmlProduct
	9,  // 3: vendorgateway.aml.ScreenCustomerRequest.customer_details:type_name -> vendorgateway.aml.CustomerDetails
	19, // 4: vendorgateway.aml.ScreenCustomerRequest.owner:type_name -> api.typesv2.common.Owner
	20, // 5: vendorgateway.aml.ScreenCustomerResponse.status:type_name -> rpc.Status
	21, // 6: vendorgateway.aml.ScreenCustomerResponse.rejection_code:type_name -> aml.RejectionCode
	0,  // 7: vendorgateway.aml.ScreenCustomerResponse.match_status:type_name -> vendorgateway.aml.MatchStatus
	22, // 8: vendorgateway.aml.ScreenCustomerResponse.match_details:type_name -> aml.MatchDetails
	23, // 9: vendorgateway.aml.CustomerDetails.name:type_name -> api.typesv2.common.Name
	23, // 10: vendorgateway.aml.CustomerDetails.father_name:type_name -> api.typesv2.common.Name
	23, // 11: vendorgateway.aml.CustomerDetails.mother_name:type_name -> api.typesv2.common.Name
	24, // 12: vendorgateway.aml.CustomerDetails.gender:type_name -> api.typesv2.common.Gender
	25, // 13: vendorgateway.aml.CustomerDetails.marital_status:type_name -> api.typesv2.common.MaritalStatus
	26, // 14: vendorgateway.aml.CustomerDetails.income_slab:type_name -> api.typesv2.common.IncomeSlab
	27, // 15: vendorgateway.aml.CustomerDetails.nationality:type_name -> api.typesv2.common.Nationality
	28, // 16: vendorgateway.aml.CustomerDetails.passport_expiry_date:type_name -> google.type.Date
	28, // 17: vendorgateway.aml.CustomerDetails.driving_license_expiry_date:type_name -> google.type.Date
	29, // 18: vendorgateway.aml.CustomerDetails.poa_type:type_name -> api.typesv2.common.DocumentProofType
	30, // 19: vendorgateway.aml.CustomerDetails.phone_number:type_name -> api.typesv2.common.PhoneNumber
	28, // 20: vendorgateway.aml.CustomerDetails.date_of_birth:type_name -> google.type.Date
	31, // 21: vendorgateway.aml.CustomerDetails.permanent_address:type_name -> api.typesv2.common.PostalAddress
	31, // 22: vendorgateway.aml.CustomerDetails.correspondence_address:type_name -> api.typesv2.common.PostalAddress
	32, // 23: vendorgateway.aml.CustomerDetails.politically_exposed_status:type_name -> api.typesv2.common.PoliticallyExposedStatus
	33, // 24: vendorgateway.aml.CustomerDetails.employment_type:type_name -> api.typesv2.common.EmploymentType
	16, // 25: vendorgateway.aml.InitiateScreeningRequest.header:type_name -> vendorgateway.RequestHeader
	19, // 26: vendorgateway.aml.InitiateScreeningRequest.owner:type_name -> api.typesv2.common.Owner
	1,  // 27: vendorgateway.aml.InitiateScreeningRequest.product:type_name -> vendorgateway.aml.Product
	2,  // 28: vendorgateway.aml.InitiateScreeningRequest.purpose:type_name -> vendorgateway.aml.Purpose
	9,  // 29: vendorgateway.aml.InitiateScreeningRequest.user_details:type_name -> vendorgateway.aml.CustomerDetails
	20, // 30: vendorgateway.aml.InitiateScreeningResponse.status:type_name -> rpc.Status
	3,  // 31: vendorgateway.aml.InitiateScreeningResponse.overall_status:type_name -> vendorgateway.aml.OverallStatus
	12, // 32: vendorgateway.aml.InitiateScreeningResponse.user_screening_result:type_name -> vendorgateway.aml.UserScreeningResult
	4,  // 33: vendorgateway.aml.UserScreeningResult.validation_outcome:type_name -> vendorgateway.aml.ValidationOutcome
	5,  // 34: vendorgateway.aml.UserScreeningResult.suggested_action:type_name -> vendorgateway.aml.SuggestedAction
	13, // 35: vendorgateway.aml.UserScreeningResult.purpose_screening_result:type_name -> vendorgateway.aml.PurposeScreeningResult
	14, // 36: vendorgateway.aml.PurposeScreeningResult.screening_result:type_name -> vendorgateway.aml.ScreeningResult
	15, // 37: vendorgateway.aml.ScreeningResult.hits:type_name -> vendorgateway.aml.Hit
	6,  // 38: vendorgateway.aml.Hit.match_type:type_name -> vendorgateway.aml.MatchType
	7,  // 39: vendorgateway.aml.Aml.ScreenCustomer:input_type -> vendorgateway.aml.ScreenCustomerRequest
	10, // 40: vendorgateway.aml.Aml.InitiateScreening:input_type -> vendorgateway.aml.InitiateScreeningRequest
	8,  // 41: vendorgateway.aml.Aml.ScreenCustomer:output_type -> vendorgateway.aml.ScreenCustomerResponse
	11, // 42: vendorgateway.aml.Aml.InitiateScreening:output_type -> vendorgateway.aml.InitiateScreeningResponse
	41, // [41:43] is the sub-list for method output_type
	39, // [39:41] is the sub-list for method input_type
	39, // [39:39] is the sub-list for extension type_name
	39, // [39:39] is the sub-list for extension extendee
	0,  // [0:39] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_aml_service_proto_init() }
func file_api_vendorgateway_aml_service_proto_init() {
	if File_api_vendorgateway_aml_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendorgateway_aml_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScreenCustomerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_aml_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScreenCustomerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_aml_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_aml_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateScreeningRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_aml_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateScreeningResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_aml_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserScreeningResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_aml_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PurposeScreeningResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_aml_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScreeningResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_aml_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Hit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_aml_service_proto_rawDesc,
			NumEnums:      7,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_vendorgateway_aml_service_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_aml_service_proto_depIdxs,
		EnumInfos:         file_api_vendorgateway_aml_service_proto_enumTypes,
		MessageInfos:      file_api_vendorgateway_aml_service_proto_msgTypes,
	}.Build()
	File_api_vendorgateway_aml_service_proto = out.File
	file_api_vendorgateway_aml_service_proto_rawDesc = nil
	file_api_vendorgateway_aml_service_proto_goTypes = nil
	file_api_vendorgateway_aml_service_proto_depIdxs = nil
}
