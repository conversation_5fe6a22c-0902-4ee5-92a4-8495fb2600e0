// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendornotification.aml.tss;

import "api/vendors/tss/tss.proto";
import "api/vendors/tss/whs501_callback.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/aml/tss";
option java_package = "com.github.epifi.gamma.api.vendornotification.aml.tss";

service TSS {
  rpc ProcessWebhookCallBack (vendors.tss.ProcessWebhookCallBackRequest) returns (vendors.tss.ProcessWebhookCallBackResponse) {
    option (google.api.http) = {
      post: "/aml/tss/response"
      body: "*"
    };
  }

  // Webhook to capture and process initial screening case details.
  // Triggered when the case reviewer clicks on "Close With No Action"
  // or "Close With Action" buttons on the TSS's Screenza web portal.
  rpc ProcessWHS501CallbackForEpifi (vendors.tss.ProcessWHS501CallbackRequest) returns (vendors.tss.ProcessWHS501CallbackResponse) {
    option (google.api.http) = {
      post: "/aml/tss/whs501/callback/epifi"
      body: "*"
    };
  }

  // Same webhook as above, but for receiving callbacks from Stock Guardian specific TSS tenant.
  // Data of Stock Guardian users has to be kept separate from data of other organizations
  // on Epifi(third-party service provider for SG) side by storing that data in a separate DB
  // and on TSS side by creating a separate tenant.
  // Hence, a separate TSS tenant is created for SG even though Epifi is the third-party service provider
  // for both SG and other organizations.
  rpc ProcessWHS501CallbackForSG (vendors.tss.ProcessWHS501CallbackRequest) returns (vendors.tss.ProcessWHS501CallbackResponse) {
    option (google.api.http) = {
      post: "/aml/tss/whs501/callback/sg"
      body: "*"
    };
  }

}
