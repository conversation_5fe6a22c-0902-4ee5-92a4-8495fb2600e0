// Code generated by MockGen. DO NOT EDIT.
// Source: api/vendornotification/aml/tss/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	tss "github.com/epifi/gamma/api/vendors/tss"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockTSSClient is a mock of TSSClient interface.
type MockTSSClient struct {
	ctrl     *gomock.Controller
	recorder *MockTSSClientMockRecorder
}

// MockTSSClientMockRecorder is the mock recorder for MockTSSClient.
type MockTSSClientMockRecorder struct {
	mock *MockTSSClient
}

// NewMockTSSClient creates a new mock instance.
func NewMockTSSClient(ctrl *gomock.Controller) *MockTSSClient {
	mock := &MockTSSClient{ctrl: ctrl}
	mock.recorder = &MockTSSClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTSSClient) EXPECT() *MockTSSClientMockRecorder {
	return m.recorder
}

// ProcessWHS501CallbackForEpifi mocks base method.
func (m *MockTSSClient) ProcessWHS501CallbackForEpifi(ctx context.Context, in *tss.ProcessWHS501CallbackRequest, opts ...grpc.CallOption) (*tss.ProcessWHS501CallbackResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessWHS501CallbackForEpifi", varargs...)
	ret0, _ := ret[0].(*tss.ProcessWHS501CallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessWHS501CallbackForEpifi indicates an expected call of ProcessWHS501CallbackForEpifi.
func (mr *MockTSSClientMockRecorder) ProcessWHS501CallbackForEpifi(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessWHS501CallbackForEpifi", reflect.TypeOf((*MockTSSClient)(nil).ProcessWHS501CallbackForEpifi), varargs...)
}

// ProcessWHS501CallbackForSG mocks base method.
func (m *MockTSSClient) ProcessWHS501CallbackForSG(ctx context.Context, in *tss.ProcessWHS501CallbackRequest, opts ...grpc.CallOption) (*tss.ProcessWHS501CallbackResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessWHS501CallbackForSG", varargs...)
	ret0, _ := ret[0].(*tss.ProcessWHS501CallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessWHS501CallbackForSG indicates an expected call of ProcessWHS501CallbackForSG.
func (mr *MockTSSClientMockRecorder) ProcessWHS501CallbackForSG(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessWHS501CallbackForSG", reflect.TypeOf((*MockTSSClient)(nil).ProcessWHS501CallbackForSG), varargs...)
}

// ProcessWebhookCallBack mocks base method.
func (m *MockTSSClient) ProcessWebhookCallBack(ctx context.Context, in *tss.ProcessWebhookCallBackRequest, opts ...grpc.CallOption) (*tss.ProcessWebhookCallBackResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessWebhookCallBack", varargs...)
	ret0, _ := ret[0].(*tss.ProcessWebhookCallBackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessWebhookCallBack indicates an expected call of ProcessWebhookCallBack.
func (mr *MockTSSClientMockRecorder) ProcessWebhookCallBack(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessWebhookCallBack", reflect.TypeOf((*MockTSSClient)(nil).ProcessWebhookCallBack), varargs...)
}

// MockTSSServer is a mock of TSSServer interface.
type MockTSSServer struct {
	ctrl     *gomock.Controller
	recorder *MockTSSServerMockRecorder
}

// MockTSSServerMockRecorder is the mock recorder for MockTSSServer.
type MockTSSServerMockRecorder struct {
	mock *MockTSSServer
}

// NewMockTSSServer creates a new mock instance.
func NewMockTSSServer(ctrl *gomock.Controller) *MockTSSServer {
	mock := &MockTSSServer{ctrl: ctrl}
	mock.recorder = &MockTSSServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTSSServer) EXPECT() *MockTSSServerMockRecorder {
	return m.recorder
}

// ProcessWHS501CallbackForEpifi mocks base method.
func (m *MockTSSServer) ProcessWHS501CallbackForEpifi(arg0 context.Context, arg1 *tss.ProcessWHS501CallbackRequest) (*tss.ProcessWHS501CallbackResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessWHS501CallbackForEpifi", arg0, arg1)
	ret0, _ := ret[0].(*tss.ProcessWHS501CallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessWHS501CallbackForEpifi indicates an expected call of ProcessWHS501CallbackForEpifi.
func (mr *MockTSSServerMockRecorder) ProcessWHS501CallbackForEpifi(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessWHS501CallbackForEpifi", reflect.TypeOf((*MockTSSServer)(nil).ProcessWHS501CallbackForEpifi), arg0, arg1)
}

// ProcessWHS501CallbackForSG mocks base method.
func (m *MockTSSServer) ProcessWHS501CallbackForSG(arg0 context.Context, arg1 *tss.ProcessWHS501CallbackRequest) (*tss.ProcessWHS501CallbackResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessWHS501CallbackForSG", arg0, arg1)
	ret0, _ := ret[0].(*tss.ProcessWHS501CallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessWHS501CallbackForSG indicates an expected call of ProcessWHS501CallbackForSG.
func (mr *MockTSSServerMockRecorder) ProcessWHS501CallbackForSG(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessWHS501CallbackForSG", reflect.TypeOf((*MockTSSServer)(nil).ProcessWHS501CallbackForSG), arg0, arg1)
}

// ProcessWebhookCallBack mocks base method.
func (m *MockTSSServer) ProcessWebhookCallBack(arg0 context.Context, arg1 *tss.ProcessWebhookCallBackRequest) (*tss.ProcessWebhookCallBackResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessWebhookCallBack", arg0, arg1)
	ret0, _ := ret[0].(*tss.ProcessWebhookCallBackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessWebhookCallBack indicates an expected call of ProcessWebhookCallBack.
func (mr *MockTSSServerMockRecorder) ProcessWebhookCallBack(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessWebhookCallBack", reflect.TypeOf((*MockTSSServer)(nil).ProcessWebhookCallBack), arg0, arg1)
}

// MockUnsafeTSSServer is a mock of UnsafeTSSServer interface.
type MockUnsafeTSSServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeTSSServerMockRecorder
}

// MockUnsafeTSSServerMockRecorder is the mock recorder for MockUnsafeTSSServer.
type MockUnsafeTSSServerMockRecorder struct {
	mock *MockUnsafeTSSServer
}

// NewMockUnsafeTSSServer creates a new mock instance.
func NewMockUnsafeTSSServer(ctrl *gomock.Controller) *MockUnsafeTSSServer {
	mock := &MockUnsafeTSSServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeTSSServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeTSSServer) EXPECT() *MockUnsafeTSSServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedTSSServer mocks base method.
func (m *MockUnsafeTSSServer) mustEmbedUnimplementedTSSServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedTSSServer")
}

// mustEmbedUnimplementedTSSServer indicates an expected call of mustEmbedUnimplementedTSSServer.
func (mr *MockUnsafeTSSServerMockRecorder) mustEmbedUnimplementedTSSServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedTSSServer", reflect.TypeOf((*MockUnsafeTSSServer)(nil).mustEmbedUnimplementedTSSServer))
}
