// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendornotification/aml/tss/service.proto

package tss

import (
	tss "github.com/epifi/gamma/api/vendors/tss"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_api_vendornotification_aml_tss_service_proto protoreflect.FileDescriptor

var file_api_vendornotification_aml_tss_service_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x61, 0x6d, 0x6c, 0x2f, 0x74, 0x73, 0x73,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x74, 0x73, 0x73, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x74, 0x73, 0x73, 0x2f, 0x74, 0x73, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x2f, 0x74, 0x73, 0x73, 0x2f, 0x77, 0x68, 0x73, 0x35, 0x30, 0x31, 0x5f, 0x63, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0xd9, 0x03, 0x0a, 0x03, 0x54,
	0x53, 0x53, 0x12, 0x8f, 0x01, 0x0a, 0x16, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x57, 0x65,
	0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x43, 0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b, 0x12, 0x2a, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x43, 0x61, 0x6c, 0x6c, 0x42, 0x61,
	0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x57,
	0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x43, 0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x3a, 0x01,
	0x2a, 0x22, 0x11, 0x2f, 0x61, 0x6d, 0x6c, 0x2f, 0x74, 0x73, 0x73, 0x2f, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa1, 0x01, 0x0a, 0x1d, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x57, 0x48, 0x53, 0x35, 0x30, 0x31, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x46, 0x6f,
	0x72, 0x45, 0x70, 0x69, 0x66, 0x69, 0x12, 0x29, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x74, 0x73, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x57, 0x48, 0x53, 0x35,
	0x30, 0x31, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x57, 0x48, 0x53, 0x35, 0x30, 0x31, 0x43, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x29, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x23, 0x3a, 0x01, 0x2a, 0x22, 0x1e, 0x2f, 0x61, 0x6d, 0x6c, 0x2f, 0x74,
	0x73, 0x73, 0x2f, 0x77, 0x68, 0x73, 0x35, 0x30, 0x31, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x12, 0x9b, 0x01, 0x0a, 0x1a, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x57, 0x48, 0x53, 0x35, 0x30, 0x31, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x46, 0x6f, 0x72, 0x53, 0x47, 0x12, 0x29, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x57, 0x48, 0x53,
	0x35, 0x30, 0x31, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73,
	0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x57, 0x48, 0x53, 0x35, 0x30, 0x31, 0x43, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x26,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x3a, 0x01, 0x2a, 0x22, 0x1b, 0x2f, 0x61, 0x6d, 0x6c, 0x2f,
	0x74, 0x73, 0x73, 0x2f, 0x77, 0x68, 0x73, 0x35, 0x30, 0x31, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x2f, 0x73, 0x67, 0x42, 0x6e, 0x0a, 0x35, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x61, 0x6d, 0x6c, 0x2e, 0x74, 0x73, 0x73, 0x5a,
	0x35, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x61,
	0x6d, 0x6c, 0x2f, 0x74, 0x73, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_api_vendornotification_aml_tss_service_proto_goTypes = []interface{}{
	(*tss.ProcessWebhookCallBackRequest)(nil),  // 0: vendors.tss.ProcessWebhookCallBackRequest
	(*tss.ProcessWHS501CallbackRequest)(nil),   // 1: vendors.tss.ProcessWHS501CallbackRequest
	(*tss.ProcessWebhookCallBackResponse)(nil), // 2: vendors.tss.ProcessWebhookCallBackResponse
	(*tss.ProcessWHS501CallbackResponse)(nil),  // 3: vendors.tss.ProcessWHS501CallbackResponse
}
var file_api_vendornotification_aml_tss_service_proto_depIdxs = []int32{
	0, // 0: vendornotification.aml.tss.TSS.ProcessWebhookCallBack:input_type -> vendors.tss.ProcessWebhookCallBackRequest
	1, // 1: vendornotification.aml.tss.TSS.ProcessWHS501CallbackForEpifi:input_type -> vendors.tss.ProcessWHS501CallbackRequest
	1, // 2: vendornotification.aml.tss.TSS.ProcessWHS501CallbackForSG:input_type -> vendors.tss.ProcessWHS501CallbackRequest
	2, // 3: vendornotification.aml.tss.TSS.ProcessWebhookCallBack:output_type -> vendors.tss.ProcessWebhookCallBackResponse
	3, // 4: vendornotification.aml.tss.TSS.ProcessWHS501CallbackForEpifi:output_type -> vendors.tss.ProcessWHS501CallbackResponse
	3, // 5: vendornotification.aml.tss.TSS.ProcessWHS501CallbackForSG:output_type -> vendors.tss.ProcessWHS501CallbackResponse
	3, // [3:6] is the sub-list for method output_type
	0, // [0:3] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_vendornotification_aml_tss_service_proto_init() }
func file_api_vendornotification_aml_tss_service_proto_init() {
	if File_api_vendornotification_aml_tss_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendornotification_aml_tss_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_vendornotification_aml_tss_service_proto_goTypes,
		DependencyIndexes: file_api_vendornotification_aml_tss_service_proto_depIdxs,
	}.Build()
	File_api_vendornotification_aml_tss_service_proto = out.File
	file_api_vendornotification_aml_tss_service_proto_rawDesc = nil
	file_api_vendornotification_aml_tss_service_proto_goTypes = nil
	file_api_vendornotification_aml_tss_service_proto_depIdxs = nil
}
