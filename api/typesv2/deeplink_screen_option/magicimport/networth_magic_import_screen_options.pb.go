// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/deeplink_screen_option/magicimport/networth_magic_import_screen_options.proto

package magicimport

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	deeplink_screen_option "github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	properties "github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Switch type to show different switch options, maps to switch_value in Switch
// e.g. Fun mode, Serious mode
type MagicImportSwitchType int32

const (
	MagicImportSwitchType_MAGIC_IMPORT_SWITCH_TYPE_UNSPECIFIED  MagicImportSwitchType = 0
	MagicImportSwitchType_MAGIC_IMPORT_SWITCH_TYPE_FUN_MODE     MagicImportSwitchType = 1
	MagicImportSwitchType_MAGIC_IMPORT_SWITCH_TYPE_SERIOUS_MODE MagicImportSwitchType = 2
)

// Enum value maps for MagicImportSwitchType.
var (
	MagicImportSwitchType_name = map[int32]string{
		0: "MAGIC_IMPORT_SWITCH_TYPE_UNSPECIFIED",
		1: "MAGIC_IMPORT_SWITCH_TYPE_FUN_MODE",
		2: "MAGIC_IMPORT_SWITCH_TYPE_SERIOUS_MODE",
	}
	MagicImportSwitchType_value = map[string]int32{
		"MAGIC_IMPORT_SWITCH_TYPE_UNSPECIFIED":  0,
		"MAGIC_IMPORT_SWITCH_TYPE_FUN_MODE":     1,
		"MAGIC_IMPORT_SWITCH_TYPE_SERIOUS_MODE": 2,
	}
)

func (x MagicImportSwitchType) Enum() *MagicImportSwitchType {
	p := new(MagicImportSwitchType)
	*p = x
	return p
}

func (x MagicImportSwitchType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MagicImportSwitchType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_enumTypes[0].Descriptor()
}

func (MagicImportSwitchType) Type() protoreflect.EnumType {
	return &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_enumTypes[0]
}

func (x MagicImportSwitchType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MagicImportSwitchType.Descriptor instead.
func (MagicImportSwitchType) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescGZIP(), []int{0}
}

// Deeplink : NETWORTH_MAGIC_IMPORT_SCREEN
type NetworthMagicImportScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,7,opt,name=header,proto3" json:"header,omitempty"`
	// Toolbar
	HeaderBar                 *ui.HeaderBar       `protobuf:"bytes,1,opt,name=header_bar,json=headerBar,proto3" json:"header_bar,omitempty"`
	ScanScreen                *ScanStateScreen    `protobuf:"bytes,2,opt,name=scan_screen,json=scanScreen,proto3" json:"scan_screen,omitempty"`
	PreviewScreen             *PreviewStateScreen `protobuf:"bytes,3,opt,name=preview_screen,json=previewScreen,proto3" json:"preview_screen,omitempty"`
	LoadingScreen             *LoadingStateScreen `protobuf:"bytes,4,opt,name=loading_screen,json=loadingScreen,proto3" json:"loading_screen,omitempty"`
	NetworkErrorScreen        *ErrorStateScreen   `protobuf:"bytes,6,opt,name=network_error_screen,json=networkErrorScreen,proto3" json:"network_error_screen,omitempty"`
	LimitExhaustedErrorScreen *ErrorStateScreen   `protobuf:"bytes,8,opt,name=limit_exhausted_error_screen,json=limitExhaustedErrorScreen,proto3" json:"limit_exhausted_error_screen,omitempty"`
	IntroPageScreen           *IntroPageScreen    `protobuf:"bytes,9,opt,name=intro_page_screen,json=introPageScreen,proto3" json:"intro_page_screen,omitempty"`
	// background color for all intermediate screens
	BgColor *widget.BackgroundColour `protobuf:"bytes,10,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// Progress bar details
	ProgressBarDetails *ProgressBarDetails `protobuf:"bytes,11,opt,name=progress_bar_details,json=progressBarDetails,proto3" json:"progress_bar_details,omitempty"`
}

func (x *NetworthMagicImportScreenOptions) Reset() {
	*x = NetworthMagicImportScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetworthMagicImportScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworthMagicImportScreenOptions) ProtoMessage() {}

func (x *NetworthMagicImportScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworthMagicImportScreenOptions.ProtoReflect.Descriptor instead.
func (*NetworthMagicImportScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescGZIP(), []int{0}
}

func (x *NetworthMagicImportScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *NetworthMagicImportScreenOptions) GetHeaderBar() *ui.HeaderBar {
	if x != nil {
		return x.HeaderBar
	}
	return nil
}

func (x *NetworthMagicImportScreenOptions) GetScanScreen() *ScanStateScreen {
	if x != nil {
		return x.ScanScreen
	}
	return nil
}

func (x *NetworthMagicImportScreenOptions) GetPreviewScreen() *PreviewStateScreen {
	if x != nil {
		return x.PreviewScreen
	}
	return nil
}

func (x *NetworthMagicImportScreenOptions) GetLoadingScreen() *LoadingStateScreen {
	if x != nil {
		return x.LoadingScreen
	}
	return nil
}

func (x *NetworthMagicImportScreenOptions) GetNetworkErrorScreen() *ErrorStateScreen {
	if x != nil {
		return x.NetworkErrorScreen
	}
	return nil
}

func (x *NetworthMagicImportScreenOptions) GetLimitExhaustedErrorScreen() *ErrorStateScreen {
	if x != nil {
		return x.LimitExhaustedErrorScreen
	}
	return nil
}

func (x *NetworthMagicImportScreenOptions) GetIntroPageScreen() *IntroPageScreen {
	if x != nil {
		return x.IntroPageScreen
	}
	return nil
}

func (x *NetworthMagicImportScreenOptions) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

func (x *NetworthMagicImportScreenOptions) GetProgressBarDetails() *ProgressBarDetails {
	if x != nil {
		return x.ProgressBarDetails
	}
	return nil
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80FFF--Wealth-Builder?node-id=16095-13829&t=qmTzSq6nX4YOgW1k-0
type IdeasBanner struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Have a left visual element as its a lottie
	LeftVisualElement      *common.VisualElement    `protobuf:"bytes,1,opt,name=left_visual_element,json=leftVisualElement,proto3" json:"left_visual_element,omitempty"`
	RightIconTextComponent *ui.IconTextComponent    `protobuf:"bytes,2,opt,name=right_icon_text_component,json=rightIconTextComponent,proto3" json:"right_icon_text_component,omitempty"`
	Background             *widget.BackgroundColour `protobuf:"bytes,3,opt,name=background,proto3" json:"background,omitempty"`
	// Note: BorderProperty is not implemented in IOS yet, client hardcodes the border
	Border *properties.BorderProperty `protobuf:"bytes,4,opt,name=border,proto3" json:"border,omitempty"`
	// Deeplink to render SDUI for ideas banner
	// Using NETWORTH_MAGIC_IMPORT_IDEAS_SCREEN as deeplink in V0
	// Relying on deeplink without any hardcoding as the deeplink can be used to render SDUI in other screens like FAQ, etc.
	Deeplink *deeplink.Deeplink `protobuf:"bytes,5,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *IdeasBanner) Reset() {
	*x = IdeasBanner{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IdeasBanner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IdeasBanner) ProtoMessage() {}

func (x *IdeasBanner) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IdeasBanner.ProtoReflect.Descriptor instead.
func (*IdeasBanner) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescGZIP(), []int{1}
}

func (x *IdeasBanner) GetLeftVisualElement() *common.VisualElement {
	if x != nil {
		return x.LeftVisualElement
	}
	return nil
}

func (x *IdeasBanner) GetRightIconTextComponent() *ui.IconTextComponent {
	if x != nil {
		return x.RightIconTextComponent
	}
	return nil
}

func (x *IdeasBanner) GetBackground() *widget.BackgroundColour {
	if x != nil {
		return x.Background
	}
	return nil
}

func (x *IdeasBanner) GetBorder() *properties.BorderProperty {
	if x != nil {
		return x.Border
	}
	return nil
}

func (x *IdeasBanner) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12409-15699&t=Iy3TPAyrAFPn1jPi-4
type ScanStateScreen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Snap & add anything to your networth
	Title *common.Text `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// Scan to know the value of any asset
	SubTitle *common.Text `protobuf:"bytes,3,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	// For best results, scan one clear object at a time.
	Message *common.Text `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	// Not an optional field
	PickerComponent *PickerComponent `protobuf:"bytes,5,opt,name=picker_component,json=pickerComponent,proto3" json:"picker_component,omitempty"`
	// no of assets supported
	NoOfAssetsSupported int32 `protobuf:"varint,6,opt,name=no_of_assets_supported,json=noOfAssetsSupported,proto3" json:"no_of_assets_supported,omitempty"`
	// Supported file types
	SupportedFiles []*SupportedFile `protobuf:"bytes,7,rep,name=supported_files,json=supportedFiles,proto3" json:"supported_files,omitempty"`
	// Max file size in kb for each file and if crosses we are not allowing to add that.
	// For images client will reduce to below this size
	// Total Max allowed size will be max_file_size_kb * no_of_assets_supported
	MaxFileSizeKb int32 `protobuf:"varint,8,opt,name=max_file_size_kb,json=maxFileSizeKb,proto3" json:"max_file_size_kb,omitempty"`
	// this will be shown to the user when permission for camera access is denied
	PermissionDenialPlaceholder *common.VisualElement                  `protobuf:"bytes,9,opt,name=permission_denial_placeholder,json=permissionDenialPlaceholder,proto3" json:"permission_denial_placeholder,omitempty"`
	IdeasBanner                 *IdeasBanner                           `protobuf:"bytes,10,opt,name=ideas_banner,json=ideasBanner,proto3" json:"ideas_banner,omitempty"`
	ScreenDisabledDetails       *ScanStateScreen_ScreenDisabledDetails `protobuf:"bytes,11,opt,name=screen_disabled_details,json=screenDisabledDetails,proto3" json:"screen_disabled_details,omitempty"`
	// If header switch bar is configured, use that otherwise use header bar
	HeaderSwitchBar *HeaderSwitchBar `protobuf:"bytes,12,opt,name=header_switch_bar,json=headerSwitchBar,proto3" json:"header_switch_bar,omitempty"`
}

func (x *ScanStateScreen) Reset() {
	*x = ScanStateScreen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScanStateScreen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScanStateScreen) ProtoMessage() {}

func (x *ScanStateScreen) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScanStateScreen.ProtoReflect.Descriptor instead.
func (*ScanStateScreen) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescGZIP(), []int{2}
}

func (x *ScanStateScreen) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *ScanStateScreen) GetSubTitle() *common.Text {
	if x != nil {
		return x.SubTitle
	}
	return nil
}

func (x *ScanStateScreen) GetMessage() *common.Text {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *ScanStateScreen) GetPickerComponent() *PickerComponent {
	if x != nil {
		return x.PickerComponent
	}
	return nil
}

func (x *ScanStateScreen) GetNoOfAssetsSupported() int32 {
	if x != nil {
		return x.NoOfAssetsSupported
	}
	return 0
}

func (x *ScanStateScreen) GetSupportedFiles() []*SupportedFile {
	if x != nil {
		return x.SupportedFiles
	}
	return nil
}

func (x *ScanStateScreen) GetMaxFileSizeKb() int32 {
	if x != nil {
		return x.MaxFileSizeKb
	}
	return 0
}

func (x *ScanStateScreen) GetPermissionDenialPlaceholder() *common.VisualElement {
	if x != nil {
		return x.PermissionDenialPlaceholder
	}
	return nil
}

func (x *ScanStateScreen) GetIdeasBanner() *IdeasBanner {
	if x != nil {
		return x.IdeasBanner
	}
	return nil
}

func (x *ScanStateScreen) GetScreenDisabledDetails() *ScanStateScreen_ScreenDisabledDetails {
	if x != nil {
		return x.ScreenDisabledDetails
	}
	return nil
}

func (x *ScanStateScreen) GetHeaderSwitchBar() *HeaderSwitchBar {
	if x != nil {
		return x.HeaderSwitchBar
	}
	return nil
}

type IntroPageScreen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Version is used to force show an intro animation
	// Whenever version changes, client shows intro animation for magic import
	// Version maps to values like 1,1.1 (to redo same intro if required),2 (new intro)
	Version           string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	IntroAnimationUrl string `protobuf:"bytes,2,opt,name=intro_animation_url,json=introAnimationUrl,proto3" json:"intro_animation_url,omitempty"`
	// Intro page will have a different header bar
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80FFF--Wealth-Builder?node-id=12384-4045&t=LLSUgth4uga5aj4V-0
	HeaderBar *ui.HeaderBar `protobuf:"bytes,3,opt,name=header_bar,json=headerBar,proto3" json:"header_bar,omitempty"`
}

func (x *IntroPageScreen) Reset() {
	*x = IntroPageScreen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IntroPageScreen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IntroPageScreen) ProtoMessage() {}

func (x *IntroPageScreen) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IntroPageScreen.ProtoReflect.Descriptor instead.
func (*IntroPageScreen) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescGZIP(), []int{3}
}

func (x *IntroPageScreen) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *IntroPageScreen) GetIntroAnimationUrl() string {
	if x != nil {
		return x.IntroAnimationUrl
	}
	return ""
}

func (x *IntroPageScreen) GetHeaderBar() *ui.HeaderBar {
	if x != nil {
		return x.HeaderBar
	}
	return nil
}

type PickerComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Background   *widget.BackgroundColour `protobuf:"bytes,1,opt,name=background,proto3" json:"background,omitempty"`
	Border       *widget.BackgroundColour `protobuf:"bytes,2,opt,name=border,proto3" json:"border,omitempty"`
	CornerRadius int32                    `protobuf:"varint,3,opt,name=corner_radius,json=cornerRadius,proto3" json:"corner_radius,omitempty"`
	GalleryItc   *ui.IconTextComponent    `protobuf:"bytes,4,opt,name=gallery_itc,json=galleryItc,proto3" json:"gallery_itc,omitempty"`
	DocumentItc  *ui.IconTextComponent    `protobuf:"bytes,5,opt,name=document_itc,json=documentItc,proto3" json:"document_itc,omitempty"`
}

func (x *PickerComponent) Reset() {
	*x = PickerComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PickerComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PickerComponent) ProtoMessage() {}

func (x *PickerComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PickerComponent.ProtoReflect.Descriptor instead.
func (*PickerComponent) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescGZIP(), []int{4}
}

func (x *PickerComponent) GetBackground() *widget.BackgroundColour {
	if x != nil {
		return x.Background
	}
	return nil
}

func (x *PickerComponent) GetBorder() *widget.BackgroundColour {
	if x != nil {
		return x.Border
	}
	return nil
}

func (x *PickerComponent) GetCornerRadius() int32 {
	if x != nil {
		return x.CornerRadius
	}
	return 0
}

func (x *PickerComponent) GetGalleryItc() *ui.IconTextComponent {
	if x != nil {
		return x.GalleryItc
	}
	return nil
}

func (x *PickerComponent) GetDocumentItc() *ui.IconTextComponent {
	if x != nil {
		return x.DocumentItc
	}
	return nil
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12409-15728&t=Iy3TPAyrAFPn1jPi-4
type PreviewStateScreen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Ready to analyse its value?
	Title *common.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// Scan more
	ScanMore *ScanMoreComponent `protobuf:"bytes,2,opt,name=scan_more,json=scanMore,proto3" json:"scan_more,omitempty"`
	// Analyse CTA
	AnalyseCta *ui.IconTextComponent `protobuf:"bytes,3,opt,name=analyse_cta,json=analyseCta,proto3" json:"analyse_cta,omitempty"`
}

func (x *PreviewStateScreen) Reset() {
	*x = PreviewStateScreen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewStateScreen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewStateScreen) ProtoMessage() {}

func (x *PreviewStateScreen) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewStateScreen.ProtoReflect.Descriptor instead.
func (*PreviewStateScreen) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescGZIP(), []int{5}
}

func (x *PreviewStateScreen) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *PreviewStateScreen) GetScanMore() *ScanMoreComponent {
	if x != nil {
		return x.ScanMore
	}
	return nil
}

func (x *PreviewStateScreen) GetAnalyseCta() *ui.IconTextComponent {
	if x != nil {
		return x.AnalyseCta
	}
	return nil
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12409-15732&t=Iy3TPAyrAFPn1jPi-4
type LoadingStateScreen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Ready to analyse its value?
	Title *common.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// Loader animation
	// Using string as we want to use rive which visual element doesn't have support.
	LoaderAnimationUrl string `protobuf:"bytes,2,opt,name=loader_animation_url,json=loaderAnimationUrl,proto3" json:"loader_animation_url,omitempty"`
	// Data is safe and secure
	ConfidentialMessage *ui.IconTextComponent `protobuf:"bytes,4,opt,name=confidential_message,json=confidentialMessage,proto3" json:"confidential_message,omitempty"`
}

func (x *LoadingStateScreen) Reset() {
	*x = LoadingStateScreen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadingStateScreen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadingStateScreen) ProtoMessage() {}

func (x *LoadingStateScreen) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadingStateScreen.ProtoReflect.Descriptor instead.
func (*LoadingStateScreen) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescGZIP(), []int{6}
}

func (x *LoadingStateScreen) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *LoadingStateScreen) GetLoaderAnimationUrl() string {
	if x != nil {
		return x.LoaderAnimationUrl
	}
	return ""
}

func (x *LoadingStateScreen) GetConfidentialMessage() *ui.IconTextComponent {
	if x != nil {
		return x.ConfidentialMessage
	}
	return nil
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12546-20031&t=snNA1uq3sIlGvlwB-4
// This is for no internet use case
type ErrorStateScreen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title   *common.Text          `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Message *common.Text          `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Icon    *common.VisualElement `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	// Max 2 ctas allowed and client will render the ctas horizontally next to each other
	// Client will add functionality cta types Done, Retry and Custom
	ActionCtas []*deeplink.Cta `protobuf:"bytes,4,rep,name=action_ctas,json=actionCtas,proto3" json:"action_ctas,omitempty"`
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80FFF--Wealth-Builder?node-id=12546-14996&t=W5bHVgb9dbtMsXcB-0
	// Client uses image captured from user in case of empty error_image here
	ErrorImage *common.VisualElement `protobuf:"bytes,5,opt,name=error_image,json=errorImage,proto3" json:"error_image,omitempty"`
}

func (x *ErrorStateScreen) Reset() {
	*x = ErrorStateScreen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ErrorStateScreen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorStateScreen) ProtoMessage() {}

func (x *ErrorStateScreen) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorStateScreen.ProtoReflect.Descriptor instead.
func (*ErrorStateScreen) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescGZIP(), []int{7}
}

func (x *ErrorStateScreen) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *ErrorStateScreen) GetMessage() *common.Text {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *ErrorStateScreen) GetIcon() *common.VisualElement {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *ErrorStateScreen) GetActionCtas() []*deeplink.Cta {
	if x != nil {
		return x.ActionCtas
	}
	return nil
}

func (x *ErrorStateScreen) GetErrorImage() *common.VisualElement {
	if x != nil {
		return x.ErrorImage
	}
	return nil
}

type SupportedFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// e.f, pdf, doc, jpg, png
	FileExtension string `protobuf:"bytes,1,opt,name=file_extension,json=fileExtension,proto3" json:"file_extension,omitempty"`
	// PDF only icon
	IconUrl *common.VisualElement `protobuf:"bytes,2,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	// Preview image url
	PreviewImg *common.VisualElement `protobuf:"bytes,3,opt,name=preview_img,json=previewImg,proto3" json:"preview_img,omitempty"`
}

func (x *SupportedFile) Reset() {
	*x = SupportedFile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SupportedFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SupportedFile) ProtoMessage() {}

func (x *SupportedFile) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SupportedFile.ProtoReflect.Descriptor instead.
func (*SupportedFile) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescGZIP(), []int{8}
}

func (x *SupportedFile) GetFileExtension() string {
	if x != nil {
		return x.FileExtension
	}
	return ""
}

func (x *SupportedFile) GetIconUrl() *common.VisualElement {
	if x != nil {
		return x.IconUrl
	}
	return nil
}

func (x *SupportedFile) GetPreviewImg() *common.VisualElement {
	if x != nil {
		return x.PreviewImg
	}
	return nil
}

type ScanMoreComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Border *properties.BorderProperty `protobuf:"bytes,1,opt,name=border,proto3" json:"border,omitempty"`
	Icon   *common.VisualElement      `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	Text   *common.Text               `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
}

func (x *ScanMoreComponent) Reset() {
	*x = ScanMoreComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScanMoreComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScanMoreComponent) ProtoMessage() {}

func (x *ScanMoreComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScanMoreComponent.ProtoReflect.Descriptor instead.
func (*ScanMoreComponent) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescGZIP(), []int{9}
}

func (x *ScanMoreComponent) GetBorder() *properties.BorderProperty {
	if x != nil {
		return x.Border
	}
	return nil
}

func (x *ScanMoreComponent) GetIcon() *common.VisualElement {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *ScanMoreComponent) GetText() *common.Text {
	if x != nil {
		return x.Text
	}
	return nil
}

type HeaderSwitchBar struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Switch to be shown at the middle of the bar
	CenterSwitch *ui.Switch `protobuf:"bytes,1,opt,name=center_switch,json=centerSwitch,proto3" json:"center_switch,omitempty"`
	// ITC to be shown at the end of the bar.
	RightItc *ui.IconTextComponent `protobuf:"bytes,2,opt,name=right_itc,json=rightItc,proto3" json:"right_itc,omitempty"`
	// ITC to be shown at the start of the bar.
	// it can be used to show back button
	LeftItc *ui.IconTextComponent `protobuf:"bytes,3,opt,name=left_itc,json=leftItc,proto3" json:"left_itc,omitempty"`
}

func (x *HeaderSwitchBar) Reset() {
	*x = HeaderSwitchBar{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HeaderSwitchBar) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeaderSwitchBar) ProtoMessage() {}

func (x *HeaderSwitchBar) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeaderSwitchBar.ProtoReflect.Descriptor instead.
func (*HeaderSwitchBar) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescGZIP(), []int{10}
}

func (x *HeaderSwitchBar) GetCenterSwitch() *ui.Switch {
	if x != nil {
		return x.CenterSwitch
	}
	return nil
}

func (x *HeaderSwitchBar) GetRightItc() *ui.IconTextComponent {
	if x != nil {
		return x.RightItc
	}
	return nil
}

func (x *HeaderSwitchBar) GetLeftItc() *ui.IconTextComponent {
	if x != nil {
		return x.LeftItc
	}
	return nil
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-FFF--Wealth-Builder?node-id=18675-25156&t=1UZJaJMqnOCaLBHY-4
type ProgressBarDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Colors for unfilled and filled Progress bar
	BgColor       *widget.BackgroundColour `protobuf:"bytes,1,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	FilledBgColor *widget.BackgroundColour `protobuf:"bytes,2,opt,name=filled_bg_color,json=filledBgColor,proto3" json:"filled_bg_color,omitempty"`
	// Height of progress bar
	Height int32 `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	// Corner radius of container
	CornerRadius int32 `protobuf:"varint,4,opt,name=corner_radius,json=cornerRadius,proto3" json:"corner_radius,omitempty"`
}

func (x *ProgressBarDetails) Reset() {
	*x = ProgressBarDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProgressBarDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProgressBarDetails) ProtoMessage() {}

func (x *ProgressBarDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProgressBarDetails.ProtoReflect.Descriptor instead.
func (*ProgressBarDetails) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescGZIP(), []int{11}
}

func (x *ProgressBarDetails) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

func (x *ProgressBarDetails) GetFilledBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.FilledBgColor
	}
	return nil
}

func (x *ProgressBarDetails) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *ProgressBarDetails) GetCornerRadius() int32 {
	if x != nil {
		return x.CornerRadius
	}
	return 0
}

type ScanStateScreen_ScreenDisabledDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Flag to disable the scan state screen
	IsDisabled bool `protobuf:"varint,1,opt,name=is_disabled,json=isDisabled,proto3" json:"is_disabled,omitempty"`
	// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80FFF--Wealth-Builder?node-id=15156-29838&t=Oa6knp83HwdBV47u-0
	// Client will use this only in case of is_disabled
	ScanDisabledPlaceholder *common.VisualElement `protobuf:"bytes,2,opt,name=scan_disabled_placeholder,json=scanDisabledPlaceholder,proto3" json:"scan_disabled_placeholder,omitempty"`
}

func (x *ScanStateScreen_ScreenDisabledDetails) Reset() {
	*x = ScanStateScreen_ScreenDisabledDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScanStateScreen_ScreenDisabledDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScanStateScreen_ScreenDisabledDetails) ProtoMessage() {}

func (x *ScanStateScreen_ScreenDisabledDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScanStateScreen_ScreenDisabledDetails.ProtoReflect.Descriptor instead.
func (*ScanStateScreen_ScreenDisabledDetails) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescGZIP(), []int{2, 0}
}

func (x *ScanStateScreen_ScreenDisabledDetails) GetIsDisabled() bool {
	if x != nil {
		return x.IsDisabled
	}
	return false
}

func (x *ScanStateScreen_ScreenDisabledDetails) GetScanDisabledPlaceholder() *common.VisualElement {
	if x != nil {
		return x.ScanDisabledPlaceholder
	}
	return nil
}

var File_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto protoreflect.FileDescriptor

var file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDesc = []byte{
	0x0a, 0x59, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x5f,
	0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x1a, 0x24, 0x61, 0x70, 0x69,
	0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2f, 0x77,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63,
	0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x73, 0x64, 0x75, 0x69, 0x2f, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f,
	0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x87, 0x08, 0x0a,
	0x20, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x4d, 0x61, 0x67, 0x69, 0x63, 0x49, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x35, 0x0a, 0x0a, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x61, 0x72, 0x52, 0x09, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x61, 0x72, 0x12, 0x60, 0x0a, 0x0b, 0x73, 0x63, 0x61, 0x6e,
	0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x53,
	0x63, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x0a,
	0x73, 0x63, 0x61, 0x6e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x69, 0x0a, 0x0e, 0x70, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x42, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x69, 0x0a, 0x0e, 0x6c, 0x6f, 0x61, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x4c,
	0x6f, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x52, 0x0d, 0x6c, 0x6f, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x12, 0x72, 0x0a, 0x14, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x2e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x52, 0x12, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x12, 0x81, 0x01, 0x0a, 0x1c, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x65,
	0x78, 0x68, 0x61, 0x75, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x19, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x45, 0x78, 0x68, 0x61, 0x75, 0x73, 0x74, 0x65, 0x64, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x6b, 0x0a, 0x11, 0x69, 0x6e, 0x74, 0x72,
	0x6f, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x50, 0x61, 0x67, 0x65, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x52, 0x0f, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x50, 0x61, 0x67, 0x65, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x49, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x74, 0x0a, 0x14, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x62, 0x61, 0x72,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x42,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x2e,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x42, 0x61, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x42, 0x61, 0x72, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x8f, 0x03, 0x0a, 0x0b, 0x49, 0x64, 0x65, 0x61, 0x73,
	0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x51, 0x0a, 0x13, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x76,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x11, 0x6c, 0x65, 0x66, 0x74, 0x56, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x5c, 0x0a, 0x19, 0x72, 0x69, 0x67,
	0x68, 0x74, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63,
	0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52,
	0x16, 0x72, 0x69, 0x67, 0x68, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x4e, 0x0a, 0x0a, 0x62, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x0a, 0x62, 0x61, 0x63,
	0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x46, 0x0a, 0x06, 0x62, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x73, 0x64, 0x75, 0x69, 0x2e, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x42, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x06, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x12,
	0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x22, 0xbc, 0x08, 0x0a, 0x0f, 0x53, 0x63, 0x61,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x2e, 0x0a, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x35, 0x0a, 0x09,
	0x73, 0x75, 0x62, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x54, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x32, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x6a, 0x0a, 0x10, 0x70, 0x69, 0x63, 0x6b, 0x65,
	0x72, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x2e, 0x50, 0x69, 0x63, 0x6b, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x52, 0x0f, 0x70, 0x69, 0x63, 0x6b, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x16, 0x6e, 0x6f, 0x5f, 0x6f, 0x66, 0x5f, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x5f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x13, 0x6e, 0x6f, 0x4f, 0x66, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x53,
	0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x12, 0x66, 0x0a, 0x0f, 0x73, 0x75, 0x70, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x3d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x2e, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x65,
	0x52, 0x0e, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x73,
	0x12, 0x27, 0x0a, 0x10, 0x6d, 0x61, 0x78, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x5f, 0x6b, 0x62, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6d, 0x61, 0x78, 0x46,
	0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x4b, 0x62, 0x12, 0x65, 0x0a, 0x1d, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x6e, 0x69, 0x61, 0x6c, 0x5f, 0x70,
	0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x1b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x6e, 0x69, 0x61, 0x6c, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72,
	0x12, 0x5e, 0x0a, 0x0c, 0x69, 0x64, 0x65, 0x61, 0x73, 0x5f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6d, 0x61, 0x67, 0x69,
	0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x49, 0x64, 0x65, 0x61, 0x73, 0x42, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x52, 0x0b, 0x69, 0x64, 0x65, 0x61, 0x73, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x12, 0x8d, 0x01, 0x0a, 0x17, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x64, 0x69, 0x73, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x55, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x15, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x6b, 0x0a, 0x11, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x73, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x5f, 0x62, 0x61, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x42, 0x61, 0x72, 0x52, 0x0f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x42, 0x61, 0x72, 0x1a, 0x97, 0x01,
	0x0a, 0x15, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x64, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73,
	0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x5d, 0x0a, 0x19, 0x73, 0x63, 0x61, 0x6e,
	0x5f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x68,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x17,
	0x73, 0x63, 0x61, 0x6e, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x50, 0x6c, 0x61, 0x63,
	0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x22, 0x92, 0x01, 0x0a, 0x0f, 0x49, 0x6e, 0x74, 0x72,
	0x6f, 0x50, 0x61, 0x67, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x13, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x5f, 0x61,
	0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x35, 0x0a, 0x0a, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f,
	0x62, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x61,
	0x72, 0x52, 0x09, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x61, 0x72, 0x22, 0xd8, 0x02, 0x0a,
	0x0f, 0x50, 0x69, 0x63, 0x6b, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x12, 0x4e, 0x0a, 0x0a, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f,
	0x6c, 0x6f, 0x75, 0x72, 0x52, 0x0a, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x12, 0x46, 0x0a, 0x06, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e,
	0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72,
	0x52, 0x06, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x72, 0x6e,
	0x65, 0x72, 0x5f, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0c, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x12, 0x42, 0x0a,
	0x0b, 0x67, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x5f, 0x69, 0x74, 0x63, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x67, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x49, 0x74,
	0x63, 0x12, 0x44, 0x0a, 0x0c, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x74,
	0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x64, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x74, 0x63, 0x22, 0xe8, 0x01, 0x0a, 0x12, 0x50, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x2e,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x5e,
	0x0a, 0x09, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x6d, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x41, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x4d, 0x6f, 0x72, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x73, 0x63, 0x61, 0x6e, 0x4d, 0x6f, 0x72, 0x65, 0x12, 0x42,
	0x0a, 0x0b, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x43,
	0x74, 0x61, 0x22, 0xcc, 0x01, 0x0a, 0x12, 0x4c, 0x6f, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x6c, 0x6f, 0x61,
	0x64, 0x65, 0x72, 0x5f, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x72, 0x41,
	0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x54, 0x0a, 0x14, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54,
	0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x13, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x22, 0xaa, 0x02, 0x0a, 0x10, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x32, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78,
	0x74, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x35, 0x0a, 0x04, 0x69, 0x63,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x69, 0x63, 0x6f,
	0x6e, 0x12, 0x37, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x74, 0x61, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x0a,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x74, 0x61, 0x73, 0x12, 0x42, 0x0a, 0x0b, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x22, 0xb8,
	0x01, 0x0a, 0x0d, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x65,
	0x12, 0x25, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a, 0x08, 0x69, 0x63, 0x6f, 0x6e, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x69, 0x63,
	0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x42, 0x0a, 0x0b, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x5f, 0x69, 0x6d, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x70,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x6d, 0x67, 0x22, 0xc0, 0x01, 0x0a, 0x11, 0x53, 0x63,
	0x61, 0x6e, 0x4d, 0x6f, 0x72, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12,
	0x46, 0x0a, 0x06, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x73, 0x64, 0x75, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73,
	0x2e, 0x42, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52,
	0x06, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x35, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x2c,
	0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x22, 0xcc, 0x01, 0x0a,
	0x0f, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x42, 0x61, 0x72,
	0x12, 0x3b, 0x0a, 0x0d, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f, 0x73, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52,
	0x0c, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x3e, 0x0a,
	0x09, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x69, 0x74, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75,
	0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x52, 0x08, 0x72, 0x69, 0x67, 0x68, 0x74, 0x49, 0x74, 0x63, 0x12, 0x3c, 0x0a,
	0x08, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x69, 0x74, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x52, 0x07, 0x6c, 0x65, 0x66, 0x74, 0x49, 0x74, 0x63, 0x22, 0xf4, 0x01, 0x0a, 0x12,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x42, 0x61, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x49, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f,
	0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x56, 0x0a,
	0x0f, 0x66, 0x69, 0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x0d, 0x66, 0x69, 0x6c, 0x6c, 0x65, 0x64, 0x42, 0x67,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x23, 0x0a,
	0x0d, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x5f, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x52, 0x61, 0x64, 0x69,
	0x75, 0x73, 0x2a, 0x93, 0x01, 0x0a, 0x15, 0x4d, 0x61, 0x67, 0x69, 0x63, 0x49, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x24,
	0x4d, 0x41, 0x47, 0x49, 0x43, 0x5f, 0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x57, 0x49,
	0x54, 0x43, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x25, 0x0a, 0x21, 0x4d, 0x41, 0x47, 0x49, 0x43, 0x5f,
	0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x46, 0x55, 0x4e, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x10, 0x01, 0x12, 0x29, 0x0a,
	0x25, 0x4d, 0x41, 0x47, 0x49, 0x43, 0x5f, 0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x57,
	0x49, 0x54, 0x43, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x49, 0x4f, 0x55,
	0x53, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x10, 0x02, 0x42, 0x90, 0x01, 0x0a, 0x45, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x50, 0x01, 0x5a, 0x45, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescOnce sync.Once
	file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescData = file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDesc
)

func file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescGZIP() []byte {
	file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescOnce.Do(func() {
		file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescData)
	})
	return file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDescData
}

var file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_goTypes = []interface{}{
	(MagicImportSwitchType)(0),                        // 0: api.typesv2.deeplink_screen_option.magicimport.MagicImportSwitchType
	(*NetworthMagicImportScreenOptions)(nil),          // 1: api.typesv2.deeplink_screen_option.magicimport.NetworthMagicImportScreenOptions
	(*IdeasBanner)(nil),                               // 2: api.typesv2.deeplink_screen_option.magicimport.IdeasBanner
	(*ScanStateScreen)(nil),                           // 3: api.typesv2.deeplink_screen_option.magicimport.ScanStateScreen
	(*IntroPageScreen)(nil),                           // 4: api.typesv2.deeplink_screen_option.magicimport.IntroPageScreen
	(*PickerComponent)(nil),                           // 5: api.typesv2.deeplink_screen_option.magicimport.PickerComponent
	(*PreviewStateScreen)(nil),                        // 6: api.typesv2.deeplink_screen_option.magicimport.PreviewStateScreen
	(*LoadingStateScreen)(nil),                        // 7: api.typesv2.deeplink_screen_option.magicimport.LoadingStateScreen
	(*ErrorStateScreen)(nil),                          // 8: api.typesv2.deeplink_screen_option.magicimport.ErrorStateScreen
	(*SupportedFile)(nil),                             // 9: api.typesv2.deeplink_screen_option.magicimport.SupportedFile
	(*ScanMoreComponent)(nil),                         // 10: api.typesv2.deeplink_screen_option.magicimport.ScanMoreComponent
	(*HeaderSwitchBar)(nil),                           // 11: api.typesv2.deeplink_screen_option.magicimport.HeaderSwitchBar
	(*ProgressBarDetails)(nil),                        // 12: api.typesv2.deeplink_screen_option.magicimport.ProgressBarDetails
	(*ScanStateScreen_ScreenDisabledDetails)(nil),     // 13: api.typesv2.deeplink_screen_option.magicimport.ScanStateScreen.ScreenDisabledDetails
	(*deeplink_screen_option.ScreenOptionHeader)(nil), // 14: api.typesv2.deeplink_screen_option.ScreenOptionHeader
	(*ui.HeaderBar)(nil),                              // 15: api.typesv2.HeaderBar
	(*widget.BackgroundColour)(nil),                   // 16: api.typesv2.common.ui.widget.BackgroundColour
	(*common.VisualElement)(nil),                      // 17: api.typesv2.common.VisualElement
	(*ui.IconTextComponent)(nil),                      // 18: api.typesv2.ui.IconTextComponent
	(*properties.BorderProperty)(nil),                 // 19: api.typesv2.ui.sdui.properties.BorderProperty
	(*deeplink.Deeplink)(nil),                         // 20: frontend.deeplink.Deeplink
	(*common.Text)(nil),                               // 21: api.typesv2.common.Text
	(*deeplink.Cta)(nil),                              // 22: frontend.deeplink.Cta
	(*ui.Switch)(nil),                                 // 23: api.typesv2.ui.Switch
}
var file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_depIdxs = []int32{
	14, // 0: api.typesv2.deeplink_screen_option.magicimport.NetworthMagicImportScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	15, // 1: api.typesv2.deeplink_screen_option.magicimport.NetworthMagicImportScreenOptions.header_bar:type_name -> api.typesv2.HeaderBar
	3,  // 2: api.typesv2.deeplink_screen_option.magicimport.NetworthMagicImportScreenOptions.scan_screen:type_name -> api.typesv2.deeplink_screen_option.magicimport.ScanStateScreen
	6,  // 3: api.typesv2.deeplink_screen_option.magicimport.NetworthMagicImportScreenOptions.preview_screen:type_name -> api.typesv2.deeplink_screen_option.magicimport.PreviewStateScreen
	7,  // 4: api.typesv2.deeplink_screen_option.magicimport.NetworthMagicImportScreenOptions.loading_screen:type_name -> api.typesv2.deeplink_screen_option.magicimport.LoadingStateScreen
	8,  // 5: api.typesv2.deeplink_screen_option.magicimport.NetworthMagicImportScreenOptions.network_error_screen:type_name -> api.typesv2.deeplink_screen_option.magicimport.ErrorStateScreen
	8,  // 6: api.typesv2.deeplink_screen_option.magicimport.NetworthMagicImportScreenOptions.limit_exhausted_error_screen:type_name -> api.typesv2.deeplink_screen_option.magicimport.ErrorStateScreen
	4,  // 7: api.typesv2.deeplink_screen_option.magicimport.NetworthMagicImportScreenOptions.intro_page_screen:type_name -> api.typesv2.deeplink_screen_option.magicimport.IntroPageScreen
	16, // 8: api.typesv2.deeplink_screen_option.magicimport.NetworthMagicImportScreenOptions.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	12, // 9: api.typesv2.deeplink_screen_option.magicimport.NetworthMagicImportScreenOptions.progress_bar_details:type_name -> api.typesv2.deeplink_screen_option.magicimport.ProgressBarDetails
	17, // 10: api.typesv2.deeplink_screen_option.magicimport.IdeasBanner.left_visual_element:type_name -> api.typesv2.common.VisualElement
	18, // 11: api.typesv2.deeplink_screen_option.magicimport.IdeasBanner.right_icon_text_component:type_name -> api.typesv2.ui.IconTextComponent
	16, // 12: api.typesv2.deeplink_screen_option.magicimport.IdeasBanner.background:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	19, // 13: api.typesv2.deeplink_screen_option.magicimport.IdeasBanner.border:type_name -> api.typesv2.ui.sdui.properties.BorderProperty
	20, // 14: api.typesv2.deeplink_screen_option.magicimport.IdeasBanner.deeplink:type_name -> frontend.deeplink.Deeplink
	21, // 15: api.typesv2.deeplink_screen_option.magicimport.ScanStateScreen.title:type_name -> api.typesv2.common.Text
	21, // 16: api.typesv2.deeplink_screen_option.magicimport.ScanStateScreen.sub_title:type_name -> api.typesv2.common.Text
	21, // 17: api.typesv2.deeplink_screen_option.magicimport.ScanStateScreen.message:type_name -> api.typesv2.common.Text
	5,  // 18: api.typesv2.deeplink_screen_option.magicimport.ScanStateScreen.picker_component:type_name -> api.typesv2.deeplink_screen_option.magicimport.PickerComponent
	9,  // 19: api.typesv2.deeplink_screen_option.magicimport.ScanStateScreen.supported_files:type_name -> api.typesv2.deeplink_screen_option.magicimport.SupportedFile
	17, // 20: api.typesv2.deeplink_screen_option.magicimport.ScanStateScreen.permission_denial_placeholder:type_name -> api.typesv2.common.VisualElement
	2,  // 21: api.typesv2.deeplink_screen_option.magicimport.ScanStateScreen.ideas_banner:type_name -> api.typesv2.deeplink_screen_option.magicimport.IdeasBanner
	13, // 22: api.typesv2.deeplink_screen_option.magicimport.ScanStateScreen.screen_disabled_details:type_name -> api.typesv2.deeplink_screen_option.magicimport.ScanStateScreen.ScreenDisabledDetails
	11, // 23: api.typesv2.deeplink_screen_option.magicimport.ScanStateScreen.header_switch_bar:type_name -> api.typesv2.deeplink_screen_option.magicimport.HeaderSwitchBar
	15, // 24: api.typesv2.deeplink_screen_option.magicimport.IntroPageScreen.header_bar:type_name -> api.typesv2.HeaderBar
	16, // 25: api.typesv2.deeplink_screen_option.magicimport.PickerComponent.background:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	16, // 26: api.typesv2.deeplink_screen_option.magicimport.PickerComponent.border:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	18, // 27: api.typesv2.deeplink_screen_option.magicimport.PickerComponent.gallery_itc:type_name -> api.typesv2.ui.IconTextComponent
	18, // 28: api.typesv2.deeplink_screen_option.magicimport.PickerComponent.document_itc:type_name -> api.typesv2.ui.IconTextComponent
	21, // 29: api.typesv2.deeplink_screen_option.magicimport.PreviewStateScreen.title:type_name -> api.typesv2.common.Text
	10, // 30: api.typesv2.deeplink_screen_option.magicimport.PreviewStateScreen.scan_more:type_name -> api.typesv2.deeplink_screen_option.magicimport.ScanMoreComponent
	18, // 31: api.typesv2.deeplink_screen_option.magicimport.PreviewStateScreen.analyse_cta:type_name -> api.typesv2.ui.IconTextComponent
	21, // 32: api.typesv2.deeplink_screen_option.magicimport.LoadingStateScreen.title:type_name -> api.typesv2.common.Text
	18, // 33: api.typesv2.deeplink_screen_option.magicimport.LoadingStateScreen.confidential_message:type_name -> api.typesv2.ui.IconTextComponent
	21, // 34: api.typesv2.deeplink_screen_option.magicimport.ErrorStateScreen.title:type_name -> api.typesv2.common.Text
	21, // 35: api.typesv2.deeplink_screen_option.magicimport.ErrorStateScreen.message:type_name -> api.typesv2.common.Text
	17, // 36: api.typesv2.deeplink_screen_option.magicimport.ErrorStateScreen.icon:type_name -> api.typesv2.common.VisualElement
	22, // 37: api.typesv2.deeplink_screen_option.magicimport.ErrorStateScreen.action_ctas:type_name -> frontend.deeplink.Cta
	17, // 38: api.typesv2.deeplink_screen_option.magicimport.ErrorStateScreen.error_image:type_name -> api.typesv2.common.VisualElement
	17, // 39: api.typesv2.deeplink_screen_option.magicimport.SupportedFile.icon_url:type_name -> api.typesv2.common.VisualElement
	17, // 40: api.typesv2.deeplink_screen_option.magicimport.SupportedFile.preview_img:type_name -> api.typesv2.common.VisualElement
	19, // 41: api.typesv2.deeplink_screen_option.magicimport.ScanMoreComponent.border:type_name -> api.typesv2.ui.sdui.properties.BorderProperty
	17, // 42: api.typesv2.deeplink_screen_option.magicimport.ScanMoreComponent.icon:type_name -> api.typesv2.common.VisualElement
	21, // 43: api.typesv2.deeplink_screen_option.magicimport.ScanMoreComponent.text:type_name -> api.typesv2.common.Text
	23, // 44: api.typesv2.deeplink_screen_option.magicimport.HeaderSwitchBar.center_switch:type_name -> api.typesv2.ui.Switch
	18, // 45: api.typesv2.deeplink_screen_option.magicimport.HeaderSwitchBar.right_itc:type_name -> api.typesv2.ui.IconTextComponent
	18, // 46: api.typesv2.deeplink_screen_option.magicimport.HeaderSwitchBar.left_itc:type_name -> api.typesv2.ui.IconTextComponent
	16, // 47: api.typesv2.deeplink_screen_option.magicimport.ProgressBarDetails.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	16, // 48: api.typesv2.deeplink_screen_option.magicimport.ProgressBarDetails.filled_bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	17, // 49: api.typesv2.deeplink_screen_option.magicimport.ScanStateScreen.ScreenDisabledDetails.scan_disabled_placeholder:type_name -> api.typesv2.common.VisualElement
	50, // [50:50] is the sub-list for method output_type
	50, // [50:50] is the sub-list for method input_type
	50, // [50:50] is the sub-list for extension type_name
	50, // [50:50] is the sub-list for extension extendee
	0,  // [0:50] is the sub-list for field type_name
}

func init() {
	file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_init()
}
func file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_init() {
	if File_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NetworthMagicImportScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IdeasBanner); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScanStateScreen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IntroPageScreen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PickerComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewStateScreen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadingStateScreen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ErrorStateScreen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SupportedFile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScanMoreComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HeaderSwitchBar); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProgressBarDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScanStateScreen_ScreenDisabledDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_goTypes,
		DependencyIndexes: file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_depIdxs,
		EnumInfos:         file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_enumTypes,
		MessageInfos:      file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_msgTypes,
	}.Build()
	File_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto = out.File
	file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_rawDesc = nil
	file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_goTypes = nil
	file_api_typesv2_deeplink_screen_option_magicimport_networth_magic_import_screen_options_proto_depIdxs = nil
}
