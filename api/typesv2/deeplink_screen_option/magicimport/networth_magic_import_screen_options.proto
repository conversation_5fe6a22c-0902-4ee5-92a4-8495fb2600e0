syntax = "proto3";

package api.typesv2.deeplink_screen_option.magicimport;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/ui/header_bar.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/sdui/properties/visual_properties.proto";
import "api/typesv2/ui/switch.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/magicimport";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.magicimport";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// Deeplink : NETWORTH_MAGIC_IMPORT_SCREEN
message NetworthMagicImportScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 7;
  // Toolbar
  typesv2.HeaderBar header_bar = 1;
  ScanStateScreen scan_screen = 2;
  PreviewStateScreen preview_screen = 3;
  LoadingStateScreen loading_screen = 4;
  ErrorStateScreen network_error_screen = 6;
  ErrorStateScreen limit_exhausted_error_screen = 8;
  IntroPageScreen intro_page_screen = 9;
  // background color for all intermediate screens
  common.ui.widget.BackgroundColour bg_color = 10;
  // Progress bar details
  ProgressBarDetails progress_bar_details = 11;
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80FFF--Wealth-Builder?node-id=16095-13829&t=qmTzSq6nX4YOgW1k-0
message IdeasBanner {
  // Have a left visual element as its a lottie
  common.VisualElement left_visual_element = 1;
  ui.IconTextComponent right_icon_text_component = 2;
  common.ui.widget.BackgroundColour background = 3;
  // Note: BorderProperty is not implemented in IOS yet, client hardcodes the border
  api.typesv2.ui.sdui.properties.BorderProperty border = 4;
  // Deeplink to render SDUI for ideas banner
  // Using NETWORTH_MAGIC_IMPORT_IDEAS_SCREEN as deeplink in V0
  // Relying on deeplink without any hardcoding as the deeplink can be used to render SDUI in other screens like FAQ, etc.
  frontend.deeplink.Deeplink deeplink = 5;
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12409-15699&t=Iy3TPAyrAFPn1jPi-4
message ScanStateScreen {
  message ScreenDisabledDetails {
    // Flag to disable the scan state screen
    bool is_disabled = 1;
    // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80FFF--Wealth-Builder?node-id=15156-29838&t=Oa6knp83HwdBV47u-0
    // Client will use this only in case of is_disabled
    common.VisualElement scan_disabled_placeholder = 2;
  }

  // Snap & add anything to your networth
  common.Text title = 2;
  // Scan to know the value of any asset
  common.Text sub_title = 3;
  // For best results, scan one clear object at a time.
  common.Text message = 4;
  // Not an optional field
  PickerComponent picker_component = 5;
  // no of assets supported
  int32 no_of_assets_supported = 6;
  // Supported file types
  repeated SupportedFile supported_files = 7;
  // Max file size in kb for each file and if crosses we are not allowing to add that.
  // For images client will reduce to below this size
  // Total Max allowed size will be max_file_size_kb * no_of_assets_supported
  int32 max_file_size_kb = 8;
  // this will be shown to the user when permission for camera access is denied
  common.VisualElement permission_denial_placeholder = 9;
  IdeasBanner ideas_banner = 10;
  ScreenDisabledDetails screen_disabled_details = 11;
  // If header switch bar is configured, use that otherwise use header bar
  HeaderSwitchBar header_switch_bar = 12;
}

message IntroPageScreen {
  // Version is used to force show an intro animation
  // Whenever version changes, client shows intro animation for magic import
  // Version maps to values like 1,1.1 (to redo same intro if required),2 (new intro)
  string version = 1;
  string intro_animation_url = 2;
  // Intro page will have a different header bar
  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80FFF--Wealth-Builder?node-id=12384-4045&t=LLSUgth4uga5aj4V-0
  typesv2.HeaderBar header_bar = 3;
}

message PickerComponent {
  common.ui.widget.BackgroundColour background = 1;
  common.ui.widget.BackgroundColour border = 2;
  int32 corner_radius = 3;
  ui.IconTextComponent gallery_itc = 4;
  ui.IconTextComponent document_itc = 5;
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12409-15728&t=Iy3TPAyrAFPn1jPi-4
message PreviewStateScreen {
  // Ready to analyse its value?
  common.Text title = 1;
  // Scan more
  ScanMoreComponent scan_more = 2;
  // Analyse CTA
  ui.IconTextComponent analyse_cta = 3;
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12409-15732&t=Iy3TPAyrAFPn1jPi-4
message LoadingStateScreen {
  // Ready to analyse its value?
  common.Text title = 1;
  // Loader animation
  // Using string as we want to use rive which visual element doesn't have support.
  string loader_animation_url = 2;
  // Data is safe and secure
  ui.IconTextComponent confidential_message = 4;
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12546-20031&t=snNA1uq3sIlGvlwB-4
// This is for no internet use case
message ErrorStateScreen {
  common.Text title = 1;
  common.Text message = 2;
  common.VisualElement icon = 3;
  // Max 2 ctas allowed and client will render the ctas horizontally next to each other
  // Client will add functionality cta types Done, Retry and Custom
  repeated frontend.deeplink.Cta action_ctas = 4;
  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80FFF--Wealth-Builder?node-id=12546-14996&t=W5bHVgb9dbtMsXcB-0
  // Client uses image captured from user in case of empty error_image here
  common.VisualElement error_image = 5;
}

message SupportedFile {
  // e.f, pdf, doc, jpg, png
  string file_extension = 1;
  // PDF only icon
  common.VisualElement icon_url = 2;
  // Preview image url
  common.VisualElement preview_img = 3;
}

message ScanMoreComponent {
  ui.sdui.properties.BorderProperty border = 1;
  common.VisualElement icon = 2;
  common.Text text = 3;
}

message HeaderSwitchBar {
  // Switch to be shown at the middle of the bar
  ui.Switch center_switch = 1;
  // ITC to be shown at the end of the bar.
  typesv2.ui.IconTextComponent right_itc = 2;
  // ITC to be shown at the start of the bar.
  // it can be used to show back button
  typesv2.ui.IconTextComponent left_itc = 3;
}

// Switch type to show different switch options, maps to switch_value in Switch
// e.g. Fun mode, Serious mode
enum MagicImportSwitchType {
  MAGIC_IMPORT_SWITCH_TYPE_UNSPECIFIED = 0;
  MAGIC_IMPORT_SWITCH_TYPE_FUN_MODE = 1;
  MAGIC_IMPORT_SWITCH_TYPE_SERIOUS_MODE = 2;
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-FFF--Wealth-Builder?node-id=18675-25156&t=1UZJaJMqnOCaLBHY-4
message ProgressBarDetails {
  // Colors for unfilled and filled Progress bar
  common.ui.widget.BackgroundColour bg_color = 1;
  common.ui.widget.BackgroundColour filled_bg_color = 2;
  // Height of progress bar
  int32 height = 3;
  // Corner radius of container
  int32 corner_radius = 4;
}
