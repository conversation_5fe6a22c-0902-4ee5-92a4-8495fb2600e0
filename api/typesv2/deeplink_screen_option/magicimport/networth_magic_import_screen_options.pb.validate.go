// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/deeplink_screen_option/magicimport/networth_magic_import_screen_options.proto

package magicimport

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on NetworthMagicImportScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *NetworthMagicImportScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetworthMagicImportScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// NetworthMagicImportScreenOptionsMultiError, or nil if none found.
func (m *NetworthMagicImportScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *NetworthMagicImportScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetworthMagicImportScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetworthMagicImportScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetworthMagicImportScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeaderBar()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetworthMagicImportScreenOptionsValidationError{
					field:  "HeaderBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetworthMagicImportScreenOptionsValidationError{
					field:  "HeaderBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderBar()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetworthMagicImportScreenOptionsValidationError{
				field:  "HeaderBar",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetScanScreen()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetworthMagicImportScreenOptionsValidationError{
					field:  "ScanScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetworthMagicImportScreenOptionsValidationError{
					field:  "ScanScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScanScreen()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetworthMagicImportScreenOptionsValidationError{
				field:  "ScanScreen",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPreviewScreen()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetworthMagicImportScreenOptionsValidationError{
					field:  "PreviewScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetworthMagicImportScreenOptionsValidationError{
					field:  "PreviewScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPreviewScreen()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetworthMagicImportScreenOptionsValidationError{
				field:  "PreviewScreen",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoadingScreen()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetworthMagicImportScreenOptionsValidationError{
					field:  "LoadingScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetworthMagicImportScreenOptionsValidationError{
					field:  "LoadingScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoadingScreen()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetworthMagicImportScreenOptionsValidationError{
				field:  "LoadingScreen",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNetworkErrorScreen()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetworthMagicImportScreenOptionsValidationError{
					field:  "NetworkErrorScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetworthMagicImportScreenOptionsValidationError{
					field:  "NetworkErrorScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNetworkErrorScreen()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetworthMagicImportScreenOptionsValidationError{
				field:  "NetworkErrorScreen",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLimitExhaustedErrorScreen()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetworthMagicImportScreenOptionsValidationError{
					field:  "LimitExhaustedErrorScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetworthMagicImportScreenOptionsValidationError{
					field:  "LimitExhaustedErrorScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLimitExhaustedErrorScreen()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetworthMagicImportScreenOptionsValidationError{
				field:  "LimitExhaustedErrorScreen",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIntroPageScreen()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetworthMagicImportScreenOptionsValidationError{
					field:  "IntroPageScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetworthMagicImportScreenOptionsValidationError{
					field:  "IntroPageScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIntroPageScreen()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetworthMagicImportScreenOptionsValidationError{
				field:  "IntroPageScreen",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetworthMagicImportScreenOptionsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetworthMagicImportScreenOptionsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetworthMagicImportScreenOptionsValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProgressBarDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetworthMagicImportScreenOptionsValidationError{
					field:  "ProgressBarDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetworthMagicImportScreenOptionsValidationError{
					field:  "ProgressBarDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProgressBarDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetworthMagicImportScreenOptionsValidationError{
				field:  "ProgressBarDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return NetworthMagicImportScreenOptionsMultiError(errors)
	}

	return nil
}

// NetworthMagicImportScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by
// NetworthMagicImportScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type NetworthMagicImportScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetworthMagicImportScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetworthMagicImportScreenOptionsMultiError) AllErrors() []error { return m }

// NetworthMagicImportScreenOptionsValidationError is the validation error
// returned by NetworthMagicImportScreenOptions.Validate if the designated
// constraints aren't met.
type NetworthMagicImportScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetworthMagicImportScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetworthMagicImportScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetworthMagicImportScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetworthMagicImportScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetworthMagicImportScreenOptionsValidationError) ErrorName() string {
	return "NetworthMagicImportScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e NetworthMagicImportScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetworthMagicImportScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetworthMagicImportScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetworthMagicImportScreenOptionsValidationError{}

// Validate checks the field values on IdeasBanner with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *IdeasBanner) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IdeasBanner with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in IdeasBannerMultiError, or
// nil if none found.
func (m *IdeasBanner) ValidateAll() error {
	return m.validate(true)
}

func (m *IdeasBanner) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLeftVisualElement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IdeasBannerValidationError{
					field:  "LeftVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IdeasBannerValidationError{
					field:  "LeftVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeftVisualElement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IdeasBannerValidationError{
				field:  "LeftVisualElement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRightIconTextComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IdeasBannerValidationError{
					field:  "RightIconTextComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IdeasBannerValidationError{
					field:  "RightIconTextComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightIconTextComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IdeasBannerValidationError{
				field:  "RightIconTextComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBackground()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IdeasBannerValidationError{
					field:  "Background",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IdeasBannerValidationError{
					field:  "Background",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackground()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IdeasBannerValidationError{
				field:  "Background",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBorder()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IdeasBannerValidationError{
					field:  "Border",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IdeasBannerValidationError{
					field:  "Border",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBorder()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IdeasBannerValidationError{
				field:  "Border",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IdeasBannerValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IdeasBannerValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IdeasBannerValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IdeasBannerMultiError(errors)
	}

	return nil
}

// IdeasBannerMultiError is an error wrapping multiple validation errors
// returned by IdeasBanner.ValidateAll() if the designated constraints aren't met.
type IdeasBannerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IdeasBannerMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IdeasBannerMultiError) AllErrors() []error { return m }

// IdeasBannerValidationError is the validation error returned by
// IdeasBanner.Validate if the designated constraints aren't met.
type IdeasBannerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IdeasBannerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IdeasBannerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IdeasBannerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IdeasBannerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IdeasBannerValidationError) ErrorName() string { return "IdeasBannerValidationError" }

// Error satisfies the builtin error interface
func (e IdeasBannerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIdeasBanner.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IdeasBannerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IdeasBannerValidationError{}

// Validate checks the field values on ScanStateScreen with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ScanStateScreen) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScanStateScreen with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScanStateScreenMultiError, or nil if none found.
func (m *ScanStateScreen) ValidateAll() error {
	return m.validate(true)
}

func (m *ScanStateScreen) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScanStateScreenValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScanStateScreenValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScanStateScreenValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScanStateScreenValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScanStateScreenValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScanStateScreenValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMessage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScanStateScreenValidationError{
					field:  "Message",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScanStateScreenValidationError{
					field:  "Message",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMessage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScanStateScreenValidationError{
				field:  "Message",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPickerComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScanStateScreenValidationError{
					field:  "PickerComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScanStateScreenValidationError{
					field:  "PickerComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPickerComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScanStateScreenValidationError{
				field:  "PickerComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NoOfAssetsSupported

	for idx, item := range m.GetSupportedFiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ScanStateScreenValidationError{
						field:  fmt.Sprintf("SupportedFiles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ScanStateScreenValidationError{
						field:  fmt.Sprintf("SupportedFiles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ScanStateScreenValidationError{
					field:  fmt.Sprintf("SupportedFiles[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for MaxFileSizeKb

	if all {
		switch v := interface{}(m.GetPermissionDenialPlaceholder()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScanStateScreenValidationError{
					field:  "PermissionDenialPlaceholder",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScanStateScreenValidationError{
					field:  "PermissionDenialPlaceholder",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPermissionDenialPlaceholder()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScanStateScreenValidationError{
				field:  "PermissionDenialPlaceholder",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIdeasBanner()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScanStateScreenValidationError{
					field:  "IdeasBanner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScanStateScreenValidationError{
					field:  "IdeasBanner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdeasBanner()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScanStateScreenValidationError{
				field:  "IdeasBanner",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetScreenDisabledDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScanStateScreenValidationError{
					field:  "ScreenDisabledDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScanStateScreenValidationError{
					field:  "ScreenDisabledDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScreenDisabledDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScanStateScreenValidationError{
				field:  "ScreenDisabledDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeaderSwitchBar()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScanStateScreenValidationError{
					field:  "HeaderSwitchBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScanStateScreenValidationError{
					field:  "HeaderSwitchBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderSwitchBar()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScanStateScreenValidationError{
				field:  "HeaderSwitchBar",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ScanStateScreenMultiError(errors)
	}

	return nil
}

// ScanStateScreenMultiError is an error wrapping multiple validation errors
// returned by ScanStateScreen.ValidateAll() if the designated constraints
// aren't met.
type ScanStateScreenMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScanStateScreenMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScanStateScreenMultiError) AllErrors() []error { return m }

// ScanStateScreenValidationError is the validation error returned by
// ScanStateScreen.Validate if the designated constraints aren't met.
type ScanStateScreenValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScanStateScreenValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScanStateScreenValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScanStateScreenValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScanStateScreenValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScanStateScreenValidationError) ErrorName() string { return "ScanStateScreenValidationError" }

// Error satisfies the builtin error interface
func (e ScanStateScreenValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScanStateScreen.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScanStateScreenValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScanStateScreenValidationError{}

// Validate checks the field values on IntroPageScreen with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *IntroPageScreen) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IntroPageScreen with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IntroPageScreenMultiError, or nil if none found.
func (m *IntroPageScreen) ValidateAll() error {
	return m.validate(true)
}

func (m *IntroPageScreen) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Version

	// no validation rules for IntroAnimationUrl

	if all {
		switch v := interface{}(m.GetHeaderBar()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IntroPageScreenValidationError{
					field:  "HeaderBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IntroPageScreenValidationError{
					field:  "HeaderBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderBar()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IntroPageScreenValidationError{
				field:  "HeaderBar",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IntroPageScreenMultiError(errors)
	}

	return nil
}

// IntroPageScreenMultiError is an error wrapping multiple validation errors
// returned by IntroPageScreen.ValidateAll() if the designated constraints
// aren't met.
type IntroPageScreenMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IntroPageScreenMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IntroPageScreenMultiError) AllErrors() []error { return m }

// IntroPageScreenValidationError is the validation error returned by
// IntroPageScreen.Validate if the designated constraints aren't met.
type IntroPageScreenValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IntroPageScreenValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IntroPageScreenValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IntroPageScreenValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IntroPageScreenValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IntroPageScreenValidationError) ErrorName() string { return "IntroPageScreenValidationError" }

// Error satisfies the builtin error interface
func (e IntroPageScreenValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIntroPageScreen.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IntroPageScreenValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IntroPageScreenValidationError{}

// Validate checks the field values on PickerComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PickerComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PickerComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PickerComponentMultiError, or nil if none found.
func (m *PickerComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *PickerComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBackground()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PickerComponentValidationError{
					field:  "Background",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PickerComponentValidationError{
					field:  "Background",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackground()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PickerComponentValidationError{
				field:  "Background",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBorder()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PickerComponentValidationError{
					field:  "Border",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PickerComponentValidationError{
					field:  "Border",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBorder()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PickerComponentValidationError{
				field:  "Border",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CornerRadius

	if all {
		switch v := interface{}(m.GetGalleryItc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PickerComponentValidationError{
					field:  "GalleryItc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PickerComponentValidationError{
					field:  "GalleryItc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGalleryItc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PickerComponentValidationError{
				field:  "GalleryItc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDocumentItc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PickerComponentValidationError{
					field:  "DocumentItc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PickerComponentValidationError{
					field:  "DocumentItc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDocumentItc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PickerComponentValidationError{
				field:  "DocumentItc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PickerComponentMultiError(errors)
	}

	return nil
}

// PickerComponentMultiError is an error wrapping multiple validation errors
// returned by PickerComponent.ValidateAll() if the designated constraints
// aren't met.
type PickerComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PickerComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PickerComponentMultiError) AllErrors() []error { return m }

// PickerComponentValidationError is the validation error returned by
// PickerComponent.Validate if the designated constraints aren't met.
type PickerComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PickerComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PickerComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PickerComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PickerComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PickerComponentValidationError) ErrorName() string { return "PickerComponentValidationError" }

// Error satisfies the builtin error interface
func (e PickerComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPickerComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PickerComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PickerComponentValidationError{}

// Validate checks the field values on PreviewStateScreen with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PreviewStateScreen) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PreviewStateScreen with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PreviewStateScreenMultiError, or nil if none found.
func (m *PreviewStateScreen) ValidateAll() error {
	return m.validate(true)
}

func (m *PreviewStateScreen) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreviewStateScreenValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreviewStateScreenValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreviewStateScreenValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetScanMore()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreviewStateScreenValidationError{
					field:  "ScanMore",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreviewStateScreenValidationError{
					field:  "ScanMore",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScanMore()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreviewStateScreenValidationError{
				field:  "ScanMore",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAnalyseCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreviewStateScreenValidationError{
					field:  "AnalyseCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreviewStateScreenValidationError{
					field:  "AnalyseCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnalyseCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreviewStateScreenValidationError{
				field:  "AnalyseCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PreviewStateScreenMultiError(errors)
	}

	return nil
}

// PreviewStateScreenMultiError is an error wrapping multiple validation errors
// returned by PreviewStateScreen.ValidateAll() if the designated constraints
// aren't met.
type PreviewStateScreenMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PreviewStateScreenMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PreviewStateScreenMultiError) AllErrors() []error { return m }

// PreviewStateScreenValidationError is the validation error returned by
// PreviewStateScreen.Validate if the designated constraints aren't met.
type PreviewStateScreenValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PreviewStateScreenValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PreviewStateScreenValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PreviewStateScreenValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PreviewStateScreenValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PreviewStateScreenValidationError) ErrorName() string {
	return "PreviewStateScreenValidationError"
}

// Error satisfies the builtin error interface
func (e PreviewStateScreenValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPreviewStateScreen.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PreviewStateScreenValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PreviewStateScreenValidationError{}

// Validate checks the field values on LoadingStateScreen with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoadingStateScreen) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoadingStateScreen with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoadingStateScreenMultiError, or nil if none found.
func (m *LoadingStateScreen) ValidateAll() error {
	return m.validate(true)
}

func (m *LoadingStateScreen) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoadingStateScreenValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoadingStateScreenValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoadingStateScreenValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoaderAnimationUrl

	if all {
		switch v := interface{}(m.GetConfidentialMessage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoadingStateScreenValidationError{
					field:  "ConfidentialMessage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoadingStateScreenValidationError{
					field:  "ConfidentialMessage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConfidentialMessage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoadingStateScreenValidationError{
				field:  "ConfidentialMessage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoadingStateScreenMultiError(errors)
	}

	return nil
}

// LoadingStateScreenMultiError is an error wrapping multiple validation errors
// returned by LoadingStateScreen.ValidateAll() if the designated constraints
// aren't met.
type LoadingStateScreenMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoadingStateScreenMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoadingStateScreenMultiError) AllErrors() []error { return m }

// LoadingStateScreenValidationError is the validation error returned by
// LoadingStateScreen.Validate if the designated constraints aren't met.
type LoadingStateScreenValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoadingStateScreenValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoadingStateScreenValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoadingStateScreenValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoadingStateScreenValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoadingStateScreenValidationError) ErrorName() string {
	return "LoadingStateScreenValidationError"
}

// Error satisfies the builtin error interface
func (e LoadingStateScreenValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoadingStateScreen.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoadingStateScreenValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoadingStateScreenValidationError{}

// Validate checks the field values on ErrorStateScreen with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ErrorStateScreen) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ErrorStateScreen with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ErrorStateScreenMultiError, or nil if none found.
func (m *ErrorStateScreen) ValidateAll() error {
	return m.validate(true)
}

func (m *ErrorStateScreen) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ErrorStateScreenValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ErrorStateScreenValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ErrorStateScreenValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMessage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ErrorStateScreenValidationError{
					field:  "Message",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ErrorStateScreenValidationError{
					field:  "Message",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMessage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ErrorStateScreenValidationError{
				field:  "Message",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ErrorStateScreenValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ErrorStateScreenValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ErrorStateScreenValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetActionCtas() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ErrorStateScreenValidationError{
						field:  fmt.Sprintf("ActionCtas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ErrorStateScreenValidationError{
						field:  fmt.Sprintf("ActionCtas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ErrorStateScreenValidationError{
					field:  fmt.Sprintf("ActionCtas[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetErrorImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ErrorStateScreenValidationError{
					field:  "ErrorImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ErrorStateScreenValidationError{
					field:  "ErrorImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetErrorImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ErrorStateScreenValidationError{
				field:  "ErrorImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ErrorStateScreenMultiError(errors)
	}

	return nil
}

// ErrorStateScreenMultiError is an error wrapping multiple validation errors
// returned by ErrorStateScreen.ValidateAll() if the designated constraints
// aren't met.
type ErrorStateScreenMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ErrorStateScreenMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ErrorStateScreenMultiError) AllErrors() []error { return m }

// ErrorStateScreenValidationError is the validation error returned by
// ErrorStateScreen.Validate if the designated constraints aren't met.
type ErrorStateScreenValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ErrorStateScreenValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ErrorStateScreenValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ErrorStateScreenValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ErrorStateScreenValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ErrorStateScreenValidationError) ErrorName() string { return "ErrorStateScreenValidationError" }

// Error satisfies the builtin error interface
func (e ErrorStateScreenValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sErrorStateScreen.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ErrorStateScreenValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ErrorStateScreenValidationError{}

// Validate checks the field values on SupportedFile with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SupportedFile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SupportedFile with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SupportedFileMultiError, or
// nil if none found.
func (m *SupportedFile) ValidateAll() error {
	return m.validate(true)
}

func (m *SupportedFile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileExtension

	if all {
		switch v := interface{}(m.GetIconUrl()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SupportedFileValidationError{
					field:  "IconUrl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SupportedFileValidationError{
					field:  "IconUrl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIconUrl()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SupportedFileValidationError{
				field:  "IconUrl",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPreviewImg()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SupportedFileValidationError{
					field:  "PreviewImg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SupportedFileValidationError{
					field:  "PreviewImg",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPreviewImg()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SupportedFileValidationError{
				field:  "PreviewImg",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SupportedFileMultiError(errors)
	}

	return nil
}

// SupportedFileMultiError is an error wrapping multiple validation errors
// returned by SupportedFile.ValidateAll() if the designated constraints
// aren't met.
type SupportedFileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SupportedFileMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SupportedFileMultiError) AllErrors() []error { return m }

// SupportedFileValidationError is the validation error returned by
// SupportedFile.Validate if the designated constraints aren't met.
type SupportedFileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SupportedFileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SupportedFileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SupportedFileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SupportedFileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SupportedFileValidationError) ErrorName() string { return "SupportedFileValidationError" }

// Error satisfies the builtin error interface
func (e SupportedFileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSupportedFile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SupportedFileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SupportedFileValidationError{}

// Validate checks the field values on ScanMoreComponent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ScanMoreComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScanMoreComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScanMoreComponentMultiError, or nil if none found.
func (m *ScanMoreComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *ScanMoreComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBorder()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScanMoreComponentValidationError{
					field:  "Border",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScanMoreComponentValidationError{
					field:  "Border",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBorder()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScanMoreComponentValidationError{
				field:  "Border",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScanMoreComponentValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScanMoreComponentValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScanMoreComponentValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScanMoreComponentValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScanMoreComponentValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScanMoreComponentValidationError{
				field:  "Text",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ScanMoreComponentMultiError(errors)
	}

	return nil
}

// ScanMoreComponentMultiError is an error wrapping multiple validation errors
// returned by ScanMoreComponent.ValidateAll() if the designated constraints
// aren't met.
type ScanMoreComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScanMoreComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScanMoreComponentMultiError) AllErrors() []error { return m }

// ScanMoreComponentValidationError is the validation error returned by
// ScanMoreComponent.Validate if the designated constraints aren't met.
type ScanMoreComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScanMoreComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScanMoreComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScanMoreComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScanMoreComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScanMoreComponentValidationError) ErrorName() string {
	return "ScanMoreComponentValidationError"
}

// Error satisfies the builtin error interface
func (e ScanMoreComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScanMoreComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScanMoreComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScanMoreComponentValidationError{}

// Validate checks the field values on HeaderSwitchBar with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *HeaderSwitchBar) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HeaderSwitchBar with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HeaderSwitchBarMultiError, or nil if none found.
func (m *HeaderSwitchBar) ValidateAll() error {
	return m.validate(true)
}

func (m *HeaderSwitchBar) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCenterSwitch()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HeaderSwitchBarValidationError{
					field:  "CenterSwitch",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HeaderSwitchBarValidationError{
					field:  "CenterSwitch",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCenterSwitch()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HeaderSwitchBarValidationError{
				field:  "CenterSwitch",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRightItc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HeaderSwitchBarValidationError{
					field:  "RightItc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HeaderSwitchBarValidationError{
					field:  "RightItc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightItc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HeaderSwitchBarValidationError{
				field:  "RightItc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLeftItc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HeaderSwitchBarValidationError{
					field:  "LeftItc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HeaderSwitchBarValidationError{
					field:  "LeftItc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeftItc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HeaderSwitchBarValidationError{
				field:  "LeftItc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return HeaderSwitchBarMultiError(errors)
	}

	return nil
}

// HeaderSwitchBarMultiError is an error wrapping multiple validation errors
// returned by HeaderSwitchBar.ValidateAll() if the designated constraints
// aren't met.
type HeaderSwitchBarMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HeaderSwitchBarMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HeaderSwitchBarMultiError) AllErrors() []error { return m }

// HeaderSwitchBarValidationError is the validation error returned by
// HeaderSwitchBar.Validate if the designated constraints aren't met.
type HeaderSwitchBarValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HeaderSwitchBarValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HeaderSwitchBarValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HeaderSwitchBarValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HeaderSwitchBarValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HeaderSwitchBarValidationError) ErrorName() string { return "HeaderSwitchBarValidationError" }

// Error satisfies the builtin error interface
func (e HeaderSwitchBarValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHeaderSwitchBar.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HeaderSwitchBarValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HeaderSwitchBarValidationError{}

// Validate checks the field values on ProgressBarDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProgressBarDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProgressBarDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProgressBarDetailsMultiError, or nil if none found.
func (m *ProgressBarDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ProgressBarDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProgressBarDetailsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProgressBarDetailsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProgressBarDetailsValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFilledBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProgressBarDetailsValidationError{
					field:  "FilledBgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProgressBarDetailsValidationError{
					field:  "FilledBgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilledBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProgressBarDetailsValidationError{
				field:  "FilledBgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Height

	// no validation rules for CornerRadius

	if len(errors) > 0 {
		return ProgressBarDetailsMultiError(errors)
	}

	return nil
}

// ProgressBarDetailsMultiError is an error wrapping multiple validation errors
// returned by ProgressBarDetails.ValidateAll() if the designated constraints
// aren't met.
type ProgressBarDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProgressBarDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProgressBarDetailsMultiError) AllErrors() []error { return m }

// ProgressBarDetailsValidationError is the validation error returned by
// ProgressBarDetails.Validate if the designated constraints aren't met.
type ProgressBarDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProgressBarDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProgressBarDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProgressBarDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProgressBarDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProgressBarDetailsValidationError) ErrorName() string {
	return "ProgressBarDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ProgressBarDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProgressBarDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProgressBarDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProgressBarDetailsValidationError{}

// Validate checks the field values on ScanStateScreen_ScreenDisabledDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ScanStateScreen_ScreenDisabledDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScanStateScreen_ScreenDisabledDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ScanStateScreen_ScreenDisabledDetailsMultiError, or nil if none found.
func (m *ScanStateScreen_ScreenDisabledDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ScanStateScreen_ScreenDisabledDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsDisabled

	if all {
		switch v := interface{}(m.GetScanDisabledPlaceholder()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScanStateScreen_ScreenDisabledDetailsValidationError{
					field:  "ScanDisabledPlaceholder",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScanStateScreen_ScreenDisabledDetailsValidationError{
					field:  "ScanDisabledPlaceholder",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScanDisabledPlaceholder()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScanStateScreen_ScreenDisabledDetailsValidationError{
				field:  "ScanDisabledPlaceholder",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ScanStateScreen_ScreenDisabledDetailsMultiError(errors)
	}

	return nil
}

// ScanStateScreen_ScreenDisabledDetailsMultiError is an error wrapping
// multiple validation errors returned by
// ScanStateScreen_ScreenDisabledDetails.ValidateAll() if the designated
// constraints aren't met.
type ScanStateScreen_ScreenDisabledDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScanStateScreen_ScreenDisabledDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScanStateScreen_ScreenDisabledDetailsMultiError) AllErrors() []error { return m }

// ScanStateScreen_ScreenDisabledDetailsValidationError is the validation error
// returned by ScanStateScreen_ScreenDisabledDetails.Validate if the
// designated constraints aren't met.
type ScanStateScreen_ScreenDisabledDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScanStateScreen_ScreenDisabledDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScanStateScreen_ScreenDisabledDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScanStateScreen_ScreenDisabledDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScanStateScreen_ScreenDisabledDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScanStateScreen_ScreenDisabledDetailsValidationError) ErrorName() string {
	return "ScanStateScreen_ScreenDisabledDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ScanStateScreen_ScreenDisabledDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScanStateScreen_ScreenDisabledDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScanStateScreen_ScreenDisabledDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScanStateScreen_ScreenDisabledDetailsValidationError{}
