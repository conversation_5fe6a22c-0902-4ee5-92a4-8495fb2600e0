// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/deeplink_screen_option/insights/networth/interactive_talk_to_ai.proto

package networth

import (
	deeplink_screen_option "github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Entrypoint int32

const (
	Entrypoint_ENTRYPOINT_UNSPECIFIED Entrypoint = 0
	// wealth builder dashboard entrypoint
	Entrypoint_ENTRYPOINT_WB_DASHBOARD Entrypoint = 1
	// entrypoint from daily and weekly portfolio tracker
	// deprecated: use ENTRYPOINT_DAILY_PORTFOLIO_TRACKER and ENTRYPOINT_WEEKLY_PORTFOLIO_TRACKER
	//
	// Deprecated: Marked as deprecated in api/typesv2/deeplink_screen_option/insights/networth/interactive_talk_to_ai.proto.
	Entrypoint_ENTRYPOINT_PORTFOLIO_TRACKER Entrypoint = 2
	// wealth analyser mutual funds report page
	Entrypoint_ENTRYPOINT_MF_REPORT_PAGE Entrypoint = 3
	// wealth analyser epf report page
	Entrypoint_ENTRYPOINT_EPF_REPORT_PAGE Entrypoint = 4
	// indian stocks dashboard page
	Entrypoint_ENTRYPOINT_INDIAN_STOCKS_DASHBOARD Entrypoint = 5
	// connected account bank account page
	Entrypoint_ENTRYPOINT_BANK_ACCOUNT_PAGE Entrypoint = 6
	// asset landing pages (manual assets, nps)
	Entrypoint_ENTRYPOINT_ASSET_LANDING Entrypoint = 7
	// entrypoint from daily portfolio tracker
	Entrypoint_ENTRYPOINT_DAILY_PORTFOLIO_TRACKER Entrypoint = 8
	// entrypoint from weekly portfolio tracker
	Entrypoint_ENTRYPOINT_WEEKLY_PORTFOLIO_TRACKER Entrypoint = 9
)

// Enum value maps for Entrypoint.
var (
	Entrypoint_name = map[int32]string{
		0: "ENTRYPOINT_UNSPECIFIED",
		1: "ENTRYPOINT_WB_DASHBOARD",
		2: "ENTRYPOINT_PORTFOLIO_TRACKER",
		3: "ENTRYPOINT_MF_REPORT_PAGE",
		4: "ENTRYPOINT_EPF_REPORT_PAGE",
		5: "ENTRYPOINT_INDIAN_STOCKS_DASHBOARD",
		6: "ENTRYPOINT_BANK_ACCOUNT_PAGE",
		7: "ENTRYPOINT_ASSET_LANDING",
		8: "ENTRYPOINT_DAILY_PORTFOLIO_TRACKER",
		9: "ENTRYPOINT_WEEKLY_PORTFOLIO_TRACKER",
	}
	Entrypoint_value = map[string]int32{
		"ENTRYPOINT_UNSPECIFIED":              0,
		"ENTRYPOINT_WB_DASHBOARD":             1,
		"ENTRYPOINT_PORTFOLIO_TRACKER":        2,
		"ENTRYPOINT_MF_REPORT_PAGE":           3,
		"ENTRYPOINT_EPF_REPORT_PAGE":          4,
		"ENTRYPOINT_INDIAN_STOCKS_DASHBOARD":  5,
		"ENTRYPOINT_BANK_ACCOUNT_PAGE":        6,
		"ENTRYPOINT_ASSET_LANDING":            7,
		"ENTRYPOINT_DAILY_PORTFOLIO_TRACKER":  8,
		"ENTRYPOINT_WEEKLY_PORTFOLIO_TRACKER": 9,
	}
)

func (x Entrypoint) Enum() *Entrypoint {
	p := new(Entrypoint)
	*p = x
	return p
}

func (x Entrypoint) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Entrypoint) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_enumTypes[0].Descriptor()
}

func (Entrypoint) Type() protoreflect.EnumType {
	return &file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_enumTypes[0]
}

func (x Entrypoint) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Entrypoint.Descriptor instead.
func (Entrypoint) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_rawDescGZIP(), []int{0}
}

type InteractiveTalkToAiScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Entrypoint.String()
	Entrypoint string `protobuf:"bytes,2,opt,name=entrypoint,proto3" json:"entrypoint,omitempty"`
}

func (x *InteractiveTalkToAiScreenOptions) Reset() {
	*x = InteractiveTalkToAiScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InteractiveTalkToAiScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InteractiveTalkToAiScreenOptions) ProtoMessage() {}

func (x *InteractiveTalkToAiScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InteractiveTalkToAiScreenOptions.ProtoReflect.Descriptor instead.
func (*InteractiveTalkToAiScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_rawDescGZIP(), []int{0}
}

func (x *InteractiveTalkToAiScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *InteractiveTalkToAiScreenOptions) GetEntrypoint() string {
	if x != nil {
		return x.Entrypoint
	}
	return ""
}

var File_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto protoreflect.FileDescriptor

var file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_rawDesc = []byte{
	0x0a, 0x51, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x74, 0x61, 0x6c, 0x6b, 0x5f, 0x74, 0x6f, 0x5f, 0x61, 0x69, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x34, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x92, 0x01, 0x0a, 0x20, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x61, 0x6c, 0x6b, 0x54, 0x6f,
	0x41, 0x69, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x1e, 0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x2a,
	0xe3, 0x02, 0x0a, 0x0a, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x1a,
	0x0a, 0x16, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x45, 0x4e,
	0x54, 0x52, 0x59, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x57, 0x42, 0x5f, 0x44, 0x41, 0x53, 0x48,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x10, 0x01, 0x12, 0x24, 0x0a, 0x1c, 0x45, 0x4e, 0x54, 0x52, 0x59,
	0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x5f,
	0x54, 0x52, 0x41, 0x43, 0x4b, 0x45, 0x52, 0x10, 0x02, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x1d, 0x0a,
	0x19, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x4d, 0x46, 0x5f, 0x52,
	0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a,
	0x45, 0x4e, 0x54, 0x52, 0x59, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x45, 0x50, 0x46, 0x5f, 0x52,
	0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x10, 0x04, 0x12, 0x26, 0x0a, 0x22,
	0x45, 0x4e, 0x54, 0x52, 0x59, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x44, 0x49, 0x41,
	0x4e, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x44, 0x41, 0x53, 0x48, 0x42, 0x4f, 0x41,
	0x52, 0x44, 0x10, 0x05, 0x12, 0x20, 0x0a, 0x1c, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x50, 0x4f, 0x49,
	0x4e, 0x54, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x50, 0x41, 0x47, 0x45, 0x10, 0x06, 0x12, 0x1c, 0x0a, 0x18, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x50,
	0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49,
	0x4e, 0x47, 0x10, 0x07, 0x12, 0x26, 0x0a, 0x22, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x50, 0x4f, 0x49,
	0x4e, 0x54, 0x5f, 0x44, 0x41, 0x49, 0x4c, 0x59, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c,
	0x49, 0x4f, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x45, 0x52, 0x10, 0x08, 0x12, 0x27, 0x0a, 0x23,
	0x45, 0x4e, 0x54, 0x52, 0x59, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x57, 0x45, 0x45, 0x4b, 0x4c,
	0x59, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x5f, 0x54, 0x52, 0x41, 0x43,
	0x4b, 0x45, 0x52, 0x10, 0x09, 0x42, 0x9c, 0x01, 0x0a, 0x4b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x74, 0x68, 0x50, 0x01, 0x5a, 0x4b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_rawDescOnce sync.Once
	file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_rawDescData = file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_rawDesc
)

func file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_rawDescGZIP() []byte {
	file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_rawDescOnce.Do(func() {
		file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_rawDescData)
	})
	return file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_rawDescData
}

var file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_goTypes = []interface{}{
	(Entrypoint)(0),                                   // 0: api.typesv2.deeplink_screen_option.insights.networth.Entrypoint
	(*InteractiveTalkToAiScreenOptions)(nil),          // 1: api.typesv2.deeplink_screen_option.insights.networth.InteractiveTalkToAiScreenOptions
	(*deeplink_screen_option.ScreenOptionHeader)(nil), // 2: api.typesv2.deeplink_screen_option.ScreenOptionHeader
}
var file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_depIdxs = []int32{
	2, // 0: api.typesv2.deeplink_screen_option.insights.networth.InteractiveTalkToAiScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() {
	file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_init()
}
func file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_init() {
	if File_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InteractiveTalkToAiScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_goTypes,
		DependencyIndexes: file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_depIdxs,
		EnumInfos:         file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_enumTypes,
		MessageInfos:      file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_msgTypes,
	}.Build()
	File_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto = out.File
	file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_rawDesc = nil
	file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_goTypes = nil
	file_api_typesv2_deeplink_screen_option_insights_networth_interactive_talk_to_ai_proto_depIdxs = nil
}
