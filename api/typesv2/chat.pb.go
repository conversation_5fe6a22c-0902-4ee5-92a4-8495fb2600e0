// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/chat.proto

package typesv2

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// InAppChatViewType identifies different typesv2 of chat views available on the client
type InAppChatViewType int32

const (
	InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_UNSPECIFIED InAppChatViewType = 0
	// Freshchat's chat sdk
	InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_FRESHCHAT_SDK InAppChatViewType = 1
	// Senseforth's chatbot webview
	InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_SENSEFORTH_WEBVIEW InAppChatViewType = 2
	// Nugget chatbot's sdk
	InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_NUGGET_CHATBOT_SDK InAppChatViewType = 3
)

// Enum value maps for InAppChatViewType.
var (
	InAppChatViewType_name = map[int32]string{
		0: "IN_APP_CHAT_VIEW_TYPE_UNSPECIFIED",
		1: "IN_APP_CHAT_VIEW_TYPE_FRESHCHAT_SDK",
		2: "IN_APP_CHAT_VIEW_TYPE_SENSEFORTH_WEBVIEW",
		3: "IN_APP_CHAT_VIEW_TYPE_NUGGET_CHATBOT_SDK",
	}
	InAppChatViewType_value = map[string]int32{
		"IN_APP_CHAT_VIEW_TYPE_UNSPECIFIED":        0,
		"IN_APP_CHAT_VIEW_TYPE_FRESHCHAT_SDK":      1,
		"IN_APP_CHAT_VIEW_TYPE_SENSEFORTH_WEBVIEW": 2,
		"IN_APP_CHAT_VIEW_TYPE_NUGGET_CHATBOT_SDK": 3,
	}
)

func (x InAppChatViewType) Enum() *InAppChatViewType {
	p := new(InAppChatViewType)
	*p = x
	return p
}

func (x InAppChatViewType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InAppChatViewType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_chat_proto_enumTypes[0].Descriptor()
}

func (InAppChatViewType) Type() protoreflect.EnumType {
	return &file_api_typesv2_chat_proto_enumTypes[0]
}

func (x InAppChatViewType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InAppChatViewType.Descriptor instead.
func (InAppChatViewType) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_chat_proto_rawDescGZIP(), []int{0}
}

// Represents the set of data fields that a chatbot can request from the backend.
type ChatbotRequestedDataField int32

const (
	ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_UNSPECIFIED                     ChatbotRequestedDataField = 0
	ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_SAVINGS_ACCOUNT_FREEZE_REASON   ChatbotRequestedDataField = 1
	ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_SAVINGS_ACCOUNT_STATUS          ChatbotRequestedDataField = 2
	ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_SAVINGS_ACCOUNT_FREEZE_TYPE     ChatbotRequestedDataField = 3
	ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_RISK_FORM_ID                    ChatbotRequestedDataField = 4
	ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_RISK_FORM_STATUS                ChatbotRequestedDataField = 5
	ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_RISK_FORM_EXPIRY_DATE           ChatbotRequestedDataField = 6
	ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_RISK_LEA_REPORTED               ChatbotRequestedDataField = 7
	ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_RISK_COMPLAINT_DETAILS          ChatbotRequestedDataField = 8
	ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_TRANSACTION_DETAILS             ChatbotRequestedDataField = 9
	ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_SAVINGS_ACCOUNT_FREEZE_DETAILS  ChatbotRequestedDataField = 10
	ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_AFU_UPDATE_ELIGIBILITY          ChatbotRequestedDataField = 11
	ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_LAST_CHEQUEBOOK_REQUEST_DETAILS ChatbotRequestedDataField = 12
	ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_CHEQUEBOOK_ELIGIBILITY          ChatbotRequestedDataField = 13
	ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_ACCOUNT_CLOSURE                 ChatbotRequestedDataField = 14
	ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_B2B_SALARY_PROGRAM              ChatbotRequestedDataField = 15
	ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_KYC_COMPLAINT_STATUS            ChatbotRequestedDataField = 16
	ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_CURRENT_TIER_DETAILS            ChatbotRequestedDataField = 17
	ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_IS_ELIGIBLE_FOR_TIER            ChatbotRequestedDataField = 18
	ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_LAST_DC_DELIVERY_DETAILS        ChatbotRequestedDataField = 19
)

// Enum value maps for ChatbotRequestedDataField.
var (
	ChatbotRequestedDataField_name = map[int32]string{
		0:  "CHATBOT_USER_DATA_FIELD_UNSPECIFIED",
		1:  "CHATBOT_USER_DATA_FIELD_SAVINGS_ACCOUNT_FREEZE_REASON",
		2:  "CHATBOT_USER_DATA_FIELD_SAVINGS_ACCOUNT_STATUS",
		3:  "CHATBOT_USER_DATA_FIELD_SAVINGS_ACCOUNT_FREEZE_TYPE",
		4:  "CHATBOT_USER_DATA_FIELD_RISK_FORM_ID",
		5:  "CHATBOT_USER_DATA_FIELD_RISK_FORM_STATUS",
		6:  "CHATBOT_USER_DATA_FIELD_RISK_FORM_EXPIRY_DATE",
		7:  "CHATBOT_USER_DATA_FIELD_RISK_LEA_REPORTED",
		8:  "CHATBOT_USER_DATA_FIELD_RISK_COMPLAINT_DETAILS",
		9:  "CHATBOT_USER_DATA_FIELD_TRANSACTION_DETAILS",
		10: "CHATBOT_USER_DATA_FIELD_SAVINGS_ACCOUNT_FREEZE_DETAILS",
		11: "CHATBOT_USER_DATA_FIELD_AFU_UPDATE_ELIGIBILITY",
		12: "CHATBOT_USER_DATA_FIELD_LAST_CHEQUEBOOK_REQUEST_DETAILS",
		13: "CHATBOT_USER_DATA_FIELD_CHEQUEBOOK_ELIGIBILITY",
		14: "CHATBOT_USER_DATA_FIELD_ACCOUNT_CLOSURE",
		15: "CHATBOT_USER_DATA_FIELD_B2B_SALARY_PROGRAM",
		16: "CHATBOT_USER_DATA_FIELD_KYC_COMPLAINT_STATUS",
		17: "CHATBOT_USER_DATA_FIELD_CURRENT_TIER_DETAILS",
		18: "CHATBOT_USER_DATA_FIELD_IS_ELIGIBLE_FOR_TIER",
		19: "CHATBOT_USER_DATA_FIELD_LAST_DC_DELIVERY_DETAILS",
	}
	ChatbotRequestedDataField_value = map[string]int32{
		"CHATBOT_USER_DATA_FIELD_UNSPECIFIED":                     0,
		"CHATBOT_USER_DATA_FIELD_SAVINGS_ACCOUNT_FREEZE_REASON":   1,
		"CHATBOT_USER_DATA_FIELD_SAVINGS_ACCOUNT_STATUS":          2,
		"CHATBOT_USER_DATA_FIELD_SAVINGS_ACCOUNT_FREEZE_TYPE":     3,
		"CHATBOT_USER_DATA_FIELD_RISK_FORM_ID":                    4,
		"CHATBOT_USER_DATA_FIELD_RISK_FORM_STATUS":                5,
		"CHATBOT_USER_DATA_FIELD_RISK_FORM_EXPIRY_DATE":           6,
		"CHATBOT_USER_DATA_FIELD_RISK_LEA_REPORTED":               7,
		"CHATBOT_USER_DATA_FIELD_RISK_COMPLAINT_DETAILS":          8,
		"CHATBOT_USER_DATA_FIELD_TRANSACTION_DETAILS":             9,
		"CHATBOT_USER_DATA_FIELD_SAVINGS_ACCOUNT_FREEZE_DETAILS":  10,
		"CHATBOT_USER_DATA_FIELD_AFU_UPDATE_ELIGIBILITY":          11,
		"CHATBOT_USER_DATA_FIELD_LAST_CHEQUEBOOK_REQUEST_DETAILS": 12,
		"CHATBOT_USER_DATA_FIELD_CHEQUEBOOK_ELIGIBILITY":          13,
		"CHATBOT_USER_DATA_FIELD_ACCOUNT_CLOSURE":                 14,
		"CHATBOT_USER_DATA_FIELD_B2B_SALARY_PROGRAM":              15,
		"CHATBOT_USER_DATA_FIELD_KYC_COMPLAINT_STATUS":            16,
		"CHATBOT_USER_DATA_FIELD_CURRENT_TIER_DETAILS":            17,
		"CHATBOT_USER_DATA_FIELD_IS_ELIGIBLE_FOR_TIER":            18,
		"CHATBOT_USER_DATA_FIELD_LAST_DC_DELIVERY_DETAILS":        19,
	}
)

func (x ChatbotRequestedDataField) Enum() *ChatbotRequestedDataField {
	p := new(ChatbotRequestedDataField)
	*p = x
	return p
}

func (x ChatbotRequestedDataField) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChatbotRequestedDataField) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_chat_proto_enumTypes[1].Descriptor()
}

func (ChatbotRequestedDataField) Type() protoreflect.EnumType {
	return &file_api_typesv2_chat_proto_enumTypes[1]
}

func (x ChatbotRequestedDataField) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChatbotRequestedDataField.Descriptor instead.
func (ChatbotRequestedDataField) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_chat_proto_rawDescGZIP(), []int{1}
}

// Data needed to initialize the chatbot SDK should be defined here
type ChatbotInitInformation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to ChatbotSpecificInitInformation:
	//
	//	*ChatbotInitInformation_NuggetChatbotInitInformation
	//	*ChatbotInitInformation_FreshchatChatbotInitInformation
	//	*ChatbotInitInformation_SenseforthChatbotInitInformation
	ChatbotSpecificInitInformation isChatbotInitInformation_ChatbotSpecificInitInformation `protobuf_oneof:"ChatbotSpecificInitInformation"`
}

func (x *ChatbotInitInformation) Reset() {
	*x = ChatbotInitInformation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_chat_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChatbotInitInformation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatbotInitInformation) ProtoMessage() {}

func (x *ChatbotInitInformation) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_chat_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatbotInitInformation.ProtoReflect.Descriptor instead.
func (*ChatbotInitInformation) Descriptor() ([]byte, []int) {
	return file_api_typesv2_chat_proto_rawDescGZIP(), []int{0}
}

func (m *ChatbotInitInformation) GetChatbotSpecificInitInformation() isChatbotInitInformation_ChatbotSpecificInitInformation {
	if m != nil {
		return m.ChatbotSpecificInitInformation
	}
	return nil
}

func (x *ChatbotInitInformation) GetNuggetChatbotInitInformation() *NuggetChatbotInitInformation {
	if x, ok := x.GetChatbotSpecificInitInformation().(*ChatbotInitInformation_NuggetChatbotInitInformation); ok {
		return x.NuggetChatbotInitInformation
	}
	return nil
}

func (x *ChatbotInitInformation) GetFreshchatChatbotInitInformation() *FreschatChatbotInitInformation {
	if x, ok := x.GetChatbotSpecificInitInformation().(*ChatbotInitInformation_FreshchatChatbotInitInformation); ok {
		return x.FreshchatChatbotInitInformation
	}
	return nil
}

func (x *ChatbotInitInformation) GetSenseforthChatbotInitInformation() *SenseforthChatbotInitInformation {
	if x, ok := x.GetChatbotSpecificInitInformation().(*ChatbotInitInformation_SenseforthChatbotInitInformation); ok {
		return x.SenseforthChatbotInitInformation
	}
	return nil
}

type isChatbotInitInformation_ChatbotSpecificInitInformation interface {
	isChatbotInitInformation_ChatbotSpecificInitInformation()
}

type ChatbotInitInformation_NuggetChatbotInitInformation struct {
	NuggetChatbotInitInformation *NuggetChatbotInitInformation `protobuf:"bytes,2,opt,name=nugget_chatbot_init_information,json=nuggetChatbotInitInformation,proto3,oneof"`
}

type ChatbotInitInformation_FreshchatChatbotInitInformation struct {
	FreshchatChatbotInitInformation *FreschatChatbotInitInformation `protobuf:"bytes,3,opt,name=freshchat_chatbot_init_information,json=freshchatChatbotInitInformation,proto3,oneof"`
}

type ChatbotInitInformation_SenseforthChatbotInitInformation struct {
	SenseforthChatbotInitInformation *SenseforthChatbotInitInformation `protobuf:"bytes,4,opt,name=senseforth_chatbot_init_information,json=senseforthChatbotInitInformation,proto3,oneof"`
}

func (*ChatbotInitInformation_NuggetChatbotInitInformation) isChatbotInitInformation_ChatbotSpecificInitInformation() {
}

func (*ChatbotInitInformation_FreshchatChatbotInitInformation) isChatbotInitInformation_ChatbotSpecificInitInformation() {
}

func (*ChatbotInitInformation_SenseforthChatbotInitInformation) isChatbotInitInformation_ChatbotSpecificInitInformation() {
}

// SenseforthChatbotInitInformation provides information
// to initialize senseforth webview on client side
type SenseforthChatbotInitInformation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// url which client has to load for chatbot webview
	WebViewUrl string `protobuf:"bytes,1,opt,name=web_view_url,json=webViewUrl,proto3" json:"web_view_url,omitempty"`
	// short token which client need to pass to senseforth
	ShortToken string `protobuf:"bytes,2,opt,name=short_token,json=shortToken,proto3" json:"short_token,omitempty"`
	// If this flag is set to TRUE , the client should reuse the short token & url stored in the cache
	ReuseCacheData common.BooleanEnum `protobuf:"varint,3,opt,name=reuse_cache_data,json=reuseCacheData,proto3,enum=api.typesv2.common.BooleanEnum" json:"reuse_cache_data,omitempty"`
	// The context to invoke the bot. Ref: https://docs.google.com/document/d/15A9XIZRPD0omXeBTIm36WXU3GuQU46zt32zagqB2wrs/edit
	// This context will be used by client only if the deeplink(which triggered this RPC) doesn't contain context_code
	BotContextCode string `protobuf:"bytes,4,opt,name=bot_context_code,json=botContextCode,proto3" json:"bot_context_code,omitempty"`
}

func (x *SenseforthChatbotInitInformation) Reset() {
	*x = SenseforthChatbotInitInformation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_chat_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SenseforthChatbotInitInformation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SenseforthChatbotInitInformation) ProtoMessage() {}

func (x *SenseforthChatbotInitInformation) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_chat_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SenseforthChatbotInitInformation.ProtoReflect.Descriptor instead.
func (*SenseforthChatbotInitInformation) Descriptor() ([]byte, []int) {
	return file_api_typesv2_chat_proto_rawDescGZIP(), []int{1}
}

func (x *SenseforthChatbotInitInformation) GetWebViewUrl() string {
	if x != nil {
		return x.WebViewUrl
	}
	return ""
}

func (x *SenseforthChatbotInitInformation) GetShortToken() string {
	if x != nil {
		return x.ShortToken
	}
	return ""
}

func (x *SenseforthChatbotInitInformation) GetReuseCacheData() common.BooleanEnum {
	if x != nil {
		return x.ReuseCacheData
	}
	return common.BooleanEnum(0)
}

func (x *SenseforthChatbotInitInformation) GetBotContextCode() string {
	if x != nil {
		return x.BotContextCode
	}
	return ""
}

type FreschatChatbotInitInformation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// mapped freshdesk_id received from vendor-mapping service for current actor
	ReferenceId string `protobuf:"bytes,1,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
	// App id of the freshchat mobile sdk
	AppId string `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// App key of the freshchat mobile sdk
	AppKey string `protobuf:"bytes,3,opt,name=app_key,json=appKey,proto3" json:"app_key,omitempty"`
	// Domain name of freshchat mobile sdk
	Domain string `protobuf:"bytes,4,opt,name=domain,proto3" json:"domain,omitempty"`
	// Custom user properties to be passed to freschat sdk via android/ios client
	CustomUserProperties map[string]string `protobuf:"bytes,5,rep,name=custom_user_properties,json=customUserProperties,proto3" json:"custom_user_properties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// backend generated email to be passed by mobile client to Freshchat sdk
	// it is not part of custom property because client sdk explicitly expects
	// email to passed separately as an identifier
	Email string `protobuf:"bytes,6,opt,name=email,proto3" json:"email,omitempty"`
	// To filter and display only Topics tagged with a specific term, use the filterByTags API in ConversationOptions instance passed to showConversations() API as below.
	// Eg: To link and display only specific Topics from say orders page in your app, those specific Topics can be tagged with the term "order_queries".
	TopicTags []string `protobuf:"bytes,7,rep,name=topic_tags,json=topicTags,proto3" json:"topic_tags,omitempty"`
}

func (x *FreschatChatbotInitInformation) Reset() {
	*x = FreschatChatbotInitInformation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_chat_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FreschatChatbotInitInformation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FreschatChatbotInitInformation) ProtoMessage() {}

func (x *FreschatChatbotInitInformation) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_chat_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FreschatChatbotInitInformation.ProtoReflect.Descriptor instead.
func (*FreschatChatbotInitInformation) Descriptor() ([]byte, []int) {
	return file_api_typesv2_chat_proto_rawDescGZIP(), []int{2}
}

func (x *FreschatChatbotInitInformation) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *FreschatChatbotInitInformation) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *FreschatChatbotInitInformation) GetAppKey() string {
	if x != nil {
		return x.AppKey
	}
	return ""
}

func (x *FreschatChatbotInitInformation) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *FreschatChatbotInitInformation) GetCustomUserProperties() map[string]string {
	if x != nil {
		return x.CustomUserProperties
	}
	return nil
}

func (x *FreschatChatbotInitInformation) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *FreschatChatbotInitInformation) GetTopicTags() []string {
	if x != nil {
		return x.TopicTags
	}
	return nil
}

type NuggetChatbotInitInformation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// access_token to initialize the SDK
	AccessToken string `protobuf:"bytes,1,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	// [Deprecated] This will not be used in favor of [BusinessContext] field
	//
	// Deprecated: Marked as deprecated in api/typesv2/chat.proto.
	CustomUserProperties map[string]string `protobuf:"bytes,2,rep,name=custom_user_properties,json=customUserProperties,proto3" json:"custom_user_properties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Namespace to be used for the chatbot
	Namespace string `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// Deeplink uri to be used for the chatbot. Note: This is not to be confused with frontend.Deeplink.
	// This deeplink is a Nugget specific deeplink used within the SDK
	DeeplinkUri string `protobuf:"bytes,4,opt,name=deeplink_uri,json=deeplinkUri,proto3" json:"deeplink_uri,omitempty"`
	// HTTP code received from Nugget's backend
	HttpCode int32 `protobuf:"varint,5,opt,name=http_code,json=httpCode,proto3" json:"http_code,omitempty"`
	// Business Context object which Nugget SDK can use for various
	// optional configurations. Some values like auth token for Server to Server call from Nugget
	// are passed here
	BusinessContext *NuggetChatbotInitInformation_BusinessContext `protobuf:"bytes,6,opt,name=business_context,json=businessContext,proto3" json:"business_context,omitempty"`
}

func (x *NuggetChatbotInitInformation) Reset() {
	*x = NuggetChatbotInitInformation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_chat_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NuggetChatbotInitInformation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NuggetChatbotInitInformation) ProtoMessage() {}

func (x *NuggetChatbotInitInformation) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_chat_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NuggetChatbotInitInformation.ProtoReflect.Descriptor instead.
func (*NuggetChatbotInitInformation) Descriptor() ([]byte, []int) {
	return file_api_typesv2_chat_proto_rawDescGZIP(), []int{3}
}

func (x *NuggetChatbotInitInformation) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

// Deprecated: Marked as deprecated in api/typesv2/chat.proto.
func (x *NuggetChatbotInitInformation) GetCustomUserProperties() map[string]string {
	if x != nil {
		return x.CustomUserProperties
	}
	return nil
}

func (x *NuggetChatbotInitInformation) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *NuggetChatbotInitInformation) GetDeeplinkUri() string {
	if x != nil {
		return x.DeeplinkUri
	}
	return ""
}

func (x *NuggetChatbotInitInformation) GetHttpCode() int32 {
	if x != nil {
		return x.HttpCode
	}
	return 0
}

func (x *NuggetChatbotInitInformation) GetBusinessContext() *NuggetChatbotInitInformation_BusinessContext {
	if x != nil {
		return x.BusinessContext
	}
	return nil
}

// Object representing the Business Context object which Nugget SDK can use
// for various optional configurations
type NuggetChatbotInitInformation_BusinessContext struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChannelHandle    string                                                              `protobuf:"bytes,1,opt,name=channel_handle,json=channelHandle,proto3" json:"channel_handle,omitempty"`
	TicketGroupingId string                                                              `protobuf:"bytes,2,opt,name=ticket_grouping_id,json=ticketGroupingId,proto3" json:"ticket_grouping_id,omitempty"`
	TicketProperties map[string]*NuggetChatbotInitInformation_BusinessContext_StringList `protobuf:"bytes,3,rep,name=ticket_properties,json=ticketProperties,proto3" json:"ticket_properties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	BotProperties    map[string]*NuggetChatbotInitInformation_BusinessContext_StringList `protobuf:"bytes,4,rep,name=bot_properties,json=botProperties,proto3" json:"bot_properties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *NuggetChatbotInitInformation_BusinessContext) Reset() {
	*x = NuggetChatbotInitInformation_BusinessContext{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_chat_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NuggetChatbotInitInformation_BusinessContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NuggetChatbotInitInformation_BusinessContext) ProtoMessage() {}

func (x *NuggetChatbotInitInformation_BusinessContext) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_chat_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NuggetChatbotInitInformation_BusinessContext.ProtoReflect.Descriptor instead.
func (*NuggetChatbotInitInformation_BusinessContext) Descriptor() ([]byte, []int) {
	return file_api_typesv2_chat_proto_rawDescGZIP(), []int{3, 1}
}

func (x *NuggetChatbotInitInformation_BusinessContext) GetChannelHandle() string {
	if x != nil {
		return x.ChannelHandle
	}
	return ""
}

func (x *NuggetChatbotInitInformation_BusinessContext) GetTicketGroupingId() string {
	if x != nil {
		return x.TicketGroupingId
	}
	return ""
}

func (x *NuggetChatbotInitInformation_BusinessContext) GetTicketProperties() map[string]*NuggetChatbotInitInformation_BusinessContext_StringList {
	if x != nil {
		return x.TicketProperties
	}
	return nil
}

func (x *NuggetChatbotInitInformation_BusinessContext) GetBotProperties() map[string]*NuggetChatbotInitInformation_BusinessContext_StringList {
	if x != nil {
		return x.BotProperties
	}
	return nil
}

type NuggetChatbotInitInformation_BusinessContext_StringList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Values []string `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *NuggetChatbotInitInformation_BusinessContext_StringList) Reset() {
	*x = NuggetChatbotInitInformation_BusinessContext_StringList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_chat_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NuggetChatbotInitInformation_BusinessContext_StringList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NuggetChatbotInitInformation_BusinessContext_StringList) ProtoMessage() {}

func (x *NuggetChatbotInitInformation_BusinessContext_StringList) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_chat_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NuggetChatbotInitInformation_BusinessContext_StringList.ProtoReflect.Descriptor instead.
func (*NuggetChatbotInitInformation_BusinessContext_StringList) Descriptor() ([]byte, []int) {
	return file_api_typesv2_chat_proto_rawDescGZIP(), []int{3, 1, 0}
}

func (x *NuggetChatbotInitInformation_BusinessContext_StringList) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

var File_api_typesv2_chat_proto protoreflect.FileDescriptor

var file_api_typesv2_chat_proto_rawDesc = []byte{
	0x0a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x68,
	0x61, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xaa, 0x03, 0x0a, 0x16, 0x43, 0x68, 0x61, 0x74,
	0x62, 0x6f, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x72, 0x0a, 0x1f, 0x6e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x68, 0x61,
	0x74, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4e, 0x75, 0x67, 0x67, 0x65, 0x74,
	0x43, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x1c, 0x6e, 0x75, 0x67, 0x67, 0x65, 0x74,
	0x43, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x7a, 0x0a, 0x22, 0x66, 0x72, 0x65, 0x73, 0x68, 0x63,
	0x68, 0x61, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x6e, 0x69, 0x74,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x46, 0x72, 0x65, 0x73, 0x63, 0x68, 0x61, 0x74, 0x43, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74,
	0x49, 0x6e, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48,
	0x00, 0x52, 0x1f, 0x66, 0x72, 0x65, 0x73, 0x68, 0x63, 0x68, 0x61, 0x74, 0x43, 0x68, 0x61, 0x74,
	0x62, 0x6f, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x7e, 0x0a, 0x23, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x66, 0x6f, 0x72, 0x74, 0x68,
	0x5f, 0x63, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x53, 0x65,
	0x6e, 0x73, 0x65, 0x66, 0x6f, 0x72, 0x74, 0x68, 0x43, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x49,
	0x6e, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00,
	0x52, 0x20, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x66, 0x6f, 0x72, 0x74, 0x68, 0x43, 0x68, 0x61, 0x74,
	0x62, 0x6f, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x20, 0x0a, 0x1e, 0x43, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x53, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x49, 0x6e, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0xda, 0x01, 0x0a, 0x20, 0x53, 0x65, 0x6e, 0x73, 0x65, 0x66, 0x6f,
	0x72, 0x74, 0x68, 0x43, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0c, 0x77, 0x65, 0x62,
	0x5f, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x77, 0x65, 0x62, 0x56, 0x69, 0x65, 0x77, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x73,
	0x68, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x49, 0x0a, 0x10,
	0x72, 0x65, 0x75, 0x73, 0x65, 0x5f, 0x63, 0x61, 0x63, 0x68, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c,
	0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0e, 0x72, 0x65, 0x75, 0x73, 0x65, 0x43, 0x61,
	0x63, 0x68, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x6f, 0x74, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x62, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x22, 0x86, 0x03, 0x0a, 0x1e, 0x46, 0x72, 0x65, 0x73, 0x63, 0x68, 0x61, 0x74, 0x43, 0x68,
	0x61, 0x74, 0x62, 0x6f, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x61, 0x70, 0x70, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x61, 0x70, 0x70, 0x4b, 0x65, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12,
	0x7b, 0x0a, 0x16, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x45, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x46, 0x72,
	0x65, 0x73, 0x63, 0x68, 0x61, 0x74, 0x43, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x49, 0x6e, 0x69,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x14, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x55, 0x73,
	0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x5f, 0x74, 0x61, 0x67, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x54, 0x61, 0x67,
	0x73, 0x1a, 0x47, 0x0a, 0x19, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xe4, 0x08, 0x0a, 0x1c, 0x4e,
	0x75, 0x67, 0x67, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x49, 0x6e, 0x69, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x7d,
	0x0a, 0x16, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4e, 0x75, 0x67,
	0x67, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x42, 0x02, 0x18, 0x01, 0x52, 0x14, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x55,
	0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x1c, 0x0a,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x75, 0x72, 0x69, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x55, 0x72, 0x69, 0x12, 0x1b,
	0x0a, 0x09, 0x68, 0x74, 0x74, 0x70, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x68, 0x74, 0x74, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x64, 0x0a, 0x10, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x4e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x62, 0x6f,
	0x74, 0x49, 0x6e, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x52, 0x0f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x1a, 0x47, 0x0a, 0x19, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x94, 0x05, 0x0a, 0x0f, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x25,
	0x0a, 0x0e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x48,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x69, 0x6e,
	0x67, 0x49, 0x64, 0x12, 0x7c, 0x0a, 0x11, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4e, 0x75, 0x67,
	0x67, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x10, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x12, 0x73, 0x0a, 0x0e, 0x62, 0x6f, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4c, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x43, 0x68,
	0x61, 0x74, 0x62, 0x6f, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x2e, 0x42, 0x6f, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x62, 0x6f, 0x74, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x1a, 0x24, 0x0a, 0x0a, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x1a, 0x89, 0x01, 0x0a,
	0x15, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x5a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74,
	0x62, 0x6f, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x86, 0x01, 0x0a, 0x12, 0x42, 0x6f, 0x74,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x5a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x44, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4e,
	0x75, 0x67, 0x67, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x49, 0x6e, 0x69, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x2a, 0xbf, 0x01, 0x0a, 0x11, 0x49, 0x6e, 0x41, 0x70, 0x70, 0x43, 0x68, 0x61, 0x74, 0x56,
	0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x21, 0x49, 0x4e, 0x5f, 0x41, 0x50,
	0x50, 0x5f, 0x43, 0x48, 0x41, 0x54, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x27,
	0x0a, 0x23, 0x49, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x43, 0x48, 0x41, 0x54, 0x5f, 0x56, 0x49,
	0x45, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x52, 0x45, 0x53, 0x48, 0x43, 0x48, 0x41,
	0x54, 0x5f, 0x53, 0x44, 0x4b, 0x10, 0x01, 0x12, 0x2c, 0x0a, 0x28, 0x49, 0x4e, 0x5f, 0x41, 0x50,
	0x50, 0x5f, 0x43, 0x48, 0x41, 0x54, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x53, 0x45, 0x4e, 0x53, 0x45, 0x46, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x57, 0x45, 0x42, 0x56,
	0x49, 0x45, 0x57, 0x10, 0x02, 0x12, 0x2c, 0x0a, 0x28, 0x49, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x5f,
	0x43, 0x48, 0x41, 0x54, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e,
	0x55, 0x47, 0x47, 0x45, 0x54, 0x5f, 0x43, 0x48, 0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x53, 0x44,
	0x4b, 0x10, 0x03, 0x2a, 0x95, 0x08, 0x0a, 0x19, 0x43, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x48, 0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x55, 0x53, 0x45,
	0x52, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x39, 0x0a, 0x35, 0x43, 0x48,
	0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x5f, 0x52, 0x45, 0x41,
	0x53, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x32, 0x0a, 0x2e, 0x43, 0x48, 0x41, 0x54, 0x42, 0x4f, 0x54,
	0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x02, 0x12, 0x37, 0x0a, 0x33, 0x43, 0x48, 0x41,
	0x54, 0x42, 0x4f, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x10, 0x03, 0x12, 0x28, 0x0a, 0x24, 0x43, 0x48, 0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x55, 0x53,
	0x45, 0x52, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x52, 0x49,
	0x53, 0x4b, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x49, 0x44, 0x10, 0x04, 0x12, 0x2c, 0x0a, 0x28,
	0x43, 0x48, 0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x41, 0x54,
	0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x46, 0x4f, 0x52,
	0x4d, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x05, 0x12, 0x31, 0x0a, 0x2d, 0x43, 0x48,
	0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f,
	0x45, 0x58, 0x50, 0x49, 0x52, 0x59, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x06, 0x12, 0x2d, 0x0a,
	0x29, 0x43, 0x48, 0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x41,
	0x54, 0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x4c, 0x45,
	0x41, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x45, 0x44, 0x10, 0x07, 0x12, 0x32, 0x0a, 0x2e,
	0x43, 0x48, 0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x41, 0x54,
	0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x43, 0x4f, 0x4d,
	0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x08,
	0x12, 0x2f, 0x0a, 0x2b, 0x43, 0x48, 0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10,
	0x09, 0x12, 0x3a, 0x0a, 0x36, 0x43, 0x48, 0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x55, 0x53, 0x45,
	0x52, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x53, 0x41, 0x56,
	0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x52, 0x45,
	0x45, 0x5a, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x0a, 0x12, 0x32, 0x0a,
	0x2e, 0x43, 0x48, 0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x41,
	0x54, 0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x41, 0x46, 0x55, 0x5f, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x10,
	0x0b, 0x12, 0x3b, 0x0a, 0x37, 0x43, 0x48, 0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x55, 0x53, 0x45,
	0x52, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4c, 0x41, 0x53,
	0x54, 0x5f, 0x43, 0x48, 0x45, 0x51, 0x55, 0x45, 0x42, 0x4f, 0x4f, 0x4b, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x0c, 0x12, 0x32,
	0x0a, 0x2e, 0x43, 0x48, 0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44,
	0x41, 0x54, 0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x43, 0x48, 0x45, 0x51, 0x55, 0x45,
	0x42, 0x4f, 0x4f, 0x4b, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59,
	0x10, 0x0d, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x48, 0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x55, 0x53,
	0x45, 0x52, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x10, 0x0e, 0x12,
	0x2e, 0x0a, 0x2a, 0x43, 0x48, 0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f,
	0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x42, 0x32, 0x42, 0x5f, 0x53,
	0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x10, 0x0f, 0x12,
	0x30, 0x0a, 0x2c, 0x43, 0x48, 0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f,
	0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x43,
	0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10,
	0x10, 0x12, 0x30, 0x0a, 0x2c, 0x43, 0x48, 0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x55, 0x53, 0x45,
	0x52, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x43, 0x55, 0x52,
	0x52, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c,
	0x53, 0x10, 0x11, 0x12, 0x30, 0x0a, 0x2c, 0x43, 0x48, 0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x55,
	0x53, 0x45, 0x52, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x49,
	0x53, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x54,
	0x49, 0x45, 0x52, 0x10, 0x12, 0x12, 0x34, 0x0a, 0x30, 0x43, 0x48, 0x41, 0x54, 0x42, 0x4f, 0x54,
	0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4c, 0x41, 0x53, 0x54, 0x5f, 0x44, 0x43, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52,
	0x59, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x13, 0x42, 0x48, 0x0a, 0x22, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x5a, 0x22, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_chat_proto_rawDescOnce sync.Once
	file_api_typesv2_chat_proto_rawDescData = file_api_typesv2_chat_proto_rawDesc
)

func file_api_typesv2_chat_proto_rawDescGZIP() []byte {
	file_api_typesv2_chat_proto_rawDescOnce.Do(func() {
		file_api_typesv2_chat_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_chat_proto_rawDescData)
	})
	return file_api_typesv2_chat_proto_rawDescData
}

var file_api_typesv2_chat_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_typesv2_chat_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_api_typesv2_chat_proto_goTypes = []interface{}{
	(InAppChatViewType)(0),                   // 0: api.typesv2.InAppChatViewType
	(ChatbotRequestedDataField)(0),           // 1: api.typesv2.ChatbotRequestedDataField
	(*ChatbotInitInformation)(nil),           // 2: api.typesv2.ChatbotInitInformation
	(*SenseforthChatbotInitInformation)(nil), // 3: api.typesv2.SenseforthChatbotInitInformation
	(*FreschatChatbotInitInformation)(nil),   // 4: api.typesv2.FreschatChatbotInitInformation
	(*NuggetChatbotInitInformation)(nil),     // 5: api.typesv2.NuggetChatbotInitInformation
	nil,                                      // 6: api.typesv2.FreschatChatbotInitInformation.CustomUserPropertiesEntry
	nil,                                      // 7: api.typesv2.NuggetChatbotInitInformation.CustomUserPropertiesEntry
	(*NuggetChatbotInitInformation_BusinessContext)(nil),            // 8: api.typesv2.NuggetChatbotInitInformation.BusinessContext
	(*NuggetChatbotInitInformation_BusinessContext_StringList)(nil), // 9: api.typesv2.NuggetChatbotInitInformation.BusinessContext.StringList
	nil,                     // 10: api.typesv2.NuggetChatbotInitInformation.BusinessContext.TicketPropertiesEntry
	nil,                     // 11: api.typesv2.NuggetChatbotInitInformation.BusinessContext.BotPropertiesEntry
	(common.BooleanEnum)(0), // 12: api.typesv2.common.BooleanEnum
}
var file_api_typesv2_chat_proto_depIdxs = []int32{
	5,  // 0: api.typesv2.ChatbotInitInformation.nugget_chatbot_init_information:type_name -> api.typesv2.NuggetChatbotInitInformation
	4,  // 1: api.typesv2.ChatbotInitInformation.freshchat_chatbot_init_information:type_name -> api.typesv2.FreschatChatbotInitInformation
	3,  // 2: api.typesv2.ChatbotInitInformation.senseforth_chatbot_init_information:type_name -> api.typesv2.SenseforthChatbotInitInformation
	12, // 3: api.typesv2.SenseforthChatbotInitInformation.reuse_cache_data:type_name -> api.typesv2.common.BooleanEnum
	6,  // 4: api.typesv2.FreschatChatbotInitInformation.custom_user_properties:type_name -> api.typesv2.FreschatChatbotInitInformation.CustomUserPropertiesEntry
	7,  // 5: api.typesv2.NuggetChatbotInitInformation.custom_user_properties:type_name -> api.typesv2.NuggetChatbotInitInformation.CustomUserPropertiesEntry
	8,  // 6: api.typesv2.NuggetChatbotInitInformation.business_context:type_name -> api.typesv2.NuggetChatbotInitInformation.BusinessContext
	10, // 7: api.typesv2.NuggetChatbotInitInformation.BusinessContext.ticket_properties:type_name -> api.typesv2.NuggetChatbotInitInformation.BusinessContext.TicketPropertiesEntry
	11, // 8: api.typesv2.NuggetChatbotInitInformation.BusinessContext.bot_properties:type_name -> api.typesv2.NuggetChatbotInitInformation.BusinessContext.BotPropertiesEntry
	9,  // 9: api.typesv2.NuggetChatbotInitInformation.BusinessContext.TicketPropertiesEntry.value:type_name -> api.typesv2.NuggetChatbotInitInformation.BusinessContext.StringList
	9,  // 10: api.typesv2.NuggetChatbotInitInformation.BusinessContext.BotPropertiesEntry.value:type_name -> api.typesv2.NuggetChatbotInitInformation.BusinessContext.StringList
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_api_typesv2_chat_proto_init() }
func file_api_typesv2_chat_proto_init() {
	if File_api_typesv2_chat_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_chat_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChatbotInitInformation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_chat_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SenseforthChatbotInitInformation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_chat_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FreschatChatbotInitInformation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_chat_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NuggetChatbotInitInformation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_chat_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NuggetChatbotInitInformation_BusinessContext); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_chat_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NuggetChatbotInitInformation_BusinessContext_StringList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_typesv2_chat_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*ChatbotInitInformation_NuggetChatbotInitInformation)(nil),
		(*ChatbotInitInformation_FreshchatChatbotInitInformation)(nil),
		(*ChatbotInitInformation_SenseforthChatbotInitInformation)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_chat_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_chat_proto_goTypes,
		DependencyIndexes: file_api_typesv2_chat_proto_depIdxs,
		EnumInfos:         file_api_typesv2_chat_proto_enumTypes,
		MessageInfos:      file_api_typesv2_chat_proto_msgTypes,
	}.Build()
	File_api_typesv2_chat_proto = out.File
	file_api_typesv2_chat_proto_rawDesc = nil
	file_api_typesv2_chat_proto_goTypes = nil
	file_api_typesv2_chat_proto_depIdxs = nil
}
