// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/ui/switch.proto

package ui

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	properties "github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-FFF--Wealth-Builder?node-id=18675-26188&t=tcKn3563muMs6vMW-4
type Switch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SwitchValue_1 *SwitchValue `protobuf:"bytes,1,opt,name=switch_value_1,json=switchValue1,proto3" json:"switch_value_1,omitempty"`
	SwitchValue_2 *SwitchValue `protobuf:"bytes,2,opt,name=switch_value_2,json=switchValue2,proto3" json:"switch_value_2,omitempty"`
	// represent bg color for switch selected by user
	SelectBgColor string `protobuf:"bytes,3,opt,name=select_bg_color,json=selectBgColor,proto3" json:"select_bg_color,omitempty"`
	// represent bg color for the entire switch component
	BgColor string `protobuf:"bytes,4,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// represent border property for switch selected by user
	SelectBorderProperty *properties.BorderProperty `protobuf:"bytes,5,opt,name=select_border_property,json=selectBorderProperty,proto3" json:"select_border_property,omitempty"`
	// represent border property for the entire switch component
	BorderProperty *properties.BorderProperty `protobuf:"bytes,6,opt,name=border_property,json=borderProperty,proto3" json:"border_property,omitempty"`
}

func (x *Switch) Reset() {
	*x = Switch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_switch_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Switch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Switch) ProtoMessage() {}

func (x *Switch) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_switch_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Switch.ProtoReflect.Descriptor instead.
func (*Switch) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_switch_proto_rawDescGZIP(), []int{0}
}

func (x *Switch) GetSwitchValue_1() *SwitchValue {
	if x != nil {
		return x.SwitchValue_1
	}
	return nil
}

func (x *Switch) GetSwitchValue_2() *SwitchValue {
	if x != nil {
		return x.SwitchValue_2
	}
	return nil
}

func (x *Switch) GetSelectBgColor() string {
	if x != nil {
		return x.SelectBgColor
	}
	return ""
}

func (x *Switch) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *Switch) GetSelectBorderProperty() *properties.BorderProperty {
	if x != nil {
		return x.SelectBorderProperty
	}
	return nil
}

func (x *Switch) GetBorderProperty() *properties.BorderProperty {
	if x != nil {
		return x.BorderProperty
	}
	return nil
}

type SwitchValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// string value of enum
	// empty string and 'unspecified' to be treated as default
	SwitchValue string `protobuf:"bytes,1,opt,name=switch_value,json=switchValue,proto3" json:"switch_value,omitempty"`
	// text if switch is not selected
	Text *common.Text `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	// text if switch is selected
	SelectedText *common.Text `protobuf:"bytes,3,opt,name=selected_text,json=selectedText,proto3" json:"selected_text,omitempty"`
	// if the switch is selected or not
	IsSelected bool `protobuf:"varint,4,opt,name=is_selected,json=isSelected,proto3" json:"is_selected,omitempty"`
	// right_component is used to show an ITC or text on the right side of each switch text (unselected state)
	RightComponent *IconTextComponent `protobuf:"bytes,7,opt,name=right_component,json=rightComponent,proto3" json:"right_component,omitempty"`
	// right_component is used to show an ITC or text on the right side of each switch text (selected state)
	SelectedRightComponent *IconTextComponent `protobuf:"bytes,8,opt,name=selected_right_component,json=selectedRightComponent,proto3" json:"selected_right_component,omitempty"`
}

func (x *SwitchValue) Reset() {
	*x = SwitchValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_ui_switch_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwitchValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwitchValue) ProtoMessage() {}

func (x *SwitchValue) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_ui_switch_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwitchValue.ProtoReflect.Descriptor instead.
func (*SwitchValue) Descriptor() ([]byte, []int) {
	return file_api_typesv2_ui_switch_proto_rawDescGZIP(), []int{1}
}

func (x *SwitchValue) GetSwitchValue() string {
	if x != nil {
		return x.SwitchValue
	}
	return ""
}

func (x *SwitchValue) GetText() *common.Text {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *SwitchValue) GetSelectedText() *common.Text {
	if x != nil {
		return x.SelectedText
	}
	return nil
}

func (x *SwitchValue) GetIsSelected() bool {
	if x != nil {
		return x.IsSelected
	}
	return false
}

func (x *SwitchValue) GetRightComponent() *IconTextComponent {
	if x != nil {
		return x.RightComponent
	}
	return nil
}

func (x *SwitchValue) GetSelectedRightComponent() *IconTextComponent {
	if x != nil {
		return x.SelectedRightComponent
	}
	return nil
}

var File_api_typesv2_ui_switch_proto protoreflect.FileDescriptor

var file_api_typesv2_ui_switch_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69,
	0x2f, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x1a, 0x1d, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f,
	0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x73, 0x64, 0x75, 0x69, 0x2f, 0x70, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x90,
	0x03, 0x0a, 0x06, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x41, 0x0a, 0x0e, 0x73, 0x77, 0x69,
	0x74, 0x63, 0x68, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c,
	0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x31, 0x12, 0x41, 0x0a, 0x0e,
	0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x32, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x0c, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x32, 0x12,
	0x26, 0x0a, 0x0f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x5f, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c,
	0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x42, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x12, 0x64, 0x0a, 0x16, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x5f, 0x62, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x75, 0x69, 0x2e, 0x73, 0x64, 0x75, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x2e, 0x42, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x52, 0x14, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x42, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x12, 0x57, 0x0a, 0x0f, 0x62, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x73, 0x64, 0x75, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69,
	0x65, 0x73, 0x2e, 0x42, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x52, 0x0e, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x22, 0xe7, 0x02, 0x0a, 0x0b, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x2c, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x04, 0x74, 0x65,
	0x78, 0x74, 0x12, 0x3d, 0x0a, 0x0d, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54,
	0x65, 0x78, 0x74, 0x52, 0x0c, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x54, 0x65, 0x78,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x12, 0x4a, 0x0a, 0x0f, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f,
	0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0e,
	0x72, 0x69, 0x67, 0x68, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x5b,
	0x0a, 0x18, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x72, 0x69, 0x67, 0x68, 0x74,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75,
	0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x52, 0x16, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x52, 0x69, 0x67,
	0x68, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x42, 0x4e, 0x0a, 0x25, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x75, 0x69, 0x5a, 0x25, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_ui_switch_proto_rawDescOnce sync.Once
	file_api_typesv2_ui_switch_proto_rawDescData = file_api_typesv2_ui_switch_proto_rawDesc
)

func file_api_typesv2_ui_switch_proto_rawDescGZIP() []byte {
	file_api_typesv2_ui_switch_proto_rawDescOnce.Do(func() {
		file_api_typesv2_ui_switch_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_ui_switch_proto_rawDescData)
	})
	return file_api_typesv2_ui_switch_proto_rawDescData
}

var file_api_typesv2_ui_switch_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_typesv2_ui_switch_proto_goTypes = []interface{}{
	(*Switch)(nil),                    // 0: api.typesv2.ui.Switch
	(*SwitchValue)(nil),               // 1: api.typesv2.ui.SwitchValue
	(*properties.BorderProperty)(nil), // 2: api.typesv2.ui.sdui.properties.BorderProperty
	(*common.Text)(nil),               // 3: api.typesv2.common.Text
	(*IconTextComponent)(nil),         // 4: api.typesv2.ui.IconTextComponent
}
var file_api_typesv2_ui_switch_proto_depIdxs = []int32{
	1, // 0: api.typesv2.ui.Switch.switch_value_1:type_name -> api.typesv2.ui.SwitchValue
	1, // 1: api.typesv2.ui.Switch.switch_value_2:type_name -> api.typesv2.ui.SwitchValue
	2, // 2: api.typesv2.ui.Switch.select_border_property:type_name -> api.typesv2.ui.sdui.properties.BorderProperty
	2, // 3: api.typesv2.ui.Switch.border_property:type_name -> api.typesv2.ui.sdui.properties.BorderProperty
	3, // 4: api.typesv2.ui.SwitchValue.text:type_name -> api.typesv2.common.Text
	3, // 5: api.typesv2.ui.SwitchValue.selected_text:type_name -> api.typesv2.common.Text
	4, // 6: api.typesv2.ui.SwitchValue.right_component:type_name -> api.typesv2.ui.IconTextComponent
	4, // 7: api.typesv2.ui.SwitchValue.selected_right_component:type_name -> api.typesv2.ui.IconTextComponent
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_api_typesv2_ui_switch_proto_init() }
func file_api_typesv2_ui_switch_proto_init() {
	if File_api_typesv2_ui_switch_proto != nil {
		return
	}
	file_api_typesv2_ui_icon_text_component_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_ui_switch_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Switch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_ui_switch_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SwitchValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_ui_switch_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_ui_switch_proto_goTypes,
		DependencyIndexes: file_api_typesv2_ui_switch_proto_depIdxs,
		MessageInfos:      file_api_typesv2_ui_switch_proto_msgTypes,
	}.Build()
	File_api_typesv2_ui_switch_proto = out.File
	file_api_typesv2_ui_switch_proto_rawDesc = nil
	file_api_typesv2_ui_switch_proto_goTypes = nil
	file_api_typesv2_ui_switch_proto_depIdxs = nil
}
