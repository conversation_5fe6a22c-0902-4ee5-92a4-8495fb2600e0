// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/ui/switch.proto

package ui

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Switch with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Switch) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Switch with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in SwitchMultiError, or nil if none found.
func (m *Switch) ValidateAll() error {
	return m.validate(true)
}

func (m *Switch) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSwitchValue_1()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SwitchValidationError{
					field:  "SwitchValue_1",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SwitchValidationError{
					field:  "SwitchValue_1",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSwitchValue_1()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SwitchValidationError{
				field:  "SwitchValue_1",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSwitchValue_2()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SwitchValidationError{
					field:  "SwitchValue_2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SwitchValidationError{
					field:  "SwitchValue_2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSwitchValue_2()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SwitchValidationError{
				field:  "SwitchValue_2",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SelectBgColor

	// no validation rules for BgColor

	if all {
		switch v := interface{}(m.GetSelectBorderProperty()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SwitchValidationError{
					field:  "SelectBorderProperty",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SwitchValidationError{
					field:  "SelectBorderProperty",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSelectBorderProperty()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SwitchValidationError{
				field:  "SelectBorderProperty",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBorderProperty()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SwitchValidationError{
					field:  "BorderProperty",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SwitchValidationError{
					field:  "BorderProperty",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBorderProperty()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SwitchValidationError{
				field:  "BorderProperty",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SwitchMultiError(errors)
	}

	return nil
}

// SwitchMultiError is an error wrapping multiple validation errors returned by
// Switch.ValidateAll() if the designated constraints aren't met.
type SwitchMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SwitchMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SwitchMultiError) AllErrors() []error { return m }

// SwitchValidationError is the validation error returned by Switch.Validate if
// the designated constraints aren't met.
type SwitchValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SwitchValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SwitchValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SwitchValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SwitchValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SwitchValidationError) ErrorName() string { return "SwitchValidationError" }

// Error satisfies the builtin error interface
func (e SwitchValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSwitch.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SwitchValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SwitchValidationError{}

// Validate checks the field values on SwitchValue with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SwitchValue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SwitchValue with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SwitchValueMultiError, or
// nil if none found.
func (m *SwitchValue) ValidateAll() error {
	return m.validate(true)
}

func (m *SwitchValue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SwitchValue

	if all {
		switch v := interface{}(m.GetText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SwitchValueValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SwitchValueValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SwitchValueValidationError{
				field:  "Text",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSelectedText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SwitchValueValidationError{
					field:  "SelectedText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SwitchValueValidationError{
					field:  "SelectedText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSelectedText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SwitchValueValidationError{
				field:  "SelectedText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsSelected

	if all {
		switch v := interface{}(m.GetRightComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SwitchValueValidationError{
					field:  "RightComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SwitchValueValidationError{
					field:  "RightComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SwitchValueValidationError{
				field:  "RightComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSelectedRightComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SwitchValueValidationError{
					field:  "SelectedRightComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SwitchValueValidationError{
					field:  "SelectedRightComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSelectedRightComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SwitchValueValidationError{
				field:  "SelectedRightComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SwitchValueMultiError(errors)
	}

	return nil
}

// SwitchValueMultiError is an error wrapping multiple validation errors
// returned by SwitchValue.ValidateAll() if the designated constraints aren't met.
type SwitchValueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SwitchValueMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SwitchValueMultiError) AllErrors() []error { return m }

// SwitchValueValidationError is the validation error returned by
// SwitchValue.Validate if the designated constraints aren't met.
type SwitchValueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SwitchValueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SwitchValueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SwitchValueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SwitchValueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SwitchValueValidationError) ErrorName() string { return "SwitchValueValidationError" }

// Error satisfies the builtin error interface
func (e SwitchValueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSwitchValue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SwitchValueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SwitchValueValidationError{}
