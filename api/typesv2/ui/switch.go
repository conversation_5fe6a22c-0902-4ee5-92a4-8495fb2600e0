// nolint
package ui

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
)

func NewSwitch() *Switch {
	return &Switch{}
}

func (x *Switch) WithSwitchValue1(switchValue *SwitchValue) *Switch {
	if switchValue != nil {
		x.SwitchValue_1 = switchValue
	}
	return x
}

func (x *Switch) WithSwitchValue2(switchValue *SwitchValue) *Switch {
	if switchValue != nil {
		x.SwitchValue_2 = switchValue
	}
	return x
}

func (x *Switch) WithSelectBgColor(color string) *Switch {
	x.SelectBgColor = color
	return x
}

func (x *Switch) WithBgColor(color string) *Switch {
	x.BgColor = color
	return x
}

func (x *Switch) WithSelectBorderProperty(selectBorderProperty *properties.BorderProperty) *Switch {
	if selectBorderProperty != nil {
		x.SelectBorderProperty = selectBorderProperty
	}
	return x
}

func (x *Switch) WithBorderProperty(borderProperty *properties.BorderProperty) *Switch {
	if borderProperty != nil {
		x.BorderProperty = borderProperty
	}
	return x
}

func NewSwitchValue() *SwitchValue {
	return &SwitchValue{}
}

func (x *SwitchValue) WithSwitchValue(switchValue string) *SwitchValue {
	x.SwitchValue = switchValue
	return x
}

func (x *SwitchValue) WithText(text *commontypes.Text) *SwitchValue {
	x.Text = text
	return x
}

func (x *SwitchValue) WithSelectedText(selectedText *commontypes.Text) *SwitchValue {
	x.SelectedText = selectedText
	return x
}

func (x *SwitchValue) MarkAsSelected() *SwitchValue {
	x.IsSelected = true
	return x
}

func (x *SwitchValue) WithRightComponent(rightComponent *IconTextComponent) *SwitchValue {
	if rightComponent != nil {
		x.RightComponent = rightComponent
	}
	return x
}

func (x *SwitchValue) WithSelectedRightComponent(selectedRightComponent *IconTextComponent) *SwitchValue {
	if selectedRightComponent != nil {
		x.SelectedRightComponent = selectedRightComponent
	}
	return x
}
