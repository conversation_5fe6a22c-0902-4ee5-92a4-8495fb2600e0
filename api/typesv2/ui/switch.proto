syntax = "proto3";

package api.typesv2.ui;

import "api/typesv2/common/text.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/sdui/properties/visual_properties.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/ui";
option java_package = "com.github.epifi.gamma.api.typesv2.ui";


// figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-FFF--Wealth-Builder?node-id=18675-26188&t=tcKn3563muMs6vMW-4
message Switch {
  SwitchValue switch_value_1 = 1;
  SwitchValue switch_value_2 = 2;
  // represent bg color for switch selected by user
  string select_bg_color = 3;
  // represent bg color for the entire switch component
  string bg_color = 4;
  // represent border property for switch selected by user
  api.typesv2.ui.sdui.properties.BorderProperty select_border_property = 5;
  // represent border property for the entire switch component
  api.typesv2.ui.sdui.properties.BorderProperty border_property = 6;
}

message SwitchValue {
  // string value of enum
  // empty string and 'unspecified' to be treated as default
  string switch_value = 1;
  // text if switch is not selected
  api.typesv2.common.Text text = 2;
  // text if switch is selected
  api.typesv2.common.Text selected_text = 3;
  // if the switch is selected or not
  bool is_selected = 4;
  // right_component is used to show an ITC or text on the right side of each switch text (unselected state)
  api.typesv2.ui.IconTextComponent right_component = 7;
  // right_component is used to show an ITC or text on the right side of each switch text (selected state)
  api.typesv2.ui.IconTextComponent selected_right_component = 8;
}
