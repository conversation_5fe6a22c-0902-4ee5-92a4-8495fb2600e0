//go:generate gen_queue_pb
syntax = "proto3";

package firefly.consumer;

import "api/auth/notification/auth_factor_update_event.proto";
import "api/aws/s3/s3.proto";
import "api/creditreportv2/notification/credit_report_download_event.proto";
import "api/firefly/enums/enums.proto";
import "api/queue/consumer_headers.proto";
import "api/vendorgateway/vendor.proto";

option go_package = "github.com/epifi/gamma/api/firefly/consumer";
option java_package = "com.github.epifi.gamma.api.firefly.consumer";

service CreditCardConsumer {
  // RPC to process eligible users file from analytics team
  rpc ProcessCreditCardEligibleUsersFile (ProcessEligibleUsersFileRequest) returns (ProcessEligibleUsersFileResponse) {}

  // RPC to process CreditCard offer csv file received from vendor
  rpc ProcessCreditCardOfferCsvFile (ProcessCreditCardOfferCsvFileRequest) returns (ProcessCreditCardOfferCsvFileResponse) {}

  // RPC to process cards sent for printing csv file received from vendor
  rpc ProcessCardsSentForPrintingCsvFile (ProcessCardsSentForPrintingCsvFileRequest) returns (ProcessCardsSentForPrintingCsvFileResponse) {}

  // RPC to process cards dispatched csv file from vendor
  rpc ProcessCardsDispatchedCsvFile (ProcessCardsDispatchedCsvFileRequest) returns (ProcessCardsDispatchedCsvFileResponse) {}

  // RPC to process credit report download event
  rpc ProcessCreditReportDownloadEvent (creditreportv2.notification.CreditReportDownloadEvent) returns (ProcessCreditReportDownloadEventResponse);

  // ProcessAuthFactorUpdateEvent will consume the AFU event and update the updated auth factors like phone number, etc. on the
  // end of credit card vendors
  rpc ProcessAuthFactorUpdateEvent (auth.AuthFactorUpdateEvent) returns (ProcessAuthFactorUpdateEventResponse) {}

  // ProcessNonFinancialNotificationEvent will consume events that are triggered when non-financial notifications are received from M2P
  // Ex: dpd notify, card status update, card deactivation reminder
  rpc ProcessNonFinancialNotificationEvent (ProcessNonFinancialNotificationEventRequest) returns (ProcessNonFinancialNotificationEventResponse) {}
}

message ProcessNonFinancialNotificationEventRequest {
  // A set of all the common attributes to be contained in a consumer response
  queue.ConsumerRequestHeader request_header = 1;

  // unique identifier for a credit card account
  string entity_id = 2;

  // Vendor from which notification is received
  vendorgateway.Vendor vendor = 3;

  enums.NonFinancialNotificationType notification_type = 4;

  // number of days user did not pay the bill since the hard due date
  int32 dpd = 5;

  // sequence number cc closure reminder notification (we get total 30 reminder notification before card closure)
  int32 closure_reminder_sequence_number = 6;
}

message ProcessNonFinancialNotificationEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessCreditReportDownloadEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessAuthFactorUpdateEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessEligibleUsersFileRequest {
  queue.ConsumerRequestHeader request_header = 1;
  // The notification message that Amazon S3 sends to publish an event is in the JSON format
  repeated aws.s3.Record records = 2 [json_name = "Records"];
}

message ProcessEligibleUsersFileResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessCreditCardOfferCsvFileRequest {
  queue.ConsumerRequestHeader request_header = 1;
  // The notification message that Amazon S3 sends to publish an event is in the JSON format
  repeated aws.s3.Record records = 2 [json_name = "Records"];
}

message ProcessCreditCardOfferCsvFileResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessCardsSentForPrintingCsvFileRequest {
  queue.ConsumerRequestHeader request_header = 1;
  // The notification message that Amazon S3 sends to publish an event is in the JSON format
  repeated aws.s3.Record records = 2 [json_name = "Records"];
}

message ProcessCardsSentForPrintingCsvFileResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessCardsDispatchedCsvFileRequest {
  queue.ConsumerRequestHeader request_header = 1;
  // The notification message that Amazon S3 sends to publish an event is in the JSON format
  repeated aws.s3.Record records = 2 [json_name = "Records"];
}

message ProcessCardsDispatchedCsvFileResponse {
  queue.ConsumerResponseHeader response_header = 1;
}
