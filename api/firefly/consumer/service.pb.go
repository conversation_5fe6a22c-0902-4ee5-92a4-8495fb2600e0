//go:generate gen_queue_pb

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/firefly/consumer/service.proto

package consumer

import (
	queue "github.com/epifi/be-common/api/queue"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	notification1 "github.com/epifi/gamma/api/auth/notification"
	s3 "github.com/epifi/gamma/api/aws/s3"
	notification "github.com/epifi/gamma/api/creditreportv2/notification"
	enums "github.com/epifi/gamma/api/firefly/enums"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProcessNonFinancialNotificationEventRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a consumer response
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// unique identifier for a credit card account
	EntityId string `protobuf:"bytes,2,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
	// Vendor from which notification is received
	Vendor           vendorgateway.Vendor               `protobuf:"varint,3,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	NotificationType enums.NonFinancialNotificationType `protobuf:"varint,4,opt,name=notification_type,json=notificationType,proto3,enum=firefly.enums.NonFinancialNotificationType" json:"notification_type,omitempty"`
	// number of days user did not pay the bill since the hard due date
	Dpd int32 `protobuf:"varint,5,opt,name=dpd,proto3" json:"dpd,omitempty"`
	// sequence number cc closure reminder notification (we get total 30 reminder notification before card closure)
	ClosureReminderSequenceNumber int32 `protobuf:"varint,6,opt,name=closure_reminder_sequence_number,json=closureReminderSequenceNumber,proto3" json:"closure_reminder_sequence_number,omitempty"`
}

func (x *ProcessNonFinancialNotificationEventRequest) Reset() {
	*x = ProcessNonFinancialNotificationEventRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_consumer_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessNonFinancialNotificationEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessNonFinancialNotificationEventRequest) ProtoMessage() {}

func (x *ProcessNonFinancialNotificationEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_consumer_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessNonFinancialNotificationEventRequest.ProtoReflect.Descriptor instead.
func (*ProcessNonFinancialNotificationEventRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_consumer_service_proto_rawDescGZIP(), []int{0}
}

func (x *ProcessNonFinancialNotificationEventRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessNonFinancialNotificationEventRequest) GetEntityId() string {
	if x != nil {
		return x.EntityId
	}
	return ""
}

func (x *ProcessNonFinancialNotificationEventRequest) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

func (x *ProcessNonFinancialNotificationEventRequest) GetNotificationType() enums.NonFinancialNotificationType {
	if x != nil {
		return x.NotificationType
	}
	return enums.NonFinancialNotificationType(0)
}

func (x *ProcessNonFinancialNotificationEventRequest) GetDpd() int32 {
	if x != nil {
		return x.Dpd
	}
	return 0
}

func (x *ProcessNonFinancialNotificationEventRequest) GetClosureReminderSequenceNumber() int32 {
	if x != nil {
		return x.ClosureReminderSequenceNumber
	}
	return 0
}

type ProcessNonFinancialNotificationEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessNonFinancialNotificationEventResponse) Reset() {
	*x = ProcessNonFinancialNotificationEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_consumer_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessNonFinancialNotificationEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessNonFinancialNotificationEventResponse) ProtoMessage() {}

func (x *ProcessNonFinancialNotificationEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_consumer_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessNonFinancialNotificationEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessNonFinancialNotificationEventResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_consumer_service_proto_rawDescGZIP(), []int{1}
}

func (x *ProcessNonFinancialNotificationEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessCreditReportDownloadEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessCreditReportDownloadEventResponse) Reset() {
	*x = ProcessCreditReportDownloadEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_consumer_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCreditReportDownloadEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCreditReportDownloadEventResponse) ProtoMessage() {}

func (x *ProcessCreditReportDownloadEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_consumer_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCreditReportDownloadEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessCreditReportDownloadEventResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_consumer_service_proto_rawDescGZIP(), []int{2}
}

func (x *ProcessCreditReportDownloadEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessAuthFactorUpdateEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessAuthFactorUpdateEventResponse) Reset() {
	*x = ProcessAuthFactorUpdateEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_consumer_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessAuthFactorUpdateEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessAuthFactorUpdateEventResponse) ProtoMessage() {}

func (x *ProcessAuthFactorUpdateEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_consumer_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessAuthFactorUpdateEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessAuthFactorUpdateEventResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_consumer_service_proto_rawDescGZIP(), []int{3}
}

func (x *ProcessAuthFactorUpdateEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessEligibleUsersFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// The notification message that Amazon S3 sends to publish an event is in the JSON format
	Records []*s3.Record `protobuf:"bytes,2,rep,name=records,json=Records,proto3" json:"records,omitempty"`
}

func (x *ProcessEligibleUsersFileRequest) Reset() {
	*x = ProcessEligibleUsersFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_consumer_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessEligibleUsersFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessEligibleUsersFileRequest) ProtoMessage() {}

func (x *ProcessEligibleUsersFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_consumer_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessEligibleUsersFileRequest.ProtoReflect.Descriptor instead.
func (*ProcessEligibleUsersFileRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_consumer_service_proto_rawDescGZIP(), []int{4}
}

func (x *ProcessEligibleUsersFileRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessEligibleUsersFileRequest) GetRecords() []*s3.Record {
	if x != nil {
		return x.Records
	}
	return nil
}

type ProcessEligibleUsersFileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessEligibleUsersFileResponse) Reset() {
	*x = ProcessEligibleUsersFileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_consumer_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessEligibleUsersFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessEligibleUsersFileResponse) ProtoMessage() {}

func (x *ProcessEligibleUsersFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_consumer_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessEligibleUsersFileResponse.ProtoReflect.Descriptor instead.
func (*ProcessEligibleUsersFileResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_consumer_service_proto_rawDescGZIP(), []int{5}
}

func (x *ProcessEligibleUsersFileResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessCreditCardOfferCsvFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// The notification message that Amazon S3 sends to publish an event is in the JSON format
	Records []*s3.Record `protobuf:"bytes,2,rep,name=records,json=Records,proto3" json:"records,omitempty"`
}

func (x *ProcessCreditCardOfferCsvFileRequest) Reset() {
	*x = ProcessCreditCardOfferCsvFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_consumer_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCreditCardOfferCsvFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCreditCardOfferCsvFileRequest) ProtoMessage() {}

func (x *ProcessCreditCardOfferCsvFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_consumer_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCreditCardOfferCsvFileRequest.ProtoReflect.Descriptor instead.
func (*ProcessCreditCardOfferCsvFileRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_consumer_service_proto_rawDescGZIP(), []int{6}
}

func (x *ProcessCreditCardOfferCsvFileRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessCreditCardOfferCsvFileRequest) GetRecords() []*s3.Record {
	if x != nil {
		return x.Records
	}
	return nil
}

type ProcessCreditCardOfferCsvFileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessCreditCardOfferCsvFileResponse) Reset() {
	*x = ProcessCreditCardOfferCsvFileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_consumer_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCreditCardOfferCsvFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCreditCardOfferCsvFileResponse) ProtoMessage() {}

func (x *ProcessCreditCardOfferCsvFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_consumer_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCreditCardOfferCsvFileResponse.ProtoReflect.Descriptor instead.
func (*ProcessCreditCardOfferCsvFileResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_consumer_service_proto_rawDescGZIP(), []int{7}
}

func (x *ProcessCreditCardOfferCsvFileResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessCardsSentForPrintingCsvFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// The notification message that Amazon S3 sends to publish an event is in the JSON format
	Records []*s3.Record `protobuf:"bytes,2,rep,name=records,json=Records,proto3" json:"records,omitempty"`
}

func (x *ProcessCardsSentForPrintingCsvFileRequest) Reset() {
	*x = ProcessCardsSentForPrintingCsvFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_consumer_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardsSentForPrintingCsvFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardsSentForPrintingCsvFileRequest) ProtoMessage() {}

func (x *ProcessCardsSentForPrintingCsvFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_consumer_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardsSentForPrintingCsvFileRequest.ProtoReflect.Descriptor instead.
func (*ProcessCardsSentForPrintingCsvFileRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_consumer_service_proto_rawDescGZIP(), []int{8}
}

func (x *ProcessCardsSentForPrintingCsvFileRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessCardsSentForPrintingCsvFileRequest) GetRecords() []*s3.Record {
	if x != nil {
		return x.Records
	}
	return nil
}

type ProcessCardsSentForPrintingCsvFileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessCardsSentForPrintingCsvFileResponse) Reset() {
	*x = ProcessCardsSentForPrintingCsvFileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_consumer_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardsSentForPrintingCsvFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardsSentForPrintingCsvFileResponse) ProtoMessage() {}

func (x *ProcessCardsSentForPrintingCsvFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_consumer_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardsSentForPrintingCsvFileResponse.ProtoReflect.Descriptor instead.
func (*ProcessCardsSentForPrintingCsvFileResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_consumer_service_proto_rawDescGZIP(), []int{9}
}

func (x *ProcessCardsSentForPrintingCsvFileResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessCardsDispatchedCsvFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// The notification message that Amazon S3 sends to publish an event is in the JSON format
	Records []*s3.Record `protobuf:"bytes,2,rep,name=records,json=Records,proto3" json:"records,omitempty"`
}

func (x *ProcessCardsDispatchedCsvFileRequest) Reset() {
	*x = ProcessCardsDispatchedCsvFileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_consumer_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardsDispatchedCsvFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardsDispatchedCsvFileRequest) ProtoMessage() {}

func (x *ProcessCardsDispatchedCsvFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_consumer_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardsDispatchedCsvFileRequest.ProtoReflect.Descriptor instead.
func (*ProcessCardsDispatchedCsvFileRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_consumer_service_proto_rawDescGZIP(), []int{10}
}

func (x *ProcessCardsDispatchedCsvFileRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessCardsDispatchedCsvFileRequest) GetRecords() []*s3.Record {
	if x != nil {
		return x.Records
	}
	return nil
}

type ProcessCardsDispatchedCsvFileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessCardsDispatchedCsvFileResponse) Reset() {
	*x = ProcessCardsDispatchedCsvFileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_consumer_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCardsDispatchedCsvFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCardsDispatchedCsvFileResponse) ProtoMessage() {}

func (x *ProcessCardsDispatchedCsvFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_consumer_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCardsDispatchedCsvFileResponse.ProtoReflect.Descriptor instead.
func (*ProcessCardsDispatchedCsvFileResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_consumer_service_proto_rawDescGZIP(), []int{11}
}

func (x *ProcessCardsDispatchedCsvFileResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

var File_api_firefly_consumer_service_proto protoreflect.FileDescriptor

var file_api_firefly_consumer_service_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x1a, 0x34, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68,
	0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x61, 0x75,
	0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x77, 0x73, 0x2f, 0x73, 0x33, 0x2f, 0x73, 0x33, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x42, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x76, 0x32, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf3, 0x02, 0x0a, 0x2b, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x4e, 0x6f, 0x6e, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52,
	0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x58, 0x0a, 0x11, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x4e, 0x6f, 0x6e, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x4e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x10, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x70, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03,
	0x64, 0x70, 0x64, 0x12, 0x47, 0x0a, 0x20, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x72,
	0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x1d, 0x63,
	0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x76, 0x0a, 0x2c,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x6f, 0x6e, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63,
	0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x22, 0x72, 0x0a, 0x28, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75,
	0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x6e, 0x0a, 0x24, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75,
	0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x90, 0x01, 0x0a, 0x1f, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x73, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x28, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x73, 0x33, 0x2e, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x07, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0x6a, 0x0a, 0x20, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x95, 0x01, 0x0a, 0x24, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x43, 0x73, 0x76, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x73, 0x33, 0x2e,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x07, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22,
	0x6f, 0x0a, 0x25, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x43, 0x73, 0x76, 0x46, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x22, 0x9a, 0x01, 0x0a, 0x29, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64,
	0x73, 0x53, 0x65, 0x6e, 0x74, 0x46, 0x6f, 0x72, 0x50, 0x72, 0x69, 0x6e, 0x74, 0x69, 0x6e, 0x67,
	0x43, 0x73, 0x76, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43,
	0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x73, 0x33, 0x2e, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x07, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0x74, 0x0a,
	0x2a, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x73, 0x53, 0x65, 0x6e,
	0x74, 0x46, 0x6f, 0x72, 0x50, 0x72, 0x69, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x73, 0x76, 0x46,
	0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x22, 0x95, 0x01, 0x0a, 0x24, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43,
	0x61, 0x72, 0x64, 0x73, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x43, 0x73,
	0x76, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x28, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x73, 0x33, 0x2e, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x07, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0x6f, 0x0a, 0x25, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x73, 0x44, 0x69, 0x73, 0x70, 0x61,
	0x74, 0x63, 0x68, 0x65, 0x64, 0x43, 0x73, 0x76, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x32, 0xac, 0x08, 0x0a,
	0x12, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x12, 0x8d, 0x01, 0x0a, 0x22, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x31, 0x2e, 0x66, 0x69, 0x72,
	0x65, 0x66, 0x6c, 0x79, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e,
	0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x92, 0x01, 0x0a, 0x1d, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x43, 0x73,
	0x76, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x36, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x43,
	0x73, 0x76, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e,
	0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x43, 0x73, 0x76, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xa1, 0x01, 0x0a, 0x22, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x73, 0x53, 0x65, 0x6e, 0x74, 0x46, 0x6f, 0x72,
	0x50, 0x72, 0x69, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x73, 0x76, 0x46, 0x69, 0x6c, 0x65, 0x12,
	0x3b, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x73, 0x53,
	0x65, 0x6e, 0x74, 0x46, 0x6f, 0x72, 0x50, 0x72, 0x69, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x73,
	0x76, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x66,
	0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x73, 0x53, 0x65, 0x6e, 0x74,
	0x46, 0x6f, 0x72, 0x50, 0x72, 0x69, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x73, 0x76, 0x46, 0x69,
	0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x92, 0x01, 0x0a,
	0x1d, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x73, 0x44, 0x69, 0x73,
	0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x43, 0x73, 0x76, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x36,
	0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x72, 0x64, 0x73, 0x44, 0x69,
	0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x43, 0x73, 0x76, 0x46, 0x69, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79,
	0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x43, 0x61, 0x72, 0x64, 0x73, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64,
	0x43, 0x73, 0x76, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x96, 0x01, 0x0a, 0x20, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x36, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x76, 0x32, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x1a, 0x3a,
	0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x75, 0x0a, 0x1c, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x1a, 0x36, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0xa7, 0x01, 0x0a, 0x24, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x6f, 0x6e,
	0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x3d, 0x2e, 0x66, 0x69, 0x72,
	0x65, 0x66, 0x6c, 0x79, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x6f, 0x6e, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61,
	0x6c, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x66, 0x69, 0x72, 0x65,
	0x66, 0x6c, 0x79, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x4e, 0x6f, 0x6e, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x5a, 0x0a, 0x2b, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_firefly_consumer_service_proto_rawDescOnce sync.Once
	file_api_firefly_consumer_service_proto_rawDescData = file_api_firefly_consumer_service_proto_rawDesc
)

func file_api_firefly_consumer_service_proto_rawDescGZIP() []byte {
	file_api_firefly_consumer_service_proto_rawDescOnce.Do(func() {
		file_api_firefly_consumer_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_firefly_consumer_service_proto_rawDescData)
	})
	return file_api_firefly_consumer_service_proto_rawDescData
}

var file_api_firefly_consumer_service_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_api_firefly_consumer_service_proto_goTypes = []interface{}{
	(*ProcessNonFinancialNotificationEventRequest)(nil),  // 0: firefly.consumer.ProcessNonFinancialNotificationEventRequest
	(*ProcessNonFinancialNotificationEventResponse)(nil), // 1: firefly.consumer.ProcessNonFinancialNotificationEventResponse
	(*ProcessCreditReportDownloadEventResponse)(nil),     // 2: firefly.consumer.ProcessCreditReportDownloadEventResponse
	(*ProcessAuthFactorUpdateEventResponse)(nil),         // 3: firefly.consumer.ProcessAuthFactorUpdateEventResponse
	(*ProcessEligibleUsersFileRequest)(nil),              // 4: firefly.consumer.ProcessEligibleUsersFileRequest
	(*ProcessEligibleUsersFileResponse)(nil),             // 5: firefly.consumer.ProcessEligibleUsersFileResponse
	(*ProcessCreditCardOfferCsvFileRequest)(nil),         // 6: firefly.consumer.ProcessCreditCardOfferCsvFileRequest
	(*ProcessCreditCardOfferCsvFileResponse)(nil),        // 7: firefly.consumer.ProcessCreditCardOfferCsvFileResponse
	(*ProcessCardsSentForPrintingCsvFileRequest)(nil),    // 8: firefly.consumer.ProcessCardsSentForPrintingCsvFileRequest
	(*ProcessCardsSentForPrintingCsvFileResponse)(nil),   // 9: firefly.consumer.ProcessCardsSentForPrintingCsvFileResponse
	(*ProcessCardsDispatchedCsvFileRequest)(nil),         // 10: firefly.consumer.ProcessCardsDispatchedCsvFileRequest
	(*ProcessCardsDispatchedCsvFileResponse)(nil),        // 11: firefly.consumer.ProcessCardsDispatchedCsvFileResponse
	(*queue.ConsumerRequestHeader)(nil),                  // 12: queue.ConsumerRequestHeader
	(vendorgateway.Vendor)(0),                            // 13: vendorgateway.Vendor
	(enums.NonFinancialNotificationType)(0),              // 14: firefly.enums.NonFinancialNotificationType
	(*queue.ConsumerResponseHeader)(nil),                 // 15: queue.ConsumerResponseHeader
	(*s3.Record)(nil),                                    // 16: aws.s3.Record
	(*notification.CreditReportDownloadEvent)(nil),       // 17: creditreportv2.notification.CreditReportDownloadEvent
	(*notification1.AuthFactorUpdateEvent)(nil),          // 18: auth.AuthFactorUpdateEvent
}
var file_api_firefly_consumer_service_proto_depIdxs = []int32{
	12, // 0: firefly.consumer.ProcessNonFinancialNotificationEventRequest.request_header:type_name -> queue.ConsumerRequestHeader
	13, // 1: firefly.consumer.ProcessNonFinancialNotificationEventRequest.vendor:type_name -> vendorgateway.Vendor
	14, // 2: firefly.consumer.ProcessNonFinancialNotificationEventRequest.notification_type:type_name -> firefly.enums.NonFinancialNotificationType
	15, // 3: firefly.consumer.ProcessNonFinancialNotificationEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	15, // 4: firefly.consumer.ProcessCreditReportDownloadEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	15, // 5: firefly.consumer.ProcessAuthFactorUpdateEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	12, // 6: firefly.consumer.ProcessEligibleUsersFileRequest.request_header:type_name -> queue.ConsumerRequestHeader
	16, // 7: firefly.consumer.ProcessEligibleUsersFileRequest.records:type_name -> aws.s3.Record
	15, // 8: firefly.consumer.ProcessEligibleUsersFileResponse.response_header:type_name -> queue.ConsumerResponseHeader
	12, // 9: firefly.consumer.ProcessCreditCardOfferCsvFileRequest.request_header:type_name -> queue.ConsumerRequestHeader
	16, // 10: firefly.consumer.ProcessCreditCardOfferCsvFileRequest.records:type_name -> aws.s3.Record
	15, // 11: firefly.consumer.ProcessCreditCardOfferCsvFileResponse.response_header:type_name -> queue.ConsumerResponseHeader
	12, // 12: firefly.consumer.ProcessCardsSentForPrintingCsvFileRequest.request_header:type_name -> queue.ConsumerRequestHeader
	16, // 13: firefly.consumer.ProcessCardsSentForPrintingCsvFileRequest.records:type_name -> aws.s3.Record
	15, // 14: firefly.consumer.ProcessCardsSentForPrintingCsvFileResponse.response_header:type_name -> queue.ConsumerResponseHeader
	12, // 15: firefly.consumer.ProcessCardsDispatchedCsvFileRequest.request_header:type_name -> queue.ConsumerRequestHeader
	16, // 16: firefly.consumer.ProcessCardsDispatchedCsvFileRequest.records:type_name -> aws.s3.Record
	15, // 17: firefly.consumer.ProcessCardsDispatchedCsvFileResponse.response_header:type_name -> queue.ConsumerResponseHeader
	4,  // 18: firefly.consumer.CreditCardConsumer.ProcessCreditCardEligibleUsersFile:input_type -> firefly.consumer.ProcessEligibleUsersFileRequest
	6,  // 19: firefly.consumer.CreditCardConsumer.ProcessCreditCardOfferCsvFile:input_type -> firefly.consumer.ProcessCreditCardOfferCsvFileRequest
	8,  // 20: firefly.consumer.CreditCardConsumer.ProcessCardsSentForPrintingCsvFile:input_type -> firefly.consumer.ProcessCardsSentForPrintingCsvFileRequest
	10, // 21: firefly.consumer.CreditCardConsumer.ProcessCardsDispatchedCsvFile:input_type -> firefly.consumer.ProcessCardsDispatchedCsvFileRequest
	17, // 22: firefly.consumer.CreditCardConsumer.ProcessCreditReportDownloadEvent:input_type -> creditreportv2.notification.CreditReportDownloadEvent
	18, // 23: firefly.consumer.CreditCardConsumer.ProcessAuthFactorUpdateEvent:input_type -> auth.AuthFactorUpdateEvent
	0,  // 24: firefly.consumer.CreditCardConsumer.ProcessNonFinancialNotificationEvent:input_type -> firefly.consumer.ProcessNonFinancialNotificationEventRequest
	5,  // 25: firefly.consumer.CreditCardConsumer.ProcessCreditCardEligibleUsersFile:output_type -> firefly.consumer.ProcessEligibleUsersFileResponse
	7,  // 26: firefly.consumer.CreditCardConsumer.ProcessCreditCardOfferCsvFile:output_type -> firefly.consumer.ProcessCreditCardOfferCsvFileResponse
	9,  // 27: firefly.consumer.CreditCardConsumer.ProcessCardsSentForPrintingCsvFile:output_type -> firefly.consumer.ProcessCardsSentForPrintingCsvFileResponse
	11, // 28: firefly.consumer.CreditCardConsumer.ProcessCardsDispatchedCsvFile:output_type -> firefly.consumer.ProcessCardsDispatchedCsvFileResponse
	2,  // 29: firefly.consumer.CreditCardConsumer.ProcessCreditReportDownloadEvent:output_type -> firefly.consumer.ProcessCreditReportDownloadEventResponse
	3,  // 30: firefly.consumer.CreditCardConsumer.ProcessAuthFactorUpdateEvent:output_type -> firefly.consumer.ProcessAuthFactorUpdateEventResponse
	1,  // 31: firefly.consumer.CreditCardConsumer.ProcessNonFinancialNotificationEvent:output_type -> firefly.consumer.ProcessNonFinancialNotificationEventResponse
	25, // [25:32] is the sub-list for method output_type
	18, // [18:25] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_api_firefly_consumer_service_proto_init() }
func file_api_firefly_consumer_service_proto_init() {
	if File_api_firefly_consumer_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_firefly_consumer_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessNonFinancialNotificationEventRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_consumer_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessNonFinancialNotificationEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_consumer_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCreditReportDownloadEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_consumer_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessAuthFactorUpdateEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_consumer_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessEligibleUsersFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_consumer_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessEligibleUsersFileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_consumer_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCreditCardOfferCsvFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_consumer_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCreditCardOfferCsvFileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_consumer_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardsSentForPrintingCsvFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_consumer_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardsSentForPrintingCsvFileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_consumer_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardsDispatchedCsvFileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_consumer_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCardsDispatchedCsvFileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_firefly_consumer_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_firefly_consumer_service_proto_goTypes,
		DependencyIndexes: file_api_firefly_consumer_service_proto_depIdxs,
		MessageInfos:      file_api_firefly_consumer_service_proto_msgTypes,
	}.Build()
	File_api_firefly_consumer_service_proto = out.File
	file_api_firefly_consumer_service_proto_rawDesc = nil
	file_api_firefly_consumer_service_proto_goTypes = nil
	file_api_firefly_consumer_service_proto_depIdxs = nil
}
