// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/firefly/comms/action_data.proto

package comms

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	comms "github.com/epifi/gamma/api/comms"
	firefly "github.com/epifi/gamma/api/firefly"
	accounting "github.com/epifi/gamma/api/firefly/accounting"
	billing "github.com/epifi/gamma/api/firefly/billing"
	enums "github.com/epifi/gamma/api/firefly/enums"
	lms "github.com/epifi/gamma/api/firefly/lms"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ActionData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Data:
	//
	//	*ActionData_SettingsUpdateActionData
	//	*ActionData_TxnActionData
	//	*ActionData_CardRequestActionData
	//	*ActionData_BillingActionData
	//	*ActionData_CardTrackingActionData
	//	*ActionData_CardClosureActionData
	//	*ActionData_SecuredCardActionData
	//	*ActionData_EmiActionData
	//	*ActionData_WebEligibilityFlowActionData
	//	*ActionData_CardCreationActionData
	//	*ActionData_CardClosureReminderActionData
	Data isActionData_Data `protobuf_oneof:"Data"`
}

func (x *ActionData) Reset() {
	*x = ActionData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_comms_action_data_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActionData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActionData) ProtoMessage() {}

func (x *ActionData) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_comms_action_data_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActionData.ProtoReflect.Descriptor instead.
func (*ActionData) Descriptor() ([]byte, []int) {
	return file_api_firefly_comms_action_data_proto_rawDescGZIP(), []int{0}
}

func (m *ActionData) GetData() isActionData_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *ActionData) GetSettingsUpdateActionData() *SettingsUpdateActionData {
	if x, ok := x.GetData().(*ActionData_SettingsUpdateActionData); ok {
		return x.SettingsUpdateActionData
	}
	return nil
}

func (x *ActionData) GetTxnActionData() *TxnActionData {
	if x, ok := x.GetData().(*ActionData_TxnActionData); ok {
		return x.TxnActionData
	}
	return nil
}

func (x *ActionData) GetCardRequestActionData() *CardRequestActionData {
	if x, ok := x.GetData().(*ActionData_CardRequestActionData); ok {
		return x.CardRequestActionData
	}
	return nil
}

func (x *ActionData) GetBillingActionData() *BillingActionData {
	if x, ok := x.GetData().(*ActionData_BillingActionData); ok {
		return x.BillingActionData
	}
	return nil
}

func (x *ActionData) GetCardTrackingActionData() *CardTrackingActionData {
	if x, ok := x.GetData().(*ActionData_CardTrackingActionData); ok {
		return x.CardTrackingActionData
	}
	return nil
}

func (x *ActionData) GetCardClosureActionData() *CardClosureActionData {
	if x, ok := x.GetData().(*ActionData_CardClosureActionData); ok {
		return x.CardClosureActionData
	}
	return nil
}

func (x *ActionData) GetSecuredCardActionData() *SecuredCardCommsActionData {
	if x, ok := x.GetData().(*ActionData_SecuredCardActionData); ok {
		return x.SecuredCardActionData
	}
	return nil
}

func (x *ActionData) GetEmiActionData() *EmiActionData {
	if x, ok := x.GetData().(*ActionData_EmiActionData); ok {
		return x.EmiActionData
	}
	return nil
}

func (x *ActionData) GetWebEligibilityFlowActionData() *WebEligibilityFlowActionData {
	if x, ok := x.GetData().(*ActionData_WebEligibilityFlowActionData); ok {
		return x.WebEligibilityFlowActionData
	}
	return nil
}

func (x *ActionData) GetCardCreationActionData() *CardCreationActionData {
	if x, ok := x.GetData().(*ActionData_CardCreationActionData); ok {
		return x.CardCreationActionData
	}
	return nil
}

func (x *ActionData) GetCardClosureReminderActionData() *CardClosureReminderActionData {
	if x, ok := x.GetData().(*ActionData_CardClosureReminderActionData); ok {
		return x.CardClosureReminderActionData
	}
	return nil
}

type isActionData_Data interface {
	isActionData_Data()
}

type ActionData_SettingsUpdateActionData struct {
	SettingsUpdateActionData *SettingsUpdateActionData `protobuf:"bytes,1,opt,name=settings_update_action_data,json=settingsUpdateActionData,proto3,oneof"`
}

type ActionData_TxnActionData struct {
	TxnActionData *TxnActionData `protobuf:"bytes,2,opt,name=txn_action_data,json=txnActionData,proto3,oneof"`
}

type ActionData_CardRequestActionData struct {
	CardRequestActionData *CardRequestActionData `protobuf:"bytes,3,opt,name=card_request_action_data,json=cardRequestActionData,proto3,oneof"`
}

type ActionData_BillingActionData struct {
	BillingActionData *BillingActionData `protobuf:"bytes,4,opt,name=billing_action_data,json=billingActionData,proto3,oneof"`
}

type ActionData_CardTrackingActionData struct {
	CardTrackingActionData *CardTrackingActionData `protobuf:"bytes,5,opt,name=card_tracking_action_data,json=cardTrackingActionData,proto3,oneof"`
}

type ActionData_CardClosureActionData struct {
	CardClosureActionData *CardClosureActionData `protobuf:"bytes,6,opt,name=card_closure_action_data,json=cardClosureActionData,proto3,oneof"`
}

type ActionData_SecuredCardActionData struct {
	SecuredCardActionData *SecuredCardCommsActionData `protobuf:"bytes,7,opt,name=secured_card_action_data,json=securedCardActionData,proto3,oneof"`
}

type ActionData_EmiActionData struct {
	EmiActionData *EmiActionData `protobuf:"bytes,8,opt,name=emi_action_data,json=emiActionData,proto3,oneof"`
}

type ActionData_WebEligibilityFlowActionData struct {
	WebEligibilityFlowActionData *WebEligibilityFlowActionData `protobuf:"bytes,9,opt,name=web_eligibility_flow_action_data,json=webEligibilityFlowActionData,proto3,oneof"`
}

type ActionData_CardCreationActionData struct {
	CardCreationActionData *CardCreationActionData `protobuf:"bytes,10,opt,name=card_creation_action_data,json=cardCreationActionData,proto3,oneof"`
}

type ActionData_CardClosureReminderActionData struct {
	CardClosureReminderActionData *CardClosureReminderActionData `protobuf:"bytes,11,opt,name=card_closure_reminder_action_data,json=cardClosureReminderActionData,proto3,oneof"`
}

func (*ActionData_SettingsUpdateActionData) isActionData_Data() {}

func (*ActionData_TxnActionData) isActionData_Data() {}

func (*ActionData_CardRequestActionData) isActionData_Data() {}

func (*ActionData_BillingActionData) isActionData_Data() {}

func (*ActionData_CardTrackingActionData) isActionData_Data() {}

func (*ActionData_CardClosureActionData) isActionData_Data() {}

func (*ActionData_SecuredCardActionData) isActionData_Data() {}

func (*ActionData_EmiActionData) isActionData_Data() {}

func (*ActionData_WebEligibilityFlowActionData) isActionData_Data() {}

func (*ActionData_CardCreationActionData) isActionData_Data() {}

func (*ActionData_CardClosureReminderActionData) isActionData_Data() {}

type CardClosureReminderActionData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreditCard            *firefly.CreditCard `protobuf:"bytes,1,opt,name=credit_card,json=creditCard,proto3" json:"credit_card,omitempty"`
	DaysLeftBeforeClosure int32               `protobuf:"varint,2,opt,name=days_left_before_closure,json=daysLeftBeforeClosure,proto3" json:"days_left_before_closure,omitempty"`
}

func (x *CardClosureReminderActionData) Reset() {
	*x = CardClosureReminderActionData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_comms_action_data_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardClosureReminderActionData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardClosureReminderActionData) ProtoMessage() {}

func (x *CardClosureReminderActionData) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_comms_action_data_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardClosureReminderActionData.ProtoReflect.Descriptor instead.
func (*CardClosureReminderActionData) Descriptor() ([]byte, []int) {
	return file_api_firefly_comms_action_data_proto_rawDescGZIP(), []int{1}
}

func (x *CardClosureReminderActionData) GetCreditCard() *firefly.CreditCard {
	if x != nil {
		return x.CreditCard
	}
	return nil
}

func (x *CardClosureReminderActionData) GetDaysLeftBeforeClosure() int32 {
	if x != nil {
		return x.DaysLeftBeforeClosure
	}
	return 0
}

type CardCreationActionData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId     string               `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	CardProgram *typesv2.CardProgram `protobuf:"bytes,2,opt,name=card_program,json=cardProgram,proto3" json:"card_program,omitempty"`
}

func (x *CardCreationActionData) Reset() {
	*x = CardCreationActionData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_comms_action_data_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardCreationActionData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardCreationActionData) ProtoMessage() {}

func (x *CardCreationActionData) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_comms_action_data_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardCreationActionData.ProtoReflect.Descriptor instead.
func (*CardCreationActionData) Descriptor() ([]byte, []int) {
	return file_api_firefly_comms_action_data_proto_rawDescGZIP(), []int{2}
}

func (x *CardCreationActionData) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *CardCreationActionData) GetCardProgram() *typesv2.CardProgram {
	if x != nil {
		return x.CardProgram
	}
	return nil
}

type TxnActionData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId                           string                                        `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	CardTransactionWithAdditionalInfo *accounting.CardTransactionWithAdditionalInfo `protobuf:"bytes,2,opt,name=card_transaction_with_additional_info,json=cardTransactionWithAdditionalInfo,proto3" json:"card_transaction_with_additional_info,omitempty"`
	FirstName                         string                                        `protobuf:"bytes,3,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	AvailableAccountBalance           *money.Money                                  `protobuf:"bytes,4,opt,name=available_account_balance,json=availableAccountBalance,proto3" json:"available_account_balance,omitempty"`
	AccountNumber                     string                                        `protobuf:"bytes,5,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	CurrencyExchangeRate              float32                                       `protobuf:"fixed32,6,opt,name=currency_exchange_rate,json=currencyExchangeRate,proto3" json:"currency_exchange_rate,omitempty"`
	MarkUpFee                         *money.Money                                  `protobuf:"bytes,7,opt,name=mark_up_fee,json=markUpFee,proto3" json:"mark_up_fee,omitempty"`
	CreditCardLastFourDigits          string                                        `protobuf:"bytes,8,opt,name=credit_card_last_four_digits,json=creditCardLastFourDigits,proto3" json:"credit_card_last_four_digits,omitempty"`
	MaskedCardNumber                  string                                        `protobuf:"bytes,9,opt,name=masked_card_number,json=maskedCardNumber,proto3" json:"masked_card_number,omitempty"`
	// For comms that have to be sent once in a billing window, we will populate this field.
	// example comms include: successful repayment in a billing window.
	BillWindow *billing.BillWindow `protobuf:"bytes,10,opt,name=bill_window,json=billWindow,proto3" json:"bill_window,omitempty"`
}

func (x *TxnActionData) Reset() {
	*x = TxnActionData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_comms_action_data_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TxnActionData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxnActionData) ProtoMessage() {}

func (x *TxnActionData) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_comms_action_data_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxnActionData.ProtoReflect.Descriptor instead.
func (*TxnActionData) Descriptor() ([]byte, []int) {
	return file_api_firefly_comms_action_data_proto_rawDescGZIP(), []int{3}
}

func (x *TxnActionData) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *TxnActionData) GetCardTransactionWithAdditionalInfo() *accounting.CardTransactionWithAdditionalInfo {
	if x != nil {
		return x.CardTransactionWithAdditionalInfo
	}
	return nil
}

func (x *TxnActionData) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *TxnActionData) GetAvailableAccountBalance() *money.Money {
	if x != nil {
		return x.AvailableAccountBalance
	}
	return nil
}

func (x *TxnActionData) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *TxnActionData) GetCurrencyExchangeRate() float32 {
	if x != nil {
		return x.CurrencyExchangeRate
	}
	return 0
}

func (x *TxnActionData) GetMarkUpFee() *money.Money {
	if x != nil {
		return x.MarkUpFee
	}
	return nil
}

func (x *TxnActionData) GetCreditCardLastFourDigits() string {
	if x != nil {
		return x.CreditCardLastFourDigits
	}
	return ""
}

func (x *TxnActionData) GetMaskedCardNumber() string {
	if x != nil {
		return x.MaskedCardNumber
	}
	return ""
}

func (x *TxnActionData) GetBillWindow() *billing.BillWindow {
	if x != nil {
		return x.BillWindow
	}
	return nil
}

type SettingsUpdateActionData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardControlType          enums.CardControlType `protobuf:"varint,1,opt,name=card_control_type,json=cardControlType,proto3,enum=firefly.enums.CardControlType" json:"card_control_type,omitempty"`
	IsEnabled                bool                  `protobuf:"varint,2,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
	ActorId                  string                `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	CreditCardLastFourDigits string                `protobuf:"bytes,4,opt,name=credit_card_last_four_digits,json=creditCardLastFourDigits,proto3" json:"credit_card_last_four_digits,omitempty"`
	FirstName                string                `protobuf:"bytes,5,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	CreditCardType           string                `protobuf:"bytes,6,opt,name=credit_card_type,json=creditCardType,proto3" json:"credit_card_type,omitempty"`
}

func (x *SettingsUpdateActionData) Reset() {
	*x = SettingsUpdateActionData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_comms_action_data_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SettingsUpdateActionData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SettingsUpdateActionData) ProtoMessage() {}

func (x *SettingsUpdateActionData) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_comms_action_data_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SettingsUpdateActionData.ProtoReflect.Descriptor instead.
func (*SettingsUpdateActionData) Descriptor() ([]byte, []int) {
	return file_api_firefly_comms_action_data_proto_rawDescGZIP(), []int{4}
}

func (x *SettingsUpdateActionData) GetCardControlType() enums.CardControlType {
	if x != nil {
		return x.CardControlType
	}
	return enums.CardControlType(0)
}

func (x *SettingsUpdateActionData) GetIsEnabled() bool {
	if x != nil {
		return x.IsEnabled
	}
	return false
}

func (x *SettingsUpdateActionData) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *SettingsUpdateActionData) GetCreditCardLastFourDigits() string {
	if x != nil {
		return x.CreditCardLastFourDigits
	}
	return ""
}

func (x *SettingsUpdateActionData) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *SettingsUpdateActionData) GetCreditCardType() string {
	if x != nil {
		return x.CreditCardType
	}
	return ""
}

type CardRequestActionData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardRequest *firefly.CardRequest `protobuf:"bytes,1,opt,name=card_request,json=cardRequest,proto3" json:"card_request,omitempty"`
}

func (x *CardRequestActionData) Reset() {
	*x = CardRequestActionData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_comms_action_data_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardRequestActionData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardRequestActionData) ProtoMessage() {}

func (x *CardRequestActionData) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_comms_action_data_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardRequestActionData.ProtoReflect.Descriptor instead.
func (*CardRequestActionData) Descriptor() ([]byte, []int) {
	return file_api_firefly_comms_action_data_proto_rawDescGZIP(), []int{5}
}

func (x *CardRequestActionData) GetCardRequest() *firefly.CardRequest {
	if x != nil {
		return x.CardRequest
	}
	return nil
}

type BillingActionData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PaymentReminderNotificationType enums.PaymentReminderNotificationType     `protobuf:"varint,1,opt,name=payment_reminder_notification_type,json=paymentReminderNotificationType,proto3,enum=firefly.enums.PaymentReminderNotificationType" json:"payment_reminder_notification_type,omitempty"`
	CardStatementNotificationType   enums.CreditCardStatementNotificationType `protobuf:"varint,2,opt,name=card_statement_notification_type,json=cardStatementNotificationType,proto3,enum=firefly.enums.CreditCardStatementNotificationType" json:"card_statement_notification_type,omitempty"`
	ActorId                         string                                    `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	UnpaidDueAmount                 *money.Money                              `protobuf:"bytes,4,opt,name=unpaid_due_amount,json=unpaidDueAmount,proto3" json:"unpaid_due_amount,omitempty"`
	PaymentDueInDays                int32                                     `protobuf:"varint,5,opt,name=payment_due_in_days,json=paymentDueInDays,proto3" json:"payment_due_in_days,omitempty"`
	// since bill generation card request does not depend on a credit card id
	// we need to pass it here for comms rules to use it.
	CreditCardId    string                 `protobuf:"bytes,6,opt,name=credit_card_id,json=creditCardId,proto3" json:"credit_card_id,omitempty"`
	MinDueAmount    *money.Money           `protobuf:"bytes,7,opt,name=min_due_amount,json=minDueAmount,proto3" json:"min_due_amount,omitempty"`
	TotalDueAmount  *money.Money           `protobuf:"bytes,8,opt,name=total_due_amount,json=totalDueAmount,proto3" json:"total_due_amount,omitempty"`
	RepaymentDoneAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=repayment_done_at,json=repaymentDoneAt,proto3" json:"repayment_done_at,omitempty"`
	DueDate         *date.Date             `protobuf:"bytes,13,opt,name=due_date,json=dueDate,proto3" json:"due_date,omitempty"`
	// For comms that have to be sent once in a billing window, we will populate this field.
	// example comms include: successful repayment in a billing window.
	BillWindow            *billing.BillWindow    `protobuf:"bytes,14,opt,name=bill_window,json=billWindow,proto3" json:"bill_window,omitempty"`
	NotificationTimestamp *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=notification_timestamp,json=notificationTimestamp,proto3" json:"notification_timestamp,omitempty"`
	Medium                comms.Medium           `protobuf:"varint,16,opt,name=medium,proto3,enum=comms.Medium" json:"medium,omitempty"`
}

func (x *BillingActionData) Reset() {
	*x = BillingActionData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_comms_action_data_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BillingActionData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillingActionData) ProtoMessage() {}

func (x *BillingActionData) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_comms_action_data_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillingActionData.ProtoReflect.Descriptor instead.
func (*BillingActionData) Descriptor() ([]byte, []int) {
	return file_api_firefly_comms_action_data_proto_rawDescGZIP(), []int{6}
}

func (x *BillingActionData) GetPaymentReminderNotificationType() enums.PaymentReminderNotificationType {
	if x != nil {
		return x.PaymentReminderNotificationType
	}
	return enums.PaymentReminderNotificationType(0)
}

func (x *BillingActionData) GetCardStatementNotificationType() enums.CreditCardStatementNotificationType {
	if x != nil {
		return x.CardStatementNotificationType
	}
	return enums.CreditCardStatementNotificationType(0)
}

func (x *BillingActionData) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *BillingActionData) GetUnpaidDueAmount() *money.Money {
	if x != nil {
		return x.UnpaidDueAmount
	}
	return nil
}

func (x *BillingActionData) GetPaymentDueInDays() int32 {
	if x != nil {
		return x.PaymentDueInDays
	}
	return 0
}

func (x *BillingActionData) GetCreditCardId() string {
	if x != nil {
		return x.CreditCardId
	}
	return ""
}

func (x *BillingActionData) GetMinDueAmount() *money.Money {
	if x != nil {
		return x.MinDueAmount
	}
	return nil
}

func (x *BillingActionData) GetTotalDueAmount() *money.Money {
	if x != nil {
		return x.TotalDueAmount
	}
	return nil
}

func (x *BillingActionData) GetRepaymentDoneAt() *timestamppb.Timestamp {
	if x != nil {
		return x.RepaymentDoneAt
	}
	return nil
}

func (x *BillingActionData) GetDueDate() *date.Date {
	if x != nil {
		return x.DueDate
	}
	return nil
}

func (x *BillingActionData) GetBillWindow() *billing.BillWindow {
	if x != nil {
		return x.BillWindow
	}
	return nil
}

func (x *BillingActionData) GetNotificationTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.NotificationTimestamp
	}
	return nil
}

func (x *BillingActionData) GetMedium() comms.Medium {
	if x != nil {
		return x.Medium
	}
	return comms.Medium(0)
}

type CardTrackingActionData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId           string                          `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	CardId            string                          `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	Carrier           string                          `protobuf:"bytes,3,opt,name=carrier,proto3" json:"carrier,omitempty"`
	Awb               string                          `protobuf:"bytes,4,opt,name=awb,proto3" json:"awb,omitempty"`
	DeliveryStatus    enums.CardRequestStageSubStatus `protobuf:"varint,5,opt,name=delivery_status,json=deliveryStatus,proto3,enum=firefly.enums.CardRequestStageSubStatus" json:"delivery_status,omitempty"`
	CardHolderName    *common.Name                    `protobuf:"bytes,6,opt,name=card_holder_name,json=cardHolderName,proto3" json:"card_holder_name,omitempty"`
	CardLastFourDigit string                          `protobuf:"bytes,7,opt,name=card_last_four_digit,json=cardLastFourDigit,proto3" json:"card_last_four_digit,omitempty"`
}

func (x *CardTrackingActionData) Reset() {
	*x = CardTrackingActionData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_comms_action_data_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardTrackingActionData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardTrackingActionData) ProtoMessage() {}

func (x *CardTrackingActionData) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_comms_action_data_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardTrackingActionData.ProtoReflect.Descriptor instead.
func (*CardTrackingActionData) Descriptor() ([]byte, []int) {
	return file_api_firefly_comms_action_data_proto_rawDescGZIP(), []int{7}
}

func (x *CardTrackingActionData) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *CardTrackingActionData) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *CardTrackingActionData) GetCarrier() string {
	if x != nil {
		return x.Carrier
	}
	return ""
}

func (x *CardTrackingActionData) GetAwb() string {
	if x != nil {
		return x.Awb
	}
	return ""
}

func (x *CardTrackingActionData) GetDeliveryStatus() enums.CardRequestStageSubStatus {
	if x != nil {
		return x.DeliveryStatus
	}
	return enums.CardRequestStageSubStatus(0)
}

func (x *CardTrackingActionData) GetCardHolderName() *common.Name {
	if x != nil {
		return x.CardHolderName
	}
	return nil
}

func (x *CardTrackingActionData) GetCardLastFourDigit() string {
	if x != nil {
		return x.CardLastFourDigit
	}
	return ""
}

type EmiActionData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId                    string           `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	CustomerName               *common.Name     `protobuf:"bytes,2,opt,name=customer_name,json=customerName,proto3" json:"customer_name,omitempty"`
	MerchantName               string           `protobuf:"bytes,3,opt,name=merchant_name,json=merchantName,proto3" json:"merchant_name,omitempty"`
	DueAmount                  *money.Money     `protobuf:"bytes,4,opt,name=due_amount,json=dueAmount,proto3" json:"due_amount,omitempty"`
	PreClosureFee              *money.Money     `protobuf:"bytes,5,opt,name=pre_closure_fee,json=preClosureFee,proto3" json:"pre_closure_fee,omitempty"`
	EmiCommsType               lms.EmiCommsType `protobuf:"varint,6,opt,name=emi_comms_type,json=emiCommsType,proto3,enum=firefly.lms.EmiCommsType" json:"emi_comms_type,omitempty"`
	TransactionAmount          *money.Money     `protobuf:"bytes,7,opt,name=transaction_amount,json=transactionAmount,proto3" json:"transaction_amount,omitempty"`
	EmiAmount                  *money.Money     `protobuf:"bytes,8,opt,name=emi_amount,json=emiAmount,proto3" json:"emi_amount,omitempty"`
	CreationDate               *date.Date       `protobuf:"bytes,9,opt,name=creation_date,json=creationDate,proto3" json:"creation_date,omitempty"`
	CancellationDate           *date.Date       `protobuf:"bytes,10,opt,name=cancellation_date,json=cancellationDate,proto3" json:"cancellation_date,omitempty"`
	TransactionDate            *date.Date       `protobuf:"bytes,11,opt,name=transaction_date,json=transactionDate,proto3" json:"transaction_date,omitempty"`
	Tenure                     int64            `protobuf:"varint,12,opt,name=tenure,proto3" json:"tenure,omitempty"`
	InterestRate               float64          `protobuf:"fixed64,13,opt,name=interest_rate,json=interestRate,proto3" json:"interest_rate,omitempty"`
	ProcessingFees             *money.Money     `protobuf:"bytes,14,opt,name=processing_fees,json=processingFees,proto3" json:"processing_fees,omitempty"`
	NextBillGenDate            *date.Date       `protobuf:"bytes,15,opt,name=next_bill_gen_date,json=nextBillGenDate,proto3" json:"next_bill_gen_date,omitempty"`
	CardLastFourDigits         string           `protobuf:"bytes,16,opt,name=card_last_four_digits,json=cardLastFourDigits,proto3" json:"card_last_four_digits,omitempty"`
	InterestCharges            *money.Money     `protobuf:"bytes,17,opt,name=interest_charges,json=interestCharges,proto3" json:"interest_charges,omitempty"`
	AmountAddedToNextStatement *money.Money     `protobuf:"bytes,18,opt,name=amount_added_to_next_statement,json=amountAddedToNextStatement,proto3" json:"amount_added_to_next_statement,omitempty"`
	CompletionDate             *date.Date       `protobuf:"bytes,19,opt,name=completion_date,json=completionDate,proto3" json:"completion_date,omitempty"`
}

func (x *EmiActionData) Reset() {
	*x = EmiActionData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_comms_action_data_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmiActionData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmiActionData) ProtoMessage() {}

func (x *EmiActionData) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_comms_action_data_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmiActionData.ProtoReflect.Descriptor instead.
func (*EmiActionData) Descriptor() ([]byte, []int) {
	return file_api_firefly_comms_action_data_proto_rawDescGZIP(), []int{8}
}

func (x *EmiActionData) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *EmiActionData) GetCustomerName() *common.Name {
	if x != nil {
		return x.CustomerName
	}
	return nil
}

func (x *EmiActionData) GetMerchantName() string {
	if x != nil {
		return x.MerchantName
	}
	return ""
}

func (x *EmiActionData) GetDueAmount() *money.Money {
	if x != nil {
		return x.DueAmount
	}
	return nil
}

func (x *EmiActionData) GetPreClosureFee() *money.Money {
	if x != nil {
		return x.PreClosureFee
	}
	return nil
}

func (x *EmiActionData) GetEmiCommsType() lms.EmiCommsType {
	if x != nil {
		return x.EmiCommsType
	}
	return lms.EmiCommsType(0)
}

func (x *EmiActionData) GetTransactionAmount() *money.Money {
	if x != nil {
		return x.TransactionAmount
	}
	return nil
}

func (x *EmiActionData) GetEmiAmount() *money.Money {
	if x != nil {
		return x.EmiAmount
	}
	return nil
}

func (x *EmiActionData) GetCreationDate() *date.Date {
	if x != nil {
		return x.CreationDate
	}
	return nil
}

func (x *EmiActionData) GetCancellationDate() *date.Date {
	if x != nil {
		return x.CancellationDate
	}
	return nil
}

func (x *EmiActionData) GetTransactionDate() *date.Date {
	if x != nil {
		return x.TransactionDate
	}
	return nil
}

func (x *EmiActionData) GetTenure() int64 {
	if x != nil {
		return x.Tenure
	}
	return 0
}

func (x *EmiActionData) GetInterestRate() float64 {
	if x != nil {
		return x.InterestRate
	}
	return 0
}

func (x *EmiActionData) GetProcessingFees() *money.Money {
	if x != nil {
		return x.ProcessingFees
	}
	return nil
}

func (x *EmiActionData) GetNextBillGenDate() *date.Date {
	if x != nil {
		return x.NextBillGenDate
	}
	return nil
}

func (x *EmiActionData) GetCardLastFourDigits() string {
	if x != nil {
		return x.CardLastFourDigits
	}
	return ""
}

func (x *EmiActionData) GetInterestCharges() *money.Money {
	if x != nil {
		return x.InterestCharges
	}
	return nil
}

func (x *EmiActionData) GetAmountAddedToNextStatement() *money.Money {
	if x != nil {
		return x.AmountAddedToNextStatement
	}
	return nil
}

func (x *EmiActionData) GetCompletionDate() *date.Date {
	if x != nil {
		return x.CompletionDate
	}
	return nil
}

type CardClosureActionData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreditCard *firefly.CreditCard `protobuf:"bytes,1,opt,name=credit_card,json=creditCard,proto3" json:"credit_card,omitempty"`
}

func (x *CardClosureActionData) Reset() {
	*x = CardClosureActionData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_comms_action_data_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardClosureActionData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardClosureActionData) ProtoMessage() {}

func (x *CardClosureActionData) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_comms_action_data_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardClosureActionData.ProtoReflect.Descriptor instead.
func (*CardClosureActionData) Descriptor() ([]byte, []int) {
	return file_api_firefly_comms_action_data_proto_rawDescGZIP(), []int{9}
}

func (x *CardClosureActionData) GetCreditCard() *firefly.CreditCard {
	if x != nil {
		return x.CreditCard
	}
	return nil
}

type SecuredCardCommsActionData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DepositId            string                     `protobuf:"bytes,1,opt,name=deposit_id,json=depositId,proto3" json:"deposit_id,omitempty"`
	ActorId              string                     `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	CardRequestStageName enums.CardRequestStageName `protobuf:"varint,3,opt,name=card_request_stage_name,json=cardRequestStageName,proto3,enum=firefly.enums.CardRequestStageName" json:"card_request_stage_name,omitempty"`
}

func (x *SecuredCardCommsActionData) Reset() {
	*x = SecuredCardCommsActionData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_comms_action_data_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecuredCardCommsActionData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecuredCardCommsActionData) ProtoMessage() {}

func (x *SecuredCardCommsActionData) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_comms_action_data_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecuredCardCommsActionData.ProtoReflect.Descriptor instead.
func (*SecuredCardCommsActionData) Descriptor() ([]byte, []int) {
	return file_api_firefly_comms_action_data_proto_rawDescGZIP(), []int{10}
}

func (x *SecuredCardCommsActionData) GetDepositId() string {
	if x != nil {
		return x.DepositId
	}
	return ""
}

func (x *SecuredCardCommsActionData) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *SecuredCardCommsActionData) GetCardRequestStageName() enums.CardRequestStageName {
	if x != nil {
		return x.CardRequestStageName
	}
	return enums.CardRequestStageName(0)
}

type WebEligibilityFlowActionData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId           string            `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	AppDownloadUrl    string            `protobuf:"bytes,2,opt,name=app_download_url,json=appDownloadUrl,proto3" json:"app_download_url,omitempty"`
	RedirectionUrl    string            `protobuf:"bytes,3,opt,name=redirection_url,json=redirectionUrl,proto3" json:"redirection_url,omitempty"`
	AdditionalDetails *AdditionalDetail `protobuf:"bytes,4,opt,name=additional_details,json=additionalDetails,proto3" json:"additional_details,omitempty"`
}

func (x *WebEligibilityFlowActionData) Reset() {
	*x = WebEligibilityFlowActionData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_comms_action_data_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebEligibilityFlowActionData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebEligibilityFlowActionData) ProtoMessage() {}

func (x *WebEligibilityFlowActionData) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_comms_action_data_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebEligibilityFlowActionData.ProtoReflect.Descriptor instead.
func (*WebEligibilityFlowActionData) Descriptor() ([]byte, []int) {
	return file_api_firefly_comms_action_data_proto_rawDescGZIP(), []int{11}
}

func (x *WebEligibilityFlowActionData) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *WebEligibilityFlowActionData) GetAppDownloadUrl() string {
	if x != nil {
		return x.AppDownloadUrl
	}
	return ""
}

func (x *WebEligibilityFlowActionData) GetRedirectionUrl() string {
	if x != nil {
		return x.RedirectionUrl
	}
	return ""
}

func (x *WebEligibilityFlowActionData) GetAdditionalDetails() *AdditionalDetail {
	if x != nil {
		return x.AdditionalDetails
	}
	return nil
}

type AdditionalDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Medium comms.Medium `protobuf:"varint,1,opt,name=medium,proto3,enum=comms.Medium" json:"medium,omitempty"`
	// Types that are assignable to CommType:
	//
	//	*AdditionalDetail_EmailType
	//	*AdditionalDetail_SmsType
	//	*AdditionalDetail_WhatsappType
	CommType isAdditionalDetail_CommType `protobuf_oneof:"comm_type"`
}

func (x *AdditionalDetail) Reset() {
	*x = AdditionalDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_comms_action_data_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdditionalDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdditionalDetail) ProtoMessage() {}

func (x *AdditionalDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_comms_action_data_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdditionalDetail.ProtoReflect.Descriptor instead.
func (*AdditionalDetail) Descriptor() ([]byte, []int) {
	return file_api_firefly_comms_action_data_proto_rawDescGZIP(), []int{12}
}

func (x *AdditionalDetail) GetMedium() comms.Medium {
	if x != nil {
		return x.Medium
	}
	return comms.Medium(0)
}

func (m *AdditionalDetail) GetCommType() isAdditionalDetail_CommType {
	if m != nil {
		return m.CommType
	}
	return nil
}

func (x *AdditionalDetail) GetEmailType() comms.EmailType {
	if x, ok := x.GetCommType().(*AdditionalDetail_EmailType); ok {
		return x.EmailType
	}
	return comms.EmailType(0)
}

func (x *AdditionalDetail) GetSmsType() comms.SmsType {
	if x, ok := x.GetCommType().(*AdditionalDetail_SmsType); ok {
		return x.SmsType
	}
	return comms.SmsType(0)
}

func (x *AdditionalDetail) GetWhatsappType() comms.WhatsappType {
	if x, ok := x.GetCommType().(*AdditionalDetail_WhatsappType); ok {
		return x.WhatsappType
	}
	return comms.WhatsappType(0)
}

type isAdditionalDetail_CommType interface {
	isAdditionalDetail_CommType()
}

type AdditionalDetail_EmailType struct {
	EmailType comms.EmailType `protobuf:"varint,2,opt,name=email_type,json=emailType,proto3,enum=comms.EmailType,oneof"`
}

type AdditionalDetail_SmsType struct {
	SmsType comms.SmsType `protobuf:"varint,3,opt,name=sms_type,json=smsType,proto3,enum=comms.SmsType,oneof"`
}

type AdditionalDetail_WhatsappType struct {
	WhatsappType comms.WhatsappType `protobuf:"varint,4,opt,name=whatsapp_type,json=whatsappType,proto3,enum=comms.WhatsappType,oneof"`
}

func (*AdditionalDetail_EmailType) isAdditionalDetail_CommType() {}

func (*AdditionalDetail_SmsType) isAdditionalDetail_CommType() {}

func (*AdditionalDetail_WhatsappType) isAdditionalDetail_CommType() {}

var File_api_firefly_comms_action_data_proto protoreflect.FileDescriptor

var file_api_firefly_comms_action_data_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x73, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x73, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2f,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61, 0x70, 0x69,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2f, 0x73, 0x6d, 0x73, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x73, 0x2f, 0x77, 0x68, 0x61, 0x74, 0x73, 0x61, 0x70, 0x70, 0x5f, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70,
	0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f,
	0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x2f, 0x6c, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc3, 0x08, 0x0a, 0x0a, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x68, 0x0a, 0x1b, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x5f,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x61, 0x48, 0x00, 0x52, 0x18, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x46, 0x0a,
	0x0f, 0x74, 0x78, 0x6e, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e, 0x54, 0x78, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0d, 0x74, 0x78, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x5f, 0x0a, 0x18, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52,
	0x15, 0x63, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x52, 0x0a, 0x13, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x73, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x11, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x62, 0x0a, 0x19, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e, 0x43, 0x61,
	0x72, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x16, 0x63, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x5f,
	0x0a, 0x18, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73,
	0x2e, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x15, 0x63, 0x61, 0x72, 0x64, 0x43, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x64, 0x0a, 0x18, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x64, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x73, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6d,
	0x6d, 0x73, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x15,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x46, 0x0a, 0x0f, 0x65, 0x6d, 0x69, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e, 0x45,
	0x6d, 0x69, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0d,
	0x65, 0x6d, 0x69, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x75, 0x0a,
	0x20, 0x77, 0x65, 0x62, 0x5f, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e, 0x57, 0x65, 0x62, 0x45, 0x6c, 0x69, 0x67, 0x69,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x46, 0x6c, 0x6f, 0x77, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x1c, 0x77, 0x65, 0x62, 0x45, 0x6c, 0x69, 0x67, 0x69,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x46, 0x6c, 0x6f, 0x77, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x62, 0x0a, 0x19, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00,
	0x52, 0x16, 0x63, 0x61, 0x72, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x78, 0x0a, 0x21, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65,
	0x72, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52,
	0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74,
	0x61, 0x48, 0x00, 0x52, 0x1d, 0x63, 0x61, 0x72, 0x64, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x42, 0x06, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x22, 0x8e, 0x01, 0x0a, 0x1d, 0x43,
	0x61, 0x72, 0x64, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64,
	0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x34, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x12, 0x37, 0x0a, 0x18, 0x64, 0x61, 0x79, 0x73, 0x5f, 0x6c, 0x65, 0x66, 0x74, 0x5f,
	0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x64, 0x61, 0x79, 0x73, 0x4c, 0x65, 0x66, 0x74, 0x42, 0x65,
	0x66, 0x6f, 0x72, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x22, 0x70, 0x0a, 0x16, 0x43,
	0x61, 0x72, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x3b, 0x0a, 0x0c, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x52, 0x0b, 0x63, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x22, 0xe0, 0x04,
	0x0a, 0x0d, 0x54, 0x78, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x87, 0x01, 0x0a, 0x25, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x77, 0x69, 0x74, 0x68, 0x5f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x66, 0x69, 0x72,
	0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57,
	0x69, 0x74, 0x68, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x21, 0x63, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x4e, 0x0a, 0x19, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x17, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x16, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f,
	0x72, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x14, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x79, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65,
	0x12, 0x32, 0x0a, 0x0b, 0x6d, 0x61, 0x72, 0x6b, 0x5f, 0x75, 0x70, 0x5f, 0x66, 0x65, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x6d, 0x61, 0x72, 0x6b, 0x55,
	0x70, 0x46, 0x65, 0x65, 0x12, 0x3e, 0x0a, 0x1c, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x66, 0x6f, 0x75, 0x72, 0x5f, 0x64, 0x69,
	0x67, 0x69, 0x74, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x63, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x61, 0x73, 0x74, 0x46, 0x6f, 0x75, 0x72, 0x44, 0x69,
	0x67, 0x69, 0x74, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x5f, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0b, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f,
	0x77, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x57, 0x69,
	0x6e, 0x64, 0x6f, 0x77, 0x52, 0x0a, 0x62, 0x69, 0x6c, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77,
	0x22, 0xa9, 0x02, 0x0a, 0x18, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4a, 0x0a,
	0x11, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x63, 0x61, 0x72, 0x64, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69,
	0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x1c, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x66, 0x6f, 0x75, 0x72, 0x5f, 0x64, 0x69, 0x67,
	0x69, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x63, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x61, 0x73, 0x74, 0x46, 0x6f, 0x75, 0x72, 0x44, 0x69, 0x67,
	0x69, 0x74, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x22, 0x50, 0x0a, 0x15,
	0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x37, 0x0a, 0x0c, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x0b, 0x63, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xe3,
	0x06, 0x0a, 0x11, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x7b, 0x0a, 0x22, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2e, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x1f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65,
	0x72, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x7b, 0x0a, 0x20, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x1d, 0x63, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x11, 0x75, 0x6e, 0x70,
	0x61, 0x69, 0x64, 0x5f, 0x64, 0x75, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x69, 0x64,
	0x44, 0x75, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x13, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x75, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x64, 0x61, 0x79, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44,
	0x75, 0x65, 0x49, 0x6e, 0x44, 0x61, 0x79, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x38,
	0x0a, 0x0e, 0x6d, 0x69, 0x6e, 0x5f, 0x64, 0x75, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0c, 0x6d, 0x69, 0x6e, 0x44,
	0x75, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3c, 0x0a, 0x10, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x64, 0x75, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x44, 0x75, 0x65,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x46, 0x0a, 0x11, 0x72, 0x65, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x64, 0x6f, 0x6e, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f, 0x72,
	0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x6f, 0x6e, 0x65, 0x41, 0x74, 0x12, 0x2c,
	0x0a, 0x08, 0x64, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x52, 0x07, 0x64, 0x75, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3c, 0x0a, 0x0b,
	0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x62, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x52, 0x0a,
	0x62, 0x69, 0x6c, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x12, 0x51, 0x0a, 0x16, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x15, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x25, 0x0a,
	0x06, 0x6d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x52, 0x06, 0x6d, 0x65,
	0x64, 0x69, 0x75, 0x6d, 0x22, 0xc0, 0x02, 0x0a, 0x16, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72,
	0x64, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x12, 0x10, 0x0a,
	0x03, 0x61, 0x77, 0x62, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x61, 0x77, 0x62, 0x12,
	0x51, 0x0a, 0x0f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x0e, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x42, 0x0a, 0x10, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x65,
	0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0e, 0x63, 0x61, 0x72, 0x64, 0x48, 0x6f, 0x6c, 0x64,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x14, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x66, 0x6f, 0x75, 0x72, 0x5f, 0x64, 0x69, 0x67, 0x69, 0x74, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x63, 0x61, 0x72, 0x64, 0x4c, 0x61, 0x73, 0x74, 0x46, 0x6f,
	0x75, 0x72, 0x44, 0x69, 0x67, 0x69, 0x74, 0x22, 0xaa, 0x08, 0x0a, 0x0d, 0x45, 0x6d, 0x69, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x31, 0x0a, 0x0a, 0x64, 0x75, 0x65, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x09, 0x64, 0x75, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3a, 0x0a, 0x0f, 0x70,
	0x72, 0x65, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x43, 0x6c, 0x6f,
	0x73, 0x75, 0x72, 0x65, 0x46, 0x65, 0x65, 0x12, 0x3f, 0x0a, 0x0e, 0x65, 0x6d, 0x69, 0x5f, 0x63,
	0x6f, 0x6d, 0x6d, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x19, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x6c, 0x6d, 0x73, 0x2e, 0x45, 0x6d,
	0x69, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x65, 0x6d, 0x69, 0x43,
	0x6f, 0x6d, 0x6d, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x41, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x31, 0x0a, 0x0a, 0x65,
	0x6d, 0x69, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x09, 0x65, 0x6d, 0x69, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x36,
	0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3e, 0x0a, 0x11, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x65, 0x52, 0x10, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3c, 0x0a, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x52, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x52, 0x61, 0x74,
	0x65, 0x12, 0x3b, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f,
	0x66, 0x65, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x46, 0x65, 0x65, 0x73, 0x12, 0x3e,
	0x0a, 0x12, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x67, 0x65, 0x6e, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0f, 0x6e,
	0x65, 0x78, 0x74, 0x42, 0x69, 0x6c, 0x6c, 0x47, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x31,
	0x0a, 0x15, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x66, 0x6f, 0x75, 0x72,
	0x5f, 0x64, 0x69, 0x67, 0x69, 0x74, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x63,
	0x61, 0x72, 0x64, 0x4c, 0x61, 0x73, 0x74, 0x46, 0x6f, 0x75, 0x72, 0x44, 0x69, 0x67, 0x69, 0x74,
	0x73, 0x12, 0x3d, 0x0a, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73,
	0x12, 0x56, 0x0a, 0x1e, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x65, 0x64,
	0x5f, 0x74, 0x6f, 0x5f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x1a, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x65, 0x64, 0x54, 0x6f, 0x4e, 0x65, 0x78, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3a, 0x0a, 0x0f, 0x63, 0x6f, 0x6d, 0x70,
	0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x61, 0x74, 0x65, 0x22, 0x4d, 0x0a, 0x15, 0x43, 0x61, 0x72, 0x64, 0x43, 0x6c, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x34, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x22, 0xb2, 0x01, 0x0a, 0x1a, 0x53, 0x65, 0x63, 0x75, 0x72, 0x65, 0x64, 0x43,
	0x61, 0x72, 0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x49,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x5a, 0x0a, 0x17,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61,
	0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e,
	0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x52, 0x14, 0x63, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xdc, 0x01, 0x0a, 0x1c, 0x57, 0x65, 0x62,
	0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x46, 0x6c, 0x6f, 0x77, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x5f, 0x64, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x61, 0x70, 0x70, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x27,
	0x0a, 0x0f, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x4e, 0x0a, 0x12, 0x61, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x73, 0x2e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x11, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xe2, 0x01, 0x0a, 0x10, 0x41, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x25, 0x0a, 0x06,
	0x6d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x73, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x52, 0x06, 0x6d, 0x65, 0x64,
	0x69, 0x75, 0x6d, 0x12, 0x31, 0x0a, 0x0a, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00, 0x52, 0x09, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x08, 0x73, 0x6d, 0x73, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73,
	0x2e, 0x53, 0x6d, 0x73, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00, 0x52, 0x07, 0x73, 0x6d, 0x73, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x3a, 0x0a, 0x0d, 0x77, 0x68, 0x61, 0x74, 0x73, 0x61, 0x70, 0x70, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x73, 0x2e, 0x57, 0x68, 0x61, 0x74, 0x73, 0x61, 0x70, 0x70, 0x54, 0x79, 0x70, 0x65, 0x48,
	0x00, 0x52, 0x0c, 0x77, 0x68, 0x61, 0x74, 0x73, 0x61, 0x70, 0x70, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x0b, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x54, 0x0a, 0x28,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_firefly_comms_action_data_proto_rawDescOnce sync.Once
	file_api_firefly_comms_action_data_proto_rawDescData = file_api_firefly_comms_action_data_proto_rawDesc
)

func file_api_firefly_comms_action_data_proto_rawDescGZIP() []byte {
	file_api_firefly_comms_action_data_proto_rawDescOnce.Do(func() {
		file_api_firefly_comms_action_data_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_firefly_comms_action_data_proto_rawDescData)
	})
	return file_api_firefly_comms_action_data_proto_rawDescData
}

var file_api_firefly_comms_action_data_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_api_firefly_comms_action_data_proto_goTypes = []interface{}{
	(*ActionData)(nil),                                   // 0: firefly.comms.ActionData
	(*CardClosureReminderActionData)(nil),                // 1: firefly.comms.CardClosureReminderActionData
	(*CardCreationActionData)(nil),                       // 2: firefly.comms.CardCreationActionData
	(*TxnActionData)(nil),                                // 3: firefly.comms.TxnActionData
	(*SettingsUpdateActionData)(nil),                     // 4: firefly.comms.SettingsUpdateActionData
	(*CardRequestActionData)(nil),                        // 5: firefly.comms.CardRequestActionData
	(*BillingActionData)(nil),                            // 6: firefly.comms.BillingActionData
	(*CardTrackingActionData)(nil),                       // 7: firefly.comms.CardTrackingActionData
	(*EmiActionData)(nil),                                // 8: firefly.comms.EmiActionData
	(*CardClosureActionData)(nil),                        // 9: firefly.comms.CardClosureActionData
	(*SecuredCardCommsActionData)(nil),                   // 10: firefly.comms.SecuredCardCommsActionData
	(*WebEligibilityFlowActionData)(nil),                 // 11: firefly.comms.WebEligibilityFlowActionData
	(*AdditionalDetail)(nil),                             // 12: firefly.comms.AdditionalDetail
	(*firefly.CreditCard)(nil),                           // 13: firefly.CreditCard
	(*typesv2.CardProgram)(nil),                          // 14: api.typesv2.CardProgram
	(*accounting.CardTransactionWithAdditionalInfo)(nil), // 15: firefly.accounting.CardTransactionWithAdditionalInfo
	(*money.Money)(nil),                                  // 16: google.type.Money
	(*billing.BillWindow)(nil),                           // 17: firefly.billing.BillWindow
	(enums.CardControlType)(0),                           // 18: firefly.enums.CardControlType
	(*firefly.CardRequest)(nil),                          // 19: firefly.CardRequest
	(enums.PaymentReminderNotificationType)(0),           // 20: firefly.enums.PaymentReminderNotificationType
	(enums.CreditCardStatementNotificationType)(0),       // 21: firefly.enums.CreditCardStatementNotificationType
	(*timestamppb.Timestamp)(nil),                        // 22: google.protobuf.Timestamp
	(*date.Date)(nil),                                    // 23: google.type.Date
	(comms.Medium)(0),                                    // 24: comms.Medium
	(enums.CardRequestStageSubStatus)(0),                 // 25: firefly.enums.CardRequestStageSubStatus
	(*common.Name)(nil),                                  // 26: api.typesv2.common.Name
	(lms.EmiCommsType)(0),                                // 27: firefly.lms.EmiCommsType
	(enums.CardRequestStageName)(0),                      // 28: firefly.enums.CardRequestStageName
	(comms.EmailType)(0),                                 // 29: comms.EmailType
	(comms.SmsType)(0),                                   // 30: comms.SmsType
	(comms.WhatsappType)(0),                              // 31: comms.WhatsappType
}
var file_api_firefly_comms_action_data_proto_depIdxs = []int32{
	4,  // 0: firefly.comms.ActionData.settings_update_action_data:type_name -> firefly.comms.SettingsUpdateActionData
	3,  // 1: firefly.comms.ActionData.txn_action_data:type_name -> firefly.comms.TxnActionData
	5,  // 2: firefly.comms.ActionData.card_request_action_data:type_name -> firefly.comms.CardRequestActionData
	6,  // 3: firefly.comms.ActionData.billing_action_data:type_name -> firefly.comms.BillingActionData
	7,  // 4: firefly.comms.ActionData.card_tracking_action_data:type_name -> firefly.comms.CardTrackingActionData
	9,  // 5: firefly.comms.ActionData.card_closure_action_data:type_name -> firefly.comms.CardClosureActionData
	10, // 6: firefly.comms.ActionData.secured_card_action_data:type_name -> firefly.comms.SecuredCardCommsActionData
	8,  // 7: firefly.comms.ActionData.emi_action_data:type_name -> firefly.comms.EmiActionData
	11, // 8: firefly.comms.ActionData.web_eligibility_flow_action_data:type_name -> firefly.comms.WebEligibilityFlowActionData
	2,  // 9: firefly.comms.ActionData.card_creation_action_data:type_name -> firefly.comms.CardCreationActionData
	1,  // 10: firefly.comms.ActionData.card_closure_reminder_action_data:type_name -> firefly.comms.CardClosureReminderActionData
	13, // 11: firefly.comms.CardClosureReminderActionData.credit_card:type_name -> firefly.CreditCard
	14, // 12: firefly.comms.CardCreationActionData.card_program:type_name -> api.typesv2.CardProgram
	15, // 13: firefly.comms.TxnActionData.card_transaction_with_additional_info:type_name -> firefly.accounting.CardTransactionWithAdditionalInfo
	16, // 14: firefly.comms.TxnActionData.available_account_balance:type_name -> google.type.Money
	16, // 15: firefly.comms.TxnActionData.mark_up_fee:type_name -> google.type.Money
	17, // 16: firefly.comms.TxnActionData.bill_window:type_name -> firefly.billing.BillWindow
	18, // 17: firefly.comms.SettingsUpdateActionData.card_control_type:type_name -> firefly.enums.CardControlType
	19, // 18: firefly.comms.CardRequestActionData.card_request:type_name -> firefly.CardRequest
	20, // 19: firefly.comms.BillingActionData.payment_reminder_notification_type:type_name -> firefly.enums.PaymentReminderNotificationType
	21, // 20: firefly.comms.BillingActionData.card_statement_notification_type:type_name -> firefly.enums.CreditCardStatementNotificationType
	16, // 21: firefly.comms.BillingActionData.unpaid_due_amount:type_name -> google.type.Money
	16, // 22: firefly.comms.BillingActionData.min_due_amount:type_name -> google.type.Money
	16, // 23: firefly.comms.BillingActionData.total_due_amount:type_name -> google.type.Money
	22, // 24: firefly.comms.BillingActionData.repayment_done_at:type_name -> google.protobuf.Timestamp
	23, // 25: firefly.comms.BillingActionData.due_date:type_name -> google.type.Date
	17, // 26: firefly.comms.BillingActionData.bill_window:type_name -> firefly.billing.BillWindow
	22, // 27: firefly.comms.BillingActionData.notification_timestamp:type_name -> google.protobuf.Timestamp
	24, // 28: firefly.comms.BillingActionData.medium:type_name -> comms.Medium
	25, // 29: firefly.comms.CardTrackingActionData.delivery_status:type_name -> firefly.enums.CardRequestStageSubStatus
	26, // 30: firefly.comms.CardTrackingActionData.card_holder_name:type_name -> api.typesv2.common.Name
	26, // 31: firefly.comms.EmiActionData.customer_name:type_name -> api.typesv2.common.Name
	16, // 32: firefly.comms.EmiActionData.due_amount:type_name -> google.type.Money
	16, // 33: firefly.comms.EmiActionData.pre_closure_fee:type_name -> google.type.Money
	27, // 34: firefly.comms.EmiActionData.emi_comms_type:type_name -> firefly.lms.EmiCommsType
	16, // 35: firefly.comms.EmiActionData.transaction_amount:type_name -> google.type.Money
	16, // 36: firefly.comms.EmiActionData.emi_amount:type_name -> google.type.Money
	23, // 37: firefly.comms.EmiActionData.creation_date:type_name -> google.type.Date
	23, // 38: firefly.comms.EmiActionData.cancellation_date:type_name -> google.type.Date
	23, // 39: firefly.comms.EmiActionData.transaction_date:type_name -> google.type.Date
	16, // 40: firefly.comms.EmiActionData.processing_fees:type_name -> google.type.Money
	23, // 41: firefly.comms.EmiActionData.next_bill_gen_date:type_name -> google.type.Date
	16, // 42: firefly.comms.EmiActionData.interest_charges:type_name -> google.type.Money
	16, // 43: firefly.comms.EmiActionData.amount_added_to_next_statement:type_name -> google.type.Money
	23, // 44: firefly.comms.EmiActionData.completion_date:type_name -> google.type.Date
	13, // 45: firefly.comms.CardClosureActionData.credit_card:type_name -> firefly.CreditCard
	28, // 46: firefly.comms.SecuredCardCommsActionData.card_request_stage_name:type_name -> firefly.enums.CardRequestStageName
	12, // 47: firefly.comms.WebEligibilityFlowActionData.additional_details:type_name -> firefly.comms.AdditionalDetail
	24, // 48: firefly.comms.AdditionalDetail.medium:type_name -> comms.Medium
	29, // 49: firefly.comms.AdditionalDetail.email_type:type_name -> comms.EmailType
	30, // 50: firefly.comms.AdditionalDetail.sms_type:type_name -> comms.SmsType
	31, // 51: firefly.comms.AdditionalDetail.whatsapp_type:type_name -> comms.WhatsappType
	52, // [52:52] is the sub-list for method output_type
	52, // [52:52] is the sub-list for method input_type
	52, // [52:52] is the sub-list for extension type_name
	52, // [52:52] is the sub-list for extension extendee
	0,  // [0:52] is the sub-list for field type_name
}

func init() { file_api_firefly_comms_action_data_proto_init() }
func file_api_firefly_comms_action_data_proto_init() {
	if File_api_firefly_comms_action_data_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_firefly_comms_action_data_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActionData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_comms_action_data_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardClosureReminderActionData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_comms_action_data_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardCreationActionData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_comms_action_data_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TxnActionData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_comms_action_data_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SettingsUpdateActionData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_comms_action_data_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardRequestActionData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_comms_action_data_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BillingActionData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_comms_action_data_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardTrackingActionData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_comms_action_data_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmiActionData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_comms_action_data_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardClosureActionData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_comms_action_data_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecuredCardCommsActionData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_comms_action_data_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebEligibilityFlowActionData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_comms_action_data_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdditionalDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_firefly_comms_action_data_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*ActionData_SettingsUpdateActionData)(nil),
		(*ActionData_TxnActionData)(nil),
		(*ActionData_CardRequestActionData)(nil),
		(*ActionData_BillingActionData)(nil),
		(*ActionData_CardTrackingActionData)(nil),
		(*ActionData_CardClosureActionData)(nil),
		(*ActionData_SecuredCardActionData)(nil),
		(*ActionData_EmiActionData)(nil),
		(*ActionData_WebEligibilityFlowActionData)(nil),
		(*ActionData_CardCreationActionData)(nil),
		(*ActionData_CardClosureReminderActionData)(nil),
	}
	file_api_firefly_comms_action_data_proto_msgTypes[12].OneofWrappers = []interface{}{
		(*AdditionalDetail_EmailType)(nil),
		(*AdditionalDetail_SmsType)(nil),
		(*AdditionalDetail_WhatsappType)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_firefly_comms_action_data_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_firefly_comms_action_data_proto_goTypes,
		DependencyIndexes: file_api_firefly_comms_action_data_proto_depIdxs,
		MessageInfos:      file_api_firefly_comms_action_data_proto_msgTypes,
	}.Build()
	File_api_firefly_comms_action_data_proto = out.File
	file_api_firefly_comms_action_data_proto_rawDesc = nil
	file_api_firefly_comms_action_data_proto_goTypes = nil
	file_api_firefly_comms_action_data_proto_depIdxs = nil
}
