package comms

import (
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
)

func (x *ActionData_TxnActionData) GetActorId() string {
	if x == nil {
		return ""
	}
	return x.TxnActionData.GetActorId()
}

func (x *ActionData_TxnActionData) GetExternalMessageId() string {
	if x == nil {
		return ""
	}
	return x.TxnActionData.GetCardTransactionWithAdditionalInfo().GetTransaction().GetId()
}

func (x *ActionData_SettingsUpdateActionData) GetActorId() string {
	if x == nil {
		return ""
	}
	return x.SettingsUpdateActionData.GetActorId()
}

func (x *ActionData_SettingsUpdateActionData) GetExternalMessageId() string {
	if x == nil {
		return ""
	}
	return x.SettingsUpdateActionData.GetActorId() + "_" + x.SettingsUpdateActionData.GetCardControlType().String()
}

func (x *ActionData_CardRequestActionData) GetActorId() string {
	if x == nil {
		return ""
	}
	return x.CardRequestActionData.GetCardRequest().GetActorId()
}

func (x *ActionData_CardRequestActionData) GetExternalMessageId() string {
	if x == nil {
		return ""
	}
	return x.CardRequestActionData.GetCardRequest().GetOrchestrationId()
}

func (x *ActionData_BillingActionData) GetActorId() string {
	if x == nil {
		return ""
	}
	return x.BillingActionData.GetActorId()
}

func (x *ActionData_BillingActionData) GetExternalMessageId() string {
	if x == nil {
		return ""
	}
	eventType := ""
	if x.BillingActionData.GetCardStatementNotificationType() != ffEnumsPb.CreditCardStatementNotificationType_CREDIT_CARD_STATEMENT_NOTIFICATION_TYPE_UNSPECIFIED {
		eventType = x.BillingActionData.GetCardStatementNotificationType().String()
	} else {
		eventType = x.BillingActionData.GetPaymentReminderNotificationType().String()
	}
	return x.BillingActionData.GetActorId() + "_" + eventType
}

func (x *ActionData_CardTrackingActionData) GetActorId() string {
	if x == nil {
		return ""
	}
	return x.CardTrackingActionData.GetActorId()
}

func (x *ActionData_CardTrackingActionData) GetExternalMessageId() string {
	if x == nil {
		return ""
	}
	return x.CardTrackingActionData.GetActorId() + "_" + x.CardTrackingActionData.GetAwb()
}

func (x *ActionData_CardClosureActionData) GetActorId() string {
	if x == nil {
		return ""
	}
	return x.CardClosureActionData.GetCreditCard().GetActorId()
}

func (x *ActionData_CardClosureActionData) GetExternalMessageId() string {
	if x == nil {
		return ""
	}
	return x.CardClosureActionData.GetCreditCard().GetId()
}

func (x *ActionData_SecuredCardActionData) GetActorId() string {
	if x == nil {
		return ""
	}
	return x.SecuredCardActionData.GetActorId()
}

func (x *ActionData_SecuredCardActionData) GetExternalMessageId() string {
	if x == nil {
		return ""
	}
	return x.SecuredCardActionData.GetActorId() + "_" + x.SecuredCardActionData.GetDepositId()
}

func (a *ActionData_EmiActionData) GetActorId() string {
	if a == nil {
		return ""
	}
	return a.EmiActionData.GetActorId()
}

func (x *ActionData_EmiActionData) GetExternalMessageId() string {
	if x == nil {
		return ""
	}
	return x.EmiActionData.GetActorId() + "_" + x.EmiActionData.GetEmiCommsType().String()
}

func (x *ActionData_WebEligibilityFlowActionData) GetActorId() string {
	if x == nil {
		return ""
	}
	return x.WebEligibilityFlowActionData.GetActorId()
}

func (x *ActionData_WebEligibilityFlowActionData) GetExternalMessageId() string {
	if x == nil {
		return ""
	}
	return x.WebEligibilityFlowActionData.GetActorId() + "_WebDropOffComm"
}

func (x *ActionData_CardCreationActionData) GetActorId() string {
	if x == nil {
		return ""
	}
	return x.CardCreationActionData.GetActorId()
}

func (x *ActionData_CardCreationActionData) GetExternalMessageId() string {
	if x == nil {
		return ""
	}
	return x.CardCreationActionData.GetActorId() + "| welcome Email |" + x.CardCreationActionData.GetCardProgram().GetCardProgramType().String()
}

func (x *ActionData_CardClosureReminderActionData) GetActorId() string {
	if x == nil {
		return ""
	}
	return x.CardClosureReminderActionData.GetCreditCard().GetActorId()
}

func (x *ActionData_CardClosureReminderActionData) GetExternalMessageId() string {
	if x == nil {
		return ""
	}
	return x.CardClosureReminderActionData.GetCreditCard().GetId() + "_CardClosureReminderComm"
}
