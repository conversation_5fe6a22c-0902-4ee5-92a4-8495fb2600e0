// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/firefly/comms/action_data.proto

package comms

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	comms "github.com/epifi/gamma/api/comms"

	enums "github.com/epifi/gamma/api/firefly/enums"

	lms "github.com/epifi/gamma/api/firefly/lms"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = comms.Medium(0)

	_ = enums.CardControlType(0)

	_ = lms.EmiCommsType(0)
)

// Validate checks the field values on ActionData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ActionData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActionData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ActionDataMultiError, or
// nil if none found.
func (m *ActionData) ValidateAll() error {
	return m.validate(true)
}

func (m *ActionData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Data.(type) {
	case *ActionData_SettingsUpdateActionData:
		if v == nil {
			err := ActionDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSettingsUpdateActionData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "SettingsUpdateActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "SettingsUpdateActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSettingsUpdateActionData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActionDataValidationError{
					field:  "SettingsUpdateActionData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActionData_TxnActionData:
		if v == nil {
			err := ActionDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTxnActionData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "TxnActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "TxnActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTxnActionData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActionDataValidationError{
					field:  "TxnActionData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActionData_CardRequestActionData:
		if v == nil {
			err := ActionDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCardRequestActionData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "CardRequestActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "CardRequestActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCardRequestActionData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActionDataValidationError{
					field:  "CardRequestActionData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActionData_BillingActionData:
		if v == nil {
			err := ActionDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBillingActionData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "BillingActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "BillingActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBillingActionData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActionDataValidationError{
					field:  "BillingActionData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActionData_CardTrackingActionData:
		if v == nil {
			err := ActionDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCardTrackingActionData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "CardTrackingActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "CardTrackingActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCardTrackingActionData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActionDataValidationError{
					field:  "CardTrackingActionData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActionData_CardClosureActionData:
		if v == nil {
			err := ActionDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCardClosureActionData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "CardClosureActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "CardClosureActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCardClosureActionData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActionDataValidationError{
					field:  "CardClosureActionData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActionData_SecuredCardActionData:
		if v == nil {
			err := ActionDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSecuredCardActionData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "SecuredCardActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "SecuredCardActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSecuredCardActionData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActionDataValidationError{
					field:  "SecuredCardActionData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActionData_EmiActionData:
		if v == nil {
			err := ActionDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEmiActionData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "EmiActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "EmiActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEmiActionData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActionDataValidationError{
					field:  "EmiActionData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActionData_WebEligibilityFlowActionData:
		if v == nil {
			err := ActionDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWebEligibilityFlowActionData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "WebEligibilityFlowActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "WebEligibilityFlowActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWebEligibilityFlowActionData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActionDataValidationError{
					field:  "WebEligibilityFlowActionData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActionData_CardCreationActionData:
		if v == nil {
			err := ActionDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCardCreationActionData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "CardCreationActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "CardCreationActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCardCreationActionData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActionDataValidationError{
					field:  "CardCreationActionData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActionData_CardClosureReminderActionData:
		if v == nil {
			err := ActionDataValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCardClosureReminderActionData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "CardClosureReminderActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActionDataValidationError{
						field:  "CardClosureReminderActionData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCardClosureReminderActionData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActionDataValidationError{
					field:  "CardClosureReminderActionData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ActionDataMultiError(errors)
	}

	return nil
}

// ActionDataMultiError is an error wrapping multiple validation errors
// returned by ActionData.ValidateAll() if the designated constraints aren't met.
type ActionDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActionDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActionDataMultiError) AllErrors() []error { return m }

// ActionDataValidationError is the validation error returned by
// ActionData.Validate if the designated constraints aren't met.
type ActionDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActionDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActionDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActionDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActionDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActionDataValidationError) ErrorName() string { return "ActionDataValidationError" }

// Error satisfies the builtin error interface
func (e ActionDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActionData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActionDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActionDataValidationError{}

// Validate checks the field values on CardClosureReminderActionData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardClosureReminderActionData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardClosureReminderActionData with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CardClosureReminderActionDataMultiError, or nil if none found.
func (m *CardClosureReminderActionData) ValidateAll() error {
	return m.validate(true)
}

func (m *CardClosureReminderActionData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCreditCard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardClosureReminderActionDataValidationError{
					field:  "CreditCard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardClosureReminderActionDataValidationError{
					field:  "CreditCard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreditCard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardClosureReminderActionDataValidationError{
				field:  "CreditCard",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DaysLeftBeforeClosure

	if len(errors) > 0 {
		return CardClosureReminderActionDataMultiError(errors)
	}

	return nil
}

// CardClosureReminderActionDataMultiError is an error wrapping multiple
// validation errors returned by CardClosureReminderActionData.ValidateAll()
// if the designated constraints aren't met.
type CardClosureReminderActionDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardClosureReminderActionDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardClosureReminderActionDataMultiError) AllErrors() []error { return m }

// CardClosureReminderActionDataValidationError is the validation error
// returned by CardClosureReminderActionData.Validate if the designated
// constraints aren't met.
type CardClosureReminderActionDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardClosureReminderActionDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardClosureReminderActionDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardClosureReminderActionDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardClosureReminderActionDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardClosureReminderActionDataValidationError) ErrorName() string {
	return "CardClosureReminderActionDataValidationError"
}

// Error satisfies the builtin error interface
func (e CardClosureReminderActionDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardClosureReminderActionData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardClosureReminderActionDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardClosureReminderActionDataValidationError{}

// Validate checks the field values on CardCreationActionData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardCreationActionData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardCreationActionData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardCreationActionDataMultiError, or nil if none found.
func (m *CardCreationActionData) ValidateAll() error {
	return m.validate(true)
}

func (m *CardCreationActionData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetCardProgram()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardCreationActionDataValidationError{
					field:  "CardProgram",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardCreationActionDataValidationError{
					field:  "CardProgram",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardProgram()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardCreationActionDataValidationError{
				field:  "CardProgram",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CardCreationActionDataMultiError(errors)
	}

	return nil
}

// CardCreationActionDataMultiError is an error wrapping multiple validation
// errors returned by CardCreationActionData.ValidateAll() if the designated
// constraints aren't met.
type CardCreationActionDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardCreationActionDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardCreationActionDataMultiError) AllErrors() []error { return m }

// CardCreationActionDataValidationError is the validation error returned by
// CardCreationActionData.Validate if the designated constraints aren't met.
type CardCreationActionDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardCreationActionDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardCreationActionDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardCreationActionDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardCreationActionDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardCreationActionDataValidationError) ErrorName() string {
	return "CardCreationActionDataValidationError"
}

// Error satisfies the builtin error interface
func (e CardCreationActionDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardCreationActionData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardCreationActionDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardCreationActionDataValidationError{}

// Validate checks the field values on TxnActionData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TxnActionData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TxnActionData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TxnActionDataMultiError, or
// nil if none found.
func (m *TxnActionData) ValidateAll() error {
	return m.validate(true)
}

func (m *TxnActionData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetCardTransactionWithAdditionalInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TxnActionDataValidationError{
					field:  "CardTransactionWithAdditionalInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TxnActionDataValidationError{
					field:  "CardTransactionWithAdditionalInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardTransactionWithAdditionalInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TxnActionDataValidationError{
				field:  "CardTransactionWithAdditionalInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FirstName

	if all {
		switch v := interface{}(m.GetAvailableAccountBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TxnActionDataValidationError{
					field:  "AvailableAccountBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TxnActionDataValidationError{
					field:  "AvailableAccountBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAvailableAccountBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TxnActionDataValidationError{
				field:  "AvailableAccountBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountNumber

	// no validation rules for CurrencyExchangeRate

	if all {
		switch v := interface{}(m.GetMarkUpFee()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TxnActionDataValidationError{
					field:  "MarkUpFee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TxnActionDataValidationError{
					field:  "MarkUpFee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMarkUpFee()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TxnActionDataValidationError{
				field:  "MarkUpFee",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CreditCardLastFourDigits

	// no validation rules for MaskedCardNumber

	if all {
		switch v := interface{}(m.GetBillWindow()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TxnActionDataValidationError{
					field:  "BillWindow",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TxnActionDataValidationError{
					field:  "BillWindow",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBillWindow()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TxnActionDataValidationError{
				field:  "BillWindow",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TxnActionDataMultiError(errors)
	}

	return nil
}

// TxnActionDataMultiError is an error wrapping multiple validation errors
// returned by TxnActionData.ValidateAll() if the designated constraints
// aren't met.
type TxnActionDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TxnActionDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TxnActionDataMultiError) AllErrors() []error { return m }

// TxnActionDataValidationError is the validation error returned by
// TxnActionData.Validate if the designated constraints aren't met.
type TxnActionDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TxnActionDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TxnActionDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TxnActionDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TxnActionDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TxnActionDataValidationError) ErrorName() string { return "TxnActionDataValidationError" }

// Error satisfies the builtin error interface
func (e TxnActionDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTxnActionData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TxnActionDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TxnActionDataValidationError{}

// Validate checks the field values on SettingsUpdateActionData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SettingsUpdateActionData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SettingsUpdateActionData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SettingsUpdateActionDataMultiError, or nil if none found.
func (m *SettingsUpdateActionData) ValidateAll() error {
	return m.validate(true)
}

func (m *SettingsUpdateActionData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardControlType

	// no validation rules for IsEnabled

	// no validation rules for ActorId

	// no validation rules for CreditCardLastFourDigits

	// no validation rules for FirstName

	// no validation rules for CreditCardType

	if len(errors) > 0 {
		return SettingsUpdateActionDataMultiError(errors)
	}

	return nil
}

// SettingsUpdateActionDataMultiError is an error wrapping multiple validation
// errors returned by SettingsUpdateActionData.ValidateAll() if the designated
// constraints aren't met.
type SettingsUpdateActionDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SettingsUpdateActionDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SettingsUpdateActionDataMultiError) AllErrors() []error { return m }

// SettingsUpdateActionDataValidationError is the validation error returned by
// SettingsUpdateActionData.Validate if the designated constraints aren't met.
type SettingsUpdateActionDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SettingsUpdateActionDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SettingsUpdateActionDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SettingsUpdateActionDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SettingsUpdateActionDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SettingsUpdateActionDataValidationError) ErrorName() string {
	return "SettingsUpdateActionDataValidationError"
}

// Error satisfies the builtin error interface
func (e SettingsUpdateActionDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSettingsUpdateActionData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SettingsUpdateActionDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SettingsUpdateActionDataValidationError{}

// Validate checks the field values on CardRequestActionData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardRequestActionData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardRequestActionData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardRequestActionDataMultiError, or nil if none found.
func (m *CardRequestActionData) ValidateAll() error {
	return m.validate(true)
}

func (m *CardRequestActionData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCardRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardRequestActionDataValidationError{
					field:  "CardRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardRequestActionDataValidationError{
					field:  "CardRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardRequestActionDataValidationError{
				field:  "CardRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CardRequestActionDataMultiError(errors)
	}

	return nil
}

// CardRequestActionDataMultiError is an error wrapping multiple validation
// errors returned by CardRequestActionData.ValidateAll() if the designated
// constraints aren't met.
type CardRequestActionDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardRequestActionDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardRequestActionDataMultiError) AllErrors() []error { return m }

// CardRequestActionDataValidationError is the validation error returned by
// CardRequestActionData.Validate if the designated constraints aren't met.
type CardRequestActionDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardRequestActionDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardRequestActionDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardRequestActionDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardRequestActionDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardRequestActionDataValidationError) ErrorName() string {
	return "CardRequestActionDataValidationError"
}

// Error satisfies the builtin error interface
func (e CardRequestActionDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardRequestActionData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardRequestActionDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardRequestActionDataValidationError{}

// Validate checks the field values on BillingActionData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BillingActionData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BillingActionData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BillingActionDataMultiError, or nil if none found.
func (m *BillingActionData) ValidateAll() error {
	return m.validate(true)
}

func (m *BillingActionData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PaymentReminderNotificationType

	// no validation rules for CardStatementNotificationType

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetUnpaidDueAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BillingActionDataValidationError{
					field:  "UnpaidDueAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BillingActionDataValidationError{
					field:  "UnpaidDueAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUnpaidDueAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BillingActionDataValidationError{
				field:  "UnpaidDueAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PaymentDueInDays

	// no validation rules for CreditCardId

	if all {
		switch v := interface{}(m.GetMinDueAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BillingActionDataValidationError{
					field:  "MinDueAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BillingActionDataValidationError{
					field:  "MinDueAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinDueAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BillingActionDataValidationError{
				field:  "MinDueAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalDueAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BillingActionDataValidationError{
					field:  "TotalDueAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BillingActionDataValidationError{
					field:  "TotalDueAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalDueAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BillingActionDataValidationError{
				field:  "TotalDueAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRepaymentDoneAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BillingActionDataValidationError{
					field:  "RepaymentDoneAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BillingActionDataValidationError{
					field:  "RepaymentDoneAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRepaymentDoneAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BillingActionDataValidationError{
				field:  "RepaymentDoneAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDueDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BillingActionDataValidationError{
					field:  "DueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BillingActionDataValidationError{
					field:  "DueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDueDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BillingActionDataValidationError{
				field:  "DueDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBillWindow()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BillingActionDataValidationError{
					field:  "BillWindow",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BillingActionDataValidationError{
					field:  "BillWindow",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBillWindow()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BillingActionDataValidationError{
				field:  "BillWindow",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNotificationTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BillingActionDataValidationError{
					field:  "NotificationTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BillingActionDataValidationError{
					field:  "NotificationTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNotificationTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BillingActionDataValidationError{
				field:  "NotificationTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Medium

	if len(errors) > 0 {
		return BillingActionDataMultiError(errors)
	}

	return nil
}

// BillingActionDataMultiError is an error wrapping multiple validation errors
// returned by BillingActionData.ValidateAll() if the designated constraints
// aren't met.
type BillingActionDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BillingActionDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BillingActionDataMultiError) AllErrors() []error { return m }

// BillingActionDataValidationError is the validation error returned by
// BillingActionData.Validate if the designated constraints aren't met.
type BillingActionDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BillingActionDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BillingActionDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BillingActionDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BillingActionDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BillingActionDataValidationError) ErrorName() string {
	return "BillingActionDataValidationError"
}

// Error satisfies the builtin error interface
func (e BillingActionDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBillingActionData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BillingActionDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BillingActionDataValidationError{}

// Validate checks the field values on CardTrackingActionData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardTrackingActionData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardTrackingActionData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardTrackingActionDataMultiError, or nil if none found.
func (m *CardTrackingActionData) ValidateAll() error {
	return m.validate(true)
}

func (m *CardTrackingActionData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for CardId

	// no validation rules for Carrier

	// no validation rules for Awb

	// no validation rules for DeliveryStatus

	if all {
		switch v := interface{}(m.GetCardHolderName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardTrackingActionDataValidationError{
					field:  "CardHolderName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardTrackingActionDataValidationError{
					field:  "CardHolderName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardHolderName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardTrackingActionDataValidationError{
				field:  "CardHolderName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardLastFourDigit

	if len(errors) > 0 {
		return CardTrackingActionDataMultiError(errors)
	}

	return nil
}

// CardTrackingActionDataMultiError is an error wrapping multiple validation
// errors returned by CardTrackingActionData.ValidateAll() if the designated
// constraints aren't met.
type CardTrackingActionDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardTrackingActionDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardTrackingActionDataMultiError) AllErrors() []error { return m }

// CardTrackingActionDataValidationError is the validation error returned by
// CardTrackingActionData.Validate if the designated constraints aren't met.
type CardTrackingActionDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardTrackingActionDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardTrackingActionDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardTrackingActionDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardTrackingActionDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardTrackingActionDataValidationError) ErrorName() string {
	return "CardTrackingActionDataValidationError"
}

// Error satisfies the builtin error interface
func (e CardTrackingActionDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardTrackingActionData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardTrackingActionDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardTrackingActionDataValidationError{}

// Validate checks the field values on EmiActionData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EmiActionData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EmiActionData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EmiActionDataMultiError, or
// nil if none found.
func (m *EmiActionData) ValidateAll() error {
	return m.validate(true)
}

func (m *EmiActionData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetCustomerName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "CustomerName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "CustomerName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomerName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmiActionDataValidationError{
				field:  "CustomerName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MerchantName

	if all {
		switch v := interface{}(m.GetDueAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "DueAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "DueAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDueAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmiActionDataValidationError{
				field:  "DueAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPreClosureFee()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "PreClosureFee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "PreClosureFee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPreClosureFee()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmiActionDataValidationError{
				field:  "PreClosureFee",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EmiCommsType

	if all {
		switch v := interface{}(m.GetTransactionAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "TransactionAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "TransactionAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmiActionDataValidationError{
				field:  "TransactionAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEmiAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "EmiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "EmiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmiAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmiActionDataValidationError{
				field:  "EmiAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreationDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "CreationDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "CreationDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreationDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmiActionDataValidationError{
				field:  "CreationDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCancellationDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "CancellationDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "CancellationDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCancellationDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmiActionDataValidationError{
				field:  "CancellationDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTransactionDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "TransactionDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "TransactionDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmiActionDataValidationError{
				field:  "TransactionDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Tenure

	// no validation rules for InterestRate

	if all {
		switch v := interface{}(m.GetProcessingFees()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "ProcessingFees",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "ProcessingFees",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcessingFees()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmiActionDataValidationError{
				field:  "ProcessingFees",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextBillGenDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "NextBillGenDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "NextBillGenDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextBillGenDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmiActionDataValidationError{
				field:  "NextBillGenDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardLastFourDigits

	if all {
		switch v := interface{}(m.GetInterestCharges()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "InterestCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "InterestCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterestCharges()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmiActionDataValidationError{
				field:  "InterestCharges",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmountAddedToNextStatement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "AmountAddedToNextStatement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "AmountAddedToNextStatement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmountAddedToNextStatement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmiActionDataValidationError{
				field:  "AmountAddedToNextStatement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCompletionDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "CompletionDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmiActionDataValidationError{
					field:  "CompletionDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletionDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmiActionDataValidationError{
				field:  "CompletionDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EmiActionDataMultiError(errors)
	}

	return nil
}

// EmiActionDataMultiError is an error wrapping multiple validation errors
// returned by EmiActionData.ValidateAll() if the designated constraints
// aren't met.
type EmiActionDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmiActionDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmiActionDataMultiError) AllErrors() []error { return m }

// EmiActionDataValidationError is the validation error returned by
// EmiActionData.Validate if the designated constraints aren't met.
type EmiActionDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmiActionDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmiActionDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmiActionDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmiActionDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmiActionDataValidationError) ErrorName() string { return "EmiActionDataValidationError" }

// Error satisfies the builtin error interface
func (e EmiActionDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmiActionData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmiActionDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmiActionDataValidationError{}

// Validate checks the field values on CardClosureActionData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardClosureActionData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardClosureActionData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardClosureActionDataMultiError, or nil if none found.
func (m *CardClosureActionData) ValidateAll() error {
	return m.validate(true)
}

func (m *CardClosureActionData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCreditCard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardClosureActionDataValidationError{
					field:  "CreditCard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardClosureActionDataValidationError{
					field:  "CreditCard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreditCard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardClosureActionDataValidationError{
				field:  "CreditCard",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CardClosureActionDataMultiError(errors)
	}

	return nil
}

// CardClosureActionDataMultiError is an error wrapping multiple validation
// errors returned by CardClosureActionData.ValidateAll() if the designated
// constraints aren't met.
type CardClosureActionDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardClosureActionDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardClosureActionDataMultiError) AllErrors() []error { return m }

// CardClosureActionDataValidationError is the validation error returned by
// CardClosureActionData.Validate if the designated constraints aren't met.
type CardClosureActionDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardClosureActionDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardClosureActionDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardClosureActionDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardClosureActionDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardClosureActionDataValidationError) ErrorName() string {
	return "CardClosureActionDataValidationError"
}

// Error satisfies the builtin error interface
func (e CardClosureActionDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardClosureActionData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardClosureActionDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardClosureActionDataValidationError{}

// Validate checks the field values on SecuredCardCommsActionData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SecuredCardCommsActionData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SecuredCardCommsActionData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SecuredCardCommsActionDataMultiError, or nil if none found.
func (m *SecuredCardCommsActionData) ValidateAll() error {
	return m.validate(true)
}

func (m *SecuredCardCommsActionData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DepositId

	// no validation rules for ActorId

	// no validation rules for CardRequestStageName

	if len(errors) > 0 {
		return SecuredCardCommsActionDataMultiError(errors)
	}

	return nil
}

// SecuredCardCommsActionDataMultiError is an error wrapping multiple
// validation errors returned by SecuredCardCommsActionData.ValidateAll() if
// the designated constraints aren't met.
type SecuredCardCommsActionDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecuredCardCommsActionDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecuredCardCommsActionDataMultiError) AllErrors() []error { return m }

// SecuredCardCommsActionDataValidationError is the validation error returned
// by SecuredCardCommsActionData.Validate if the designated constraints aren't met.
type SecuredCardCommsActionDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecuredCardCommsActionDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecuredCardCommsActionDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecuredCardCommsActionDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecuredCardCommsActionDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecuredCardCommsActionDataValidationError) ErrorName() string {
	return "SecuredCardCommsActionDataValidationError"
}

// Error satisfies the builtin error interface
func (e SecuredCardCommsActionDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecuredCardCommsActionData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecuredCardCommsActionDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecuredCardCommsActionDataValidationError{}

// Validate checks the field values on WebEligibilityFlowActionData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WebEligibilityFlowActionData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WebEligibilityFlowActionData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WebEligibilityFlowActionDataMultiError, or nil if none found.
func (m *WebEligibilityFlowActionData) ValidateAll() error {
	return m.validate(true)
}

func (m *WebEligibilityFlowActionData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for AppDownloadUrl

	// no validation rules for RedirectionUrl

	if all {
		switch v := interface{}(m.GetAdditionalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WebEligibilityFlowActionDataValidationError{
					field:  "AdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WebEligibilityFlowActionDataValidationError{
					field:  "AdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdditionalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WebEligibilityFlowActionDataValidationError{
				field:  "AdditionalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return WebEligibilityFlowActionDataMultiError(errors)
	}

	return nil
}

// WebEligibilityFlowActionDataMultiError is an error wrapping multiple
// validation errors returned by WebEligibilityFlowActionData.ValidateAll() if
// the designated constraints aren't met.
type WebEligibilityFlowActionDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WebEligibilityFlowActionDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WebEligibilityFlowActionDataMultiError) AllErrors() []error { return m }

// WebEligibilityFlowActionDataValidationError is the validation error returned
// by WebEligibilityFlowActionData.Validate if the designated constraints
// aren't met.
type WebEligibilityFlowActionDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WebEligibilityFlowActionDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WebEligibilityFlowActionDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WebEligibilityFlowActionDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WebEligibilityFlowActionDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WebEligibilityFlowActionDataValidationError) ErrorName() string {
	return "WebEligibilityFlowActionDataValidationError"
}

// Error satisfies the builtin error interface
func (e WebEligibilityFlowActionDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWebEligibilityFlowActionData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WebEligibilityFlowActionDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WebEligibilityFlowActionDataValidationError{}

// Validate checks the field values on AdditionalDetail with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AdditionalDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AdditionalDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AdditionalDetailMultiError, or nil if none found.
func (m *AdditionalDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *AdditionalDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Medium

	switch v := m.CommType.(type) {
	case *AdditionalDetail_EmailType:
		if v == nil {
			err := AdditionalDetailValidationError{
				field:  "CommType",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for EmailType
	case *AdditionalDetail_SmsType:
		if v == nil {
			err := AdditionalDetailValidationError{
				field:  "CommType",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for SmsType
	case *AdditionalDetail_WhatsappType:
		if v == nil {
			err := AdditionalDetailValidationError{
				field:  "CommType",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for WhatsappType
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return AdditionalDetailMultiError(errors)
	}

	return nil
}

// AdditionalDetailMultiError is an error wrapping multiple validation errors
// returned by AdditionalDetail.ValidateAll() if the designated constraints
// aren't met.
type AdditionalDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AdditionalDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AdditionalDetailMultiError) AllErrors() []error { return m }

// AdditionalDetailValidationError is the validation error returned by
// AdditionalDetail.Validate if the designated constraints aren't met.
type AdditionalDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AdditionalDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AdditionalDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AdditionalDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AdditionalDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AdditionalDetailValidationError) ErrorName() string { return "AdditionalDetailValidationError" }

// Error satisfies the builtin error interface
func (e AdditionalDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAdditionalDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AdditionalDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AdditionalDetailValidationError{}
