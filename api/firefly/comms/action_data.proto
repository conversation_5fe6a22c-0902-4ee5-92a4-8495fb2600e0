syntax = "proto3";

package firefly.comms;

import "api/comms/email_template.proto";
import "api/comms/enums.proto";
import "api/comms/sms_template.proto";
import "api/comms/whatsapp_template.proto";
import "api/firefly/accounting/service.proto";
import "api/firefly/billing/service.proto";
import "api/firefly/enums/enums.proto";
import "api/firefly/internal/card_request.proto";
import "api/firefly/internal/credit_card.proto";
import "api/firefly/lms/enums/enums.proto";
import "api/typesv2/firefly.proto";
import "api/typesv2/common/name.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/firefly/comms";
option java_package = "com.github.epifi.gamma.api.firefly.comms";


message ActionData {
  oneof Data {
    SettingsUpdateActionData settings_update_action_data = 1;
    TxnActionData txn_action_data = 2;
    CardRequestActionData card_request_action_data = 3;
    BillingActionData billing_action_data = 4;
    CardTrackingActionData card_tracking_action_data = 5;
    CardClosureActionData card_closure_action_data = 6;
    SecuredCardCommsActionData secured_card_action_data = 7;
    EmiActionData emi_action_data = 8;
    WebEligibilityFlowActionData web_eligibility_flow_action_data = 9;
    CardCreationActionData card_creation_action_data = 10;
  }
}

message CardClosureReminderActionData {
  firefly.CreditCard credit_card = 1;
  int32 days_left_before_closure = 2;
}

message CardCreationActionData {
  string actor_id = 1;
  api.typesv2.CardProgram card_program = 2;
}

message TxnActionData {
  string actor_id = 1;
  firefly.accounting.CardTransactionWithAdditionalInfo card_transaction_with_additional_info = 2;
  string first_name = 3;
  google.type.Money available_account_balance = 4;
  string account_number = 5;
  float currency_exchange_rate = 6;
  google.type.Money mark_up_fee = 7;
  string credit_card_last_four_digits = 8;
  string masked_card_number = 9;
  // For comms that have to be sent once in a billing window, we will populate this field.
  // example comms include: successful repayment in a billing window.
  firefly.billing.BillWindow bill_window = 10;
}

message SettingsUpdateActionData {
  firefly.enums.CardControlType card_control_type = 1;
  bool is_enabled = 2;
  string actor_id = 3;
  string credit_card_last_four_digits = 4;
  string first_name = 5;
  string credit_card_type = 6;
}

message CardRequestActionData {
  firefly.CardRequest card_request = 1;
}

message BillingActionData {
  enums.PaymentReminderNotificationType payment_reminder_notification_type = 1;
  enums.CreditCardStatementNotificationType card_statement_notification_type = 2;
  string actor_id = 3;
  google.type.Money unpaid_due_amount = 4;
  int32 payment_due_in_days = 5;
  // since bill generation card request does not depend on a credit card id
  // we need to pass it here for comms rules to use it.
  string credit_card_id = 6;
  google.type.Money min_due_amount = 7;
  google.type.Money total_due_amount = 8;
  google.protobuf.Timestamp repayment_done_at = 12;
  google.type.Date due_date = 13;
  // For comms that have to be sent once in a billing window, we will populate this field.
  // example comms include: successful repayment in a billing window.
  firefly.billing.BillWindow bill_window = 14;
  google.protobuf.Timestamp notification_timestamp = 15;
  .comms.Medium medium = 16;
}

message CardTrackingActionData {
  string actor_id = 1;
  string card_id = 2;
  string carrier = 3;
  string awb = 4;
  enums.CardRequestStageSubStatus delivery_status = 5;
  api.typesv2.common.Name card_holder_name = 6;
  string card_last_four_digit = 7;
}

message EmiActionData {
  string actor_id = 1;
  api.typesv2.common.Name customer_name = 2;
  string merchant_name = 3;
  google.type.Money due_amount = 4;
  google.type.Money pre_closure_fee = 5;
  firefly.lms.EmiCommsType emi_comms_type = 6;
  google.type.Money transaction_amount = 7;
  google.type.Money emi_amount = 8;
  google.type.Date creation_date = 9;
  google.type.Date cancellation_date = 10;
  google.type.Date transaction_date = 11;
  int64 tenure = 12;
  double interest_rate = 13;
  google.type.Money processing_fees = 14;
  google.type.Date next_bill_gen_date = 15;
  string card_last_four_digits = 16;
  google.type.Money interest_charges = 17;
  google.type.Money amount_added_to_next_statement = 18;
  google.type.Date completion_date = 19;
}

message CardClosureActionData {
  firefly.CreditCard credit_card = 1;
}

message SecuredCardCommsActionData {
  string deposit_id = 1;
  string actor_id = 2;
  firefly.enums.CardRequestStageName card_request_stage_name = 3;
}

message WebEligibilityFlowActionData {
  string actor_id = 1;
  string app_download_url = 2;
  string redirection_url = 3;
  AdditionalDetail additional_details = 4;
}

message AdditionalDetail {
  .comms.Medium medium = 1;
  oneof comm_type {
    .comms.EmailType email_type = 2;
    .comms.SmsType sms_type = 3;
    .comms.WhatsappType whatsapp_type = 4;
  }
}
