// nolint
package process_amc_eligible_users

import (
	"context"
	"time"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/google/uuid"
	errorsPkg "github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"golang.org/x/time/rate"
	"google.golang.org/genproto/googleapis/type/date"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/money"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/be-common/pkg/epifitemporal"

	savingsAccountEnums "github.com/epifi/gamma/api/accounts/enums"
	cardPb "github.com/epifi/gamma/api/card"
	cardActivityPb "github.com/epifi/gamma/api/card/activity/processamceligibleusers"
	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	cpPb "github.com/epifi/gamma/api/card/provisioning"
	externalPb "github.com/epifi/gamma/api/tiering/external"
)

var (
	operationalStatusMap = map[savingsAccountEnums.OperationalStatus]cardEnumsPb.OperationalStatus{
		savingsAccountEnums.OperationalStatus_OPERATIONAL_STATUS_UNSPECIFIED: cardEnumsPb.OperationalStatus_OPERATIONAL_STATUS_UNSPECIFIED,
		savingsAccountEnums.OperationalStatus_OPERATIONAL_STATUS_ACTIVE:      cardEnumsPb.OperationalStatus_OPERATIONAL_STATUS_ACTIVE,
		savingsAccountEnums.OperationalStatus_OPERATIONAL_STATUS_INACTIVE:    cardEnumsPb.OperationalStatus_OPERATIONAL_STATUS_INACTIVE,
		savingsAccountEnums.OperationalStatus_OPERATIONAL_STATUS_DORMANT:     cardEnumsPb.OperationalStatus_OPERATIONAL_STATUS_DORMANT,
		savingsAccountEnums.OperationalStatus_OPERATIONAL_STATUS_CLOSED:      cardEnumsPb.OperationalStatus_OPERATIONAL_STATUS_CLOSED,
	}
	freezeStatusMap = map[savingsAccountEnums.FreezeStatus]cardEnumsPb.FreezeStatus{
		savingsAccountEnums.FreezeStatus_FREEZE_STATUS_UNSPECIFIED:   cardEnumsPb.FreezeStatus_FREEZE_STATUS_UNSPECIFIED,
		savingsAccountEnums.FreezeStatus_FREEZE_STATUS_TOTAL_FREEZE:  cardEnumsPb.FreezeStatus_FREEZE_STATUS_TOTAL_FREEZE,
		savingsAccountEnums.FreezeStatus_FREEZE_STATUS_CREDIT_FREEZE: cardEnumsPb.FreezeStatus_FREEZE_STATUS_CREDIT_FREEZE,
		savingsAccountEnums.FreezeStatus_FREEZE_STATUS_DEBIT_FREEZE:  cardEnumsPb.FreezeStatus_FREEZE_STATUS_DEBIT_FREEZE,
	}
)

type AmcUserRecord struct {
	CardId           string
	ActorId          string
	SavingsAccountId string
	MaskedCardNumber string
	AmcCharge        *money.Money
	AnniversaryTier  externalPb.Tier
	AnniversaryDate  *date.Date
}

func (p *Processor) ProcessAmcUserBatch(ctx context.Context, req *cardActivityPb.ProcessAmcUserBatchRequest) (*cardActivityPb.ProcessAmcUserBatchResponse, error) {
	var (
		res = &cardActivityPb.ProcessAmcUserBatchResponse{}
	)
	lg := activity.GetLogger(ctx)

	amcUserRecordsForBatch, userDataErr := p.parseFileGetAmcUserRecords(ctx, req)
	if userDataErr != nil {
		lg.Error("error getting actor ids for batch", userDataErr)
		return nil, epifitemporal.NewTransientError(errorsPkg.Wrap(userDataErr, "error getting actor ids for batch"))
	}

	failedUserCount := p.processOperationalAccountStatusForBatch(ctx, amcUserRecordsForBatch, req.GetFileGenDate(), req.GetBatchNumber())
	if failedUserCount > 0 {
		return nil, epifitemporal.NewTransientError(errorsPkg.New("operational status fetch and card request creation failed for few users"))
	}

	return res, nil
}

func (p *Processor) parseFileGetAmcUserRecords(ctx context.Context, req *cardActivityPb.ProcessAmcUserBatchRequest) ([]*AmcUserRecord, error) {
	var (
		amcUserRecord = make([]*AmcUserRecord, 0)
	)

	// this will fetch all the records in the file
	_, amcUserInfos, csvReadError := p.getAmcUsersInfoFromCsv(ctx, req.GetUserBaseFileS3Path())
	if csvReadError != nil {
		return nil, epifitemporal.NewTransientError(errorsPkg.Wrap(csvReadError, "read csv file failed"))
	}

	for _, userRecord := range amcUserInfos {
		// ignore processing for the card id if not part of the batch
		// TODO(CB): add batch specific iteration logic here instead of iterating over the whole user base.
		if !lo.Contains(req.GetCardIds(), userRecord.CardId) {
			continue
		}
		cardDetails, err := p.cardDao.GetByID(ctx, userRecord.CardId, cardPb.CardFieldMask_CARD_ACTOR_ID, cardPb.CardFieldMask_CARD_SAVINGS_ACCOUNT_ID, cardPb.CardFieldMask_CARD_BASIC_INFO)
		if err != nil {
			return nil, err
		}

		amcCharge, err := p.evaluateAmcChargeForUser(userRecord.AnniversaryTier, userRecord.AnniversaryDate)
		if err != nil {
			return nil, err
		}
		amcUserRecord = append(amcUserRecord, &AmcUserRecord{
			CardId:           userRecord.CardId,
			ActorId:          cardDetails.GetActorId(),
			SavingsAccountId: cardDetails.GetSavingsAccountId(),
			MaskedCardNumber: cardDetails.GetBasicInfo().GetMaskedCardNumber(),
			AmcCharge:        amcCharge,
			AnniversaryTier:  userRecord.AnniversaryTier,
			AnniversaryDate:  userRecord.AnniversaryDate,
		})
	}
	return amcUserRecord, nil
}

func (p *Processor) evaluateAmcChargeForUser(anniversaryTier externalPb.Tier, anniversaryDate *date.Date) (*money.Money, error) {
	amcCharge, err := p.conf.AmcConfig.GetAmcCharge(anniversaryTier.String(), anniversaryDate)
	if err != nil {
		return nil, errorsPkg.Wrap(err, "error getting amc charge")
	}
	return &money.Money{Pb: amcCharge}, nil
}

func (p *Processor) processOperationalAccountStatusForBatch(ctx context.Context, amcUserRecords []*AmcUserRecord, fileGenDate *date.Date, batchNumber int32) int {
	lg := activity.GetLogger(ctx)
	failedUserCount := 0
	limiter := rate.NewLimiter(rate.Every(time.Second/12), 5)
	for _, amcUserRecord := range amcUserRecords {
		err := limiter.Wait(ctx)
		if err != nil {
			lg.Error("error waiting for rate limiter", zap.Error(err))
			failedUserCount += 1
			continue
		}
		cardRequests, err := p.cardRequestDao.GetByActorIdAndWorkflow(ctx, amcUserRecord.ActorId, cardEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_PROCESS_AMC_CHARGES,
			nil)
		switch {
		case storageV2.IsRecordNotFoundError(err):
			_, cardErr := p.fetchOperationalStatusAndCreateCardRequest(ctx, amcUserRecord, fileGenDate, batchNumber, nil)
			lg.Error("error fetching op status and creating card request", zap.Error(cardErr))
			if cardErr != nil {
				failedUserCount += 1
			}
			continue
		case err != nil:
			lg.Error("error fetching card request", zap.Error(err))
			failedUserCount += 1
			continue
		}
		if !p.isValidCardRequest(cardRequests[0]) ||
			cardRequests[0].GetStatus() == cardEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED ||
			cardRequests[0].GetStatus() == cardEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_PERMANENT_FAILURE {
			_, cardErr := p.fetchOperationalStatusAndCreateCardRequest(ctx, amcUserRecord, fileGenDate, batchNumber, cardRequests[0])
			if cardErr != nil {
				lg.Error("error fetching op status and creating card request", zap.Error(cardErr))
				failedUserCount += 1
			}
		}
	}
	return failedUserCount
}

func (p *Processor) isValidCardRequest(cardRequest *cpPb.CardRequest) bool {
	createdAtDate := datetimePkg.TimestampToDateInLoc(cardRequest.GetCreatedAt(), datetimePkg.IST)
	currentTimeDate := datetimePkg.TimestampToDateInLoc(timestampPb.Now(), datetimePkg.IST)
	return createdAtDate.GetYear() == currentTimeDate.GetYear()
}

func (p *Processor) fetchOperationalStatusAndCreateCardRequest(ctx context.Context, amcUserRecord *AmcUserRecord, fileGenDate *date.Date, batchNumber int32, existingRequest *cpPb.CardRequest) (*cpPb.CardRequest, error) {
	cardRequest := &cpPb.CardRequest{
		CardId:          amcUserRecord.CardId,
		ActorId:         amcUserRecord.ActorId,
		OrchestrationId: uuid.New().String(),
		Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
		RequestDetails:  &cpPb.CardRequestDetails{},
		Workflow:        cardEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_PROCESS_AMC_CHARGES,
		Status:          cardEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS,
	}

	cardRequest.GetRequestDetails().Data = &cpPb.CardRequestDetails_AmcChargesDetails{
		AmcChargesDetails: &cpPb.AmcChargesDetails{
			FileGenDate:           fileGenDate,
			BatchNumber:           batchNumber,
			AmcChargeAmount:       amcUserRecord.AmcCharge.GetPb(),
			AnniversaryDate:       amcUserRecord.AnniversaryDate,
			TierAtAnniversaryDate: amcUserRecord.AnniversaryTier,
		},
	}

	createResponse, createErr := p.cardRequestDao.Create(ctx, cardRequest)
	if createErr != nil {
		return nil, errorsPkg.Wrap(createErr, "error creating card request")
	}

	return createResponse, nil
}

func parseAnniversaryDateString(anniversaryDateString string) (*date.Date, error) {
	anniversaryDate, err := datetimePkg.ParseStringToDateInLocation(datetimePkg.DATE_LAYOUT_YYYYMMDD, anniversaryDateString, datetimePkg.IST)
	if err != nil {
		return nil, errorsPkg.Wrap(err, "error parsing amc anniversary date string")
	}
	return anniversaryDate, nil
}
