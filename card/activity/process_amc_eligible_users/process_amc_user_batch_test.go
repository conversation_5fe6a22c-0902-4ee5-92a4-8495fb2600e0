package process_amc_eligible_users_test

import (
	"testing"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	"github.com/golang/mock/gomock"
	"github.com/jszwec/csvutil"
	"github.com/stretchr/testify/mock"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"

	cardNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/card"

	cardPb "github.com/epifi/gamma/api/card"
	cardActivityPb "github.com/epifi/gamma/api/card/activity/processamceligibleusers"
	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	cpPb "github.com/epifi/gamma/api/card/provisioning"
	tieringExternalPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/card/activity/process_amc_eligible_users"
)

func TestProcessor_ProcessAmcUserBatch(t *testing.T) {
	proc, md, cleanUpFunc := newAmcCardActivityProcessorWithMocks(t)
	defer cleanUpFunc()

	expectedTestCardData := []*process_amc_eligible_users.AmcUserRecord{
		// Tier - PLUS
		// anniversary after or on 8/3/2025
		{
			CardId:           "card-id-1",
			ActorId:          "actor-id-1",
			SavingsAccountId: "account-id-1",
			MaskedCardNumber: "4123-XXXX-XXXX-4123",
			AmcCharge:        &moneyPkg.Money{Pb: &money.Money{CurrencyCode: "INR", Units: 199}},
			AnniversaryTier:  tieringExternalPb.Tier_TIER_FI_PLUS,
			AnniversaryDate:  &date.Date{Year: 2025, Month: 3, Day: 8},
		},
		// Tier - PLUS
		// anniversary before or on 8/3/2025
		{
			CardId:           "card-id-2",
			ActorId:          "actor-id-2",
			SavingsAccountId: "account-id-2",
			MaskedCardNumber: "4123-XXXX-XXXX-4123",
			AmcCharge:        &moneyPkg.Money{Pb: &money.Money{CurrencyCode: "INR", Units: 199}},
			AnniversaryTier:  tieringExternalPb.Tier_TIER_FI_PLUS,
			AnniversaryDate:  &date.Date{Year: 2025, Month: 1, Day: 1},
		},
		// Tier - BASIC
		// anniversary after or on 8/3/2025
		{
			CardId:           "card-id-3",
			ActorId:          "actor-id-3",
			SavingsAccountId: "account-id-3",
			MaskedCardNumber: "4123-XXXX-XXXX-4123",
			AmcCharge:        &moneyPkg.Money{Pb: &money.Money{CurrencyCode: "INR", Units: 299}},
			AnniversaryTier:  tieringExternalPb.Tier_TIER_FI_BASIC,
			AnniversaryDate:  &date.Date{Year: 2025, Month: 3, Day: 8},
		},
		// Tier - BASIC
		// anniversary after or on 8/3/2025
		{
			CardId:           "card-id-4",
			ActorId:          "actor-id-4",
			SavingsAccountId: "account-id-4",
			MaskedCardNumber: "4123-XXXX-XXXX-4123",
			AmcCharge:        &moneyPkg.Money{Pb: &money.Money{CurrencyCode: "INR", Units: 199}},
			AnniversaryTier:  tieringExternalPb.Tier_TIER_FI_BASIC,
			AnniversaryDate:  &date.Date{Year: 2025, Month: 1, Day: 1},
		},
		// Tier - REGULAR
		// anniversary after or on 8/3/2025
		{
			CardId:           "card-id-5",
			ActorId:          "actor-id-5",
			SavingsAccountId: "account-id-5",
			MaskedCardNumber: "4123-XXXX-XXXX-4123",
			AmcCharge:        &moneyPkg.Money{Pb: &money.Money{CurrencyCode: "INR", Units: 399}},
			AnniversaryTier:  tieringExternalPb.Tier_TIER_FI_REGULAR,
			AnniversaryDate:  &date.Date{Year: 2025, Month: 6, Day: 1},
		},
		// Tier - REGULAR
		// anniversary after or on 8/3/2025
		{
			CardId:           "card-id-6",
			ActorId:          "actor-id-6",
			SavingsAccountId: "account-id-6",
			MaskedCardNumber: "4123-XXXX-XXXX-4123",
			AmcCharge:        &moneyPkg.Money{Pb: &money.Money{CurrencyCode: "INR", Units: 300}},
			AnniversaryTier:  tieringExternalPb.Tier_TIER_FI_REGULAR,
			AnniversaryDate:  &date.Date{Year: 2025, Month: 1, Day: 1},
		},
	}
	csvData := []*process_amc_eligible_users.AmcSourceFileCsvRecord{
		{
			CardId:          "card-id-0",
			AnniversaryTier: "TIER_FI_PLUS",
			AnniversaryDate: "2025-10-26",
		},
		{
			CardId:          "card-id-1",
			AnniversaryTier: "TIER_FI_PLUS",
			AnniversaryDate: "2025-03-08",
		},
		{
			CardId:          "card-id-2",
			AnniversaryTier: "TIER_FI_PLUS",
			AnniversaryDate: "2025-01-01",
		},
		{
			CardId:          "card-id-3",
			AnniversaryTier: "TIER_FI_BASIC",
			AnniversaryDate: "2025-03-08",
		},
		{
			CardId:          "card-id-4",
			AnniversaryTier: "TIER_FI_BASIC",
			AnniversaryDate: "2025-01-01",
		},
		{
			CardId:          "card-id-5",
			AnniversaryTier: "TIER_FI_REGULAR",
			AnniversaryDate: "2025-06-01",
		},
		{
			CardId:          "card-id-6",
			AnniversaryTier: "TIER_FI_REGULAR",
			AnniversaryDate: "2025-01-01",
		},
		{
			CardId:          "card-id-7",
			AnniversaryTier: "TIER_FI_REGULAR",
			AnniversaryDate: "2025-10-26",
		},
	}
	marshalledCsvData, err := csvutil.Marshal(csvData)
	if err != nil {
		t.Fatalf("failed to marshal csv data: %v", err)
	}
	fileGenDate := &date.Date{Year: 2025, Month: 10, Day: 26}

	tests := []struct {
		name           string
		req            *cardActivityPb.ProcessAmcUserBatchRequest
		setupMockCalls func()
		want           *cardActivityPb.ProcessAmcUserBatchResponse
		wantErr        bool
		assertErr      func(err error) bool
	}{
		{
			name: "Success case - no existing card request",
			req: &cardActivityPb.ProcessAmcUserBatchRequest{
				CardIds:     []string{"card-id-1", "card-id-2", "card-id-3", "card-id-4", "card-id-5", "card-id-6"},
				FileGenDate: fileGenDate,
				BatchNumber: 2,
			},
			setupMockCalls: func() {
				md.dcDocsS3Client.EXPECT().Read(gomock.Any(), gomock.Any()).Return(marshalledCsvData, nil)
				for _, cardData := range expectedTestCardData {
					md.cardDao.EXPECT().GetByID(gomock.Any(), cardData.CardId, gomock.Any(), gomock.Any(), gomock.Any()).Return(
						&cardPb.Card{
							ActorId:          cardData.ActorId,
							SavingsAccountId: cardData.SavingsAccountId,
							BasicInfo:        &cardPb.BasicCardInfo{MaskedCardNumber: cardData.MaskedCardNumber},
						}, nil)
					md.cardRequestDao.EXPECT().GetByActorIdAndWorkflow(gomock.Any(), cardData.ActorId,
						cardEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_PROCESS_AMC_CHARGES, nil).
						Return(nil, epifierrors.ErrRecordNotFound)
					md.cardRequestDao.EXPECT().Create(gomock.Any(), newCardRequestMatcher(&cpPb.CardRequest{
						CardId:          cardData.CardId,
						ActorId:         cardData.ActorId,
						OrchestrationId: mock.Anything,
						Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
						RequestDetails: &cpPb.CardRequestDetails{
							Data: &cpPb.CardRequestDetails_AmcChargesDetails{
								AmcChargesDetails: &cpPb.AmcChargesDetails{
									FileGenDate:           fileGenDate,
									BatchNumber:           2,
									AmcChargeAmount:       cardData.AmcCharge.GetPb(),
									AnniversaryDate:       cardData.AnniversaryDate,
									TierAtAnniversaryDate: cardData.AnniversaryTier,
								},
							},
						},
						Workflow: cardEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_PROCESS_AMC_CHARGES,
						Status:   cardEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS,
					})).Return(&cpPb.CardRequest{
						CardId:          cardData.CardId,
						ActorId:         cardData.ActorId,
						OrchestrationId: mock.Anything,
						Vendor:          commonvgpb.Vendor_FEDERAL_BANK,
						RequestDetails: &cpPb.CardRequestDetails{
							Data: &cpPb.CardRequestDetails_AmcChargesDetails{
								AmcChargesDetails: &cpPb.AmcChargesDetails{
									FileGenDate:           fileGenDate,
									BatchNumber:           2,
									AmcChargeAmount:       cardData.AmcCharge.GetPb(),
									OperationalStatus:     cardEnumsPb.OperationalStatus_OPERATIONAL_STATUS_ACTIVE,
									FreezeStatus:          cardEnumsPb.FreezeStatus_FREEZE_STATUS_UNSPECIFIED,
									AnniversaryDate:       cardData.AnniversaryDate,
									TierAtAnniversaryDate: cardData.AnniversaryTier,
								},
							},
						},
						Workflow: cardEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_PROCESS_AMC_CHARGES,
						Status:   cardEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS,
					}, nil)
				}
			},
			want:    &cardActivityPb.ProcessAmcUserBatchResponse{},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()

			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(proc)

			var result *cardActivityPb.ProcessAmcUserBatchResponse
			got, err := env.ExecuteActivity(cardNs.ProcessAmcUserBatch, tt.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("ProcessAmcUserBatch() error = %v failed to fetch type value from convertible", err)
					return
				}
			}
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessAmcUserBatch() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.wantErr && !tt.assertErr(err) {
				t.Errorf("ProcessAmcUserBatch() error = %v, assertion failed", err)
			}
			if tt.want != nil && !proto.Equal(result, tt.want) {
				t.Errorf("ProcessAmcUserBatch() got = %v, want %v", result, tt.want)
			}
		})
	}
}

type cardRequestMatcher struct {
	want *cpPb.CardRequest
}

func newCardRequestMatcher(want *cpPb.CardRequest) *cardRequestMatcher {
	return &cardRequestMatcher{want: want}
}

func (m *cardRequestMatcher) String() string {
	return m.want.String()
}

func (m *cardRequestMatcher) Matches(x interface{}) bool {
	got, ok := x.(*cpPb.CardRequest)
	if !ok {
		return false
	}
	got.OrchestrationId = m.want.GetOrchestrationId()
	return proto.Equal(got, m.want)
}
